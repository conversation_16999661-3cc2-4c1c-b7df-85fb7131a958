# Node.js, Grunt - files
/node_modules/
.sass-cache/
.sublime-grunt.cache

# MacOS - files
Thumbs.db
.DS_Store

# SublimeText - project files
tipli.sublime-project
tipli.sublime-workspace

#Ignore css source map
www/css/*.map

.idea/*
nbproject/*

log/*
migrations/*
temp/*
temp/sessions/*
!temp/sessions/.gitignore
vendor/*
www/webtemp/*
!www/webtemp/.gitignore
www/.sass-cache/*
www/upload/*
!www/upload/images/.gitignore
!www/upload/thumbnails/.gitignore
!www/upload/files/.gitignore
!.gitignore
!.htaccess
!web.config
app/config/config.local.neon
app/config/config.production.neon

tests/codeception/tests/_output/
!tests/codeception/tests/_output/.gitignore

#local codeception mkurricz
tests/_data/*
tests/_output/*
tests/_support/*
tests/acceptance/*
tests/acceptance/SeeHomepageCest.php
tests/functional/*
tests/unit/*
tests/acceptance.suite.yml
tests/functional.suite.yml
tests/unit.suite.yml
codeception.yml
www/keen/tools/node_modules/
/temp_local/

/volumes/
