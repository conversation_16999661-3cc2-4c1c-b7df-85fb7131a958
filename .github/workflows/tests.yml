name: Tests

on:
    push:
        branches: [ master ]
    pull_request:
        branches: [ master ]

env:
    PHP_VERSION: '8.1'
    MYSQL_ROOT_PASSWORD: root
    MYSQL_DATABASE: tipli
    MYSQL_USER: tipli
    MYSQL_PASSWORD: tipli
    env: test

concurrency:
    group: ${{ github.workflow }}-${{ github.ref }}
    cancel-in-progress: true

jobs:
    codeception-tests:
        runs-on: ubuntu-latest

        services:
            mysql:
                image: mysql:8.0
                env:
                    MYSQL_ROOT_PASSWORD: ${{ env.MYSQL_ROOT_PASSWORD }}
                    MYSQL_DATABASE: ${{ env.MYSQL_DATABASE }}
                    MYSQL_USER: ${{ env.MYSQL_USER }}
                    MYSQL_PASSWORD: ${{ env.MYSQL_PASSWORD }}
                ports:
                    - 3306:3306
                options: >-
                    --health-cmd="mysqladmin ping --silent"
                    --health-interval=5s
                    --health-timeout=3s
                    --health-retries=5
                    --tmpfs=/var/lib/mysql:rw,noexec,nosuid,size=2g

        strategy:
            matrix:
                test-suite: [acceptance, api]
            fail-fast: false

        steps:
            - uses: actions/checkout@v4

            - name: Setup PHP with optimizations
              uses: shivammathur/setup-php@v2
              with:
                  php-version: ${{ env.PHP_VERSION }}
                  extensions: zip, gd, pdo_mysql, soap, mysqli, intl, opcache, apcu
                  coverage: none
                  ini-values: |
                      memory_limit=2G
                      opcache.enable=1
                      opcache.memory_consumption=256
                      opcache.max_accelerated_files=20000
                      opcache.validate_timestamps=0
                      opcache.revalidate_freq=0
                      realpath_cache_size=8M
                      realpath_cache_ttl=600

            - name: Cache Composer dependencies
              uses: actions/cache@v4
              with:
                  path: |
                      ~/.composer/cache
                      vendor
                  key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
                  restore-keys: ${{ runner.os }}-composer-

            - name: Cache application temp directories
              uses: actions/cache@v4
              with:
                  path: |
                      temp
                      temp_local
                  key: ${{ runner.os }}-app-temp-${{ github.sha }}
                  restore-keys: ${{ runner.os }}-app-temp-

            - name: Install dependencies
              run: composer install --no-progress --prefer-dist --optimize-autoloader --ignore-platform-reqs

            - name: Setup directories and permissions
              run: |
                  mkdir -p temp/{sessions,cache,proxies} temp_local/{cache,sessions} www/{upload/thumbnails,webtemp} log
                  chmod -R 0777 temp log www/upload www/webtemp tests
                  chmod -R 0755 vendor/bin/
                  sudo chown -R $USER:$USER .

            - name: Configure MySQL for performance
              run: |
                  echo "127.0.0.1 mysql" | sudo tee -a /etc/hosts
                  mysql -h127.0.0.1 -uroot -proot -e "SET GLOBAL character_set_server=utf8mb4;"
                  mysql -h127.0.0.1 -uroot -proot -e "SET GLOBAL collation_server=utf8mb4_unicode_ci;"
                  mysql -h127.0.0.1 -uroot -proot -e "SET GLOBAL innodb_flush_log_at_trx_commit=0;"
                  mysql -h127.0.0.1 -uroot -proot -e "SET GLOBAL sync_binlog=0;"

                  # Additional performance optimizations for CI environment
                  mysql -h127.0.0.1 -uroot -proot -e "SET GLOBAL innodb_buffer_pool_size=1073741824;" # 1GB
                  mysql -h127.0.0.1 -uroot -proot -e "SET GLOBAL slow_query_log=1;"
                  mysql -h127.0.0.1 -uroot -proot -e "SET GLOBAL long_query_time=2;"

            - name: Setup environment for Codeception
              run: |
                  cp app/Config/config.buddy.neon app/Config/config.local.neon
                  cp app/Config/buddy-htaccess www/.htaccess
                  sudo apt-get update -qq
                  sudo apt-get install -y nginx php8.1-fpm php8.1-zip php8.1-gd php8.1-mysql php8.1-soap php8.1-intl php8.1-mbstring php8.1-xml php8.1-curl
                  sudo mkdir -p /var/www/tipli
                  sudo cp -r . /var/www/tipli/
                  sudo mkdir -p /var/www/tipli/temp/{sessions,cache} /var/www/tipli/temp_local/{sessions,cache}
                  sudo tee /etc/php/8.1/fpm/pool.d/tipli.conf > /dev/null <<EOL
                  [tipli]
                  user = www-data
                  group = www-data
                  listen = /var/run/php/php8.1-fpm-tipli.sock
                  listen.owner = www-data
                  listen.group = www-data
                  listen.mode = 0660
                  pm = dynamic
                  pm.max_children = 8
                  pm.start_servers = 3
                  pm.min_spare_servers = 2
                  pm.max_spare_servers = 4
                  pm.max_requests = 200
                  php_admin_value[session.save_handler] = files
                  php_admin_value[session.save_path] = /var/www/tipli/temp_local/sessions
                  php_admin_value[upload_tmp_dir] = /var/www/tipli/temp_local
                  php_admin_flag[display_errors] = on
                  php_admin_value[error_reporting] = E_ALL
                  php_admin_value[memory_limit] = 384M
                  EOL
                  sudo tee /etc/nginx/sites-available/tipli > /dev/null <<EOL
                  server {
                      listen 8080;
                      server_name tipli.czdev *.czdev *.skdev *.pldev *.rodev *.hudev *.sidev *.hrdev *.rsdev *.bgdev;
                      root /var/www/tipli/www;
                      index index.php;
                      location ~ /\. {
                          deny all;
                      }
                      location ~* \.(pdf|js|ico|gif|jpg|png|css|rar|zip|tar\.gz|map)$ {
                          expires 1y;
                          add_header Cache-Control "public, immutable";
                          try_files \$uri =404;
                      }
                      location / {
                          try_files \$uri \$uri/ @rewrite;
                      }
                      location @rewrite {
                          rewrite ^(.*)$ /index.php last;
                      }
                      location ~ \.php$ {
                          include fastcgi_params;
                          fastcgi_pass unix:/var/run/php/php8.1-fpm-tipli.sock;
                          fastcgi_param SCRIPT_FILENAME \$document_root\$fastcgi_script_name;
                          fastcgi_param HTTP_AUTHORIZATION \$http_authorization;
                      }
                  }
                  EOL
                  sudo ln -sf /etc/nginx/sites-available/tipli /etc/nginx/sites-enabled/
                  sudo rm -f /etc/nginx/sites-enabled/default
                  sudo chown -R www-data:www-data /var/www/tipli
                  sudo find /var/www/tipli -type d -exec chmod 755 {} \;
                  sudo find /var/www/tipli -type f -exec chmod 644 {} \;
                  sudo chmod -R 777 /var/www/tipli/temp /var/www/tipli/temp_local /var/www/tipli/log /var/www/tipli/www/upload /var/www/tipli/www/webtemp
                  echo "127.0.0.1 tipli.czdev" | sudo tee -a /etc/hosts
                  echo "127.0.0.1 tipli.skdev" | sudo tee -a /etc/hosts
                  echo "127.0.0.1 tipli.pldev" | sudo tee -a /etc/hosts
                  echo "127.0.0.1 tipli.rodev" | sudo tee -a /etc/hosts
                  echo "127.0.0.1 tiplino.hudev" | sudo tee -a /etc/hosts
                  echo "127.0.0.1 tipli.sidev" | sudo tee -a /etc/hosts
                  echo "127.0.0.1 tipli.hrdev" | sudo tee -a /etc/hosts
                  echo "127.0.0.1 tipli.rsdev" | sudo tee -a /etc/hosts
                  echo "127.0.0.1 tipli.bgdev" | sudo tee -a /etc/hosts
                  sudo nginx -t
                  sudo systemctl restart php8.1-fpm nginx

            - name: Clear Doctrine caches and proxies
              run: |
                  rm -rf temp/cache/* temp_local/cache/* temp/proxies/*

            - name: Initialize database
              run: |
                  mysql -h127.0.0.1 -uroot -proot -e "DROP DATABASE IF EXISTS tipli; CREATE DATABASE tipli CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
                  php -d memory_limit=2G bin/console tipli:import-sql-data:run
                  php bin/console orm:generate-proxies
                  php -d memory_limit=2G bin/console orm:schema-tool:update --force --verbose
                  php bin/console orm:validate-schema --skip-sync

            - name: Apply performance indexes
              run: |
                  echo "Applying performance optimization indexes..."
                  mysql -h127.0.0.1 -uroot -proot tipli < data/performance_indexes.sql
                  echo "Performance indexes applied successfully"

            - name: Configure Codeception for parallel execution
              run: |
                  # Configure main codeception.yml
                  sed -i 's/%DB_HOST%/127.0.0.1/' tests/codeception/codeception.yml
                  sed -i 's/%DB_NAME%/tipli/' tests/codeception/codeception.yml
                  sed -i 's/url: .*/url: http:\/\/tipli.czdev:8080\//' tests/codeception/tests/acceptance.suite.yml
                  sed -i 's/memory_limit: 6G/memory_limit: 2G/' tests/codeception/codeception.yml

                  # Create parallel test configurations
                  for i in {1..8}; do
                      cp tests/codeception/codeception.yml tests/codeception/codeception_$i.yml
                      sed -i "s/tipli/tipli_test_$i/" tests/codeception/codeception_$i.yml
                  done

            - name: Run optimized Codeception tests with parallelization
              run: |
                  case ${{ matrix.test-suite }} in
                      acceptance)
                          # Configuration
                          PROCESSES=8
                          
                          # Create multiple database schemas for parallel execution
                          for i in $(seq 1 $PROCESSES); do
                              mysql -h127.0.0.1 -uroot -proot -e "DROP DATABASE IF EXISTS tipli_test_$i; CREATE DATABASE tipli_test_$i CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
                              mysql -h127.0.0.1 -uroot -proot tipli_test_$i < data/data.sql 2>/dev/null || true
                              mysql -h127.0.0.1 -uroot -proot tipli_test_$i < data/performance_indexes.sql 2>/dev/null || true
                          done

                          # Get list of all acceptance tests
                          TEST_FILES=($(find tests/codeception/tests/acceptance -name "*Cest.php" | sort))
                          TOTAL_TESTS=${#TEST_FILES[@]}
                          TESTS_PER_PROCESS=$((TOTAL_TESTS / PROCESSES))
                          
                          # Function to run tests for a specific process
                          run_test_group() {
                              set +e  # Disable strict error handling for this function
                              
                              local process_id=$1
                              local config_file=$2
                              shift 2  # Remove first 2 arguments (process_id and config_file)
                              local test_files=("$@")  # Remaining arguments are the test files
                              local failed_tests=()
                              local passed_tests=0
                              local total_tests_in_process=${#test_files[@]}
                              local start_time=$(date +%s)
                              local result_file="/tmp/process_${process_id}_results.txt"

                              echo "Process $process_id: Starting $total_tests_in_process test files at $(date '+%H:%M:%S')"

                              for test_file in "${test_files[@]}"; do
                                  current_test_num=$((passed_tests + ${#failed_tests[@]} + 1))
                                  echo "Process $process_id: Running test $current_test_num/$total_tests_in_process: $(basename "$test_file")"
                                  
                                  # Run test with timeout protection
                                  if timeout 300 php -d memory_limit=1G vendor/bin/codecept run acceptance "$test_file" \
                                      -c "$config_file" \
                                      --no-colors \
                                      --no-interaction; then
                                      passed_tests=$((passed_tests + 1))
                                  else
                                      local exit_code=$?
                                      echo "❌ Process $process_id: FAILED $(basename "$test_file") (exit code: $exit_code)"
                                      if [ $exit_code -eq 124 ]; then
                                          echo "Process $process_id: Test timed out after 300 seconds"
                                      fi
                                      failed_tests+=("$(basename "$test_file")")
                                  fi
                                  
                                  # Brief pause between tests
                                  sleep 0.5
                              done

                              local end_time=$(date +%s)
                              local duration=$((end_time - start_time))
                              
                              # Write results to temporary file for main script to read
                              echo "PASSED=$passed_tests" > "$result_file"
                              echo "TOTAL=$total_tests_in_process" >> "$result_file"
                              echo "DURATION=$duration" >> "$result_file"
                              echo "FAILED=${failed_tests[*]}" >> "$result_file"
                              
                              if [ ${#failed_tests[@]} -gt 0 ]; then
                                  echo "Process $process_id: Completed with $passed_tests/$total_tests_in_process passed (${duration}s) - FAILED: ${failed_tests[*]}"
                                  return 1
                              else
                                  echo "Process $process_id: Completed successfully - $passed_tests/$total_tests_in_process passed (${duration}s)"
                                  return 0
                              fi
                          }

                          # Start parallel processes
                          echo "Starting parallel execution with $PROCESSES processes for $TOTAL_TESTS test files"
                          
                          # Monitor system resources before starting
                          echo "=== SYSTEM RESOURCES BEFORE TESTS ==="
                          echo "Memory: $(free -m | awk 'NR==2{printf "Used: %sMB (%.1f%%), Available: %sMB", $3, $3*100/$2, $7}')"
                          echo "Disk space: $(df -h . | awk 'NR==2{printf "Used: %s (%s), Available: %s", $3, $5, $4}')"
                          echo "MySQL processes: $(mysql -h127.0.0.1 -uroot -proot -e "SHOW PROCESSLIST;" 2>/dev/null | wc -l || echo 'N/A')"
                          echo "======================================"

                          process_pids=()
                          for i in $(seq 1 $PROCESSES); do
                              start_idx=$(((i-1) * TESTS_PER_PROCESS))
                              if [ $i -eq $PROCESSES ]; then
                                  end_idx=$((TOTAL_TESTS - 1))
                              else
                                  end_idx=$((start_idx + TESTS_PER_PROCESS - 1))
                              fi

                              # Extract specific test files for this process
                              process_files=()
                              for ((j=start_idx; j<=end_idx && j<TOTAL_TESTS; j++)); do
                                  process_files+=("${TEST_FILES[$j]}")
                              done

                              echo "Process $i: ${#process_files[@]} files ($(basename "${process_files[0]}" 2>/dev/null)...$(basename "${process_files[-1]}" 2>/dev/null))"
                              
                              run_test_group $i "tests/codeception/codeception_$i.yml" "${process_files[@]}" &
                              process_pids[$i]=$!
                              echo "Started process $i (PID: ${process_pids[$i]})"
                              
                              # Brief delay to avoid startup race conditions
                              sleep 0.2
                          done

                          # Wait for all processes to complete
                          echo "Waiting for all $PROCESSES processes to complete..."
                          failed_processes=0
                          monitor_start_time=$(date +%s)
                          
                          # Start periodic monitoring in background
                          {
                              while true; do
                                  sleep 30  # Monitor every 30 seconds
                                  current_time=$(date +%s)
                                  elapsed=$((current_time - monitor_start_time))
                                  
                                  # Check if any processes are still running
                                  running_count=0
                                  for i in $(seq 1 $PROCESSES); do
                                      if kill -0 "${process_pids[$i]}" 2>/dev/null; then
                                          ((running_count++))
                                      fi
                                  done
                                  
                                  if [ $running_count -eq 0 ]; then
                                      break  # All processes completed
                                  fi
                                  
                                  echo "Still running: $running_count/$PROCESSES processes after ${elapsed}s"
                              done
                          } &
                          monitor_pid=$!
                          
                          for i in $(seq 1 $PROCESSES); do
                              echo "Waiting for process $i (PID: ${process_pids[$i]})..."
                              if wait ${process_pids[$i]} 2>/dev/null; then
                                  echo "✅ Process $i completed successfully"
                              else
                                  exit_code=$?
                                  echo "❌ Process $i failed (exit code: $exit_code)"
                                  failed_processes=$((failed_processes + 1))
                                  
                                  # If process failed but didn't create result file, create a failure result
                                  result_file="/tmp/process_${i}_results.txt"
                                  if [ ! -f "$result_file" ]; then
                                      echo "Process $i crashed before writing results, creating failure record"
                                      echo "PASSED=0" > "$result_file" 2>/dev/null || true
                                      echo "TOTAL=0" >> "$result_file" 2>/dev/null || true
                                      echo "DURATION=0" >> "$result_file" 2>/dev/null || true
                                      echo "FAILED=PROCESS_CRASHED_EXIT_CODE_$exit_code" >> "$result_file" 2>/dev/null || true
                                  fi
                              fi
                          done
                          
                          # Stop monitoring
                          kill $monitor_pid 2>/dev/null || true

                          # Collect and display comprehensive summary
                          total_execution_time=$(($(date +%s) - monitor_start_time))
                          total_passed=0
                          total_failed=0
                          failed_test_details=()
                          
                          echo ""
                          echo "══════════════════════════════════════════════════════════════"
                          echo "                      TEST EXECUTION SUMMARY"
                          echo "══════════════════════════════════════════════════════════════"
                          echo "Total test files: $TOTAL_TESTS"
                          echo "Processes used: $PROCESSES"
                          echo "Total execution time: ${total_execution_time}s ($(echo "$total_execution_time / 60" | bc -l 2>/dev/null | awk '{printf "%.1f", $1}' || awk "BEGIN {printf \"%.1f\", $total_execution_time/60}")m)"
                          echo ""
                          
                          # Collect results from all processes
                          for i in $(seq 1 $PROCESSES); do
                              result_file="/tmp/process_${i}_results.txt"
                              
                              # Initialize defaults
                              passed=0
                              total=0
                              duration=0
                              failed_tests=""
                              
                              if [ -f "$result_file" ]; then
                                  # Source the results from the temporary file with error handling
                                  if source "$result_file" 2>/dev/null; then
                                      # Safely extract values with defaults
                                      passed=${PASSED:-0}
                                      total=${TOTAL:-0}
                                      duration=${DURATION:-0}
                                      failed_tests="${FAILED:-}"
                                  else
                                      echo "Warning: Could not read result file for process $i"
                                      # Try manual parsing as fallback
                                      passed=$(grep "^PASSED=" "$result_file" 2>/dev/null | cut -d= -f2 || echo "0")
                                      total=$(grep "^TOTAL=" "$result_file" 2>/dev/null | cut -d= -f2 || echo "0")
                                      duration=$(grep "^DURATION=" "$result_file" 2>/dev/null | cut -d= -f2 || echo "0")
                                      failed_tests=$(grep "^FAILED=" "$result_file" 2>/dev/null | cut -d= -f2- || echo "")
                                  fi
                              else
                                  echo "Warning: Result file for process $i not found"
                              fi
                              
                              total_passed=$((total_passed + passed))
                              failed_count=$((total - passed))
                              total_failed=$((total_failed + failed_count))
                              
                              if [ $failed_count -gt 0 ]; then
                                  echo "❌ Process $i: $passed/$total passed (${duration}s) - FAILED: $failed_tests"
                                  failed_test_details+=("Process $i: $failed_tests")
                              else
                                  echo "✅ Process $i: $passed/$total passed (${duration}s)"
                              fi
                          done
                          
                          echo ""
                          echo "OVERALL RESULTS:"
                          echo "✅ Passed: $total_passed test files"
                          if [ $total_failed -gt 0 ]; then
                              echo "❌ Failed: $total_failed test files"
                              echo ""
                              echo "FAILED TEST DETAILS:"
                              for detail in "${failed_test_details[@]}"; do
                                  echo "  $detail"
                              done
                          else
                              echo "❌ Failed: 0 test files"
                          fi
                          
                          echo ""
                          echo "FINAL SYSTEM RESOURCES:"
                          echo "Memory: $(free -m | awk 'NR==2{printf "Used: %sMB (%.1f%%), Available: %sMB", $3, $3*100/$2, $7}' || echo 'N/A')"
                          echo "Disk space: $(df -h . | awk 'NR==2{printf "Used: %s (%s), Available: %s", $3, $5, $4}' || echo 'N/A')"
                          echo "══════════════════════════════════════════════════════════════"

                          # If there were failures, show essential diagnostic information
                          if [ $total_failed -gt 0 ]; then
                              echo ""
                              echo "=== FAILURE DIAGNOSTICS ==="
                              echo "Recent Codeception output files:"
                              find tests/codeception/tests/_output -name "*.html" -mmin -10 -exec ls -la {} \; 2>/dev/null || echo "No recent output files found"
                              
                              echo ""
                              echo "Application log files available:"
                              find log temp/log temp_local/log -name "*.log" -type f 2>/dev/null | head -10 || echo "No application log files found"
                              
                              echo ""
                              echo "Nginx/PHP-FPM status (if accessible):"
                              sudo systemctl status php8.1-fpm nginx 2>/dev/null | grep -E "(Active:|Main PID:|Memory:|Tasks:)" || echo "Service status not accessible"
                              
                              echo "=== END FAILURE DIAGNOSTICS ==="
                              echo ""
                          fi

                          # Cleanup temporary files (but preserve them until after artifact upload if there were failures)
                          if [ $total_failed -eq 0 ]; then
                              rm -f tests/codeception/codeception_*.yml
                              rm -f /tmp/process_*_results.txt
                          else
                              # Keep result files for artifact upload, only clean codeception configs
                              rm -f tests/codeception/codeception_*.yml
                              echo "Note: Process result files preserved for artifact upload"
                          fi

                          if [ $total_failed -gt 0 ]; then
                              echo ""
                              echo "❌ PIPELINE FAILED: $total_failed test file(s) failed"
                              exit 1
                          else
                              echo ""
                              echo "PIPELINE SUCCESS: All $total_passed test files passed!"
                              exit 0
                          fi
                          ;;
                      api)
                          php -d memory_limit=2G vendor/bin/codecept run api \
                              -c tests/codeception/codeception.yml \
                              --no-colors \
                              --fail-fast
                          ;;
                  esac

            -   name: Upload Codeception failure artifacts and logs
                if: failure()
                uses: actions/upload-artifact@v4
                with:
                    name: test-failure-artifacts-${{ matrix.test-suite }}-${{ github.run_id }}
                    path: |
                        tests/codeception/tests/_output/
                        log/
                        temp/log/
                        temp_local/log/
                        /tmp/process_*_results.txt
                    if-no-files-found: ignore

            -   name: Cleanup diagnostic files
                if: always()
                run: |
                    # Clean up any remaining temporary files
                    rm -f /tmp/process_*_results.txt 2>/dev/null || true
                    echo "Cleanup completed"

    static-analysis:
        runs-on: ubuntu-latest
        strategy:
            matrix:
                tool: [phpstan, codesniffer]
            fail-fast: false

        steps:
            - uses: actions/checkout@v4

            - name: Setup PHP
              uses: shivammathur/setup-php@v2
              with:
                  php-version: ${{ env.PHP_VERSION }}
                  extensions: zip, gd, pdo_mysql, soap, mysqli, intl
                  coverage: none
                  ini-values: memory_limit=3G

            - name: Cache Composer dependencies
              uses: actions/cache@v4
              with:
                  path: |
                      ~/.composer/cache
                      vendor
                  key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
                  restore-keys: ${{ runner.os }}-composer-

            - name: Install dependencies
              run: composer install --no-progress --prefer-dist --optimize-autoloader --ignore-platform-reqs

            - name: Run static analysis
              run: |
                  case ${{ matrix.tool }} in
                      phpstan)
                          php -d memory_limit=4G vendor/bin/phpstan analyse app \
                              -l 4 -c app/Config/phpstan.neon \
                              --autoload-file=tests/phpstan/autoload-phpstan.php \
                              --no-progress --error-format=github
                          ;;
                      codesniffer)
                          php vendor/bin/phpcs \
                              --standard=ruleset.xml \
                              --extensions=php \
                              --tab-width=4 \
                              --parallel=2 \
                              --report=full \
                              --colors \
                              app
                          ;;
                  esac
