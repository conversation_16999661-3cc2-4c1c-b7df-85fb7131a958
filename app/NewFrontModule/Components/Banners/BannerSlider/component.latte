<div class="relative"
		style="
		{if $contentSection !== null}
				/* Aktuální cover */
			{if $contentSection->getImage()}
					width: 100%;
					background-image: url({$getImagePath($contentSection->getImage())});
					background-repeat: no-repeat;
					background-size: cover;
					background-position: center;
				{if $contentSection->getBackgroundColor()}{$contentSection->getBackgroundColor()|noescape};{/if}
					/* Nový cover */
			{elseif $contentSection->hasBackground()}
					/*background-image: url({$getImagePath($contentSection->getTopImage())|noescape});*/
					background-position: top center;
					background-repeat: no-repeat;
					{if $contentSection->getBackgroundColor()}background-color: {$contentSection->getBackgroundColor()|noescape};{/if}
					background-size: 1240px 110px;
			{/if}
		{/if}
				">
	{*	TOP IMAGE START *}
	{if $contentSection !== null && $contentSection->getTopImage() && $contentSection->getTopImageUrl()}
		<a href="{$contentSection->getTopImageUrl()}" class="hidden md:block absolute top-0 left-1/2 -translate-x-1/2 my-5 z-10 w-full xl:w-[1240px]">
	{/if}

	<img n:if="$contentSection !== null && $contentSection->getTopImage()" src="{$getImagePath($contentSection->getTopImage())|noescape}" loading="lazy" {if $contentSection->getTopImageUrl() === null}class="hidden md:block absolute top-0 left-1/2 -translate-x-1/2 my-5" {else} class="w-full" {/if}>

	{if $contentSection !== null && $contentSection->getTopImage() && $contentSection->getTopImageUrl()}
		</a>
	{/if}
	{*	TOP IMAGE END *}

	{*	LEFT IMAGE START *}
	{if $contentSection !== null && $contentSection->getLeftImage() && $contentSection->getLeftImageUrl()}
		<a href="{$contentSection->getLeftImageUrl()}">
	{/if}

	<img n:if="$contentSection !== null && $contentSection->getLeftImage()" src="{$getImagePath($contentSection->getLeftImage())|noescape}" loading="lazy" class="hidden lg:block absolute left-[50%] ml-[-919px] mt-5">

	{if $contentSection !== null && $contentSection->getLeftImage() && $contentSection->getLeftImageUrl()}
		</a>
	{/if}
	{*	LEFT IMAGE END *}

	{*	RIGHT IMAGE START *}
	{if $contentSection !== null && $contentSection->getRightImage() && $contentSection->getRightImageUrl()}
		<a href="{$contentSection->getRightImageUrl()}">
	{/if}

	<img n:if="$contentSection !== null && $contentSection->getRightImage()" src="{$getImagePath($contentSection->getRightImage())|noescape}" loading="lazy" class="hidden lg:block absolute right-[50%] mr-[-919px] mt-5">

	{if $contentSection !== null && $contentSection->getRightImage() && $contentSection->getRightImageUrl()}
		</a>
	{/if}
	{*	RIGHT IMAGE END *}

	<div class="absolute pt-[290px]">
		<img class="w-screen" src="{$basePath}/new-design/sales-bg.svg" alt="bg">
	</div>

	<div class="container relative pt-[21px] {if $contentSection !== null && $contentSection->hasBackground()} md:pt-[150px] {/if}">
		<div class="swiper-container relative z-20 pb-2 md:pb-[45px] min-h-[calc(100%-40px)] md:min-h-[442px] lg:min-h-[436px] xl:min-h-[506px]">
			<div class="swiper banner-swiper">
				<div class="swiper-wrapper">
					{foreach $banners as $banner}
						<div class="swiper-slide">
							{control bannerItem, $banner, $iterator->counter}
						</div>
					{/foreach}
				</div>
			</div>
			<div class="swiper-pagination banner-swiper-pagination"></div>
			<div class="swiper-button-prev banner-swiper-button-prev"></div>
			<div class="swiper-button-next banner-swiper-button-next"></div>
		</div>
	</div>
</div>
