{var $variant = $googleExperimentVariantResolver->getExperimentVariant('googleoptimize', 2)->getVariant()}
{*var $variant = 0*}

{var $validSinceDay = $translator->translate('front.calendar.days.' . $leaflet->getValidSince()->format('w'))}
{var $validTillDay = $translator->translate('front.calendar.days.' . $leaflet->getValidTill()->format('w'))}

{block metaTitle}
    {if isset($pageExtension) && $pageExtension->getHeading()}
        {$pageExtension->getHeading()}
    {else}
		{_'front.leaflet.validSince', [brand => $leaflet->getShop()->getName(), date => $validSinceDay . ' ' . $leaflet->getValidSince()->format($localization->getDateFormat('j. n.')), dateTill => $validTillDay . ' ' . $leaflet->getValidTill()->format($localization->getDateFormat('j. n. Y'))]}
    {/if}
{/block}

{block keywords}{_'front.leaflet.branch.keywords', [name => $leaflet->getShop()->getName(), title => $leaflet->getTitle(), validSince => $leaflet->getValidSince()->format($localization->getDateFormat('j. n. Y'))]}{/block}

{block description}
    {var $random = 1}

    {if $random == 1}
        {_'front.leaflet.description1', [brand => $leaflet->getShop()->getName(), validSince => $leaflet->getValidSince()->format($localization->getDateFormat('j. n.')), validTill => $leaflet->getValidTill()->format($localization->getDateFormat('j. n. Y'))]}
    {elseif $random == 2}
        {_'front.leaflet.description2', [brand => $leaflet->getShop()->getName(), validSince => $leaflet->getValidSince()->format($localization->getDateFormat('j. n.')), validTill => $leaflet->getValidTill()->format($localization->getDateFormat('j. n. Y'))]}
    {else}
        {_'front.leaflet.description3', [brand => $leaflet->getShop()->getName(), validSince => $leaflet->getValidSince()->format($localization->getDateFormat('j. n.')), validTill => $leaflet->getValidTill()->format($localization->getDateFormat('j. n. Y'))]}
    {/if}
{/block}

{block #styles}
    {control cssBuilderControl ['css/leaflet/main.leaflet.css']}
{/block}

{block #scripts}
	{if (!isset($pageExtension) || $pageExtension->isAdsenseAllowed())}
		<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-3515231881192503"
				crossorigin="anonymous"></script>

		<script type="text/javascript" src="{$basePath}/js/showVisibleAds.js" defer></script>
	{/if}
{/block}

{block content}
<!-- variant: {$variant} -->
<span itemscope itemtype="http://schema.org/SaleEvent">
    <meta itemprop="name" content="{$leaflet->getTitle()}">
    <meta itemprop="description" content="{include description}">
    <meta itemprop="url" content="{_'front.links.homepage'}{link :NewFront:Leaflets:Leaflet:leaflet, $leaflet}">
    <meta itemprop="image" content="{($leaflet->getFirstLeafletPage()) ? $leaflet->getFirstLeafletPage()->getDownloadUrl() |image:198,198,'exactTop',null}">
    <meta itemprop="startDate" content="{$leaflet->getValidSince()->format('Y-m-d')}">
    <meta itemprop="endDate" content="{$leaflet->getValidTill()->format('Y-m-d')}">
    <span itemprop="location" itemscope itemtype="http://schema.org/ShoppingCenter">
        <meta itemprop="name" content="{$leaflet->getShop()->getName()}">
        <meta itemprop="url" content="{_'front.links.homepage'}{link :NewFront:Shops:Shop:default $leaflet->getShop()}">
        <meta itemprop="image" content="{$leaflet->getShop()->getCurrentLogo() |image:60}">
        <meta itemprop="address" content="{$leaflet->getShop()->getName()} {_'front.leaflets.country.' . $leaflet->getLocalization()->getLocale()}">
        <meta itemprop="telephone" content=" ">
        <meta itemprop="priceRange" content=" ">
    </span>
    <span itemprop="performer" itemtype="https://schema.org/Person" itemscope>
        <meta itemprop="name" content="{$leaflet->getShop()->getName()}">
    </span>
    <span itemprop="offers" itemtype="http://schema.org/Offer" itemscope>
        <meta itemprop="priceCurrency" content="{$leaflet->getLocalization()->getCurrency()}">
        <meta itemprop="availability" content="In Stock">
        <meta itemprop="price" content="">
        <meta itemprop="url" content="{_'front.links.homepage'}{link :NewFront:Shops:Shop:default $leaflet->getShop()}">
        <meta itemprop="validFrom" content="{$leaflet->getValidSince()->format('Y-m-d')}">
    </span>
</span>

<div class="leaflet lf-n-layout">
    <div class="container">
		<div class="leaflet__content">
			<div class="d-block overflow-hidden">
				<div class="page-header leaflet__detail-header leaflet__detail-header--mobile-row">
					<div class="leaflet__detail-header-content">

						{if isset($pageExtension) && $pageExtension->getHeading()}
							<h1 class="text-[20px] md:text-[26px] leading-[35px] md:leading-[39px] font-medium mt-5 mb-5 md:mt-10 md:mb-[32px]">{$pageExtension->getHeading()}</h1>
						{else}
							<h1 class="text-[20px] md:text-[26px] leading-[35px] md:leading-[39px] font-medium mt-5 mb-5 md:mt-10 md:mb-[32px]">
								{$leaflet->getTitle()}
								{var $sinceYear = $leaflet->getValidSince()->format('Y')}
								{var $tillYear = $leaflet->getValidTill()->format('Y')}
								<span class="leaflet__date">
									{$leaflet->getValidSince()->format($localization->getDateFormat('j. n.'))} –
									{if $sinceYear != $tillYear}
										{$leaflet->getValidTill()->format($localization->getDateFormat('j. n. Y'))}
									{else}
										{$leaflet->getValidTill()->format($localization->getDateFormat('j. n.'))}
									{/if}
								</span>
							</h1>
						{/if}

						<p class="mb-5 md:mb-10">{_'front.leaflet.text', [brand => $leaflet->getShop()->getName(), validSinceDay => $validSinceDay , validSince => $leaflet->getValidSince()->format($localization->getDateFormat('j. n.')), validTillDay => $validTillDay, validTill => $leaflet->getValidTill()->format($localization->getDateFormat('j. n. Y'))]}</p>
					</div>

					<div class="leaflet__detail-header-side">
						<a href="{link ':NewFront:Leaflets:Leaflet:leaflets', 'shop' => $leaflet->getShop()}" class="h-[60px] w-[80px]">
							<img src="{$leaflet->getShop()->getCurrentLogo() |image:100}" loading="lazy" class=" leaflet__detail-header-logo" alt="{$leaflet->getShop()->getName()}">
						</a>
					</div>
				</div>

				{if isset($pageExtension) && $pageExtension->getMiddleDescription()}
				<div class="page-extension content-block">
					{$pageExtension->getMiddleDescription() |noescape}
				</div>
				{elseif $localization->isLiteVersion() && $leaflet->getShop()}
					{$leaflet->getShop()->getShortDescription() |noescape}
				{/if}

				{if (!isset($pageExtension) || $pageExtension->isAdsenseAllowed())}
					<div class="leaflet__ads-wrapper min-h-[280px]" style="justify-content: center;">
							<!-- Letaky - Detail letaku - Ctverec - 1 -->
							<ins class="adsbygoogle" data-ad-client="ca-pub-3515231881192503" data-ad-slot="5330840193" data-ad-format="rectangle" data-full-width-responsive="true"></ins>

							<!-- Letaky - Detail letaku - Ctverec - 2 -->
							<ins class="adsbygoogle hide-xs" data-ad-client="ca-pub-3515231881192503" data-ad-slot="5139268505" data-ad-format="rectangle" data-full-width-responsive="true"></ins>
					</div>
				{/if}

				<div class="leaflet__paginator relative">
					{cache ($cdnImagesAllowed ? 'proxy' : 'native') . 'leaflet-pages-paginator-top-' . $leaflet->getId() . '-' . $currentLeafletPageNumber, expire => '16 hours'}
						{control leafletPagesPaginator}
					{/cache}
				</div>

				{if (!isset($pageExtension) || $pageExtension->isAdsenseAllowed())}
					<div>
						<!-- Letaky - Detail letaku - Responsive - 1 -->
						<ins class="adsbygoogle" data-ad-client="ca-pub-3515231881192503" data-ad-slot="4372981741" data-ad-format="auto" data-full-width-responsive="true"></ins>
					</div>
				{/if}

				<div class="leaflet-preview">
					{cache ($isAdmin ? 'a' : 'u') . ($cdnImagesAllowed ? 'proxy' :  'native') . 'leaflet-pages-' . $leaflet->getId() . '-' . $currentLeafletPageNumber, expire => '15 minutes'}
						<!-- {(new \DateTime)->format('d.m.Y H:i:s')} -->
						{foreach $getLeafletPages() as $leafletPage}
							{* Rozmery nahledu - width 870px - retina = 1740px *}
							{*if $isAdmin*}
							{*<img src="{$leafletPage->getDownloadUrl() |image:870}" loading="lazy" alt="{$leaflet->getShop()->getName()}">*}
							<picture>
								<source
									srcset="
										{$leafletPage->getDownloadUrl() |image:335,null,'fit',false,null,true} 335w,
										{$leafletPage->getDownloadUrl() |image:605,null,'fit',false,null,true} 605w,
										{$leafletPage->getDownloadUrl() |image:728,null,'fit',false,null,true} 728w,
										{$leafletPage->getDownloadUrl() |image:830,null,'fit',false,null,true} 830w
									"
									sizes="
										(max-width: 639px) calc(100vw - 40px),
										(max-width: 767px) 600px,
										(max-width: 1023px) 728px,
										(max-width: 1239px) 830px,
										(max-width: 1509px) 605px,
										830px
									"
									type="image/webp"
								>
								<img
									src="{$leafletPage->getDownloadUrl() |image:335,null}"
									srcset="
										{$leafletPage->getDownloadUrl() |image:335,null} 335w,
										{$leafletPage->getDownloadUrl() |image:605,null} 605w,
										{$leafletPage->getDownloadUrl() |image:728,null} 728w,
										{$leafletPage->getDownloadUrl() |image:830,null} 830w
									"
									sizes="
										(max-width: 639px) calc(100vw - 40px),
										(max-width: 767px) 600px,
										(max-width: 1023px) 728px,
										(max-width: 1239px) 830px,
										(max-width: 1509px) 605px,
										830px
									"
									width="830"
									height="1075"
									alt="{$leaflet->getShop()->getName()}"
									title="{$leaflet->getShop()->getName()}"
									loading="lazy"
									class="{if $leaflet->isExpired()}grayscale{/if}"
								>
                                </picture>

						{/foreach}
					{/cache}
				</div>

				{if $partnerLink = $leaflet->getPartnerLink()}
					<div class="ta-center">
						<a href="{$partnerLink |noescape}" target="_blank" class="btn btn-green mt-4">
							{_'front.leaflet.partnerLinkButtonText'}
						</a>
					</div>
				{/if}

				{if (!isset($pageExtension) || $pageExtension->isAdsenseAllowed())}
					<div>
						<!-- Letaky - Detail letaku - Responsive - 2 -->
						<ins class="adsbygoogle" data-ad-client="ca-pub-3515231881192503" data-ad-slot="6284829214" data-ad-format="auto" data-full-width-responsive="true"></ins>
					</div>
				{/if}

				<div class="leaflet__paginator relative">
					{cache ($cdnImagesAllowed ? 'proxy' :  'native') . 'leaflet-pages-paginator-bottom-' . $leaflet->getId() . '-' . $currentLeafletPageNumber, expire => '16 hours'}
						{control leafletPagesPaginator}
					{/cache}
				</div>

				{cache ($cdnImagesAllowed ? 'proxy' :  'native') . 'leaflet-bottom-section-' . $leaflet->getId(), expire => '2 hours'}
					{if (!isset($pageExtension) || $pageExtension->isAdsenseAllowed())}
						<div class="leaflet__ads-wrapper">
							<!-- Letaky - Detail letaku - Responsive - 3 -->
							<ins class="adsbygoogle" data-ad-client="ca-pub-3515231881192503" data-ad-slot="5531803139" data-ad-format="auto" data-full-width-responsive="true"></ins>
						</div>
					{/if}

					{if isset($pageExtension) && $pageExtension->getTopDescription()}
						<p class="mb-5 md:mb-10"><strong>{$pageExtension->getTopDescription() |content |noescape}</strong></p>
					{else}
						<p class="mb-5 md:mb-10"><strong>{$leaflet->getShop()->getName()} {_'front.leaflet.branch.valid', [date => $leaflet->getValidSince()->format($localization->getDateFormat('j. n. Y'))]}</strong></p>
					{/if}

					{capture $leafletBrandLink}
						<a href="{link ':NewFront:Leaflets:Leaflet:leaflets', 'shop' => $leaflet->getShop()}">{$leaflet->getShop()->getName()}</a>
					{/capture}

					{capture $topLeafletShopLinks}
{*						{cache 'leafletTopShopsLinks-' . $leaflet->getShop()->getId(), expire => '1 hour'}*}
							{var $topLeafletShops = $getTopLeafletShops()}

							{foreach $topLeafletShops as $topLeafletShop}
								<a href="{link ':NewFront:Leaflets:Leaflet:leaflets', 'shop' => $topLeafletShop}">{$topLeafletShop->getName()}</a>{sep}, {/sep}
							{/foreach}
{*						{/cache}*}
					{/capture}

					{capture $moreShopsLink}
						{link :NewFront:Leaflets:Leaflet:shops}
					{/capture}

					<p class="mb-5 md:mb-10">{_'front.leaflet.leafletText', [brand => $leafletBrandLink, validSinceDay => $validSinceDay , validSince => $leaflet->getValidSince()->format($localization->getDateFormat('j. n.')), validTillDay => $validTillDay ,validTill => $leaflet->getValidTill()->format($localization->getDateFormat('j. n. Y')), countOfPages => $getCountOfPages(), topLeafletShops => $topLeafletShopLinks, moreShopsLink => $moreShopsLink] |noescape}</p>

					<div class="leaflet__line">
						<a n:href="default" class="leaflet__back"><i class="fa fa-long-arrow-left" aria-hidden="true"></i>{_'front.leaflet.list.backLink'}</a>
						<a n:if="!$localization->isLiteVersion() && !$leaflet->getShop()->isOnlyLeafletShop()" n:href=":NewFront:Shops:Shop:default, $leaflet->getShop()" class="leaflet__back">{_'front.leaflet.list.shopLink', ['shop' => $leaflet->getShop()->getName()]}</a>
						<a n:href="leaflets, shop => $leaflet->getShop()" class="leaflet__next">{_'front.leaflet.list.allLink', [title => $leaflet->getShop()->getName()]}<i class="fa fa-long-arrow-right" aria-hidden="true"></i></a>
					</div>

					{var $shopLeaflets = $getShopLeaflets()}
					{if count($shopLeaflets) > 0}
						<div class="">
							<h2 class="text-[20px] md:text-[26px] leading-[35px] md:leading-[39px] font-medium mt-5 mb-5 md:mt-10 md:mb-[32px]">
								<a n:href="leaflets, shop => $leaflet->getShop()">{_'front.leaflet.list.shop.title'} {$leaflet->getShop()->getName()}</a>
							</h2>
						</div>

						<div class="grid grid-cols-2 md:grid-cols-4 gap-3">
							{foreach $shopLeaflets as $shopLeaflet}
								{include leafletSnippet, leaflet => $shopLeaflet}
							{/foreach}
						</div>
					{/if}

					{var $tagLeaflets = $getTagLeaflets()}
					{if $tagLeaflets !== null}
						<div class="">
							<h2 class="text-[20px] md:text-[26px] leading-[35px] md:leading-[39px] font-medium mt-5 mb-5 md:mt-10 md:mb-[32px]">
								{var $leafletFirstTag = $getLeafletFirstTag()}
								{_'front.leaflet.list.title.category', [category => $leafletFirstTag->getName()]}
							</h2>
						</div>

						<div class="grid grid-cols-2 md:grid-cols-4 gap-3">
							{foreach $tagLeaflets as $tagLeaflet}
								{include leafletSnippet, leaflet => $tagLeaflet}
							{/foreach}
						</div>
					{/if}
				{/cache}
			</div>

			<div class="leaflet__aside">
				{cache ($cdnImagesAllowed ? 'proxy' :  'native') . 'top-leaflets-global-' . $localization->getId(), expire => '6 hours'}
					<!-- {(new \DateTime)->format('d.m.Y H:i:s')} -->
					<div class="lf__box lf__box-lg-border">
						<h3 class="lf__box-title">{_'front.leaflet.favoriteLeaflets'}</h3>
						<div class="d-flex flex-direction-row flex-direction-lg-column">
							{var $topLeaflets = $topLeaflets(5)}

							{foreach $topLeaflets as $topLeaflet}
								<div class="lf__box-item flex-direction-column flex-direction-lg-row mb-lg-3">
									<a n:href=":NewFront:Leaflets:Leaflet:leaflet, $topLeaflet" class="lf__box-image-wrapper mb-3 mr-3"><img src="{($topLeaflet->getFirstLeafletPage()) ? $topLeaflet->getFirstLeafletPage()->getDownloadUrl() |image:268,268,'exactTop',null}"  class="img-responsive" loading="lazy" alt="preview"></a>
									<p class="fz-xxs fz-sm-xs mb-0">
										<a n:href=":NewFront:Leaflets:Leaflet:leaflet, $topLeaflet" class="d-block color-black strong">{$topLeaflet->getTitle()}</a>
										<small class="block">{$topLeaflet->getValidSince()->format($localization->getDateFormat('j. n.'))} - {$topLeaflet->getValidTill()->format($localization->getDateFormat('j. n. Y'))}</small>
									</p>
								</div>
							{/foreach}
						</div>
					</div>
				{/cache}

				<!-- Letaky - Detail letaku - Sidebar - 1 -->
				<ins class="adsbygoogle" style="display:block" data-ad-client="ca-pub-3515231881192503" data-ad-slot="2448365833" data-ad-format="auto" data-full-width-responsive="true"></ins>
			</div>
		</div>

		<div class="leaflet__sidebar">
			{cache ($cdnImagesAllowed ? 'proxy' :  'native') . 'leafletTopShops-' . $leaflet->getShop()->getId(), expire => '8 hours'}
				{var $expiredLeaflets = $expiredLeaflets(3)}
				<div class="lf__box" n:if="$expiredLeaflets->isEmpty() === false">
					<h3 class="lf__box-title mt-3 mt-md-0">{_'front.leaflet.otherLeaflets', [brand => $leaflet->getShop()->getName()]}</h3>
					<div class="d-flex flex-direction-row flex-direction-md-column">
						<!-- {(new \DateTime)->format('d.m.Y H:i:s')} -->
						{foreach $expiredLeaflets as $expiredLeaflet}
							<div class="lf__box-item flex-direction-column flex-direction-lg-row mb-3">
								<a n:href=":NewFront:Leaflets:Leaflet:leaflet, $expiredLeaflet" class="lf__box-image-wrapper lf__box-image--medium mb-3 lg:mr-3"><img src="{($expiredLeaflet->getFirstLeafletPage()) ? $expiredLeaflet->getFirstLeafletPage()->getDownloadUrl() |image:268,268,'exactTop',null}" loading="lazy" class="img-responsive" alt="preview"></a>
								<p class="fz-xxs fz-sm-xs mb-0">
									<a n:href=":NewFront:Leaflets:Leaflet:leaflet, $expiredLeaflet" class="d-block color-black strong">{$expiredLeaflet->getTitle()}</a>
									<small class="block">{$expiredLeaflet->getValidSince()->format($localization->getDateFormat('j. n.'))} - {$expiredLeaflet->getValidTill()->format($localization->getDateFormat('j. n. Y'))}</small>
								</p>
							</div>
						{/foreach}

					</div>
				</div>
			{/cache}

			<div class="float-wrapper">
				<!-- Letaky - Detail letaku - Sidebar - 2 -->
				<ins class="adsbygoogle" style="display:block" data-ad-client="ca-pub-3515231881192503" data-ad-slot="7931313721" data-ad-format="auto" data-full-width-responsive="true"></ins>
			</div>

			{cache ($cdnImagesAllowed ? 'proxy' :  'native') . 'leafletStores-' . $leaflet->getId(), expire => '8 hours'}
			{foreach $leaflet->getStores() as $store}
				{if $store}
					<div class="leaflet-box">
						<h3 class="leaflet-box__title">{$store->getName()}</h3>
						<p class="leaflet-box__text">{$store->getDescription()}</p>

						{if $store->getCity() || $store->getAddress()}
							<p class="leaflet-box__text">
								<strong>{_'front.leaflet.branch.address'}:</strong><br>
								{$store->getCity()} <br>
								{$store->getAddress()}

								{if $store->getZipCode()}
									, {$store->getZipCode()}
								{/if}
								<br>
							</p>
						{/if}

						{if $store->getPhoneNumber()}
							<p class="leaflet-box__text">
								<strong>{_'front.leaflet.branch.phone'}:</strong><br>
								{$store->getPhoneNumber()}
							</p>
						{/if}

						{if $store->getEmail()}
							<p class="leaflet-box__text">
								<strong>{_'front.leaflet.branch.email'}:</strong><br>
								<span>{$store->getEmail() }</span>
							</p>
						{/if}

						<p class="leaflet-box__text">
							<strong>{_'front.leaflet.branch.openHour'}:</strong><br>
							7:00-21:00
						</p>
					</div>

					{if $store->getCity()}
						<div class="leaflet-box__map">
							<iframe width="" height="200" frameborder="0" style="border:0" src="https://www.google.com/maps/embed/v1/place?key=AIzaSyA3FEliQngvGpLZ_g2wlGzmZczNcPEEyCA&q={$store->getFullAddress()}" allowfullscreen></iframe>
						</div>
					{/if}
				{/if}
			{/foreach}
			{/cache}
		</div>
	</div>

	<div class="float-form__stop"></div>

	<div class="container">
		<div>
			<h2 class="text-[20px] md:text-[26px] leading-[35px] md:leading-[39px] font-medium mt-5 mb-5 md:mt-10 md:mb-[32px] mt-3">{_'front.leaflet.favoriteShops'}</h2>
			<div class="leaflet__wrapper mt-3">
				{cache ($cdnImagesAllowed ? 'proxy' :  'native') . 'leafletShops-' . $leaflet->getShop()->getId(), expire => '8 hour'}
					{var $leafletShops = $getLeafletShops()}
					{if !$leafletShops->isEmpty()}
						<div class="">
							<div class="grid grid-cols-2 md:grid-cols-4 gap-6">
								{foreach $leafletShops as $leafletShop}
									{include shopSnippet, shop => $leafletShop}
								{/foreach}
							</div>
						</div>
					{/if}
				{/cache}
			</div>
		</div>
	</div>
</div>

{if isset($pageExtension) && $pageExtension->getBottomDescription()}
    <div class="page-extension content-block">
        <div class="container">
			{$pageExtension->getBottomDescription() |noescape}
        </div>
    </div>
{elseif $localization->isLiteVersion() && $leaflet->getShop() && !empty($leaflet->getShop()->getDescription())}
    <div class="page-extension content-block">
        <div class="container">
			{$leaflet->getShop()->getDescription() |noescape}
        </div>
    </div>
{/if}
