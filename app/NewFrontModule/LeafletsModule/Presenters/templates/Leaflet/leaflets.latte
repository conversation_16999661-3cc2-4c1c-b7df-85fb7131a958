{var $newestLeaflets = $newestLeaflets()}
{var $leaflets = $newestLeaflets->toArray()}

{foreach $leaflets as $leaflet}
    {var $firstLeaflet = $leaflet}
    {breakIf $leaflet->isValid()}
{/foreach}
{var $random = 1}
{block metaTitle}
	{if $pageExtension && $pageExtension->getMetaTitle()}
		{$pageExtension->getMetaTitle()}
    {elseif (count($leaflets) > 0 && isset($shop))}
		{var $validSinceDay = $translator->translate('front.calendar.days.' . $leafletToMetaTitle->getValidSince()->format('w'))}
        {_'front.leaflet.metaTitle' . $random, [brand => mb_strtoupper($shop->getName()), validSince => $leafletToMetaTitle->getValidSince()->format('d.m.Y'), validSinceDay => $validSinceDay] |trim}
    {else}
        {if isset($shop)}
            {_'front.leaflet.list.shop.title'} {$shop->getName()}
        {elseif isset($tag)}
            {_'front.leaflet.list.tag.title'} {$tag->getName()}
        {elseif isset($region)}
            {_'front.leaflet.list.region.title'} {$region->getName()}
        {/if}
    {/if}
{/block}
{block keywords}{if !$pageExtension}{if isset($shop)}{_'front.leaflet.list.shop.keywords', [name => $shop->getName()]}{elseif isset($tag)}{_'front.leaflet.list.tag.keywords', [name => $tag->getName()]}}{elseif isset($region)}{_'front.leaflet.list.region.keywords', [name => $region->getName()]}{/if}{/if}{/block}
{block metaDescription}
    {if (count($leaflets) > 0 && isset($shop))}
        {var $random = 1}
        {_'front.leaflet.metaDescription' . $random, [brand => $shop->getName(), validTill => $leafletToMetaTitle->getValidSince()->format('d.m.Y')]}
    {else}
        {if isset($shop)}
			{_'front.leaflet.list.shop.description', [shop => $shop->getName()]}
		{elseif isset($tag)}
			{_'front.leaflet.list.tag.description', [name => $tag->getName()]}
		{/if}
	{/if}
{/block}

{block #scripts}
	{if (!isset($pageExtension) || $pageExtension->isAdsenseAllowed())}
		<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-3515231881192503"
				crossorigin="anonymous"></script>

		<script type="text/javascript" src="{$basePath}/js/showVisibleAds.js" defer></script>
	{/if}
{/block}

{block content}
{if isset($shop)}
    {var $reviews = $reviews()}
    {if count($reviews) > 0}
     {foreach $reviews as $review}
         {capture $reviewUrl}{$shop->getCurrentLogo() |image:170,0}{/capture}
            <script type="application/ld+json">
                {
                "@context": "http://schema.org/",
                "@type": "Review",
                "itemReviewed": {
                    "@type": "LocalBusiness",
                    "image": {$reviewUrl},
                    "name": {$shop->getName()}
                    },
                "reviewRating": {
                    "@type": "Rating",
                    "ratingValue": {$review->getRate()}
                    },
                    "name": {$review->getText()},
                        "author": {
                            "@type": "Person",
                            "name": {$review->getShortUsername()}
                            },
                        "reviewBody": {$review->getText()},
                        "publisher": {
                        "@type": "Person",
                        "name": {$review->getShortUsername()}
                        }
                }
            </script>
    {/foreach}
    {/if}
{/if}

<div class="leaflet">
    <div class="container container--medium">
		<div class="leaflet__content">
			{if (!isset($pageExtension) || $pageExtension->isAdsenseAllowed())}
				<div class="relative pt-5 min-h-[300px]">
					<!-- Letaky - Vypis letaku obchodu - Responsive - 1 -->
					<ins class="adsbygoogle leaflet-adsbygoogle" style="display:block" data-ad-client="ca-pub-3515231881192503" data-ad-slot="9362973607" data-ad-format="auto" data-full-width-responsive="true"></ins>
				</div>
			{/if}

			<div class="page-header">
				{if isset($pageExtension) && $pageExtension->getHeading()}
					<h1 class="text-[20px] md:text-[26px] leading-[35px] md:leading-[39px] font-medium mt-5 mb-5 md:mt-10 md:mb-[32px]">{$pageExtension->getHeading()}</h1>
				{elseif isset($shop)}
					<h1 class="text-[20px] md:text-[26px] leading-[35px] md:leading-[39px] font-medium mt-5 mb-5 md:mt-10 md:mb-[32px]">{_'front.leaflet.list.shop.title'} {$shop->getName()}</h1>
				{elseif isset($tag)}
					<h1 class="text-[20px] md:text-[26px] leading-[35px] md:leading-[39px] font-medium mt-5 mb-5 md:mt-10 md:mb-[32px]">{_'front.leaflet.list.tag.title'} {$tag->getName()}</h1>
				{elseif isset($region)}
					<h1 class="text-[20px] md:text-[26px] leading-[35px] md:leading-[39px] font-medium mt-5 mb-5 md:mt-10 md:mb-[32px]">{_'front.leaflet.list.region.title'} {$region->getName()}</h1>
				{/if}

				{if isset($pageExtension) && $pageExtension->getTopDescription()}
					<div class="mb-5 md:mb-10">{$pageExtension->getTopDescription() |content |noescape}</div>
				{elseif isset($shop)}
					<div class="mb-5 md:mb-10">{_'front.leaflet.list.shop.perex'} {$shop->getName()}</div>
				{elseif isset($tag)}
					<div class="mb-5 md:mb-10">{_'front.leaflet.list.tag.perex'} {$tag->getName()}</div>
				{elseif isset($region)}
					<div class="mb-5 md:mb-10">{_'front.leaflet.list.region.perex', ['region' => $region->getName()]}</div>
				{/if}
			</div>

			{cache ($cdnImagesAllowed ? 'proxy' :  'native') . 'newestLeaflets' . $localization->getLocale() . (isset($tag) ? ('-' . $tag->getId()) : ('')) . (isset($shop) ? ('-' . $shop->getId()) : ('')) . (isset($region) ? ('-' . $region->getId()) : ('')), expire => '15 minutes'}
				{if !$newestLeaflets->isEmpty()}
					<div class="leaflet__title-wrapper leaflet__title-wrapper--no-top">
						{if isset($pageExtension) && $pageExtension->getSubHeading()}
							<h2 class="text-[20px] md:text-[26px] leading-[35px] md:leading-[39px] font-medium mt-5 mb-5 md:mt-10 md:mb-[32px]">{$pageExtension->getSubHeading()}</h2>
						{elseif isset($shop)}
							<h2 class="text-[20px] md:text-[26px] leading-[35px] md:leading-[39px] font-medium mt-5 mb-5 md:mt-10 md:mb-[32px]">{_'front.leaflet.list.title.actual'} {$shop->getName()}</h2>
						{else}
							<h2 class="text-[20px] md:text-[26px] leading-[35px] md:leading-[39px] font-medium mt-5 mb-5 md:mt-10 md:mb-[32px]">{_'front.leaflet.list.title.new'}</h2>
						{/if}
					</div>

					<div class="grid grid-cols-2 md:grid-cols-4 gap-3">
						{if (!isset($pageExtension) || $pageExtension->isAdsenseAllowed())}
							<div class="min-h-[300px]">
								<!-- Letaky - Vypis letaku obchodu - Ctverec - 1 -->
								<ins class="adsbygoogle srec-sm srec-md srec-lg" style="display:inline-block;width:250px;height:250px" data-ad-client="ca-pub-3515231881192503" data-ad-slot="9762412947"></ins>
							</div>
						{/if}

						{foreach $newestLeaflets as $leaflet}
							{include leafletSnippet, leaflet => $leaflet}
						{/foreach}
					</div>

					<script type="application/ld+json">
					{
						"@context": "http://schema.org",
						"itemListElement": [
							{foreach $newestLeaflets as $leaflet}
								{
									"endDate": {$leaflet->getValidTill()->format('Y-m-d')},
									"startDate": {$leaflet->getValidSince()->format('Y-m-d')},
									"location": {
										"address": {
											"name": {$leaflet->getShop()->getName()},
											"@type": "PostalAddress"
										},
										"url": {link //:NewFront:Shops:Shop:default $leaflet->getShop()},
										"image": {$leaflet->getShop()->getCurrentLogo() |image:60 |replace: '/upload', 'upload' |noescape},
										"name": {$leaflet->getShop()->getName()},
										"@type": "Place"
									},
									"performer": {
										"name": {$leaflet->getShop()->getName()},
										"@type": "Organization"
									},
									"image": {($leaflet->getFirstLeafletPage()) ? $leaflet->getFirstLeafletPage()->getDownloadUrl() |image:198,198,'exactTop',null |replace: '/upload', 'upload' |noescape},
									"name": {$leaflet->getTitle()},
									"url": {link //:NewFront:Leaflets:Leaflet:leaflet, $leaflet},
									"description": {_'front.leaflet.metaTitle1', [brand => $shop ? $shop->getName() : $leaflet->getShop()->getName(), validTill => $firstLeaflet->getValidSince()->format('d.m.Y')] |trim},
									"eventAttendanceMode": "https://schema.org/OfflineEventAttendanceMode",
									"eventStatus": "https://schema.org/EventScheduled",
									"organizer": {
										"@type": "Organization",
										"name": {$leaflet->getShop()->getName()},
										"url": {link //:NewFront:Shops:Shop:default $leaflet->getShop()}
									},
									"@type": "SaleEvent"
								}{sep},{/sep}
							{/foreach}
						],
						"@type": "OfferCatalog"
					}
					</script>
				{else}
					{if isset($shop)}
						<div class="alert alert-success icon"><i class="fa fa-info" aria-hidden="true"></i>{_'front.leaflet.list.noValidLeaflets'}</div>
					{/if}
				{/if}
			{/cache}

			{if (!isset($pageExtension) || $pageExtension->isAdsenseAllowed())}
				<!-- Letaky - Vypis letaku obchodu - Responsive - 3 -->
				<ins class="adsbygoogle leaflet-adsbygoogle mb1" style="display:block" data-ad-client="ca-pub-3515231881192503" data-ad-slot="3859349573" data-ad-format="auto" data-full-width-responsive="true"></ins>
			{/if}

			{if (isset($shop) && isset($shopArticle))}
				<div class="article-horizontal">
					<a href="{plink :NewFront:Articles:Article:default, article => $shopArticle, page => null}" class="article-horizontal__image">
						<img n:if="$shopArticle->getPreviewImage()" src="{$basePath}/images/shop-card-bg.png" data-src="{$shopArticle->getPreviewImage() |image:150,150}" data-src-retina="{$shopArticle->getPreviewImage() |image:300,300}" alt="{$shopArticle->getName()}" class="unveil">
					</a>

					<div class="article-horizontal__content">
						<h3 class="article-horizontal__title title"><a href="{plink :NewFront:Articles:Article:default, article => $shopArticle, page => null}">{$shopArticle->getName()}</a></h3>
						<div class="article-horizontal__text">{$shopArticle->getDescription()|stripHtml|truncate:300,'...'}</div>
					</div>
				</div>
			{/if}

			{cache ($cdnImagesAllowed ? 'proxy' :  'native') . 'relatedLeaflets' . $localization->getLocale() . (isset($shop) ? ('-' . $shop->getId()) : ('')) . (isset($region) ? ('-' . $region->getId()) : ('')), expire => '1 hour'}
				{if
					(!($newestLeaflets instanceof tipli\Model\Doctrine\QueryObject\ResultSet) || $newestLeaflets->isEmpty()) &&
					(!($expiredLeaflets instanceof tipli\Model\Doctrine\QueryObject\ResultSet) || $expiredLeaflets->isEmpty()) &&
					(isset($shop) || isset($region))
					}
					<div class="alert alert-info icon">
						<i class="fa fa-info" aria-hidden="true"></i>
						{if isset($shop)}
							{_'front.leaflet.list.noLeaflets'}
						{elseif isset($region)}
							{_'front.leaflet.list.noRegionLeaflets', ['region' => $region->getName()]}
						{/if}
					</div>

					{if isset($shop)}
						{var $relatedLeafletsTag = $shop->getFirstShopLeafletTag()}
					{else}
						{var $relatedLeafletsTag = null}
					{/if}

					{var $relatedLeafletsByTag = $relatedLeafletsByTag($relatedLeafletsTag)}
					{if !$relatedLeafletsByTag->isEmpty()}
						<div class="leaflet__title-wrapper leaflet__title-wrapper--no-top">
							<h2 class="text-[20px] md:text-[26px] leading-[35px] md:leading-[39px] font-medium mt-5 mb-5 md:mt-10 md:mb-[32px]">
								{if isset($shop)}
									{_'front.leaflet.list.title.category', ['category' => ($relatedLeafletsTag && $relatedLeafletsTag->getTag() ? $relatedLeafletsTag->getTag()->getName() : null)]}
								{elseif isset($region)}
									{_'front.leaflet.list.title.region', ['region' => $region->getName()]}
								{/if}
							</h2>
						</div>

						<div class="grid grid-cols-2 md:grid-cols-4 gap-3">
							{if isset($shop) && isset($relatedLeafletsByTag) && !$relatedLeafletsByTag->isEmpty()}
								{foreach $relatedLeafletsByTag as $relatedLeaflet}
									{include leafletSnippet, leaflet => $relatedLeaflet}
								{/foreach}
							{elseif isset($region) && isset($leafletsWithoutStores) && !$leafletsWithoutStores->isEmpty()}
								{foreach $leafletsWithoutStores as $leafletWithoutStores}
									{include leafletSnippet, leaflet => $leafletWithoutStores}
								{/foreach}
							{/if}
						</div>
					{/if}
				{/if}
			{/cache}
		</div>
    </div>
</div>

<div class="float-form__stop"></div>

{if isset($pageExtension) && $pageExtension->getBottomDescription()}
	<div class="page-extension content-block">
		<div class="container">
			<div class="leaflet__line">
				<a n:href="default" class="leaflet__back"><i class="fa fa-long-arrow-left" aria-hidden="true"></i>{_'front.leaflet.list.backLink'}</a>
				{if isset($shop) && !$shop->isOnlyLeafletShop()}
					<a n:href=":NewFront:Shops:Shop:default, $shop" class="leaflet__back">{_'front.leaflet.list.shopLink', ['shop' => $shop->getName()]}</a>
				{/if}
			</div>

			<div class="page-extension" n:if="isset($pageExtension) && $pageExtension->getMiddleDescription()">
				{$pageExtension->getMiddleDescription() |noescape}
			</div>

			<img src="/images/placeholder-bg-1140.jpg" data-src="{$shop->getLeafletCover() |image:1140,460,'exact'}" n:if="isset($shop) && $shop->getLeafletCover()" n:attr="alt => $shop->getLeafletCoverAlt()" class="img-responsive mb-3 lazyload">

			<div class="page-extension" n:if="isset($pageExtension) && $pageExtension->getBottomDescription()">
				{$pageExtension->getBottomDescription() |noescape}
			</div>

			{if isset($shop)}
				{cache ($cdnImagesAllowed ? 'proxy' :  'native') . 'leafletRelatedShops-' . $shop->getId() . '-' . $localization->getLocale(), expire => '1 hour'}
					{var $leafletRelatedShops = $leafletRelatedShops()}
					{if !$leafletRelatedShops->isEmpty()}
						<div class="leaflet__title-wrapper leaflet__title-wrapper--no-top">
							<h2 class="text-[20px] md:text-[26px] leading-[35px] md:leading-[39px] font-medium mt-5 mb-5 md:mt-10 md:mb-[32px]">
								{capture $relatedShopsTitle}
									{if $pageExtension->getBottomTitle()}
										{$pageExtension->getBottomTitle()}
									{else}
										{_'front.leaflet.list.title.relatedShops', [shop => $shop->getName()]}
									{/if}
								{/capture}

								{$relatedShopsTitle}
							</h2>
						</div>

						<div class="">
							<div class="grid grid-cols-2 md:grid-cols-4 gap-6">
								{foreach $leafletRelatedShops as $leafletRelatedShop}
									{include shopSnippet, shop => $leafletRelatedShop}
								{/foreach}
							</div>
						</div>
					{/if}
				{/cache}
			{/if}
		</div>
	</div>

{elseif $localization->isLiteVersion() && isset($shop) && !empty($shop->getDescription())}
    <div class="page-extension content-block">
        <div class="container">
            <div class="leaflet__line">
                {$shop->getDescription() |noescape}
            </div>
        </div>
    </div>
{/if}

