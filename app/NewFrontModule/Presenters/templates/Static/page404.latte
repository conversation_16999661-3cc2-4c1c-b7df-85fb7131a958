<!DOCTYPE html>
<html lang="cs" xmlns:n="http://www.w3.org/1999/xhtml">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">


    <title>{_'front.error404.title'}</title>
    <meta name="keywords" content="">
    <meta name="description" content="{_'front.error404.text'}">
    <meta name="author" content="Tipli">

    <meta name="robots" content="noindex,follow">

    <link rel="apple-touch-icon" sizes="152x152" href="https://www.tipli.cz/images/favicon/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="https://www.tipli.cz/images/favicon/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="https://www.tipli.cz/images/favicon/favicon-16x16.png">
    <link rel="manifest" href="https://www.tipli.cz/images/favicon/manifest.json">
    <link rel="mask-icon" href="https://www.tipli.cz/images/favicon/safari-pinned-tab.svg" color="#ee7836">
    <meta name="theme-color" content="#ffffff">

    <link rel="manifest" href="/manifest.json">


    <link rel="stylesheet" href="https://www.tipli.cz//css2/output.css?v=0.18">

    <style>
        #footer-background.lazy {
            background-image: none;
            background-color: #f58b21;
        }

        #footer-background {
            background-image: url('https://www.tipli.cz/new-design/bg-footer-mobile.png');
            background-color: transparent;
        }

        .sidebar {
            transition: transform 0.3s ease-in-out;
            transform: translateX(-100%);
        }

        .sidebar.open {
            transform: translateX(0);
            display: block;
        }

        .transition-transform {
            transition: transform 0.3s;
        }

        .rotate-180 {
            transform: rotate(180deg);
        }
    </style>

	{if $configuration->isTrackingAllowed()}
		{include '../pixels/gtm.latte'}
	{/if}

    <!-- Viewport for mobile devices -->
    <meta content="width=device-width, initial-scale=1.0, user-scalable=1, minimum-scale=1.0" name="viewport">

</head>

<body class="cs overflow-x-hidden" data-basePath="https://www.tipli.cz" data-locale="cs" data-search-url="/hledat"
    data-isAdmin="true">

	{if $configuration->isTrackingAllowed()}
	{include '../pixels/gtmNoScript.latte'}
	{/if}

    <header id="header" class="bg-white w-full relative z-50">
        <div class="border-b w-full border-b-light-4">
            <div class="relative flex justify-between container p-0 pl-5 h-[64px] items-center">
                <a n:href=":NewFront:Homepage:default" data-google-interstitial="false">
                    {if $localization->isHungarian()}
						<img src="{$basePath}/images/tiplino_logo_new_color.svg" title="Tipli" alt="Tiplino" loading="lazy" class="w-[60px] h-[29px] md:w-[78px] md:h-[39px] mt-[3px]">
					{else}
						<svg class="w-[60px] h-[29px] md:w-[78px] md:h-[39px] mt-[3px]" width="78" height="39" viewBox="0 0 78 39" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path fill-rule="evenodd" clip-rule="evenodd"
										d="M21.8419 7.28024H27.4368V27.3415H21.8419V7.28024ZM62.3468 27.1098V7.50599H67.482V27.1103L62.3468 27.1098ZM53.2192 27.1098V0.339844H58.3599V27.1103L53.2192 27.1098ZM44.8271 17.2604C44.8271 16.7853 44.7389 16.2929 44.5681 15.7818C44.3914 15.2713 44.1325 14.808 43.7673 14.3987C43.4003 13.9803 42.9524 13.6383 42.4536 13.3957C41.9294 13.1404 41.3407 13.0155 40.6812 13.0155H36.3999V21.4635H40.6812C41.3407 21.4635 41.9294 21.3268 42.4536 21.0538C43.4112 20.5591 44.1557 19.7269 44.5441 18.7153C44.7326 18.2225 44.8266 17.7415 44.8266 17.2609L44.8271 17.2604ZM30.9054 7.42876H40.6576C42.3655 7.42876 43.8377 7.73174 45.086 8.33724C46.3342 8.94275 47.359 9.72042 48.1658 10.6766C49.6897 12.4899 50.5378 14.7884 50.5573 17.1664C50.536 19.5748 49.6825 21.9042 48.148 23.7493C47.3068 24.76 46.2584 25.5826 45.0801 26.1595C43.8477 26.7477 42.4981 27.0548 41.134 27.0548C40.9878 27.0548 40.8374 27.0516 40.6921 27.0448L36.3999 27.0444V34.0148H30.9054V7.42876ZM9.75216 21.3091C9.28111 21.3091 8.79233 21.2201 8.28585 21.0479C7.24699 20.6845 6.39393 19.9173 5.91879 18.917C5.66577 18.3947 5.54178 17.7951 5.54178 17.136V13.1222H14.216V7.476H5.54131V0.339844H0V17.1123C0 18.828 0.300248 20.3179 0.901213 21.5707C1.50172 22.8294 2.27349 23.8624 3.22149 24.676C4.1695 25.4895 5.21199 26.0891 6.3426 26.4866C7.47912 26.8845 8.58658 27.0862 9.65814 27.0862C10.7297 27.0862 11.843 26.8845 12.9973 26.4866C19.1509 24.3611 19.463 18.6617 19.463 15.8118H13.9689C13.9571 17.0351 13.8631 18.2461 13.5156 18.917C13.2507 19.4335 12.921 19.8728 12.5262 20.2226C11.7576 20.9026 10.7742 21.2882 9.75216 21.3091ZM21.8419 0.339844H27.4368V5.98697H21.8419V0.339844Z"
										fill="#646C7C" />
							<path fill-rule="evenodd" clip-rule="evenodd"
										d="M62.3465 0.339844H67.4817V5.52319H62.3465V0.339844ZM74.6606 26.552L77.9998 29.7285C73.133 34.8664 68.2344 38.395 61.1623 38.5867C53.9952 38.2737 49.1076 34.5285 44.4961 29.7403L47.8116 26.5342C51.5042 30.3808 55.5742 33.6854 61.1973 33.9375C66.8531 33.7695 70.745 30.6779 74.6606 26.552Z"
										fill="#EF7F1A" />
						</svg>
					{/if}
                </a>

                <div class="flex items-center shrink-0 gap-6">
                    <a data-google-interstitial="false"
                        class="hidden font-medium hover:underline text-sm leading-[24.5px] md:block" n:href=":NewFront:Sign:in">
                        {_newFront.sign.in.form.submit}
                    </a>
                    <a data-google-interstitial="false"
                        class="px-7 py-3 rounded-xl bg-orange-gradient text-white font-medium text-sm leading-[24.5px] mr-2 md:mr-0 cursor-pointer xl:hover:bg-orange-gradient-hover"
                        n:href=":NewFront:Sign:up">
                        {_newFront.sign.up.create}
                    </a>
                </div>
            </div>
        </div>

        <div class="hidden md:block border-b border-b-light-4 ">
            <div class="flex py-3 container justify-between">
                <div class="flex items-center">
                    <a data-google-interstitial="false"
                        class="flex items-center cursor-pointer gap-2 js-show-shops xl:hover:text-primary-orange"
                        n:href=":NewFront:Shops:Shops:default">
                        <svg width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M7.85714 7.25909C7.85714 7.44353 7.7849 7.62043 7.6563 7.75085C7.5277 7.88127 7.35329 7.95455 7.17143 7.95455H1.68571C1.50386 7.95455 1.32944 7.88127 1.20084 7.75085C1.07224 7.62043 1 7.44353 1 7.25909V1.69545C1 1.51101 1.07224 1.33412 1.20084 1.20369C1.32944 1.07327 1.50386 1 1.68571 1H7.17143C7.35329 1 7.5277 1.07327 7.6563 1.20369C7.7849 1.33412 7.85714 1.51101 7.85714 1.69545V7.25909Z"
                                stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path
                                d="M17 7.25909C17 7.44353 16.9278 7.62043 16.7992 7.75085C16.6706 7.88127 16.4961 7.95455 16.3143 7.95455H10.8286C10.6467 7.95455 10.4723 7.88127 10.3437 7.75085C10.2151 7.62043 10.1429 7.44353 10.1429 7.25909V1.69545C10.1429 1.51101 10.2151 1.33412 10.3437 1.20369C10.4723 1.07327 10.6467 1 10.8286 1H16.3143C16.4961 1 16.6706 1.07327 16.7992 1.20369C16.9278 1.33412 17 1.51101 17 1.69545V7.25909Z"
                                stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path
                                d="M17 17.3045C17 17.489 16.9278 17.6659 16.7992 17.7963C16.6706 17.9267 16.4961 18 16.3143 18H10.8286C10.6467 18 10.4723 17.9267 10.3437 17.7963C10.2151 17.6659 10.1429 17.489 10.1429 17.3045V11.7409C10.1429 11.5565 10.2151 11.3796 10.3437 11.2491C10.4723 11.1187 10.6467 11.0455 10.8286 11.0455H16.3143C16.4961 11.0455 16.6706 11.1187 16.7992 11.2491C16.9278 11.3796 17 11.5565 17 11.7409V17.3045Z"
                                stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path
                                d="M7.85714 17.3045C7.85714 17.489 7.7849 17.6659 7.6563 17.7963C7.5277 17.9267 7.35329 18 7.17143 18H1.68571C1.50386 18 1.32944 17.9267 1.20084 17.7963C1.07224 17.6659 1 17.489 1 17.3045V11.7409C1 11.5565 1.07224 11.3796 1.20084 11.2491C1.32944 11.1187 1.50386 11.0455 1.68571 11.0455H7.17143C7.35329 11.0455 7.5277 11.1187 7.6563 11.2491C7.7849 11.3796 7.85714 11.5565 7.85714 11.7409V17.3045Z"
                                stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"></path>
                        </svg>

                        <div class="hidden font-medium text-sm leading-[24.5px] md:block">
                            {_newFront.navbar.shops}
                        </div>
                    </a>

                    <a data-google-interstitial="false"
                        class="flex items-center cursor-pointer gap-2 ml-9 xl:hover:text-primary-orange" n:href=":NewFront:Deals:Deals:default">
                        <svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
                            <path
                                d="M13.1201 4.55111C13.1201 4.83966 13.2349 5.11637 13.4392 5.3204C13.6436 5.52443 13.9206 5.63905 14.2096 5.63905C14.4986 5.63905 14.7756 5.52443 14.98 5.3204C15.1843 5.11637 15.299 4.83966 15.299 4.55111C15.299 4.26257 15.1843 3.98585 14.98 3.78182C14.7756 3.5778 14.4986 3.46317 14.2096 3.46317C13.9206 3.46317 13.6436 3.5778 13.4392 3.78182C13.2349 3.98585 13.1201 4.26257 13.1201 4.55111Z"
                                stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path
                                d="M16.8273 1.00002H9.73813C9.57803 1.001 9.41968 1.03429 9.27283 1.09791C9.1259 1.16153 8.99335 1.25416 8.88319 1.37023L1.31738 9.43154C1.20974 9.54494 1.12625 9.67897 1.07195 9.82554C1.01764 9.97211 0.993653 10.1281 1.00143 10.2842C1.00921 10.4402 1.04859 10.5931 1.1172 10.7335C1.18581 10.874 1.28222 10.9991 1.4006 11.1012L8.96641 17.712C9.19551 17.9113 9.49315 18.014 9.79654 17.9985C10.1 17.983 10.3855 17.8505 10.5931 17.6289L17.6822 10.0737C17.8864 9.85999 18.0002 9.57584 18 9.28044V2.16352C18 2.01009 17.9697 1.85817 17.9106 1.71652C17.8516 1.57486 17.7651 1.44626 17.6561 1.33812C17.5471 1.22998 17.4177 1.14444 17.2755 1.08641C17.1333 1.02839 16.981 0.999029 16.8273 1.00002Z"
                                stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"></path>
                        </svg>

                        <div class="hidden font-medium text-sm leading-[24.5px] md:block">
                            {_newFront.navbar.coupons}
                        </div>
                    </a>
                </div>

            </div>
        </div>
    </header>

    <div id="main" class="main" data-exit-popup="false">

        <div class="bg-img-404">
            <div
                class="flex flex-col md:flex-row justify-center items-center pt-10 md:pt-[121px] pb-10 md:pb-20 gap-5 md:gap-10">
                <div>
                    <img src="https://www.tipli.cz/new-design/404-donkey.png" alt="oslik"
                        class="h-[209px] lg:h-[428px]">
                </div>
                <div class="text-white w-[491px] max-w-full text-center p-5 md:p-0">
                    <div class="text-[28px] md:text-[40px] font-bold leading-[39px] md:leading-[58px] mb-2.5">{_'front.error404.title'}</div>
					<div class="text-base md:text-[20px] leading-[24.5px] md:leading-[35px] mb-5 md:mb-10">{_'front.error404.text'}</div>

                    <a class="text-sm md:text-base text-dark-1 font-bold leading-7 border border-transparent py-[14px] w-[401px] max-w-full bg-white rounded-xl hover:bg-transparent hover:text-white hover:border hover:border-white px-5"
                        n:href=":NewFront:Homepage:default">
                        {_'front.error404.btnLabel'}
                    </a>
                </div>
            </div>
        </div>

        <style>
            .bg-img-404 {
                left: 0;
                z-index: 10;
                position: relative;
                width: 100%;
                height: auto;
                background-image: url(https://www.tipli.cz/new-design/404.png);
                background-repeat: no-repeat;
                background-size: cover;
                background-position: bottom;
            }
        </style>
    </div>

    <footer class="footer">


        <div class="relative w-full bg-[#182B4A]">

            <div class="container max-w-[1240px]">

                <div class="flex items-center justify-between pt-10 pb-10">

                    <img src="https://www.tipli.cz//new-design/footer/tipli.svg" alt="Tipli" loading="lazy"
                        class="max-w-[110px] md:max-w-[150px]">

                    <div class="hidden md:block text-right text-white text-sm font-normal leading-normal">
                        {_'newFront.footer.copyright', ['year' => date('Y')]}
                    </div>

                    <img src="https://www.tipli.cz/new-design/footer/apek.svg" alt="APEK" loading="lazy" class="">

                </div>
                <div class="md:hidden w-fill h-px opacity-30 bg-white"></div>

                <div class="md:hidden text-center text-white text-sm font-normal pt-[36px] pb-[32px] leading-normal">
                    {_'newFront.footer.copyright', ['year' => date('Y')]}
                </div>
            </div>
        </div>



    </footer>
</body>

</html>
