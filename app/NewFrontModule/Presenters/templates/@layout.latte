{varType tipli\Model\Localization\Entities\Localization $localization}
{var $variant = $googleExperimentVariantResolver->getExperimentVariant('googleoptimize', 2)->getVariant()}
{var $canonicalUrl = new \Nette\Http\Url($presenter->getHttpRequest()->getUrl()->getAbsoluteUrl())}
{if $presenter->isLinkCurrent(':NewFront:Shops:Shop:default')}
{var $canonicalUrl = $canonicalUrl->setQuery([])}
{/if}
<!DOCTYPE html>
<html lang="{$locale}" xmlns:n="http://www.w3.org/1999/xhtml">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
	{capture $metaTitle}
	{include metaTitle |striptags |strip}
	{/capture}
	{var $showTipliPipeline = (!$presenter->isLinkCurrent(':NewFront:Leaflets:Leaflet:leaflets') ?: strlen($metaTitle) <=
		50) && ($pageExtension !==null && !$pageExtension->isDisableGeneratedMetaTitle())}
		<title>{include metaTitle |striptags |strip}{if $showTipliPipeline} | {$localization->getLocale() === 'hu' ?
			'Tiplino' : 'Tipli'}{/if}</title>
		<meta name="keywords" content="{include metaKeywords}">
		<meta name="description" content="{include metaDescription|stripHtml|truncate:255|strip|replace: [" \r", "\n"
			], '' }">
		<meta name="author" content="Tipli">

		{ifset $robotsNoIndex}
		<meta name="robots" content="noindex,nofollow" />
		{elseifset $robotsNoIndexFollow}
		<meta name="robots" content="noindex,follow" />
		{else}
		<meta name="robots" content="{block #robots|stripHtml|trim}index,follow{/block}">
		{/ifset}

		{if $pageExtension}
		{cache 'hreflang-' . $localization->getLocale() . '-' . $currentUrlPath, expire => '30 minutes', tags =>
		['pageExtension/' . $pageExtension->getId()]}
		{var $groupedPageExtensions = $groupedPageExtensions()}
		{if $groupedPageExtensions}
		{foreach $groupedPageExtensions as $pe}
		<link rel="alternate" href="{$pe->getFullUrl()|noescape}" hreflang="{$pe->getHreflang()}" />
		{if $pe->getLocalization() === $localization}
		<link rel="alternate" href="{$pe->getFullUrl()|noescape}" hreflang="x-default" />
		{/if}
		{/foreach}
		{/if}
		{/cache}
		{/if}

		<meta property="og:title" content="{include metaTitle |striptags |strip}" />
		<meta property="og:site_name" content="{ifset ogSiteName}{include ogSiteName}{else}{_'newFront.head.title'}{/ifset}" />
		<meta property="og:url" content="{$canonicalUrl |canonicalUrl}" />
		<meta property="og:description" content="{include metaDescription|stripHtml|truncate:255|strip|replace: ["
			\r", "\n" ], '' }" />
		<meta property="og:type" content="website" />
		<meta property="og:image" content="{ifset image}{include image |strip}{else}{_'newFront.head.image'}{/ifset}" />
		<meta property="fb:app_id" content="{$configuration->getFacebookAppId()}" />

		<meta n:if="$localization->isCzech()" property="fb:pages" content="1230263787039989" />
		<meta n:if="$localization->isSlovak()" property="fb:pages" content="1150797308345080" />
		<meta n:if="$localization->isBulgarian()" name="facebook-domain-verification" content="b0usg0lg8ks51algt8loxh2qk2b2h1" />
		<meta n:if="$localization->isSlovenian()" name="facebook-domain-verification" content="1tlrooitos13hieemulsm9khfijzuc" />
		<meta n:if="$localization->isCroatian()" name="facebook-domain-verification" content="m3u212ou8rv0g6ztgyqzr1lcs0rpzs" />

		<meta name="ahrefs-site-verification" content="1ad8b2b6271f5e03727b3a2cd166ecf2d30a511a714ff75330adf401c1d7f4ed">

		<meta name="twitter:card" content="{ifset image}summary{else}summary_large_image{/ifset}" />
		<meta name="twitter:site" content="{_'front.head.twitterUsername'}" />
		<meta name="twitter:title" content="{include metaTitle |striptags}" />
		<meta name="twitter:description" content="{include metaDescription|stripHtml|truncate:255}" />
		<meta name="twitter:image" content="{ifset image}{include image}{else}{_'front.head.image'}{/ifset}" />

		<link rel="apple-touch-icon" sizes="152x152" href="{$basePath}/images/favicon/apple-touch-icon.png">
		<link rel="icon" type="image/png" sizes="32x32" href="{$basePath}/images/favicon/favicon-32x32.png">
		<link rel="icon" type="image/png" sizes="16x16" href="{$basePath}/images/favicon/favicon-16x16.png">
		<link rel="manifest" href="{$basePath}/images/favicon/manifest.json">
		<link rel="mask-icon" href="{$basePath}/images/favicon/safari-pinned-tab.svg" color="#ee7836">
		<meta name="theme-color" content="#ffffff">

		{if !isset($disableCanonical)}
		{define canonicalLink, $url, $rel}
		<link rel="{$rel}" href="{$url |canonicalUrl}" />
		{/define}

		{include canonicalLink, $canonicalUrl->getAbsoluteUrl(), "canonical"|replace: "&amp;","&"}

		{if
			(isset($presenter['paginator']) && $presenter->getComponent('paginator')) &&
			!$presenter->isLinkCurrent(':NewFront:Shops:Shop:default') &&
			!$presenter->isLinkCurrent(':NewFront:Leaflets:Leaflet:default') &&
			!$presenter->isLinkCurrent(':NewFront:Leaflets:Leaflet:leaflets') &&
			!$presenter->isLinkCurrent(':NewFront:Articles:Article:default')
		}
			{var $paginator = $presenter->getComponent('paginator')->getPaginator()}

			{if isset($presenter['leafletPagesPaginator']) && $presenter->getView() === 'leaflet'}
				{var $paginator = $presenter->getComponent('leafletPagesPaginator')->getPaginator()}
			{/if}

			{var $couponsPaginator = false}
			{if isset($presenter['couponsPaginator']) && $presenter['couponsPaginator']->getPaginator()->getPage()}
				{var $paginator = $presenter->getComponent('couponsPaginator')->getPaginator()}
				{var $couponsPaginator = true}
			{/if}

			{var $lastProductsPaginator = false}
			{if isset($presenter['lastProductsPaginator']) && $presenter['lastProductsPaginator']->getPaginator()->getPage()
			> 1}
				{var $paginator = $presenter->getComponent('lastProductsPaginator')->getPaginator()}
				{var $lastProductsPaginator = true}
			{/if}

			{var $nonCashbackShopsPaginator = false}
			{if isset($presenter['paginatorNonCashbackShops']) &&
			$presenter['paginatorNonCashbackShops']->getPaginator()->getPage() > 1}
				{var $paginator = $presenter->getComponent('paginatorNonCashbackShops')->getPaginator()}
				{var $nonCashbackShopsPaginator = true}
			{/if}

			{var $page = $paginator->getPage()}
			{if isset($presenter['leafletPagesPaginator'])}
				{if !$paginator->isFirst()}
					{include canonicalLink, $presenter->link('//leafletPagesPaginator-page!', [leafletPagesPaginator-page => $page -
					1]), "prev"|replace: "&amp;","&"}
				{/if}
				{if !$paginator->isLast()}
					{include canonicalLink, $presenter->link('//leafletPagesPaginator-page!', [leafletPagesPaginator-page => $page +
					1]), "next"|replace: "&amp;","&"}
				{/if}
			{elseif $couponsPaginator}
				{if !$paginator->isFirst()}
					{include canonicalLink, $presenter->link('//couponsPaginator-page!', [couponsPaginator-page => $page - 1]),
					"prev"|replace: "&amp;","&"}
				{/if}
				{if !$paginator->isLast()}
					{include canonicalLink, $presenter->link('//couponsPaginator-page!', [couponsPaginator-page => $page + 1]),
					"next"|replace: "&amp;","&"}
				{/if}
			{elseif $lastProductsPaginator}
				{if !$paginator->isFirst()}
					{include canonicalLink, $presenter->link('//lastProductsPaginator-page!', [lastProductsPaginator-page => $page -
					1]), "prev"|replace: "&amp;","&"}
				{/if}
				{if !$paginator->isLast()}
					{include canonicalLink, $presenter->link('//lastProductsPaginator-page!', [lastProductsPaginator-page => $page +
					1]), "next"|replace: "&amp;","&"}
				{/if}
			{elseif $nonCashbackShopsPaginator}
				{if !$paginator->isFirst()}
					{include canonicalLink, $presenter->link('//paginatorNonCashbackShops-page!', [paginatorNonCashbackShops-page =>
					$page - 1]), "prev"|replace: "&amp;","&"}
				{/if}
				{if !$paginator->isLast()}
					{include canonicalLink, $presenter->link('//paginatorNonCashbackShops-page!', [paginatorNonCashbackShops-page =>
					$page + 1]), "next"|replace: "&amp;","&"}
				{/if}
			{else}
				{if !$paginator->isFirst()}
					{include canonicalLink, $presenter->link('//paginator-page!', [paginator-page => $page - 1]), "prev"|replace:
					"&amp;","&"}
				{/if}
				{if !$paginator->isLast()}
					{include canonicalLink, $presenter->link('//paginator-page!', [paginator-page => $page + 1]), "next"|replace:
					"&amp;","&"}
				{/if}
			{/if}
		{/if}
	{/if}

		<link rel="alternate" type="application/rss+xml" title="RSS {_'front.head.title'}" href="{link :NewFront:Articles:Rss:articles}" />
		<link rel="search" type="application/opensearchdescription+xml" title="Tipli" href="/opensearch.xml">

		<link rel="manifest" href="/manifest.json" />

		<meta n:if="$localization->isCzech()" name="seznam-wmt" content="3O9X5BDC58BwOeu4JZo2V412PgPrBM6W" />
		<meta n:if="$localization->isSlovak()" name="seznam-wmt" content="s1RJcXc1THp4CiBeDbBc5OOpedCxfJv1" />

		<meta n:if="$localization->isCzech()" name="facebook-domain-verification" content="8wqb5kh3ey7hl8tdim5byvv3g09ng5" />
		<meta n:if="$localization->isSlovak()" name="facebook-domain-verification" content="lc2ekp3xsdp3giirq2c3qyeeoqolw2" />
		<meta n:if="$localization->isPolish()" name="facebook-domain-verification" content="ozotvxg9f7q7l687hpb2cb93yc3b52" />
		<meta n:if="$localization->isRomanian()" name="facebook-domain-verification" content="9hjefy0uootolbqlqug8cbarkf0b6l" />
		<meta n:if="$localization->isHungarian()" name="facebook-domain-verification" content="9jqlx098exrpoogszbmvf7skc1crh5" />


		{var $version = 0.18}

		<link rel="stylesheet" href="{$basePath}/css2/output.css?v={$version}">

		{if false}
			<link rel="preconnect" href="https://fonts.googleapis.com">
			<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
			<link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&display=swap">
			{*<link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Readex+Pro:wght,HEXP@160..700,0..100&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap">*}
		{/if}

		<style>
			#footer-background.lazy {
				background-image: none;
				background-color: #f58b21;
			}

			#footer-background {
				background-image: url('{$basePath}/new-design/bg-footer-mobile.png');
				background-color: transparent;
			}

			.sidebar {
				transition: transform 0.3s ease-in-out;
				transform: translateX(-100%);
			}

			.sidebar.open {
				transform: translateX(0);
				display: block;
			}

			.transition-transform {
				transition: transform 0.3s;
			}

			.rotate-180 {
				transform: rotate(180deg);
			}
		</style>

		{block #styles}{/block}

		{if $configuration->isTrackingAllowed()}
			<script n:ifset="$experimentId">
				var expId = { $experimentId }
				var expVar = { $experimentVariation }
			</script>

			{include 'pixels/gtm.latte'}
		{/if}

		<!-- Viewport for mobile devices -->
		{var $viewportContent = $viewport == 'desktop' ? 'width=1280' : 'width=device-width, initial-scale=1.0, user-scalable=1, minimum-scale=1.0'}
		<meta content="{$viewportContent}" name="viewport">

		<meta name="google-signin-client_id" content="{$configuration->getGoogleWebClientId()}">
		<meta name='ir-site-verification-token' value='616239723' />

	{block head}{/block}
</head>

<body class="{$locale} overflow-x-hidden" data-basePath="{$baseUrl}" data-locale="{$locale}" data-search-url="{plink :NewFront:Shops:Shops:search}"
	data-isAdmin="{if $user->isLoggedIn() && $user->getIdentity()->isAdmin()}true{else}false{/if}">

	{if $configuration->isTrackingAllowed()}
	{include 'pixels/gtmNoScript.latte'}
	{/if}
	{if $presenter->isLinkCurrent(':NewFront:Articles:Article:default')}
	{include 'pixels/fbSDK.latte'}
	{/if}

	{if !$isMobileInAppBrowser}
	{include navbar}
	{/if}

	<div id="main" class="main {if $isMobileInAppBrowser}main--no-layout{/if}" {if
		isset($shop)}data-ajax-popup="{link aqPopup-open!, aqPopup-type => exit, aqPopup-shopId => $shop->getId()}"
		{else}data-ajax-popup="{link aqPopup-open!, aqPopup-type => exit}" {/if}
		data-exit-popup="{if $isExitPopupAllowed}true{else}false{/if}">

		{snippet flashMessages}
		{foreach $flashes as $flash}
			{switch $flash->type}
				{case 'success'}
					{var $type = 'bg-secondary-green'}
				{case 'info'}
					{var $type = 'bg-secondary-green'}
				{case 'error'}
					{var $type = 'bg-secondary-red'}
				{case 'warning'}
					{var $type = 'bg-secondary-red'}
				{default}
					{var $type = 'bg-secondary-light-green'}
			{/switch}
			<div
				class="flex justify-center items-center gap-[9px] text-sm leading-[24.5px] p-6 {$type} rounded-2xl text-white fixed bottom-10 left-5 right-5 md:text-xl md:gap-[20px] md:left-auto md:right-10 md:p-14 z-20 js-flash-message">
				<svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" viewBox="0 0 21 21" fill="none" class="w-[19px] h-[19px] md:w-[32px] md:h-[32px]">
					<path
						d="M10.5 13.9545V9.63636M10.5 7.04545H10.5104M20 10.5C20 15.7467 15.7467 20 10.5 20C5.25329 20 1 15.7467 1 10.5C1 5.25329 5.25329 1 10.5 1C15.7467 1 20 5.25329 20 10.5Z"
						stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
				</svg>
				{$flash->message}
				<svg class="absolute right-[-21px] top-[-16px] cursor-pointer" xmlns="http://www.w3.org/2000/svg" width="67" height="68"
					viewBox="0 0 67 68" fill="none" onclick="hideFlashMessage(event)">
					<g filter="url(#filter0_d_783_2736)">
						<rect x="26" y="8" width="28" height="28" rx="14" fill="white" />
					</g>
					<path d="M36.3164 25.6844L44.4217 17.5791M44.4217 25.6844L36.3164 17.5791" stroke="#080B10"
						stroke-width="1.5" stroke-linecap="round" />
					<defs>
						<filter id="filter0_d_783_2736" x="0" y="0" width="68" height="68" filterUnits="userSpaceOnUse"
							color-interpolation-filters="sRGB">
							<feFlood flood-opacity="0" result="BackgroundImageFix" />
							<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
								result="hardAlpha" />
							<feOffset dx="-6" dy="12" />
							<feGaussianBlur stdDeviation="10" />
							<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0" />
							<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_783_2736" />
							<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_783_2736" result="shape" />
						</filter>
					</defs>
				</svg>
			</div>
		{/foreach}
		{*
		<div class="flashmessage-w" n:if="$flashes">
			<div n:foreach="$flashes as $flash" class="flashmessage {$flash->type}">
				<div class="container">
					{$flash->message}<i class="icon-close"></i>
				</div>
			</div>
		</div>
		*}
		{/snippet}

		{include content}
	</div>

	{if !$isMobileInAppBrowser}
		<footer class="footer">
			{include footer}
		</footer>
	{/if}


	{* popups *}
	{*<script type="text/javascript" src="{$basePath}/js/homepage/homepage.socialLogin.js?v=0.9.3" async defer></script>*}

	{control jsBuilderControl ['js/jquery-1.12.0.min.js']}
	{control jsBuilderControl ['js/netteForms.js', 'js/nette.ajax.js']}

	<script src="{$basePath}/js/main.front-new.js?v={$version}" defer></script>

	<!-- Acquisition popup -->
	<script type="text/javascript" src="{$basePath}/js/popup/aqPopup-new.js?v=0.1" defer></script>


	{if $user->isLoggedIn() && $user->getIdentity()->isAdmin()}
		{include 'snippets/seoExtension.latte'}
	{/if}

	{if !$presenter->isLinkCurrent(':NewFront:Static:speedTest')}
	{* AQ popup *}
	{control aqPopup}
	{/if}


	{snippet popupCampaign}
		{if isset($popupCampaign)}
			<div class="campaign-popup-bg fixed z-50 left-0 top-0 w-full h-full overflow-auto bg-[#182B4AE5] backdrop-blur-sm justify-center items-center p-5 hidden">
				<div class="campaign-popup flex flex-col items-center justify-center">
					{if isset($registrationPage) && $registrationPage->hasThankYouPopup()}
					<svg class="campaign-popup__close" data-close-link="{$baseUrl}">{('times-solid'|svg)|noescape}</svg>

					<a href="{$registrationPage->getThankYouUrl()}" class="campaign-popup__link" {if
						$registrationPage->isThankYouOpenInNewTab()} target="_blank"{/if}>
						<picture class="campaign-popup__img rounded-t-2xl">
							<source media="(min-width: 768px)"
								srcset="{$registrationPage->getThankYouImageDesktop() |image:690}">
							<source media="(min-width: 320px)"
								srcset="{$registrationPage->getThankYouImageMobile() |image:375}">
							<img src="{$registrationPage->getThankYouImageDesktop() |image:690}" alt="thx">
						</picture>
					</a>
					{else}
					<svg class="campaign-popup__close absolute w-5 h-5 text-white top-10 right-10 cursor-pointer"
						data-close-link="{$baseUrl}{$popupInteractionCloseLink}">{('times-solid'|svg)|noescape}</svg>

					<a href="{$popupCampaignLink}" class="campaign-popup__link" {if $popupCampaign->getOpenInNewTab()}
						target="_blank"{/if}>
						<picture class="campaign-popup__img rounded-t-2xl">
							<source media="(min-width: 768px)" srcset="{$popupCampaign->getImage() |image:690}">
							<source media="(min-width: 320px)" srcset="{$popupCampaign->getImageMobile() |image:375}">
							<img src="{$popupCampaign->getImage() |image:690}" alt="info">
						</picture>
					</a>

					<div class="campaign-popup__row">
						<a href="{$popupCampaignLink}" class="campaign-popup__cta max-w-60 block text-center w-full p-3.5 mt-3 relative z-20 rounded-xl bg-orange-gradient text-white font-medium text-xs md:text-base md:py-4 leading-[28px] md:mb-2 cursor-pointer xl:hover:bg-orange-gradient-hover" {if $popupCampaign->getOpenInNewTab()}
							target="_blank"{/if}>{$popupCampaign->getCtaLabel()}</a>
					</div>
					{/if}
				</div>
			</div>

			<!-- Campaign popup -->
			<script type="text/javascript" src="{$basePath}/js/popup/campaignPopup-new.js" defer></script>
		{/if}
	{/snippet}

	{ifset $anchor}
		<script type="text/javascript">
			window.location.hash = { $anchor }
		</script>
	{/ifset}

	{block #scripts}{/block}

	<!-- Algolia -->
	{if !$isMobileInAppBrowser}
		<script src="https://cdn.jsdelivr.net/npm/algoliasearch@4.23.3/dist/algoliasearch.umd.js" defer integrity="sha256-76mmfHsYYb491qbcs1Vd/iK80pdRqKCOEYJtPEy8dys=" crossorigin="anonymous"></script>
		<script src="https://cdn.jsdelivr.net/npm/@algolia/autocomplete-js" defer></script>
		<script src="{$basePath}/js/algolia-new.js?v=0.11" defer></script>
	{/if}


	{if $configuration->isTrackingAllowed()}
		{include 'pixels/googleAdwords.latte'}
		{include 'pixels/googleAdwordsRemarketing.latte'}
	{/if}

{*	{if*}
{*		$presenter->getName() !== 'NewFront:LuckyShops:LuckyShops' &&*}
{*		(*}
{*		$localization->isCzech() ||*}
{*		$localization->isSlovak() ||*}
{*		$localization->isPolish() ||*}
{*		$localization->isRomanian() ||*}
{*		$localization->isHungarian() ||*}
{*		$localization->isCroatian() ||*}
{*		$localization->isBulgarian() ||*}
{*		($user->isLoggedIn() && $user->getIdentity()->isAdmin())*}
{*		)*}
{*	}*}
{*		{include '../templates/pixels/freshChat2.latte'}*}
{*	{/if}*}

	<div data-gtm-data='{"visitorLoginState": "{$user->isLoggedIn() ? ' Logged in' : 'Guest' }"}' data-timing="immediately" class="hidden js-gtm-data"></div>

	{control dealDetailControl}

	<script n:if="!$presenter->isLinkCurrent(':NewFront:Shops:Shop:default')" type="application/ld+json">
		{
		"@context": "http://schema.org",
		"@type": "Organization",
		"name": "{_'front.base.name' |noescape}",
		"url": {$presenter->link('//:NewFront:Homepage:default')},
		"logo": "{_'front.links.homepage' |noescape}images/logo-1.png",
		"email": "{_'front.base.email' |noescape}",
		"sameAs": [
			"{_'front.links.facebookPage' |noescape}"
		],
		"potentialAction": {
			"@type": "SearchAction",
			"target": "{_'front.links.shops.search' |noescape}{('{'.'search_term_string'.'}')|noescape}",
			"query-input": "required name=search_term_string"
		}
		}
	</script>
</body>

</html>

{define navbar}
{var $menuUserLogged = $user->isLoggedIn()}
{var $menuHomepage = $presenter->isLinkCurrent(':NewFront:Homepage:default')}
{var $menuShowDropdownAndSearch = true}
{var $menuShowShops = true}

<header id="header" class="bg-white w-full relative z-50">
	<div class="border-b w-full border-b-light-4">
		<div class="relative flex justify-between container p-0 h-[64px] items-center">
			<div class="flex items-center pl-5 w-full">
				<div id="mobileMenu" class="absolute left-0 flex justify-center items-center w-[61px] h-[63px] block md:hidden">
					<svg id="hamburgerIcon" xmlns="http://www.w3.org/2000/svg" width="21" height="18"
						viewBox="0 0 21 18" fill="none" class="block md:hidden">
						<rect width="21" height="2" rx="1" transform="matrix(-1 0 0 1 21 0)" fill="#080B10" />
						<rect x="21" y="10" width="21" height="2" rx="1" transform="rotate(180 21 10)" fill="#080B10" />
						<rect x="10.5" y="18" width="10.5" height="2" rx="1" transform="rotate(180 10.5 18)"
							fill="#080B10" />
					</svg>

					<svg id="closeIcon" xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17"
						fill="none" class="hidden">
						<path
							d="M0.294033 0.294038C0.686077 -0.0980127 1.32171 -0.0980127 1.71375 0.294038L16.7057 15.2862C17.0977 15.6783 17.0977 16.3139 16.7057 16.706C16.3137 17.098 15.678 17.098 15.286 16.706L0.294034 1.71378C-0.0980106 1.32173 -0.0980117 0.686089 0.294033 0.294038Z"
							fill="#080B10" />
						<path
							d="M16.706 0.294038C16.3139 -0.0980127 15.6783 -0.0980127 15.2863 0.294038L0.294309 15.2862C-0.0977358 15.6783 -0.0977369 16.3139 0.294308 16.706C0.686352 17.098 1.32198 17.098 1.71403 16.706L16.706 1.71378C17.098 1.32173 17.098 0.686089 16.706 0.294038Z"
							fill="#080B10" />
					</svg>
				</div>
				<div class="w-px h-[63px] bg-zinc-200 ml-10 mr-5 md:hidden"></div>
				<div class="overlay fixed inset-0 bg-black bg-opacity-50 hidden" style="top: 65px; background: rgba(24, 43, 74, 0.70); backdrop-filter: blur(3px);"></div>

				<div id="sidebar" class="hidden sidebar fixed h-full top-[65px] left-0 w-[313px] shadow-lg z-50 bg-white">
					<div class="flex justify-between h-full flex-col">
						<div class="pl-[21px]">
							<a n:href=":NewFront:Shops:Shops:default" data-google-interstitial="false" class="flex items-center gap-[14px] py-4 text-sm tex-dark-1 font-medium leading-[24.5px]">
								<svg xmlns="http://www.w3.org/2000/svg" width="18" height="19" viewBox="0 0 18 19"
									 fill="none">
									<path
										d="M7.85714 7.22979C7.85714 7.41424 7.7849 7.59113 7.6563 7.72156C7.5277 7.85198 7.35329 7.92525 7.17143 7.92525H1.68571C1.50386 7.92525 1.32944 7.85198 1.20084 7.72156C1.07224 7.59113 1 7.41424 1 7.22979V1.66616C1 1.48172 1.07224 1.30482 1.20084 1.17439C1.32944 1.04397 1.50386 0.970703 1.68571 0.970703H7.17143C7.35329 0.970703 7.5277 1.04397 7.6563 1.17439C7.7849 1.30482 7.85714 1.48172 7.85714 1.66616V7.22979Z"
										stroke="#080B10" stroke-linecap="round" stroke-linejoin="round" />
									<path
										d="M17 7.22979C17 7.41424 16.9278 7.59113 16.7992 7.72156C16.6706 7.85198 16.4961 7.92525 16.3143 7.92525H10.8286C10.6467 7.92525 10.4723 7.85198 10.3437 7.72156C10.2151 7.59113 10.1429 7.41424 10.1429 7.22979V1.66616C10.1429 1.48172 10.2151 1.30482 10.3437 1.17439C10.4723 1.04397 10.6467 0.970703 10.8286 0.970703H16.3143C16.4961 0.970703 16.6706 1.04397 16.7992 1.17439C16.9278 1.30482 17 1.48172 17 1.66616V7.22979Z"
										stroke="#080B10" stroke-linecap="round" stroke-linejoin="round" />
									<path
										d="M17 17.2752C17 17.4597 16.9278 17.6366 16.7992 17.767C16.6706 17.8974 16.4961 17.9707 16.3143 17.9707H10.8286C10.6467 17.9707 10.4723 17.8974 10.3437 17.767C10.2151 17.6366 10.1429 17.4597 10.1429 17.2752V11.7116C10.1429 11.5272 10.2151 11.3503 10.3437 11.2198C10.4723 11.0894 10.6467 11.0162 10.8286 11.0162H16.3143C16.4961 11.0162 16.6706 11.0894 16.7992 11.2198C16.9278 11.3503 17 11.5272 17 11.7116V17.2752Z"
										stroke="#080B10" stroke-linecap="round" stroke-linejoin="round" />
									<path
										d="M7.85714 17.2752C7.85714 17.4597 7.7849 17.6366 7.6563 17.767C7.5277 17.8974 7.35329 17.9707 7.17143 17.9707H1.68571C1.50386 17.9707 1.32944 17.8974 1.20084 17.767C1.07224 17.6366 1 17.4597 1 17.2752V11.7116C1 11.5272 1.07224 11.3503 1.20084 11.2198C1.32944 11.0894 1.50386 11.0162 1.68571 11.0162H7.17143C7.35329 11.0162 7.5277 11.0894 7.6563 11.2198C7.7849 11.3503 7.85714 11.5272 7.85714 11.7116V17.2752Z"
										stroke="#080B10" stroke-linecap="round" stroke-linejoin="round" />
								</svg>
								{_newFront.navbar.shops}
							</a>
							<a n:href=":NewFront:Deals:Deals:default" data-google-interstitial="false" class="flex items-center gap-[14px] py-4 text-sm tex-dark-1 font-medium leading-[24.5px]">
								<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18"
									 fill="none">
									<path
										d="M12.4071 4.34222C12.4071 4.61379 12.5152 4.87423 12.7075 5.06626C12.8998 5.25829 13.1606 5.36617 13.4325 5.36617C13.7045 5.36617 13.9653 5.25829 14.1576 5.06626C14.3499 4.87423 14.4579 4.61379 14.4579 4.34222C14.4579 4.07066 14.3499 3.81021 14.1576 3.61819C13.9653 3.42616 13.7045 3.31828 13.4325 3.31828C13.1606 3.31828 12.8998 3.42616 12.7075 3.61819C12.5152 3.81021 12.4071 4.07066 12.4071 4.34222Z"
										stroke="#080B10" stroke-linecap="round" stroke-linejoin="round" />
									<path
										d="M15.8963 1.00002H9.22412C9.07344 1.00094 8.92441 1.03228 8.78619 1.09215C8.64791 1.15203 8.52315 1.23921 8.41947 1.34845L1.29871 8.93557C1.1974 9.0423 1.11882 9.16844 1.06771 9.30639C1.0166 9.44434 0.994026 9.59118 1.00135 9.73808C1.00867 9.88492 1.04574 10.0288 1.11031 10.161C1.17488 10.2931 1.26562 10.4109 1.37704 10.507L8.4978 16.7289C8.71342 16.9165 8.99355 17.0131 9.27909 16.9986C9.56471 16.984 9.83344 16.8593 10.0288 16.6507L16.7009 9.53998C16.893 9.33882 17.0002 9.07138 17 8.79335V2.09507C17 1.95067 16.9714 1.80769 16.9158 1.67437C16.8603 1.54104 16.7789 1.42001 16.6763 1.31823C16.5738 1.21645 16.452 1.13594 16.3181 1.08133C16.1842 1.02672 16.0409 0.999086 15.8963 1.00002Z"
										stroke="#080B10" stroke-linecap="round" stroke-linejoin="round" />
								</svg>
								{_newFront.navbar.coupons}
							</a>
							<a n:href=":NewFront:Static:howItWorks" data-google-interstitial="false" class="flex items-center gap-[14px] py-4 text-sm tex-dark-1 font-medium leading-[24.5px]">
								<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path d="M1 10.6V17V10.6Z" fill="#ECEDF0"/>
									<path d="M1 15.9333H12.7333C12.7333 15.3676 12.5085 14.8249 12.1085 14.4249C11.7084 14.0248 11.1657 13.8 10.6 13.8H7.93332C7.93332 13.2342 7.70854 12.6916 7.30848 12.2915C6.90841 11.8914 6.36579 11.6667 5.79999 11.6667H1" fill="#ECEDF0"/>
									<path d="M5.26666 13.8H7.93332H5.26666Z" fill="#ECEDF0"/>
									<path d="M5.18133 1H16.0186C16.2815 1.00375 16.5321 1.11127 16.716 1.2991C16.8998 1.48693 17.0019 1.73985 17 2.00267V8.17511C17.0019 8.43794 16.8998 8.69081 16.716 8.87868C16.5321 9.06649 16.2815 9.17401 16.0186 9.17778H5.18133C4.91853 9.17401 4.66788 9.06649 4.48403 8.87868C4.3002 8.69081 4.1981 8.43794 4.2 8.17511V2.00267C4.1981 1.73985 4.3002 1.48693 4.48403 1.2991C4.66788 1.11127 4.91853 1.00375 5.18133 1Z" fill="#ECEDF0"/>
									<path d="M8.99999 5.08889C8.99999 5.51324 9.16859 5.9202 9.46861 6.22026C9.7687 6.52032 10.1757 6.68889 10.6 6.68889C11.0243 6.68889 11.4313 6.52032 11.7314 6.22026C12.0314 5.9202 12.2 5.51324 12.2 5.08889C12.2 4.66454 12.0314 4.25758 11.7314 3.95752C11.4313 3.65746 11.0243 3.48889 10.6 3.48889C10.1757 3.48889 9.7687 3.65746 9.46861 3.95752C9.16859 4.25758 8.99999 4.66454 8.99999 5.08889Z" fill="#ECEDF0"/>
									<path d="M6.15555 3.31111H7.22221H6.15555Z" fill="#ECEDF0"/>
									<path d="M13.9778 6.86667H15.0444H13.9778Z" fill="#ECEDF0"/>
									<path d="M1 10.6V17M1 15.9333H12.7333C12.7333 15.3676 12.5085 14.8249 12.1085 14.4249C11.7084 14.0248 11.1657 13.8 10.6 13.8H7.93332M7.93332 13.8C7.93332 13.2342 7.70854 12.6916 7.30848 12.2915C6.90841 11.8914 6.36579 11.6667 5.79999 11.6667H1M7.93332 13.8H5.26666M6.15555 3.31111H7.22221M13.9778 6.86667H15.0444M5.18133 1H16.0186C16.2815 1.00375 16.5321 1.11127 16.716 1.2991C16.8998 1.48693 17.0019 1.73985 17 2.00267V8.17511C17.0019 8.43794 16.8998 8.69081 16.716 8.87868C16.5321 9.06649 16.2815 9.17401 16.0186 9.17778H5.18133C4.91853 9.17401 4.66788 9.06649 4.48403 8.87868C4.3002 8.69081 4.1981 8.43794 4.2 8.17511V2.00267C4.1981 1.73985 4.3002 1.48693 4.48403 1.2991C4.66788 1.11127 4.91853 1.00375 5.18133 1ZM8.99999 5.08889C8.99999 5.51324 9.16859 5.9202 9.46861 6.22026C9.7687 6.52032 10.1757 6.68889 10.6 6.68889C11.0243 6.68889 11.4313 6.52032 11.7314 6.22026C12.0314 5.9202 12.2 5.51324 12.2 5.08889C12.2 4.66454 12.0314 4.25758 11.7314 3.95752C11.4313 3.65746 11.0243 3.48889 10.6 3.48889C10.1757 3.48889 9.7687 3.65746 9.46861 3.95752C9.16859 4.25758 8.99999 4.66454 8.99999 5.08889Z" stroke="#080B10" stroke-linecap="round" stroke-linejoin="round"/>
								</svg>

								{_newFront.navbar.howItWorks}
							</a>
							<a n:if="$user->isLoggedIn()" n:href=":NewFront:Account:User:tellFriend" data-google-interstitial="false" class="flex items-center gap-[14px] py-4 text-sm tex-dark-1 font-medium leading-[24.5px]">
								<svg xmlns="http://www.w3.org/2000/svg" width="19" height="17" viewBox="0 0 19 17"
									 fill="none">
									<path
										d="M1 7.17647H5.47368V16H1M7.84285 3.2826C7.84285 2.67726 8.09245 2.09669 8.53675 1.66861C8.98106 1.24052 9.58366 1 10.212 1C10.8403 1 11.443 1.24052 11.8873 1.66861C12.3316 2.09669 12.5812 2.67726 12.5812 3.2826V5.00433C12.5812 5.22177 12.6575 5.43268 12.7975 5.60316C12.9376 5.77364 13.1332 5.89351 13.3528 5.94346L15.6949 6.15216C16.3787 6.31977 16.9814 6.71002 17.4009 7.25681C17.8203 7.80346 18.0306 8.47311 17.9964 9.15215L17.7933 12.4782C17.6923 13.4468 17.2203 14.3443 16.4692 14.9955C15.7182 15.6467 14.7422 16.0048 13.7319 16H10.1037C9.72767 16.0009 9.35356 15.9482 8.99358 15.8434L6.52965 15.1782C6.18803 15.0744 5.8319 15.0216 5.47368 15.0217V8.69563C6.28563 8.03889 6.92388 7.20555 7.3358 6.26433C7.74773 5.32325 7.92151 4.30116 7.84285 3.2826Z"
										stroke="#080B10" stroke-miterlimit="10" stroke-linecap="round"
										stroke-linejoin="round" />
								</svg>
								<span>
									{_newFront.navbar.tellFriend, [upTo => ($recommendationBonusAmount |amount)] |noescape}
								</span>
							</a>
							<a n:if="$user->isLoggedIn()" n:href=":NewFront:Account:User:guarantee" data-google-interstitial="false" class="flex items-center gap-[14px] py-4 text-sm tex-dark-1 font-medium leading-[24.5px]">
								<svg xmlns="http://www.w3.org/2000/svg" width="18" height="17" viewBox="0 0 18 17"
									 fill="none">
									<path
										d="M8.22546 1.44149C8.29773 1.29993 8.40838 1.18096 8.54526 1.09784C8.68196 1.01472 8.83936 0.970703 8.99997 0.970703C9.16059 0.970703 9.31799 1.01472 9.45487 1.09784C9.59157 1.18096 9.70221 1.29993 9.77449 1.44149L11.5751 5.02419C11.6374 5.147 11.7288 5.25312 11.8416 5.33348C11.9544 5.41385 12.0854 5.46606 12.223 5.48568L16.2633 6.06166C16.421 6.08354 16.5697 6.14836 16.6923 6.24883C16.8149 6.34932 16.9066 6.48151 16.9575 6.6306C17.0073 6.7791 17.0135 6.93851 16.9752 7.09032C16.937 7.24214 16.856 7.38016 16.7416 7.4884L13.8184 10.2961C13.7197 10.3901 13.6456 10.5064 13.6028 10.635C13.5598 10.7636 13.5493 10.9006 13.5721 11.0341L14.2645 14.992C14.2908 15.1468 14.272 15.3058 14.2108 15.4507C14.1496 15.5956 14.0482 15.7205 13.9183 15.8111C13.7875 15.9032 13.6333 15.9578 13.4731 15.9687C13.3126 15.9796 13.1524 15.9463 13.01 15.8727L9.40329 14.0162C9.27908 13.9511 9.1406 13.9171 8.99997 13.9171C8.85935 13.9171 8.72086 13.9511 8.59665 14.0162L4.99892 15.8727C4.85655 15.9463 4.69627 15.9796 4.53591 15.9687C4.37554 15.9578 4.22139 15.9032 4.09056 15.8111C3.96068 15.7205 3.85931 15.5956 3.79806 15.4507C3.73683 15.3058 3.71822 15.1468 3.74435 14.992L4.43677 11.0341C4.45958 10.9006 4.44907 10.7636 4.40615 10.635C4.36323 10.5064 4.28922 10.3901 4.1905 10.2961L1.2584 7.4884C1.14398 7.38016 1.06297 7.24214 1.02474 7.09032C0.986537 6.93851 0.992676 6.7791 1.04247 6.6306C1.09327 6.48151 1.18511 6.34932 1.30773 6.24883C1.43033 6.14836 1.57886 6.08354 1.73667 6.06166L5.77699 5.48568C5.91476 5.46606 6.04557 5.41385 6.15836 5.33348C6.27114 5.25312 6.36251 5.147 6.4248 5.02419L8.22546 1.44149Z"
										stroke="#080B10" stroke-linecap="round" stroke-linejoin="round" />
								</svg>
								{_newFront.navbar.refunds}
							</a>
						</div>

						{if $currentLuckyShopStateForUser !== null}
							{include 'snippets/luckyShopMobilePromo.latte'}
						{/if}
					</div>
				</div>

				{if $menuHomepage}
					<a n:href=":NewFront:Homepage:default" data-google-interstitial="false">
						{if $localization->isHungarian()}
							<img src="{$basePath}/images/tiplino_logo_new_color.svg" title="Tipli" alt="Tiplino" loading="lazy" class="w-[60px] h-[29px] md:w-[78px] md:h-[39px] mt-[3px]">
						{else}
							<svg class="w-[60px] h-[29px] md:w-[78px] md:h-[39px] mt-[3px]" width="78" height="39" viewBox="0 0 78 39" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path fill-rule="evenodd" clip-rule="evenodd"
											d="M21.8419 7.28024H27.4368V27.3415H21.8419V7.28024ZM62.3468 27.1098V7.50599H67.482V27.1103L62.3468 27.1098ZM53.2192 27.1098V0.339844H58.3599V27.1103L53.2192 27.1098ZM44.8271 17.2604C44.8271 16.7853 44.7389 16.2929 44.5681 15.7818C44.3914 15.2713 44.1325 14.808 43.7673 14.3987C43.4003 13.9803 42.9524 13.6383 42.4536 13.3957C41.9294 13.1404 41.3407 13.0155 40.6812 13.0155H36.3999V21.4635H40.6812C41.3407 21.4635 41.9294 21.3268 42.4536 21.0538C43.4112 20.5591 44.1557 19.7269 44.5441 18.7153C44.7326 18.2225 44.8266 17.7415 44.8266 17.2609L44.8271 17.2604ZM30.9054 7.42876H40.6576C42.3655 7.42876 43.8377 7.73174 45.086 8.33724C46.3342 8.94275 47.359 9.72042 48.1658 10.6766C49.6897 12.4899 50.5378 14.7884 50.5573 17.1664C50.536 19.5748 49.6825 21.9042 48.148 23.7493C47.3068 24.76 46.2584 25.5826 45.0801 26.1595C43.8477 26.7477 42.4981 27.0548 41.134 27.0548C40.9878 27.0548 40.8374 27.0516 40.6921 27.0448L36.3999 27.0444V34.0148H30.9054V7.42876ZM9.75216 21.3091C9.28111 21.3091 8.79233 21.2201 8.28585 21.0479C7.24699 20.6845 6.39393 19.9173 5.91879 18.917C5.66577 18.3947 5.54178 17.7951 5.54178 17.136V13.1222H14.216V7.476H5.54131V0.339844H0V17.1123C0 18.828 0.300248 20.3179 0.901213 21.5707C1.50172 22.8294 2.27349 23.8624 3.22149 24.676C4.1695 25.4895 5.21199 26.0891 6.3426 26.4866C7.47912 26.8845 8.58658 27.0862 9.65814 27.0862C10.7297 27.0862 11.843 26.8845 12.9973 26.4866C19.1509 24.3611 19.463 18.6617 19.463 15.8118H13.9689C13.9571 17.0351 13.8631 18.2461 13.5156 18.917C13.2507 19.4335 12.921 19.8728 12.5262 20.2226C11.7576 20.9026 10.7742 21.2882 9.75216 21.3091ZM21.8419 0.339844H27.4368V5.98697H21.8419V0.339844Z"
											fill="#646C7C" />
								<path fill-rule="evenodd" clip-rule="evenodd"
											d="M62.3465 0.339844H67.4817V5.52319H62.3465V0.339844ZM74.6606 26.552L77.9998 29.7285C73.133 34.8664 68.2344 38.395 61.1623 38.5867C53.9952 38.2737 49.1076 34.5285 44.4961 29.7403L47.8116 26.5342C51.5042 30.3808 55.5742 33.6854 61.1973 33.9375C66.8531 33.7695 70.745 30.6779 74.6606 26.552Z"
											fill="#EF7F1A" />
							</svg>
						{/if}
					</a>

					<div class="hidden md:block h-[65px] w-[1px] bg-light-4 mx-[25px] -my-2.5"></div>

					<a n:href=":NewFront:Shops:Shops:default" data-google-interstitial="false" class="hidden md:flex items-center cursor-pointer gap-2  xl:hover:text-primary-orange">
						<svg width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path
								d="M7.85714 7.25909C7.85714 7.44353 7.7849 7.62043 7.6563 7.75085C7.5277 7.88127 7.35329 7.95455 7.17143 7.95455H1.68571C1.50386 7.95455 1.32944 7.88127 1.20084 7.75085C1.07224 7.62043 1 7.44353 1 7.25909V1.69545C1 1.51101 1.07224 1.33412 1.20084 1.20369C1.32944 1.07327 1.50386 1 1.68571 1H7.17143C7.35329 1 7.5277 1.07327 7.6563 1.20369C7.7849 1.33412 7.85714 1.51101 7.85714 1.69545V7.25909Z"
								stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" />
							<path
								d="M17 7.25909C17 7.44353 16.9278 7.62043 16.7992 7.75085C16.6706 7.88127 16.4961 7.95455 16.3143 7.95455H10.8286C10.6467 7.95455 10.4723 7.88127 10.3437 7.75085C10.2151 7.62043 10.1429 7.44353 10.1429 7.25909V1.69545C10.1429 1.51101 10.2151 1.33412 10.3437 1.20369C10.4723 1.07327 10.6467 1 10.8286 1H16.3143C16.4961 1 16.6706 1.07327 16.7992 1.20369C16.9278 1.33412 17 1.51101 17 1.69545V7.25909Z"
								stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" />
							<path
								d="M17 17.3045C17 17.489 16.9278 17.6659 16.7992 17.7963C16.6706 17.9267 16.4961 18 16.3143 18H10.8286C10.6467 18 10.4723 17.9267 10.3437 17.7963C10.2151 17.6659 10.1429 17.489 10.1429 17.3045V11.7409C10.1429 11.5565 10.2151 11.3796 10.3437 11.2491C10.4723 11.1187 10.6467 11.0455 10.8286 11.0455H16.3143C16.4961 11.0455 16.6706 11.1187 16.7992 11.2491C16.9278 11.3796 17 11.5565 17 11.7409V17.3045Z"
								stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" />
							<path
								d="M7.85714 17.3045C7.85714 17.489 7.7849 17.6659 7.6563 17.7963C7.5277 17.9267 7.35329 18 7.17143 18H1.68571C1.50386 18 1.32944 17.9267 1.20084 17.7963C1.07224 17.6659 1 17.489 1 17.3045V11.7409C1 11.5565 1.07224 11.3796 1.20084 11.2491C1.32944 11.1187 1.50386 11.0455 1.68571 11.0455H7.17143C7.35329 11.0455 7.5277 11.1187 7.6563 11.2491C7.7849 11.3796 7.85714 11.5565 7.85714 11.7409V17.3045Z"
								stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" />
						</svg>

						<div class="font-medium text-sm leading-[24.5px]">
							{_newFront.navbar.shops}
						</div>
					</a>
				{else}
					<div class="flex justify-between w-full md:w-auto items-center">
					<div>
					    {var $homepageLink = $user->isLoggedIn() ? ($user->getIdentity()->isActiveUser() ? ':NewFront:HomepageLogged:defaultActiveUser' : ':NewFront:HomepageLogged:default') : ':NewFront:Homepage:default'}
						<a href="{plink $homepageLink}" data-google-interstitial="false">
							{if $localization->isHungarian()}
								<img src="{$basePath}/images/tiplino_logo_new_color.svg" title="Tipli" alt="Tiplino" loading="lazy" class="w-[60px] h-[29px] md:w-[78px] md:h-[39px] mt-[3px]">
							{else}
								<svg class="w-[60px] h-[29px] md:w-[78px] md:h-[39px] mt-[3px]" width="78" height="39" viewBox="0 0 78 39" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path fill-rule="evenodd" clip-rule="evenodd"
												d="M21.8419 7.28024H27.4368V27.3415H21.8419V7.28024ZM62.3468 27.1098V7.50599H67.482V27.1103L62.3468 27.1098ZM53.2192 27.1098V0.339844H58.3599V27.1103L53.2192 27.1098ZM44.8271 17.2604C44.8271 16.7853 44.7389 16.2929 44.5681 15.7818C44.3914 15.2713 44.1325 14.808 43.7673 14.3987C43.4003 13.9803 42.9524 13.6383 42.4536 13.3957C41.9294 13.1404 41.3407 13.0155 40.6812 13.0155H36.3999V21.4635H40.6812C41.3407 21.4635 41.9294 21.3268 42.4536 21.0538C43.4112 20.5591 44.1557 19.7269 44.5441 18.7153C44.7326 18.2225 44.8266 17.7415 44.8266 17.2609L44.8271 17.2604ZM30.9054 7.42876H40.6576C42.3655 7.42876 43.8377 7.73174 45.086 8.33724C46.3342 8.94275 47.359 9.72042 48.1658 10.6766C49.6897 12.4899 50.5378 14.7884 50.5573 17.1664C50.536 19.5748 49.6825 21.9042 48.148 23.7493C47.3068 24.76 46.2584 25.5826 45.0801 26.1595C43.8477 26.7477 42.4981 27.0548 41.134 27.0548C40.9878 27.0548 40.8374 27.0516 40.6921 27.0448L36.3999 27.0444V34.0148H30.9054V7.42876ZM9.75216 21.3091C9.28111 21.3091 8.79233 21.2201 8.28585 21.0479C7.24699 20.6845 6.39393 19.9173 5.91879 18.917C5.66577 18.3947 5.54178 17.7951 5.54178 17.136V13.1222H14.216V7.476H5.54131V0.339844H0V17.1123C0 18.828 0.300248 20.3179 0.901213 21.5707C1.50172 22.8294 2.27349 23.8624 3.22149 24.676C4.1695 25.4895 5.21199 26.0891 6.3426 26.4866C7.47912 26.8845 8.58658 27.0862 9.65814 27.0862C10.7297 27.0862 11.843 26.8845 12.9973 26.4866C19.1509 24.3611 19.463 18.6617 19.463 15.8118H13.9689C13.9571 17.0351 13.8631 18.2461 13.5156 18.917C13.2507 19.4335 12.921 19.8728 12.5262 20.2226C11.7576 20.9026 10.7742 21.2882 9.75216 21.3091ZM21.8419 0.339844H27.4368V5.98697H21.8419V0.339844Z"
												fill="#646C7C" />
									<path fill-rule="evenodd" clip-rule="evenodd"
												d="M62.3465 0.339844H67.4817V5.52319H62.3465V0.339844ZM74.6606 26.552L77.9998 29.7285C73.133 34.8664 68.2344 38.395 61.1623 38.5867C53.9952 38.2737 49.1076 34.5285 44.4961 29.7403L47.8116 26.5342C51.5042 30.3808 55.5742 33.6854 61.1973 33.9375C66.8531 33.7695 70.745 30.6779 74.6606 26.552Z"
												fill="#EF7F1A" />
								</svg>
							{/if}
						</a>
					</div>

					<div class="md:hidden">
						<div id="svg-container" class="md:hidden mr-5 cursor-pointer">
							<svg id="svg-icon" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
								<path d="M12.2467 12.2461C11.0112 13.4816 9.33552 14.1756 7.5882 14.1756C5.84088 14.1756 4.16522 13.4816 2.92965 12.2461C1.69408 11.0106 1 9.33501 1 7.5878C1 5.84059 1.69408 4.16503 2.92965 2.92954C4.16522 1.69404 5.84088 1 7.5882 1C9.33552 1 11.0112 1.69404 12.2467 2.92954C13.4823 4.16503 14.1764 5.84059 14.1764 7.5878C14.1764 9.33501 13.4823 11.0106 12.2467 12.2461ZM12.2467 12.2461L17 17" stroke="#080B10" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
							</svg>
						</div>
					</div>

					<div id="dropdown-autocomplete-mobile" class="hidden absolute top-[64px] left-0 w-full h-svh bg-white z-10">
						<div class="flex justify-between items-center bg-primary-blue-dark py-2.5 pr-2.5 pl-[26px] gap-[26px]">
							<svg id="close-icon" class="flex-shrink-0 cursor-pointer" xmlns="http://www.w3.org/2000/svg" width="11" height="20" viewBox="0 0 11 20" fill="none">
								<path d="M9.99986 1L1 10.0003L10 19" stroke="white" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
							</svg>

							<div class="relative w-full">
								<div id="autocomplete-mobile" class="flex items-center h-[47px] border-none rounded-xl w-full text-sm leading-[24.5px] text-dark-2 bg-white"></div>
							</div>
						</div>

                        {cache 'navbarShops-' . $localization->getId(), 'expiration' => '6 hours'}
                            {var $navbarShops = $getNavbarShops()}
                            <div class="px-5">
                                <div class="text-dark-1 leading-7 font-medium mt-5 mb-2.5">{_newFront.navbar.shopsTitle}</div>
                                <div>
                                    {foreach $navbarShops as $topShop}
                                        <a n:href=":NewFront:Shops:Shop:default $topShop" data-google-interstitial="false"
                                            class="flex gap-4 items-center mb-5 last:mb-0 hover:underline">
                                            <div
                                                class="border border-light-5 rounded-xl flex justify-center items-center w-[97px] h-[55px]">
                                                <img class="max-w-[58px] max-h-[38px]" alt="{$topShop->getName()}" src="{$topShop->getCurrentLogo() |image:116,0,'fit',false,$topShop->getName()}" loading="lazy" />
                                            </div>

                                            <div class="text-sm text-dark-1 leading-[18.75px]">
                                                <div class="text-sm leading-[24.5px] mb-[5px]">{$topShop->getName()}</div>
                                                <div class="similar-shop__value">
                                                   {$topShop |reward:true,'extended'|noescape}
                                                </div>
                                            </div>
                                        </a>
                                        {breakIf $iterator->counter >= 6}
                                    {/foreach}
                                </div>

                                <a n:href=":NewFront:Shops:Shops:default" data-google-interstitial="false" class="block w-full text-center mt-5 mb-[35px] leading-7 font-bold text-white bg-orange-gradient py-[14px] rounded-xl">
                                    {_newFront.navbar.cta, ['count' => ($countOfCashbackShops |amount)] |noescape}
                                </a>
                            </div>
						{/cache}
					</div>
				</div>

					<div id="autocomplete" class="hidden md:block lg:max-w-[275px] xl:max-w-full" data-placeholder="{_newFront.navbar.search}"></div>

					{include 'snippets/luckyShopDesktopPromo.latte'}

					<a n:href=":NewFront:Static:howItWorks" n:if="$user->isLoggedIn() && !$hasLuckyShops" class="hidden lg:flex gap-2 items-center text-primary-orange leading-[24.5px] text-sm py-2 px-4 bg-pastel-orange-light rounded-xl ml-[11px] xl:hover:underline">
						<div>
							{_newFront.navbar.howItWorksWithTipli |noescape}
						</div>
						<svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg"
							xmlns:xlink="http://www.w3.org/1999/xlink">
							<rect width="21" height="21" fill="url(#pattern0_758_3627)" />
							<defs>
								<pattern id="pattern0_758_3627" patternContentUnits="objectBoundingBox" width="1"
									height="1">
									<use xlink:href="#image0_758_3627" transform="scale(0.0138889)" />
								</pattern>
								<image id="image0_758_3627" width="72" height="72"
									xlink:href="data:image/png;base64,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" />
							</defs>
						</svg>
					</a>
				{/if}
			</div>

			{if $menuUserLogged == false}
                <div class="flex items-center shrink-0 gap-6">
                    <a n:href=":NewFront:Sign:in" data-google-interstitial="false" class="hidden font-medium hover:underline text-sm leading-[24.5px] md:block">
                        {_newFront.sign.in.form.submit}
                    </a>
                    <a n:href=":NewFront:Sign:up" data-google-interstitial="false"
                        class="px-7 py-3 rounded-xl bg-orange-gradient text-white font-medium text-sm leading-[24.5px] mr-2 md:mr-0 cursor-pointer xl:hover:bg-orange-gradient-hover">
                         {_newFront.sign.up.create}
                    </a>
                </div>
			{else}
                <div class="flex gap-4">
                    <div class="w-px h-[63px] bg-zinc-200"></div>
                    <div class="flex items-center">
                        {cache 'layout-userBalance-' . $user->getIdentity()->getId(), expiration => '15 minutes', tags => ['user/' . $user->getIdentity()->getId()]}
                            <a n:href=":NewFront:Account:Transaction:default"
									data-google-interstitial="false"
                                class="balance-value hidden md:flex items-center justify-center min-w-[69px] h-[27px] rounded-md border border-slate-700 text-slate-700 text-sm font-medium px-2 xl:hover:bg-light-6 whitespace-nowrap">
                                {$userBalance()|amount, 2, ',',false}&nbsp;{$user->getIdentity()->getLocalization()->getCurrency() |currency}
                            </a>
                        {/cache}
                        <div class="flex items-center cursor-pointer js-show-user-dropdown" data-google-interstitial="false">
                            <div
                                class="relative flex justify-center items-center w-[41px] h-[41px] bg-zinc-200 rounded-xl mr-2">
                                <div class="text-zinc-950 text-sm font-medium">
                                    {if $user->getIdentity()->getInitials()}
                                    {$user->getIdentity()->getInitials()}
                                    {else}
                                    <svg xmlns="http://www.w3.org/2000/svg" width="15" height="21" viewBox="0 0 15 21"
                                        fill="none">
                                        <path
                                            d="M7.49451 9.77799C7.38454 9.76696 7.25258 9.76696 7.13161 9.77799C4.51431 9.6898 2.43586 7.54018 2.43586 4.89451C2.43586 2.19371 4.61328 0 7.31856 0C10.0128 0 12.2013 2.19371 12.2013 4.89451C12.1903 7.54018 10.1118 9.6898 7.49451 9.77799Z"
                                            fill="#ADB3BF" />
                                        <path
                                            d="M1.99597 13.0078C-0.665323 14.7937 -0.665323 17.7039 1.99597 19.4787C5.02016 21.5071 9.97984 21.5071 13.004 19.4787C15.6653 17.6929 15.6653 14.7827 13.004 13.0078C9.99084 10.9905 5.03116 10.9905 1.99597 13.0078Z"
                                            fill="#ADB3BF" />
                                    </svg>
                                    {/if}
                                </div>
                                <div n:if="$unreadNotificationsCount >= 1"
                                    class="absolute flex items-center justify-center right-[-3px] top-[-3px] text-white text-[9px] font-bold leading-[17.50px] w-3.5 h-3.5 bg-red-500 rounded-[300px]">
                                    {$unreadNotificationsCount}
                                </div>

                                {cache 'newFront-shareLevel-' . $user->getIdentity()->getId(), expire => '15 minutes', tags =>
                                ['user/' . $user->getIdentity()->getId(), 'user/rewards/' . $user->getIdentity()->getId()]}
                                {var $shareLevel = $shareLevel()}

                                {if $shareLevel == 'friendsAndFamily'}
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="11" viewBox="0 0 16 11" fill="none" class="absolute right-[-3px] bottom-[-3px]">
                                    <path
                                        d="M7.58872 1.59488C7.78752 1.30734 8.21248 1.30734 8.41128 1.59489L10.8115 5.06668C10.9744 5.30225 11.3015 5.35316 11.5282 5.17823L13.9308 3.32484C14.2982 3.04139 14.82 3.36788 14.7258 3.82227L13.5271 9.60154C13.479 9.83361 13.2745 10 13.0375 10H2.96249C2.72549 10 2.52104 9.83361 2.47291 9.60154L1.27425 3.82227C1.18 3.36788 1.7018 3.04139 2.06923 3.32484L4.47178 5.17823C4.69854 5.35316 5.0256 5.30225 5.18847 5.06668L7.58872 1.59488Z"
                                        fill="white" stroke="#BBA24D" />
                                </svg>
                                {/if}

                                {if $shareLevel == 'vip'}
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="11" viewBox="0 0 16 11" fill="none"
                                    class="absolute right-[-3px] bottom-[-3px]">
                                    <path
                                        d="M7.58872 1.59488C7.78752 1.30734 8.21248 1.30734 8.41128 1.59489L10.8115 5.06668C10.9744 5.30225 11.3015 5.35316 11.5282 5.17823L13.9308 3.32484C14.2982 3.04139 14.82 3.36788 14.7258 3.82227L13.5271 9.60154C13.479 9.83361 13.2745 10 13.0375 10H2.96249C2.72549 10 2.52104 9.83361 2.47291 9.60154L1.27425 3.82227C1.18 3.36788 1.7018 3.04139 2.06923 3.32484L4.47178 5.17823C4.69854 5.35316 5.0256 5.30225 5.18847 5.06668L7.58872 1.59488Z"
                                        fill="white" stroke="#BBA24D" />
                                </svg>
                                {/if}

                                {if $shareLevel == 'genius'}
                                {* @todo *}
                                {/if}
                                {/cache}
                            </div>

                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="8" viewBox="0 0 14 8" fill="none"
                                class="js-dropdown-down">
                                <path d="M1 1.00009L7.00018 7L13 1" stroke="#080B10" stroke-miterlimit="10"
                                    stroke-linecap="round" stroke-linejoin="round" />
                            </svg>

                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="8" viewBox="0 0 14 8" fill="none"
                                class="hidden js-dropdown-up">
                                <path d="M1 6.99991L7.00018 1L13 7" stroke="#080B10" stroke-miterlimit="10"
                                    stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                        </div>
                    </div>
                    <div class="w-px h-[63px] bg-zinc-200"></div>
                </div>
			{/if}
		</div>
	</div>

	{if $menuHomepage == false && !isset($hideSubMenu)}
		<div class="hidden md:block border-b border-b-light-4 ">
		<div class="flex py-3 container justify-between">
			<div class="flex items-center">
				<a n:href=":NewFront:Shops:Shops:default" data-google-interstitial="false"
					class="flex items-center cursor-pointer gap-2 js-show-shops xl:hover:text-primary-orange">
					<svg width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path
							d="M7.85714 7.25909C7.85714 7.44353 7.7849 7.62043 7.6563 7.75085C7.5277 7.88127 7.35329 7.95455 7.17143 7.95455H1.68571C1.50386 7.95455 1.32944 7.88127 1.20084 7.75085C1.07224 7.62043 1 7.44353 1 7.25909V1.69545C1 1.51101 1.07224 1.33412 1.20084 1.20369C1.32944 1.07327 1.50386 1 1.68571 1H7.17143C7.35329 1 7.5277 1.07327 7.6563 1.20369C7.7849 1.33412 7.85714 1.51101 7.85714 1.69545V7.25909Z"
							stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" />
						<path
							d="M17 7.25909C17 7.44353 16.9278 7.62043 16.7992 7.75085C16.6706 7.88127 16.4961 7.95455 16.3143 7.95455H10.8286C10.6467 7.95455 10.4723 7.88127 10.3437 7.75085C10.2151 7.62043 10.1429 7.44353 10.1429 7.25909V1.69545C10.1429 1.51101 10.2151 1.33412 10.3437 1.20369C10.4723 1.07327 10.6467 1 10.8286 1H16.3143C16.4961 1 16.6706 1.07327 16.7992 1.20369C16.9278 1.33412 17 1.51101 17 1.69545V7.25909Z"
							stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" />
						<path
							d="M17 17.3045C17 17.489 16.9278 17.6659 16.7992 17.7963C16.6706 17.9267 16.4961 18 16.3143 18H10.8286C10.6467 18 10.4723 17.9267 10.3437 17.7963C10.2151 17.6659 10.1429 17.489 10.1429 17.3045V11.7409C10.1429 11.5565 10.2151 11.3796 10.3437 11.2491C10.4723 11.1187 10.6467 11.0455 10.8286 11.0455H16.3143C16.4961 11.0455 16.6706 11.1187 16.7992 11.2491C16.9278 11.3796 17 11.5565 17 11.7409V17.3045Z"
							stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" />
						<path
							d="M7.85714 17.3045C7.85714 17.489 7.7849 17.6659 7.6563 17.7963C7.5277 17.9267 7.35329 18 7.17143 18H1.68571C1.50386 18 1.32944 17.9267 1.20084 17.7963C1.07224 17.6659 1 17.489 1 17.3045V11.7409C1 11.5565 1.07224 11.3796 1.20084 11.2491C1.32944 11.1187 1.50386 11.0455 1.68571 11.0455H7.17143C7.35329 11.0455 7.5277 11.1187 7.6563 11.2491C7.7849 11.3796 7.85714 11.5565 7.85714 11.7409V17.3045Z"
							stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" />
					</svg>

					<div class="hidden font-medium text-sm leading-[24.5px] md:block">
						{_newFront.navbar.shops}
					</div>

					<svg width="12" height="7" viewBox="0 0 12 7" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path
							d="M6.00015 6L5.46983 6.53034C5.76273 6.82323 6.2376 6.82322 6.53048 6.53032L6.00015 6ZM1.53032 0.469734C1.23742 0.176847 0.762545 0.176858 0.469658 0.469758C0.176771 0.762657 0.176782 1.23753 0.469682 1.53042L1.53032 0.469734ZM11.5303 1.53032C11.8232 1.23742 11.8232 0.762551 11.5303 0.469662C11.2374 0.176773 10.7626 0.17678 10.4697 0.469678L11.5303 1.53032ZM6.53046 5.46966L1.53032 0.469734L0.469682 1.53042L5.46983 6.53034L6.53046 5.46966ZM6.53048 6.53032L11.5303 1.53032L10.4697 0.469678L5.46981 5.46968L6.53048 6.53032Z"
							fill="currentColor" />
					</svg>
				</a>

				<a n:href=":NewFront:Deals:Deals:default" data-google-interstitial="false"
					class="flex items-center cursor-pointer gap-2 ml-9 xl:hover:text-primary-orange">
					<svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
						<path
							d="M13.1201 4.55111C13.1201 4.83966 13.2349 5.11637 13.4392 5.3204C13.6436 5.52443 13.9206 5.63905 14.2096 5.63905C14.4986 5.63905 14.7756 5.52443 14.98 5.3204C15.1843 5.11637 15.299 4.83966 15.299 4.55111C15.299 4.26257 15.1843 3.98585 14.98 3.78182C14.7756 3.5778 14.4986 3.46317 14.2096 3.46317C13.9206 3.46317 13.6436 3.5778 13.4392 3.78182C13.2349 3.98585 13.1201 4.26257 13.1201 4.55111Z"
							stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" />
						<path
							d="M16.8273 1.00002H9.73813C9.57803 1.001 9.41968 1.03429 9.27283 1.09791C9.1259 1.16153 8.99335 1.25416 8.88319 1.37023L1.31738 9.43154C1.20974 9.54494 1.12625 9.67897 1.07195 9.82554C1.01764 9.97211 0.993653 10.1281 1.00143 10.2842C1.00921 10.4402 1.04859 10.5931 1.1172 10.7335C1.18581 10.874 1.28222 10.9991 1.4006 11.1012L8.96641 17.712C9.19551 17.9113 9.49315 18.014 9.79654 17.9985C10.1 17.983 10.3855 17.8505 10.5931 17.6289L17.6822 10.0737C17.8864 9.85999 18.0002 9.57584 18 9.28044V2.16352C18 2.01009 17.9697 1.85817 17.9106 1.71652C17.8516 1.57486 17.7651 1.44626 17.6561 1.33812C17.5471 1.22998 17.4177 1.14444 17.2755 1.08641C17.1333 1.02839 16.981 0.999029 16.8273 1.00002Z"
							stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" />
					</svg>

					<div class="hidden font-medium text-sm leading-[24.5px] md:block">
						{_newFront.navbar.coupons}
					</div>
				</a>
			</div>
			<div class="flex items-center gap-[35px]">
				<a n:if="$user->isLoggedIn() && ($user->getIdentity()->isActiveUser() || ($localization->isSlovak() === false && $localization->isCzech() === false))" n:href=":NewFront:Account:User:tellFriend" data-google-interstitial="false"
					class="flex items-center gap-2 xl:hover:underline">
					<svg xmlns="http://www.w3.org/2000/svg" width="21" height="19" viewBox="0 0 21 19" fill="none">
						<path d="M1 8H6V18H1" fill="#F0F8EC" />
						<path
							d="M8.64789 3.58695C8.64789 2.90089 8.92685 2.24292 9.42343 1.75775C9.92001 1.27259 10.5935 1 11.2958 1C11.998 1 12.6715 1.27259 13.1681 1.75775C13.6647 2.24292 13.9437 2.90089 13.9437 3.58695V5.53825C13.9437 5.78467 14.0289 6.02371 14.1855 6.21691C14.3421 6.41012 14.5607 6.54597 14.8061 6.60259L17.4237 6.83911C18.188 7.02907 18.8616 7.47136 19.3304 8.09105C19.7991 8.71058 20.0342 9.46952 19.996 10.2391L19.769 14.0087C19.6561 15.1064 19.1285 16.1236 18.2891 16.8615C17.4498 17.5996 16.359 18.0054 15.2298 17.9999H11.1747C10.7545 18.001 10.3363 17.9413 9.934 17.8226L7.1802 17.0686C6.79839 16.951 6.40036 16.8911 6 16.8913V9.72171C6.90747 8.97741 7.62081 8.03295 8.08119 6.96624C8.54158 5.89968 8.73581 4.74132 8.64789 3.58695Z"
							fill="#F0F8EC" />
						<path
							d="M1 8H6V18H1M8.64789 3.58695C8.64789 2.90089 8.92685 2.24292 9.42343 1.75775C9.92001 1.27259 10.5935 1 11.2958 1C11.998 1 12.6715 1.27259 13.1681 1.75775C13.6647 2.24292 13.9437 2.90089 13.9437 3.58695V5.53825C13.9437 5.78467 14.0289 6.02371 14.1855 6.21691C14.3421 6.41012 14.5607 6.54597 14.8061 6.60259L17.4237 6.83911C18.188 7.02907 18.8616 7.47136 19.3304 8.09105C19.7991 8.71058 20.0342 9.46952 19.996 10.2391L19.769 14.0087C19.6561 15.1064 19.1285 16.1236 18.2891 16.8615C17.4498 17.5996 16.359 18.0054 15.2298 17.9999H11.1747C10.7545 18.001 10.3363 17.9413 9.934 17.8226L7.1802 17.0686C6.79839 16.951 6.40036 16.8911 6 16.8913V9.72171C6.90747 8.97741 7.62081 8.03295 8.08119 6.96624C8.54158 5.89968 8.73581 4.74132 8.64789 3.58695Z"
							stroke="#66B940" stroke-linecap="round" stroke-linejoin="round" />
					</svg>
					<div class="text-dark-1 text-sm leading-[24.5px]">
						{_newFront.navbar.tellFriend, [upTo => ($recommendationBonusAmount |amount)] |noescape}
					</div>
				</a>

				<a n:if="$user->isLoggedIn() && ($localization->isSlovak() || $localization->isCzech()) && $user->getIdentity()->isActiveUser() === false" n:href=":NewFront:Static:howItWorks"
						data-google-interstitial="false"
						class="flex items-center gap-2 xl:hover:underline">
					<svg xmlns="http://www.w3.org/2000/svg" width="17" height="18" viewBox="0 0 17 18" fill="none">
					<path d="M1.55289 1.70763L9.96728 4.16303C10.5375 4.35529 11.0354 4.72442 11.3926 5.21952C11.7497 5.71462 11.9484 6.31129 11.9613 6.92723V15.2277C11.9853 15.4898 11.9467 15.7539 11.849 15.9972C11.7512 16.2405 11.5972 16.4558 11.4003 16.6243C11.2034 16.7928 10.9694 16.9095 10.7186 16.9644C10.4679 17.0192 10.2079 17.0106 9.96112 16.9392L3.00068 14.9963C2.43044 14.8142 1.93046 14.4525 1.5715 13.9625C1.21254 13.4725 1.0128 12.879 1.00054 12.2661V3.22163C0.987903 2.64795 1.19676 2.09243 1.58155 1.67623C1.96634 1.26004 2.49584 1.01695 3.05453 1H13.6921C14.3043 1 14.8913 1.24962 15.3241 1.69396C15.7569 2.13829 16 2.74093 16 3.36931V10.4401C15.9973 10.7596 15.9332 11.0753 15.8114 11.3692C15.6897 11.6632 15.5126 11.9296 15.2903 12.1531C15.068 12.3766 14.805 12.553 14.5162 12.672C14.2275 12.791 13.9187 12.8504 13.6075 12.8466H11.9613" fill="#FEF3E9"/>
					<path d="M8.78796 10.4773C8.62864 10.4773 8.49948 10.3446 8.49948 10.1811C8.49948 10.0175 8.62864 9.88492 8.78796 9.88492" fill="#FEF3E9"/>
					<path d="M8.78796 10.4773C8.94728 10.4773 9.07644 10.3446 9.07644 10.1811C9.07644 10.0175 8.94728 9.88492 8.78796 9.88492" fill="#FEF3E9"/>
					<path d="M16 6.92328H11.9613H16Z" fill="#FEF3E9"/>
					<path d="M1.55289 1.70763L9.96728 4.16303C10.5375 4.35529 11.0354 4.72442 11.3926 5.21952C11.7497 5.71462 11.9484 6.31129 11.9613 6.92723V15.2277C11.9853 15.4898 11.9467 15.7539 11.849 15.9972C11.7512 16.2405 11.5972 16.4558 11.4003 16.6243C11.2034 16.7928 10.9694 16.9095 10.7186 16.9644C10.4679 17.0192 10.2079 17.0106 9.96112 16.9392L3.00068 14.9963C2.43044 14.8142 1.93046 14.4525 1.5715 13.9625C1.21254 13.4725 1.0128 12.879 1.00054 12.2661V3.22163C0.987903 2.64795 1.19676 2.09243 1.58155 1.67623C1.96634 1.26004 2.49584 1.01695 3.05453 1H13.6921C14.3043 1 14.8913 1.24962 15.3241 1.69396C15.7569 2.13829 16 2.74093 16 3.36931V10.4401C15.9973 10.7596 15.9332 11.0753 15.8114 11.3692C15.6897 11.6632 15.5126 11.9296 15.2903 12.1531C15.068 12.3766 14.805 12.553 14.5162 12.672C14.2275 12.791 13.9187 12.8504 13.6075 12.8466H11.9613M16 6.92328H11.9613M8.78796 10.4773C8.62864 10.4773 8.49948 10.3446 8.49948 10.1811C8.49948 10.0175 8.62864 9.88492 8.78796 9.88492C8.94728 9.88492 9.07645 10.0175 9.07645 10.1811C9.07645 10.3446 8.94728 10.4773 8.78796 10.4773Z" stroke="url(#paint0_linear_5908_3480)" stroke-linecap="round" stroke-linejoin="round"/>
					<defs>
						<linearGradient id="paint0_linear_5908_3480" x1="1" y1="17" x2="15.2959" y2="6.1249" gradientUnits="userSpaceOnUse">
						<stop stop-color="#EF7F1A"/>
						<stop offset="1" stop-color="#FFA439"/>
						</linearGradient>
					</defs>
					</svg>
					<div class="text-dark-1 text-sm leading-[24.5px]">
						{_newFront.navbar.firstPurchase}
					</div>
				</a>

				<a n:if="$user->isLoggedIn()" n:href=":NewFront:Account:User:guarantee"
					data-google-interstitial="false"
					class="flex items-center gap-2 xl:hover:underline">
					<svg xmlns="http://www.w3.org/2000/svg" width="17" height="16" viewBox="0 0 17 16" fill="none">
						<path
							d="M7.83795 1.45409C7.90635 1.31755 8.01107 1.2028 8.1406 1.12263C8.26997 1.04245 8.41893 1 8.57093 1C8.72293 1 8.87189 1.04245 9.00143 1.12263C9.1308 1.2028 9.23551 1.31755 9.30391 1.45409L11.008 4.90972C11.0669 5.02817 11.1534 5.13053 11.2602 5.20803C11.3669 5.28556 11.4909 5.33591 11.6211 5.35484L15.4447 5.91039C15.594 5.93149 15.7347 5.99401 15.8507 6.09092C15.9668 6.18784 16.0536 6.31535 16.1017 6.45915C16.1488 6.60238 16.1547 6.75614 16.1184 6.90257C16.0823 7.049 16.0056 7.18213 15.8973 7.28653L13.1309 9.99462C13.0375 10.0853 12.9675 10.1975 12.9269 10.3216C12.8862 10.4456 12.8763 10.5777 12.8979 10.7065L13.5532 14.524C13.578 14.6733 13.5603 14.8267 13.5023 14.9664C13.4444 15.1061 13.3485 15.2266 13.2255 15.314C13.1017 15.4029 12.9558 15.4555 12.8041 15.466C12.6523 15.4765 12.5006 15.4445 12.3659 15.3734L8.95262 13.5828C8.83508 13.52 8.70402 13.4872 8.57093 13.4872C8.43785 13.4872 8.30679 13.52 8.18924 13.5828L4.78446 15.3734C4.64972 15.4445 4.49804 15.4765 4.34628 15.466C4.19451 15.4555 4.04863 15.4029 3.92481 15.314C3.8019 15.2266 3.70597 15.1061 3.648 14.9664C3.59006 14.8267 3.57244 14.6733 3.59717 14.524L4.25246 10.7065C4.27404 10.5777 4.26409 10.4456 4.22348 10.3216C4.18286 10.1975 4.11282 10.0853 4.01939 9.99462L1.24455 7.28653C1.13625 7.18213 1.05959 7.049 1.02342 6.90257C0.987259 6.75614 0.993069 6.60238 1.04019 6.45915C1.08827 6.31535 1.17518 6.18784 1.29123 6.09092C1.40725 5.99401 1.54782 5.93149 1.69717 5.91039L5.5208 5.35484C5.65118 5.33591 5.77497 5.28556 5.88171 5.20803C5.98845 5.13053 6.07492 5.02817 6.13386 4.90972L7.83795 1.45409Z"
							fill="#FEF3E9" stroke="url(#paint0_linear_758_3664)" stroke-linecap="round"
							stroke-linejoin="round" />
						<defs>
							<linearGradient id="paint0_linear_758_3664" x1="1" y1="15.468" x2="14.2364" y2="4.22728"
								gradientUnits="userSpaceOnUse">
								<stop stop-color="#EF7F1A" />
								<stop offset="1" stop-color="#FFA439" />
							</linearGradient>
						</defs>
					</svg>
					<div class="text-dark-1 text-sm leading-[24.5px]">{_newFront.navbar.refunds}</div>
				</a>

				<div class="relative">
					<a n:href=":NewFront:Static:addon" n:if="$isThereAddon === true && $isMobile === false"
						data-google-interstitial="false"
					   class="flex items-center gap-2 xl:hover:underline">

						{if $isChrome}
						<svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
							<path d="M8.50016 13.2293C11.1123 13.2293 13.2297 11.1118 13.2297 8.49968C13.2297 5.88758 11.1123 3.77002 8.50016 3.77002C5.88807 3.77002 3.77051 5.88758 3.77051 8.49968C3.77051 11.1118 5.88801 13.2293 8.5001 13.2293" fill="white"/>
							<path d="M2.38322 6.17572C2.03038 5.56456 1.61565 4.92314 1.13903 4.25146C0.392867 5.54356 2.53458e-05 7.00932 1.22642e-09 8.50139C-2.53433e-05 9.99346 0.392767 11.4592 1.13888 12.7514C1.885 14.0435 2.95815 15.1164 4.25043 15.8622C5.5427 16.6081 7.00857 17.0006 8.50063 17.0002C9.28294 15.903 9.81411 15.1118 10.0941 14.6269C10.6319 13.6953 11.3275 12.3616 12.1808 10.6258V10.6248C11.808 11.2712 11.2716 11.808 10.6255 12.1812C9.97945 12.5545 9.24648 12.7511 8.50033 12.7512C7.75418 12.7513 7.02115 12.5549 6.37496 12.1819C5.72877 11.8088 5.19219 11.2722 4.8192 10.6259C3.66028 8.46455 2.84828 6.9811 2.38322 6.17572Z" fill="#229342"/>
							<path d="M8.50055 16.9995C9.61681 16.9997 10.7222 16.78 11.7535 16.3529C12.7848 15.9258 13.7219 15.2997 14.5112 14.5104C15.3005 13.721 15.9265 12.7839 16.3536 11.7526C16.7806 10.7212 17.0003 9.61585 17 8.49959C16.9997 7.00751 16.6066 5.5418 15.8603 4.24979C14.2501 4.09107 13.0617 4.01172 12.2952 4.01172C11.4261 4.01172 10.161 4.09107 8.49995 4.24979L8.49902 4.25045C9.24519 4.25008 9.9783 4.4462 10.6246 4.81907C11.2709 5.19195 11.8077 5.72844 12.1809 6.37459C12.554 7.02073 12.7505 7.75376 12.7505 8.49993C12.7505 9.24611 12.554 9.97912 12.1808 10.6253L8.50055 16.9995Z" fill="#FBC116"/>
							<path d="M8.50055 11.8654C10.3589 11.8654 11.8653 10.3589 11.8653 8.50055C11.8653 6.64217 10.3589 5.13574 8.50048 5.13574C6.64224 5.13574 5.13574 6.64224 5.13574 8.50055C5.13574 10.3589 6.64224 11.8654 8.50055 11.8654Z" fill="#1A73E8"/>
							<path d="M8.50065 4.25029H15.8609C15.1151 2.958 14.0422 1.88484 12.75 1.13874C11.4579 0.392644 9.99208 -0.00010094 8.5 1.94596e-08C7.00792 0.000100979 5.54217 0.393044 4.25013 1.13932C2.95809 1.88559 1.8853 2.95889 1.13965 4.25129L4.81982 10.6256L4.82082 10.6262C4.44745 9.98013 4.25076 9.24718 4.25055 8.50101C4.25033 7.75485 4.44658 7.02178 4.81957 6.37553C5.19255 5.72928 5.72913 5.19263 6.37532 4.81955C7.02152 4.44647 7.75456 4.25011 8.50072 4.25023L8.50065 4.25029Z" fill="#E33B2E"/>
						</svg>
						{elseif $isFirefox}
						<svg width="19" height="19" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
							<g clip-path="url(#clip0_2027_3117)">
								<path d="M48.4439 16.7383C47.3563 14.2102 45.1502 11.4805 43.4225 10.6177C44.6546 12.9232 45.5097 15.3991 45.9574 17.9573L45.9619 17.9979C43.1318 11.1817 38.3326 8.43338 34.4131 2.44885C34.2106 2.14379 34.014 1.83512 33.8234 1.523C33.7252 1.35995 33.6333 1.19349 33.5477 1.02395C33.385 0.719834 33.2597 0.398411 33.1742 0.06621C33.1744 0.0508634 33.1687 0.0360006 33.1582 0.0244743C33.1477 0.0129479 33.1332 0.00557022 33.1174 0.00375721C33.1019 4.96793e-05 33.0858 4.96793e-05 33.0703 0.00375721C33.0672 0.00375721 33.0619 0.00941759 33.058 0.0107383C33.0541 0.0120591 33.0457 0.0175308 33.04 0.0199836L33.0496 0.00375721C26.7625 3.56017 24.6291 10.1436 24.4328 13.4366C21.922 13.6032 19.5215 14.497 17.5443 16.0013C17.3381 15.8324 17.1225 15.6746 16.8984 15.5285C16.328 13.6 16.3037 11.5589 16.8281 9.61829C14.5192 10.6987 12.4675 12.2305 10.8031 14.1166H10.7914C9.79941 12.9019 9.86914 8.8964 9.92598 8.05961C9.63255 8.17368 9.35242 8.31737 9.09023 8.48829C8.21453 9.09211 7.39585 9.7696 6.64375 10.5128C5.78685 11.3523 5.00417 12.2595 4.30391 13.2251V13.2287V13.2243C2.69489 15.4285 1.55348 17.9185 0.945508 20.5509L0.912109 20.7106C0.819739 21.2123 0.737692 21.7157 0.666016 22.2205C0.666016 22.2387 0.662109 22.2555 0.660156 22.2736C0.440952 23.3737 0.305175 24.4878 0.253906 25.607V25.7319C0.270703 44.3472 21.1414 55.9639 37.8211 46.6422C40.9954 44.8681 43.7275 42.4419 45.8241 39.5351C47.9206 36.6284 49.3305 33.3121 49.9539 29.8209C49.9959 29.509 50.0301 29.2002 50.0676 28.8851C50.5831 24.7687 50.025 20.5931 48.4439 16.7383ZM19.5941 35.6662C19.7111 35.7206 19.8209 35.7792 19.941 35.8309L19.9584 35.8417C19.8361 35.785 19.7146 35.7265 19.5941 35.6662ZM45.9639 18.0036V17.9804L45.9684 18.006L45.9639 18.0036Z" fill="url(#paint0_linear_2027_3117)"/>
								<path d="M48.4439 16.7382C47.3563 14.2101 45.1502 11.4805 43.4225 10.6177C44.6546 12.9231 45.5097 15.399 45.9574 17.9573V17.9803L45.9619 18.006C47.8911 23.3394 47.6121 29.1868 45.183 34.3262C42.3143 40.2726 35.3701 46.3675 24.5006 46.0711C12.7561 45.7497 2.41289 37.3309 0.479297 26.3039C0.127344 24.565 0.479297 23.6835 0.65625 22.2703C0.414766 23.368 0.280065 24.485 0.253906 25.6069V25.7318C0.270703 44.3471 21.1414 55.9639 37.8211 46.6422C40.9954 44.8681 43.7275 42.4418 45.8241 39.5351C47.9206 36.6283 49.3305 33.3121 49.9539 29.8209C49.9959 29.509 50.0301 29.2001 50.0676 28.885C50.5831 24.7687 50.025 20.5931 48.4439 16.7382Z" fill="url(#paint1_radial_2027_3117)"/>
								<path d="M48.4439 16.7382C47.3563 14.2101 45.1502 11.4805 43.4225 10.6177C44.6546 12.9231 45.5097 15.399 45.9574 17.9573V17.9803L45.9619 18.006C47.8911 23.3394 47.6121 29.1868 45.183 34.3262C42.3143 40.2726 35.3701 46.3675 24.5006 46.0711C12.7561 45.7497 2.41289 37.3309 0.479297 26.3039C0.127344 24.565 0.479297 23.6835 0.65625 22.2703C0.414766 23.368 0.280065 24.485 0.253906 25.6069V25.7318C0.270703 44.3471 21.1414 55.9639 37.8211 46.6422C40.9954 44.8681 43.7275 42.4418 45.8241 39.5351C47.9206 36.6283 49.3305 33.3121 49.9539 29.8209C49.9959 29.509 50.0301 29.2001 50.0676 28.885C50.5831 24.7687 50.025 20.5931 48.4439 16.7382Z" fill="url(#paint2_radial_2027_3117)"/>
								<path d="M36.2799 19.5809C36.3342 19.6177 36.3846 19.6545 36.4355 19.6913C35.8067 18.6143 35.0241 17.6282 34.1105 16.762C26.3309 9.24523 32.0717 0.463718 33.0398 0.0176798L33.0494 0.00390625C26.7623 3.56032 24.6289 10.1437 24.4326 13.4367C24.7246 13.4173 25.0139 13.3935 25.3123 13.3935C27.5431 13.3975 29.7331 13.9721 31.6595 15.0589C33.5859 16.1457 35.1801 17.7059 36.2799 19.5809Z" fill="url(#paint3_radial_2027_3117)"/>
								<path d="M25.3285 21.082C25.2871 21.6836 23.0873 23.7579 22.3182 23.7579C15.2004 23.7579 14.0449 27.9175 14.0449 27.9175C14.3602 31.4202 16.8867 34.3051 19.9408 35.8311C20.0803 35.9011 20.2219 35.9641 20.3633 36.0258C20.6053 36.1295 20.8504 36.2265 21.0982 36.3166C22.1464 36.6749 23.2442 36.8795 24.3551 36.9236C36.8309 37.4888 39.2477 22.5115 30.2445 18.1653C32.3684 17.8982 34.5192 18.4027 36.2799 19.5809C35.1801 17.7059 33.5859 16.1457 31.6595 15.0589C29.7331 13.9721 27.5431 13.3975 25.3123 13.3936C25.0152 13.3936 24.7246 13.4173 24.4326 13.4368C21.9218 13.6034 19.5213 14.4971 17.5441 16.0015C17.9258 16.3136 18.3566 16.7303 19.2641 17.5943C20.9625 19.2109 25.3187 20.8854 25.3285 21.082Z" fill="url(#paint4_radial_2027_3117)"/>
								<path d="M25.3285 21.082C25.2871 21.6836 23.0873 23.7579 22.3182 23.7579C15.2004 23.7579 14.0449 27.9175 14.0449 27.9175C14.3602 31.4202 16.8867 34.3051 19.9408 35.8311C20.0803 35.9011 20.2219 35.9641 20.3633 36.0258C20.6053 36.1295 20.8504 36.2265 21.0982 36.3166C22.1464 36.6749 23.2442 36.8795 24.3551 36.9236C36.8309 37.4888 39.2477 22.5115 30.2445 18.1653C32.3684 17.8982 34.5192 18.4027 36.2799 19.5809C35.1801 17.7059 33.5859 16.1457 31.6595 15.0589C29.7331 13.9721 27.5431 13.3975 25.3123 13.3936C25.0152 13.3936 24.7246 13.4173 24.4326 13.4368C21.9218 13.6034 19.5213 14.4971 17.5441 16.0015C17.9258 16.3136 18.3566 16.7303 19.2641 17.5943C20.9625 19.2109 25.3187 20.8854 25.3285 21.082Z" fill="url(#paint5_radial_2027_3117)"/>
								<path d="M16.3777 15.197C16.5521 15.3042 16.7244 15.4147 16.8943 15.5284C16.3239 13.5999 16.2996 11.5588 16.824 9.61816C14.515 10.6986 12.4633 12.2304 10.7988 14.1165C10.9209 14.1133 14.552 14.0502 16.3777 15.197Z" fill="url(#paint6_radial_2027_3117)"/>
								<path d="M0.482816 26.3038C2.41446 37.3308 12.7596 45.7497 24.5041 46.071C35.3736 46.368 42.3176 40.2725 45.1865 34.3261C47.6156 29.1868 47.8946 23.3394 45.9654 18.0059V17.9829C45.9654 17.9648 45.9615 17.954 45.9654 17.9597L45.9699 18.0003C46.858 23.601 43.909 29.0272 39.299 32.6963L39.2848 32.7276C30.3025 39.7946 21.7063 36.9914 19.9664 35.8471C19.844 35.7905 19.7226 35.7321 19.6022 35.6718C14.365 33.2535 12.2016 28.644 12.6652 24.691C11.4221 24.7088 10.2002 24.3779 9.14778 23.7384C8.09535 23.0989 7.25749 22.1782 6.73575 21.088C8.11051 20.2744 9.67802 19.8145 11.2889 19.7521C12.8998 19.6896 14.5005 20.0267 15.9383 20.7312C18.9026 22.0312 22.2757 22.1592 25.3365 21.088C25.327 20.8914 20.9707 19.2163 19.2721 17.6004C18.3647 16.7363 17.934 16.3201 17.5523 16.0076C17.3461 15.8387 17.1305 15.6808 16.9064 15.5346C16.7578 15.4367 16.5906 15.3306 16.3898 15.2033C14.5641 14.0565 10.933 14.1195 10.8129 14.1227H10.8014C9.80938 12.908 9.87911 8.90252 9.93594 8.06592C9.64252 8.17993 9.36239 8.32356 9.1002 8.49441C8.2245 9.09824 7.40581 9.77572 6.65372 10.5189C5.79372 11.356 5.00777 12.2612 4.30411 13.225V13.2286V13.2242C2.69509 15.4284 1.55368 17.9184 0.945707 20.5508C0.933597 20.6001 0.0441442 24.3559 0.482816 26.3038Z" fill="url(#paint7_radial_2027_3117)"/>
								<path d="M34.1118 16.7619C35.0256 17.629 35.8083 18.6162 36.4368 19.6943C36.5665 19.7877 36.6917 19.8868 36.812 19.9913C42.487 25.0449 39.5136 32.1887 39.2921 32.6972C43.9021 29.0279 46.8483 23.6019 45.963 18.0009C43.1315 11.1817 38.3323 8.43338 34.4128 2.44885C34.2103 2.14379 34.0137 1.83512 33.8231 1.523C33.725 1.35995 33.633 1.19349 33.5474 1.02395C33.3847 0.719834 33.2594 0.398411 33.1739 0.0662101C33.1741 0.0508634 33.1684 0.0360006 33.1579 0.0244743C33.1474 0.0129479 33.1329 0.00557022 33.1171 0.00375721C33.1016 4.96792e-05 33.0855 4.96792e-05 33.07 0.00375721C33.0669 0.00375721 33.0616 0.00941759 33.0577 0.0107383C33.0538 0.0120591 33.0454 0.0175308 33.0397 0.0199836C32.0716 0.463569 26.3308 9.24508 34.1118 16.7619Z" fill="url(#paint8_radial_2027_3117)"/>
								<path d="M36.8084 19.9881C36.6881 19.8836 36.5629 19.7845 36.4332 19.6911C36.3822 19.6543 36.3318 19.6175 36.2775 19.5807C34.5169 18.4025 32.3661 17.898 30.2422 18.165C39.2451 22.5137 36.8297 37.4886 24.3527 36.9233C23.2418 36.8793 22.144 36.6747 21.0959 36.3164C20.8479 36.2267 20.6028 36.1297 20.3609 36.0256C20.2193 35.9632 20.0779 35.9009 19.9385 35.8309L19.9559 35.8416C21.6957 36.989 30.2893 39.7922 39.2742 32.722L39.2885 32.6909C39.5125 32.1854 42.4859 25.0399 36.8084 19.9881Z" fill="url(#paint9_radial_2027_3117)"/>
								<path d="M14.0449 27.9172C14.0449 27.9172 15.2004 23.7575 22.3182 23.7575C23.0873 23.7575 25.2891 21.6832 25.3285 21.0817C22.2677 22.1529 18.8946 22.0247 15.9303 20.7247C14.4924 20.0202 12.8917 19.6831 11.2808 19.7456C9.66985 19.8081 8.10231 20.2681 6.72754 21.0817C7.24929 22.1719 8.08714 23.0926 9.13958 23.7321C10.192 24.3716 11.4138 24.7025 12.657 24.6847C12.1934 28.6385 14.3568 33.2479 19.5939 35.6655C19.7109 35.7198 19.8207 35.7785 19.9408 35.8302C16.8842 34.3047 14.3602 31.4198 14.0449 27.9172Z" fill="url(#paint10_radial_2027_3117)"/>
								<path d="M48.4439 16.7383C47.3563 14.2102 45.1502 11.4805 43.4225 10.6177C44.6546 12.9232 45.5097 15.3991 45.9574 17.9573L45.9619 17.9979C43.1318 11.1817 38.3326 8.43338 34.4131 2.44885C34.2106 2.14379 34.014 1.83512 33.8234 1.523C33.7252 1.35995 33.6333 1.19349 33.5477 1.02395C33.385 0.719834 33.2597 0.398411 33.1742 0.06621C33.1744 0.0508634 33.1687 0.0360006 33.1582 0.0244743C33.1477 0.0129479 33.1332 0.00557022 33.1174 0.00375721C33.1019 4.96793e-05 33.0858 4.96793e-05 33.0703 0.00375721C33.0672 0.00375721 33.0619 0.00941759 33.058 0.0107383C33.0541 0.0120591 33.0457 0.0175308 33.04 0.0199836L33.0496 0.00375721C26.7625 3.56017 24.6291 10.1436 24.4328 13.4366C24.7248 13.4172 25.0141 13.3934 25.3125 13.3934C27.5433 13.3974 29.7333 13.972 31.6597 15.0588C33.5861 16.1455 35.1803 17.7057 36.2801 19.5807C34.5194 18.4026 32.3686 17.8981 30.2447 18.1651C39.2479 22.5138 36.8324 37.4887 24.3553 36.9234C23.2444 36.8793 22.1466 36.6747 21.0984 36.3164C20.8505 36.2267 20.6054 36.1297 20.3635 36.0256C20.2221 35.9632 20.0805 35.9009 19.941 35.8309L19.9584 35.8417C19.8361 35.785 19.7146 35.7265 19.5941 35.6662C19.7111 35.7206 19.8209 35.7792 19.941 35.8309C16.8844 34.3049 14.3604 31.42 14.0451 27.9173C14.0451 27.9173 15.2006 23.7577 22.3184 23.7577C23.0875 23.7577 25.2893 21.6834 25.3287 21.0819C25.3189 20.8853 20.9627 19.2102 19.2643 17.5941C18.3568 16.7302 17.926 16.3139 17.5443 16.0013C17.3381 15.8324 17.1225 15.6746 16.8984 15.5285C16.328 13.6 16.3037 11.5589 16.8281 9.61829C14.5192 10.6987 12.4675 12.2305 10.8031 14.1166H10.7914C9.79941 12.9019 9.86914 8.8964 9.92598 8.05961C9.63255 8.17368 9.35242 8.31737 9.09023 8.48829C8.21453 9.09211 7.39585 9.7696 6.64375 10.5128C5.78685 11.3523 5.00417 12.2595 4.30391 13.2251V13.2287V13.2243C2.69489 15.4285 1.55348 17.9185 0.945508 20.5509L0.912109 20.7106C0.864844 20.9234 0.653711 22.0039 0.623242 22.236C0.623242 22.2179 0.623242 22.2536 0.623242 22.236C0.430055 23.3515 0.306718 24.4772 0.253906 25.607V25.7319C0.270703 44.3472 21.1414 55.9639 37.8211 46.6422C40.9954 44.8681 43.7275 42.4419 45.8241 39.5351C47.9206 36.6284 49.3305 33.3121 49.9539 29.8209C49.9959 29.509 50.0301 29.2002 50.0676 28.8851C50.5831 24.7687 50.025 20.5931 48.4439 16.7383Z" fill="url(#paint11_linear_2027_3117)"/>
							</g>
							<defs>
								<linearGradient id="paint0_linear_2027_3117" x1="45.1307" y1="7.73016" x2="4.96349" y2="47.8464" gradientUnits="userSpaceOnUse">
									<stop offset="0.048" stop-color="#FFF44F"/>
									<stop offset="0.111" stop-color="#FFE847"/>
									<stop offset="0.225" stop-color="#FFC830"/>
									<stop offset="0.368" stop-color="#FF980E"/>
									<stop offset="0.401" stop-color="#FF8B16"/>
									<stop offset="0.462" stop-color="#FF672A"/>
									<stop offset="0.534" stop-color="#FF3647"/>
									<stop offset="0.705" stop-color="#E31587"/>
								</linearGradient>
								<radialGradient id="paint1_radial_2027_3117" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(43.2115 5.5385) scale(52.1819 50.4097)">
									<stop offset="0.129" stop-color="#FFBD4F"/>
									<stop offset="0.186" stop-color="#FFAC31"/>
									<stop offset="0.247" stop-color="#FF9D17"/>
									<stop offset="0.283" stop-color="#FF980E"/>
									<stop offset="0.403" stop-color="#FF563B"/>
									<stop offset="0.467" stop-color="#FF3750"/>
									<stop offset="0.71" stop-color="#F5156C"/>
									<stop offset="0.782" stop-color="#EB0878"/>
									<stop offset="0.86" stop-color="#E50080"/>
								</radialGradient>
								<radialGradient id="paint2_radial_2027_3117" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(24.1437 26.1474) scale(52.1819 50.4097)">
									<stop offset="0.3" stop-color="#960E18"/>
									<stop offset="0.351" stop-color="#B11927" stop-opacity="0.74"/>
									<stop offset="0.435" stop-color="#DB293D" stop-opacity="0.343"/>
									<stop offset="0.497" stop-color="#F5334B" stop-opacity="0.094"/>
									<stop offset="0.53" stop-color="#FF3750" stop-opacity="0"/>
								</radialGradient>
								<radialGradient id="paint3_radial_2027_3117" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(30.4346 -5.90124) scale(37.8036 36.5197)">
									<stop offset="0.132" stop-color="#FFF44F"/>
									<stop offset="0.252" stop-color="#FFDC3E"/>
									<stop offset="0.506" stop-color="#FF9D12"/>
									<stop offset="0.526" stop-color="#FF980E"/>
								</radialGradient>
								<radialGradient id="paint4_radial_2027_3117" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(18.3716 39.3151) scale(24.8461 24.0023)">
									<stop offset="0.353" stop-color="#3A8EE6"/>
									<stop offset="0.472" stop-color="#5C79F0"/>
									<stop offset="0.669" stop-color="#9059FF"/>
									<stop offset="1" stop-color="#C139E6"/>
								</radialGradient>
								<radialGradient id="paint5_radial_2027_3117" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(27.0029 22.0169) rotate(-13.1462) scale(13.1489 14.9263)">
									<stop offset="0.206" stop-color="#9059FF" stop-opacity="0"/>
									<stop offset="0.278" stop-color="#8C4FF3" stop-opacity="0.064"/>
									<stop offset="0.747" stop-color="#7716A8" stop-opacity="0.45"/>
									<stop offset="0.975" stop-color="#6E008B" stop-opacity="0.6"/>
								</radialGradient>
								<radialGradient id="paint6_radial_2027_3117" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(23.4853 3.47579) scale(17.8743 17.2672)">
									<stop stop-color="#FFE226"/>
									<stop offset="0.121" stop-color="#FFDB27"/>
									<stop offset="0.295" stop-color="#FFC82A"/>
									<stop offset="0.502" stop-color="#FFA930"/>
									<stop offset="0.732" stop-color="#FF7E37"/>
									<stop offset="0.792" stop-color="#FF7139"/>
								</radialGradient>
								<radialGradient id="paint7_radial_2027_3117" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(37.5347 -7.47622) scale(76.2614 73.6714)">
									<stop offset="0.113" stop-color="#FFF44F"/>
									<stop offset="0.456" stop-color="#FF980E"/>
									<stop offset="0.622" stop-color="#FF5634"/>
									<stop offset="0.716" stop-color="#FF3647"/>
									<stop offset="0.904" stop-color="#E31587"/>
								</radialGradient>
								<radialGradient id="paint8_radial_2027_3117" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(31.1189 -3.38114) rotate(83.7659) scale(54.0169 36.6682)">
									<stop stop-color="#FFF44F"/>
									<stop offset="0.06" stop-color="#FFE847"/>
									<stop offset="0.168" stop-color="#FFC830"/>
									<stop offset="0.304" stop-color="#FF980E"/>
									<stop offset="0.356" stop-color="#FF8B16"/>
									<stop offset="0.455" stop-color="#FF672A"/>
									<stop offset="0.57" stop-color="#FF3647"/>
									<stop offset="0.737" stop-color="#E31587"/>
								</radialGradient>
								<radialGradient id="paint9_radial_2027_3117" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(23.0639 9.87153) scale(47.6113 45.9943)">
									<stop offset="0.137" stop-color="#FFF44F"/>
									<stop offset="0.48" stop-color="#FF980E"/>
									<stop offset="0.592" stop-color="#FF5634"/>
									<stop offset="0.655" stop-color="#FF3647"/>
									<stop offset="0.904" stop-color="#E31587"/>
								</radialGradient>
								<radialGradient id="paint10_radial_2027_3117" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(35.6317 12.5635) scale(52.1102 50.3404)">
									<stop offset="0.094" stop-color="#FFF44F"/>
									<stop offset="0.231" stop-color="#FFE141"/>
									<stop offset="0.509" stop-color="#FFAF1E"/>
									<stop offset="0.626" stop-color="#FF980E"/>
								</radialGradient>
								<linearGradient id="paint11_linear_2027_3117" x1="44.6314" y1="7.52302" x2="10.4955" y2="42.8585" gradientUnits="userSpaceOnUse">
									<stop offset="0.167" stop-color="#FFF44F" stop-opacity="0.8"/>
									<stop offset="0.266" stop-color="#FFF44F" stop-opacity="0.634"/>
									<stop offset="0.489" stop-color="#FFF44F" stop-opacity="0.217"/>
									<stop offset="0.6" stop-color="#FFF44F" stop-opacity="0"/>
								</linearGradient>
								<clipPath id="clip0_2027_3117">
									<rect width="50" height="50" fill="white"/>
								</clipPath>
							</defs>
						</svg>

						{elseif $isSafari}
						<svg width="17" height="17" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
							<g clip-path="url(#clip0_2027_3110)">
								<path d="M25 49.4141C38.4835 49.4141 49.4141 38.4835 49.4141 25C49.4141 11.5165 38.4835 0.585938 25 0.585938C11.5165 0.585938 0.585938 11.5165 0.585938 25C0.585938 38.4835 11.5165 49.4141 25 49.4141Z" fill="url(#paint0_linear_2027_3110)"/>
								<path d="M24.8043 2.96875V6.5625H25.1949V2.96875H24.8043ZM23.2801 3.07031L22.891 3.10156L23.0473 5.05469L23.4363 5.02344L23.2801 3.07031ZM26.7191 3.07031L26.5629 5.02344L26.952 5.05469L27.1082 3.10156L26.7191 3.07031ZM21.3641 3.28672L20.9789 3.35391L21.6039 6.90898L21.9891 6.84141L21.3641 3.28672ZM28.6352 3.28672L28.0102 6.84141L28.3953 6.90937L29.0203 3.35391L28.6352 3.28672ZM19.4852 3.69922L19.1082 3.80117L19.6156 5.67656L19.9926 5.57461L19.4852 3.69922ZM30.5141 3.69922L30.0066 5.57422L30.3836 5.67656L30.891 3.80156L30.5141 3.69922ZM17.6836 4.23164L17.3152 4.36289L18.527 7.76133L18.8945 7.62969L17.6836 4.23164ZM32.3156 4.23164L31.1047 7.63008L31.4723 7.76094L32.684 4.36289L32.3156 4.23164ZM34.1203 4.95508L33.2609 6.75234L33.6133 6.92031L34.473 5.12344L34.1203 4.95508ZM15.8801 4.95781L15.5254 5.12031L16.3457 6.91719L16.7004 6.75547L15.8801 4.95781ZM14.1926 5.84062L13.8539 6.03516L15.6508 9.16016L15.9895 8.96562L14.1926 5.84062ZM35.8066 5.84062L34.0098 8.96562L34.3488 9.16016L36.1457 6.03516L35.8066 5.84062ZM12.5414 6.84062L12.2227 7.06602L13.3555 8.66797L13.6746 8.44219L12.5414 6.84062ZM37.4578 6.84062L36.325 8.44219L36.6437 8.66797L37.7766 7.06602L37.4578 6.84062ZM11.0477 8L10.7488 8.25156L13.0535 10.9859L13.352 10.7344L11.0477 8ZM38.9516 8L36.6473 10.7344L36.9457 10.9859L39.2504 8.25156L38.9516 8ZM9.58906 9.27461L9.3168 9.55469L10.723 10.9219L10.9949 10.6418L9.58906 9.27461ZM8.28828 10.6707L8.03867 10.9707L10.8121 13.2754L11.0621 12.9746L8.28828 10.6707ZM41.7109 10.6707L38.9371 12.975L39.1871 13.2758L41.9605 10.9707L41.7109 10.6707ZM7.10273 12.1836L6.88047 12.5051L8.52148 13.6379L8.74375 13.316L7.10273 12.1836ZM42.8965 12.1836L41.2559 13.316L41.4777 13.6379L43.1184 12.5051L42.8965 12.1836ZM6.07383 13.8152L5.8793 14.1539L9.0043 15.9508L9.19883 15.6121L6.07383 13.8152ZM43.9262 13.8152L40.8012 15.6121L40.9957 15.9508L44.1207 14.1539L43.9262 13.8152ZM5.19961 15.4871L5.03477 15.841L6.79258 16.6613L6.95742 16.307L5.19961 15.4871ZM44.8004 15.4871L43.0426 16.3074L43.2074 16.6609L44.9652 15.8406L44.8004 15.4871ZM4.44297 17.2383L4.30625 17.6043L7.66562 18.8551L7.80234 18.4887L4.44297 17.2383ZM45.5562 17.2383L42.1969 18.4887L42.3336 18.8551L45.693 17.6043L45.5562 17.2383ZM3.83867 19.0688L3.73867 19.4465L5.65273 19.9547L5.75312 19.577L3.83867 19.0688ZM46.1605 19.0688L44.2465 19.577L44.3461 19.9547L46.2602 19.4469L46.1605 19.0688ZM3.39336 20.9402L3.32539 21.325L6.88008 21.9504L6.94766 21.5652L3.39336 20.9402ZM46.6059 20.9402L43.0512 21.5652L43.1195 21.9504L46.6742 21.325L46.6059 20.9402ZM3.14414 22.8133L3.10586 23.2023L5.05898 23.3977L5.09727 23.0086L3.14414 22.8133ZM46.8559 22.8133L44.9027 23.0086L44.941 23.3977L46.8941 23.2023L46.8559 22.8133ZM3.04688 24.7656V25.1563H6.64023V24.7656H3.04688ZM43.359 24.7656V25.1563H46.9527V24.7656H43.359ZM5.05859 26.4855L3.10547 26.6809L3.14375 27.0691L5.09687 26.8738L5.05859 26.4855ZM44.9406 26.4855L44.9023 26.8738L46.8555 27.0691L46.8937 26.6809L44.9406 26.4855ZM6.87969 27.9328L3.325 28.5578L3.39336 28.9422L6.94805 28.3172L6.87969 27.9328ZM43.1195 27.9328L43.0516 28.3172L46.6063 28.9422L46.6738 28.5574L43.1195 27.9328ZM5.65312 29.9289L3.73906 30.4359L3.83867 30.8137L5.75273 30.3062L5.65312 29.9289ZM44.3461 29.9289L44.2461 30.3062L46.1602 30.8137L46.2605 30.4359L44.3461 29.9289ZM7.66562 31.0281L4.30625 32.2777L4.44297 32.6438L7.80234 31.3941L7.66562 31.0281ZM42.3336 31.0281L42.1969 31.3941L45.5562 32.6441L45.693 32.2777L42.3336 31.0281ZM6.79219 33.2215L5.03437 34.0418L5.19922 34.3957L6.95703 33.5754L6.79219 33.2215ZM43.207 33.2215L43.0422 33.5754L44.8 34.3957L44.9648 34.0414L43.207 33.2215ZM9.00391 33.9715L5.87891 35.7684L6.07344 36.107L9.19844 34.3102L9.00391 33.9715ZM40.9953 33.9715L40.8008 34.3102L43.9258 36.107L44.1203 35.768L40.9953 33.9715ZM8.52148 36.2457L6.88086 37.3785L7.10273 37.7L8.74336 36.5672L8.52148 36.2457ZM41.4777 36.2457L41.2555 36.5668L42.8965 37.6996L43.1188 37.3785L41.4777 36.2457ZM10.8121 36.6074L8.03867 38.9121L8.28828 39.2129L11.0621 36.9082L10.8121 36.6074ZM39.1871 36.6074L38.9371 36.9082L41.7109 39.2129L41.9605 38.9121L39.1871 36.6074ZM13.0535 38.8977L10.7488 41.632L11.0477 41.884L13.352 39.1496L13.0535 38.8977ZM36.9457 38.8977L36.6473 39.1496L38.9516 41.884L39.2504 41.632L36.9457 38.8977ZM39.2766 38.9617L39.0043 39.2418L40.4105 40.609L40.6824 40.3289L39.2766 38.9617ZM15.6516 40.7609L13.8547 43.8469L14.1918 44.0438L15.9887 40.9578L15.6516 40.7609ZM34.348 40.7609L34.0105 40.9578L35.8074 44.0438L36.1445 43.8469L34.348 40.7609ZM13.3559 41.2156L12.2227 42.8172L12.5414 43.0426L13.6742 41.4406L13.3559 41.2156ZM36.6437 41.2156L36.3246 41.4406L37.4574 43.0426L37.7766 42.8168L36.6437 41.2156ZM18.5266 42.1219L17.3156 45.5203L17.6836 45.6512L18.8945 42.2531L18.5266 42.1219ZM31.4723 42.1219L31.1047 42.2531L32.3156 45.6516L32.684 45.5199L31.4723 42.1219ZM21.6039 42.9742L20.9789 46.5289L21.3641 46.5965L21.9891 43.0418L21.6039 42.9742ZM28.3953 42.9742L28.0102 43.0422L28.6352 46.5969L29.0203 46.5285L28.3953 42.9742ZM16.3867 43L15.527 44.7578L15.8777 44.9297L16.7379 43.1719L16.3867 43ZM33.6125 43L33.2613 43.1719L34.1215 44.9297L34.4723 44.7578L33.6125 43ZM24.8043 43.3203V46.9141H25.1949V43.3203H24.8043ZM19.6156 44.2078L19.1074 46.1219L19.4852 46.2219L19.993 44.3078L19.6156 44.2078ZM30.3836 44.2078L30.0059 44.3078L30.5141 46.2219L30.8918 46.1219L30.3836 44.2078ZM23.0473 44.8668L22.891 46.8199L23.2801 46.8512L23.4363 44.898L23.0473 44.8668ZM26.952 44.8668L26.5629 44.898L26.7191 46.8512L27.1082 46.8199L26.952 44.8668Z" fill="white"/>
								<path d="M41.6797 8.20312L22.9297 22.9297L24.9609 24.9609L41.6797 8.20312Z" fill="#FF0000"/>
								<path d="M24.9609 24.9609L27.3047 27.3047L41.6797 8.20312L24.9609 24.9609Z" fill="#D01414"/>
								<path d="M22.9297 22.9297L8.20312 41.6797L24.9609 24.9609L22.9297 22.9297Z" fill="white"/>
								<path d="M8.20312 41.6797L27.3047 27.3047L24.9609 24.9609L8.20312 41.6797Z" fill="#ACACAC"/>
							</g>
							<defs>
								<linearGradient id="paint0_linear_2027_3110" x1="25.0078" y1="0.590625" x2="25.0078" y2="49.4156" gradientUnits="userSpaceOnUse">
									<stop stop-color="#19D7FF"/>
									<stop offset="1" stop-color="#1E64F0"/>
								</linearGradient>
								<clipPath id="clip0_2027_3110">
									<rect width="50" height="50" fill="white"/>
								</clipPath>
							</defs>
						</svg>

						{/if}
						<div class="text-dark-1 text-sm leading-[24.5px]">{_newFront.navbar.addon}</div>
					</a>

					{if $hasAddonPromoUntilMidnight && $presenter->getAction() != 'afterSignUp'}
					<div id="addon-promo-header" class="hidden lg:block absolute top-[36px] left-1/2 transform -translate-x-1/2 min-w-[316px] bg-primary-blue-dark pt-[25px] pb-[13px] px-[25px] rounded-xl">
						<svg id="addon-promo-close-btn" class="absolute top-2.5 right-2.5 cursor-pointer"
							 xmlns="http://www.w3.org/2000/svg" width="10"
							 height="10"
							 viewBox="0 0 10 10"
							 fill="none">
							<path opacity="0.8" d="M1 9.42969L8.99999 1.42969M9 9.42969L1.00001 1.42969" stroke="white" stroke-linecap="round"/>
						</svg>

						<img class="-translate-x-1/2 w-[30px] absolute left-1/2 top-[-8px]"
							 src="{$basePath}/new-design/addon-promo-arrow.png"
							 alt="">

						<div class="text-white text-sm leading-[16px]">
							{_newFront.headerAddonPromo.exclusiveBonusText, ['bonus' => $addonBonusAmount()]|noescape}
						</div>

						<div
							class="flex items-center justify-center gap-2.5 text-secondary-red leading-7 py-2 bg-white rounded-lg mt-3">
							<svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21" fill="none">
								<path d="M10 19.4297C14.9706 19.4297 19 15.4003 19 10.4297C19 5.45912 14.9706 1.42969 10 1.42969C5.02944 1.42969 1 5.45912 1 10.4297C1 15.4003 5.02944 19.4297 10 19.4297Z" stroke="#F72F49" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
								<path d="M10 5.03125V10.4313L13.6 12.2313" stroke="#F72F49" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
							</svg>
							<span id="header-countdown"></span>
						</div>

						<div class="text-center text-sm leading-[24.5px] text-white mt-2.5"> {_newFront.headerAddonPromo.bonusText, ['bonus' => $defaultAddonBonusAmount()]|noescape}</div>
					</div>
					<script>
						function updateCountdown() {
							var now = new Date();
							var midnight = new Date(
									now.getFullYear(),
									now.getMonth(),
									now.getDate() + 1,
									0, 0, 0
							);

							var diff = midnight - now;

							var hours = Math.floor(diff / (1000 * 60 * 60));
							var minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
							var seconds = Math.floor((diff % (1000 * 60)) / 1000);


							document.getElementById("header-countdown").innerHTML = hours + "h: " + minutes + "m : " + seconds + "s ";

							if (diff < 0) {
								clearInterval(x);
								document.getElementById("header-countdown").innerHTML = "Je polnoc!";
							}
						}
						var x = setInterval(updateCountdown, 1000);
					</script>

					{/if}
				</div>
			</div>
		</div>
	</div>
	{/if}
</header>

{if $menuShowDropdownAndSearch}
<div class="hidden absolute bg-blue-950/70 backdrop-blur-[6px] z-50 top-[64px] w-full h-full left-0 js-user-dropdown">
	<div class="relative flex items-start container px-0 md:p-4 md:pt-2">
		<div n:if="$user->isLoggedIn()"
			class="fixed md:static top-[0px] right-[0px] md:right-auto w-[313px] md:w-[278px] h-full md:h-auto bg-white md:rounded-tl-2xl md:rounded-tr-[3px] rounded-bl-2xl rounded-br-2xl ml-auto pt-3 z-50">
			<a n:href=":NewFront:Account:Transaction:default"
					data-google-interstitial="false"
				class="flex gap-2 text-zinc-950 text-sm font-medium leading-normal px-6 py-3.5 xl:hover:underline">
				<svg xmlns="http://www.w3.org/2000/svg" width="18" height="19" viewBox="0 0 18 19" fill="none">
					<path
						d="M7.85714 7.22979C7.85714 7.41424 7.7849 7.59113 7.6563 7.72156C7.5277 7.85198 7.35329 7.92525 7.17143 7.92525H1.68571C1.50386 7.92525 1.32944 7.85198 1.20084 7.72156C1.07224 7.59113 1 7.41424 1 7.22979V1.66616C1 1.48172 1.07224 1.30482 1.20084 1.17439C1.32944 1.04397 1.50386 0.970703 1.68571 0.970703H7.17143C7.35329 0.970703 7.5277 1.04397 7.6563 1.17439C7.7849 1.30482 7.85714 1.48172 7.85714 1.66616V7.22979Z"
						stroke="#080B10" stroke-linecap="round" stroke-linejoin="round" />
					<path
						d="M17 7.22979C17 7.41424 16.9278 7.59113 16.7992 7.72156C16.6706 7.85198 16.4961 7.92525 16.3143 7.92525H10.8286C10.6467 7.92525 10.4723 7.85198 10.3437 7.72156C10.2151 7.59113 10.1429 7.41424 10.1429 7.22979V1.66616C10.1429 1.48172 10.2151 1.30482 10.3437 1.17439C10.4723 1.04397 10.6467 0.970703 10.8286 0.970703H16.3143C16.4961 0.970703 16.6706 1.04397 16.7992 1.17439C16.9278 1.30482 17 1.48172 17 1.66616V7.22979Z"
						stroke="#080B10" stroke-linecap="round" stroke-linejoin="round" />
					<path
						d="M17 17.2752C17 17.4597 16.9278 17.6366 16.7992 17.767C16.6706 17.8974 16.4961 17.9707 16.3143 17.9707H10.8286C10.6467 17.9707 10.4723 17.8974 10.3437 17.767C10.2151 17.6366 10.1429 17.4597 10.1429 17.2752V11.7116C10.1429 11.5272 10.2151 11.3503 10.3437 11.2198C10.4723 11.0894 10.6467 11.0162 10.8286 11.0162H16.3143C16.4961 11.0162 16.6706 11.0894 16.7992 11.2198C16.9278 11.3503 17 11.5272 17 11.7116V17.2752Z"
						stroke="#080B10" stroke-linecap="round" stroke-linejoin="round" />
					<path
						d="M7.85714 17.2752C7.85714 17.4597 7.7849 17.6366 7.6563 17.767C7.5277 17.8974 7.35329 17.9707 7.17143 17.9707H1.68571C1.50386 17.9707 1.32944 17.8974 1.20084 17.767C1.07224 17.6366 1 17.4597 1 17.2752V11.7116C1 11.5272 1.07224 11.3503 1.20084 11.2198C1.32944 11.0894 1.50386 11.0162 1.68571 11.0162H7.17143C7.35329 11.0162 7.5277 11.0894 7.6563 11.2198C7.7849 11.3503 7.85714 11.5272 7.85714 11.7116V17.2752Z"
						stroke="#080B10" stroke-linecap="round" stroke-linejoin="round" />
				</svg>
				{_newFront.accountNavbar.rewards}
			</a>
			<a n:href=":NewFront:Account:Payout:default"
					data-google-interstitial="false"
				class="flex gap-2 text-zinc-950 text-sm font-medium leading-normal px-6 py-3.5 xl:hover:underline">
				<svg xmlns="http://www.w3.org/2000/svg" width="18" height="19" viewBox="0 0 18 19" fill="none">
					<path
						d="M1.72931 1.24244C1.46414 1.16536 1.18669 1.31784 1.10962 1.58301C1.03254 1.84817 1.18502 2.12562 1.45019 2.20269L1.72931 1.24244ZM10.5651 4.33142L10.7243 3.85744C10.7178 3.85526 10.7112 3.85321 10.7047 3.8513L10.5651 4.33142ZM12.0854 5.45394L12.4904 5.16069L12.4904 5.16065L12.0854 5.45394ZM12.692 7.26839H13.1921L13.1919 7.25792L12.692 7.26839ZM12.692 16.0877H12.192C12.192 16.1029 12.1927 16.1182 12.1941 16.1334L12.692 16.0877ZM12.5723 16.9052L13.036 17.0923L13.036 17.0922L12.5723 16.9052ZM12.0936 17.5715L11.7694 17.1909L11.7693 17.191L12.0936 17.5715ZM11.3665 17.9329L11.4729 18.4215L11.473 18.4214L11.3665 17.9329ZM10.5585 17.9061L10.6971 17.4256L10.6925 17.4243L10.5585 17.9061ZM3.13406 15.8418L2.98248 16.3183C2.98833 16.3201 2.99421 16.3219 3.00012 16.3235L3.13406 15.8418ZM1.6096 14.7434L2.0124 14.4471L2.01239 14.4471L1.6096 14.7434ZM1.00058 12.9409H0.500477L0.500679 12.9509L1.00058 12.9409ZM1.00058 3.33118H1.5007L1.50046 3.32012L1.00058 3.33118ZM3.1915 0.970703V0.470703C3.18646 0.470703 3.18143 0.470779 3.1764 0.470931L3.1915 0.970703ZM17 11.0008L17.5 11.0051V11.0008H17ZM16.243 12.8209L16.5968 13.1742L16.5969 13.1741L16.243 12.8209ZM15.4173 13.3722L15.6071 13.8348L15.6072 13.8348L15.4173 13.3722ZM14.448 13.5577L14.4541 13.0577H14.448V13.5577ZM12.692 13.0577C12.4159 13.0577 12.192 13.2815 12.192 13.5577C12.192 13.8338 12.4159 14.0577 12.692 14.0577V13.0577ZM17 7.76419C17.2761 7.76419 17.5 7.54033 17.5 7.26419C17.5 6.98805 17.2761 6.76419 17 6.76419V7.76419ZM12.692 6.76419C12.4159 6.76419 12.192 6.98805 12.192 7.26419C12.192 7.54033 12.4159 7.76419 12.692 7.76419V6.76419ZM1.45019 2.20269L10.4255 4.81155L10.7047 3.8513L1.72931 1.24244L1.45019 2.20269ZM10.4059 4.80541C10.914 4.97607 11.3598 5.3044 11.6805 5.74723L12.4904 5.16065C12.0491 4.55139 11.4326 4.09532 10.7243 3.85744L10.4059 4.80541ZM11.6805 5.74718C12.0012 6.19014 12.1805 6.72529 12.1921 7.27885L13.1919 7.25792C13.1761 6.5026 12.9315 5.76982 12.4904 5.16069L11.6805 5.74718ZM12.192 7.26839V16.0877H13.192V7.26839H12.192ZM12.1941 16.1334C12.2125 16.3329 12.1829 16.5337 12.1085 16.7183L13.036 17.0922C13.1701 16.7597 13.2228 16.3993 13.1899 16.0419L12.1941 16.1334ZM12.1086 16.7182C12.0341 16.9028 11.9173 17.0648 11.7694 17.1909L12.4179 17.9521C12.6901 17.7202 12.9019 17.4248 13.036 17.0923L12.1086 16.7182ZM11.7693 17.191C11.6213 17.3171 11.4465 17.4037 11.26 17.4444L11.473 18.4214C11.8216 18.3454 12.1458 18.184 12.418 17.952L11.7693 17.191ZM11.2602 17.4444C11.0738 17.4849 10.8806 17.4786 10.6971 17.4256L10.4199 18.3865C10.7629 18.4854 11.1245 18.4973 11.4729 18.4215L11.2602 17.4444ZM10.6925 17.4243L3.268 15.3601L3.00012 16.3235L10.4246 18.3878L10.6925 17.4243ZM3.28564 15.3653C2.77943 15.2043 2.33355 14.8838 2.0124 14.4471L1.20681 15.0396C1.65143 15.6441 2.27218 16.0923 2.98248 16.3183L3.28564 15.3653ZM2.01239 14.4471C1.69114 14.0103 1.5115 13.4799 1.50048 12.9309L0.500679 12.9509C0.515803 13.7044 0.762277 14.4352 1.20682 15.0396L2.01239 14.4471ZM1.50058 12.9409V3.33118H0.500578V12.9409H1.50058ZM1.50046 3.32012C1.48981 2.83903 1.66582 2.37516 1.98679 2.02935L1.25385 1.34906C0.753946 1.88765 0.484378 2.60428 0.5007 3.34224L1.50046 3.32012ZM1.98679 2.02935C2.30742 1.68391 2.74622 1.48439 3.2066 1.47048L3.1764 0.470931C2.4449 0.493035 1.75411 0.810084 1.25385 1.34906L1.98679 2.02935ZM3.1915 1.4707H14.5383V0.470703H3.1915V1.4707ZM14.5383 1.4707C15.055 1.4707 15.5528 1.68048 15.9216 2.05761L16.6365 1.35846C16.082 0.791378 15.3275 0.470703 14.5383 0.470703V1.4707ZM15.9216 2.05761C16.2907 2.43511 16.5 2.94946 16.5 3.4881H17.5C17.5 2.69143 17.1907 1.92516 16.6365 1.35846L15.9216 2.05761ZM16.5 3.4881V11.0008H17.5V3.4881H16.5ZM16.5 10.9966C16.4977 11.2718 16.4423 11.5434 16.3372 11.796L17.2605 12.18C17.4153 11.808 17.4966 11.4088 17.5 11.0051L16.5 10.9966ZM16.3372 11.796C16.2322 12.0486 16.0797 12.2767 15.8891 12.4677L16.5969 13.1741C16.8805 12.89 17.1058 12.5521 17.2605 12.18L16.3372 11.796ZM15.8892 12.4676C15.6985 12.6586 15.4735 12.8086 15.2274 12.9097L15.6072 13.8348C15.9771 13.6829 16.3133 13.4582 16.5968 13.1742L15.8892 12.4676ZM15.2275 12.9097C14.9813 13.0107 14.7186 13.0609 14.4541 13.0577L14.442 14.0576C14.8413 14.0625 15.2373 13.9865 15.6071 13.8348L15.2275 12.9097ZM14.448 13.0577H12.692V14.0577H14.448V13.0577ZM9.30716 10.5403C9.42385 10.5403 9.49945 10.6339 9.49945 10.7256H8.49945C8.49945 11.1649 8.85059 11.5403 9.30716 11.5403V10.5403ZM9.49945 10.7256C9.49945 10.8174 9.42385 10.9109 9.30716 10.9109V9.91093C8.85059 9.91093 8.49945 10.2863 8.49945 10.7256H9.49945ZM9.30716 11.5403C9.76373 11.5403 10.1149 11.1649 10.1149 10.7256H9.11487C9.11487 10.6339 9.19047 10.5403 9.30716 10.5403V11.5403ZM10.1149 10.7256C10.1149 10.2863 9.76373 9.91093 9.30716 9.91093V10.9109C9.19047 10.9109 9.11487 10.8174 9.11487 10.7256H10.1149ZM17 6.76419H12.692V7.76419H17V6.76419Z"
						fill="#080B10" />
				</svg>
				{_newFront.accountNavbar.payouts}
			</a>
			<a n:href=":NewFront:Account:User:settings"
					data-google-interstitial="false"
				class="flex gap-2 text-zinc-950 text-sm font-medium leading-normal px-6 py-3.5 xl:hover:underline">
				<svg xmlns="http://www.w3.org/2000/svg" width="18" height="19" viewBox="0 0 18 19" fill="none">
					<path
						d="M9.00009 11.523C10.1789 11.523 11.1346 10.6041 11.1346 9.47068C11.1346 8.33721 10.1789 7.41836 9.00009 7.41836C7.82115 7.41836 6.86562 8.33721 6.86562 9.47068C6.86562 10.6041 7.82115 11.523 9.00009 11.523Z"
						stroke="#080B10" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
					<path
						d="M10.7076 2.09948L11.2767 3.87816C11.419 4.56226 12.1305 4.83592 12.842 4.6991L14.8342 4.28862C16.5418 3.87816 17.6801 5.93046 16.5418 7.16186L15.1188 8.53007C14.6919 9.07735 14.6919 9.76148 15.1188 10.3087L16.5418 11.6769C17.6801 12.9083 16.5418 14.8238 14.8342 14.5502L12.842 14.1397C12.1305 14.0029 11.5613 14.4133 11.2767 14.9606L10.7076 16.7393C10.1384 18.3812 7.86159 18.3812 7.2924 16.7393L6.72321 14.9606C6.58091 14.2765 5.86947 14.0029 5.15798 14.1397L3.16582 14.5502C1.45825 14.9606 0.319848 12.9083 1.45823 11.6769L2.88121 10.3087C3.3081 9.76148 3.3081 9.07735 2.88121 8.53007L1.45823 7.16186C0.319848 5.93046 1.45825 4.01498 3.16582 4.28862L5.15798 4.6991C5.86947 4.83592 6.43862 4.42544 6.72321 3.87816L7.2924 2.09948C7.86159 0.594444 10.2807 0.594444 10.7076 2.09948Z"
						stroke="#080B10" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
				</svg>
				{_newFront.accountNavbar.settings}
			</a>
			<a n:href=":NewFront:Account:LastRedirections:default"
					data-google-interstitial="false"
					class="flex gap-2 text-zinc-950 text-sm font-medium leading-normal px-6 py-3.5 xl:hover:underline">
				<svg xmlns="http://www.w3.org/2000/svg" width="19" height="19" viewBox="0 0 19 19" fill="none">
					<path d="M17.9143 18L17.9906 17.1523C18.041 16.6035 17.8877 16.0554 17.5593 15.6104C17.231 15.1654 16.7502 14.854 16.2067 14.7345L13.0222 14.0333V9.5C13.0222 9.19944 12.9016 8.91112 12.6868 8.69858C12.4721 8.48604 12.1809 8.36667 11.8772 8.36667C11.5735 8.36667 11.2823 8.48604 11.0676 8.69858C10.8529 8.91112 10.7322 9.19944 10.7322 9.5V16.8667L9.43456 15.9011C9.20938 15.7336 8.93077 15.6523 8.6498 15.672C8.36889 15.6917 8.10456 15.811 7.90545 16.0081C7.70631 16.2053 7.58572 16.4669 7.56585 16.7449C7.54598 17.023 7.62816 17.2988 7.79728 17.5217L8.15906 18M8.44233 11.2H2.14498C1.84131 11.2 1.55008 11.0806 1.33536 10.8681C1.12063 10.6555 1 10.3672 1 10.0667V2.13333C1 1.83276 1.12063 1.54448 1.33536 1.33195C1.55008 1.1194 1.84131 1 2.14498 1H3.86245M13.0222 1H14.7397C15.0434 1 15.3346 1.1194 15.5494 1.33195C15.7641 1.54448 15.8847 1.83276 15.8847 2.13333V10.0667C15.8847 10.3672 15.7641 10.6555 15.5494 10.8681C15.3346 11.0806 15.0434 11.2 14.7397 11.2M7.86987 11.2V13.4667M6.1524 13.4667H8.44233M12.0895 6.12112L12.3757 5.36556C12.4405 5.1944 12.4624 5.01026 12.4397 4.82888C12.4171 4.64748 12.3504 4.47421 12.2454 4.32385C12.1405 4.17348 12.0003 4.05047 11.8369 3.96532C11.6735 3.88016 11.4917 3.83539 11.3071 3.83481H5.44861C5.26382 3.83487 5.08179 3.8792 4.91807 3.96401C4.75435 4.04883 4.6138 4.17161 4.50843 4.32187C4.40305 4.47213 4.33598 4.64539 4.31295 4.82687C4.28991 5.00836 4.3116 5.19267 4.37615 5.36405L5.37457 7.99871C5.41526 8.10653 5.4882 8.19954 5.58367 8.26527C5.67914 8.331 5.7926 8.36636 5.9089 8.36667H8.4408M5.51347 3.83333L6.65845 1M11.2384 3.83333L10.0934 1" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"/>
				</svg>
				{_newFront.accountNavbar.redirections}
			</a>
			<a n:href=":NewFront:Account:User:guarantee"
					data-google-interstitial="false"
				class="flex gap-2 text-zinc-950 text-sm font-medium leading-normal px-6 py-3.5 xl:hover:underline">
				<svg xmlns="http://www.w3.org/2000/svg" width="18" height="17" viewBox="0 0 18 17" fill="none">
					<path
						d="M8.22546 1.44149C8.29773 1.29993 8.40838 1.18096 8.54526 1.09784C8.68196 1.01472 8.83936 0.970703 8.99997 0.970703C9.16059 0.970703 9.31799 1.01472 9.45487 1.09784C9.59157 1.18096 9.70221 1.29993 9.77449 1.44149L11.5751 5.02419C11.6374 5.147 11.7288 5.25312 11.8416 5.33348C11.9544 5.41385 12.0854 5.46606 12.223 5.48568L16.2633 6.06166C16.421 6.08354 16.5697 6.14836 16.6923 6.24883C16.8149 6.34932 16.9066 6.48151 16.9575 6.6306C17.0073 6.7791 17.0135 6.93851 16.9752 7.09032C16.937 7.24214 16.856 7.38016 16.7416 7.4884L13.8184 10.2961C13.7197 10.3901 13.6456 10.5064 13.6028 10.635C13.5598 10.7636 13.5493 10.9006 13.5721 11.0341L14.2645 14.992C14.2908 15.1468 14.272 15.3058 14.2108 15.4507C14.1496 15.5956 14.0482 15.7205 13.9183 15.8111C13.7875 15.9032 13.6333 15.9578 13.4731 15.9687C13.3126 15.9796 13.1524 15.9463 13.01 15.8727L9.40329 14.0162C9.27908 13.9511 9.1406 13.9171 8.99997 13.9171C8.85935 13.9171 8.72086 13.9511 8.59665 14.0162L4.99892 15.8727C4.85655 15.9463 4.69627 15.9796 4.53591 15.9687C4.37554 15.9578 4.22139 15.9032 4.09056 15.8111C3.96068 15.7205 3.85931 15.5956 3.79806 15.4507C3.73683 15.3058 3.71822 15.1468 3.74435 14.992L4.43677 11.0341C4.45958 10.9006 4.44907 10.7636 4.40615 10.635C4.36323 10.5064 4.28922 10.3901 4.1905 10.2961L1.2584 7.4884C1.14398 7.38016 1.06297 7.24214 1.02474 7.09032C0.986537 6.93851 0.992676 6.7791 1.04247 6.6306C1.09327 6.48151 1.18511 6.34932 1.30773 6.24883C1.43033 6.14836 1.57886 6.08354 1.73667 6.06166L5.77699 5.48568C5.91476 5.46606 6.04557 5.41385 6.15836 5.33348C6.27114 5.25312 6.36251 5.147 6.4248 5.02419L8.22546 1.44149Z"
						stroke="#080B10" stroke-linecap="round" stroke-linejoin="round" />
				</svg>
				{_newFront.accountNavbar.refunds}
			</a>

			<a n:if="$user->isLoggedIn() && $user->getIdentity()->isActiveUser() === false" n:href=":NewFront:Account:User:tellFriend"
					class="flex gap-2 text-zinc-950 text-sm font-medium leading-normal px-6 py-3.5 xl:hover:underline">
				<svg xmlns="http://www.w3.org/2000/svg" width="19" height="17" viewBox="0 0 19 17"
					 fill="none">
					<path
							d="M1 7.17647H5.47368V16H1M7.84285 3.2826C7.84285 2.67726 8.09245 2.09669 8.53675 1.66861C8.98106 1.24052 9.58366 1 10.212 1C10.8403 1 11.443 1.24052 11.8873 1.66861C12.3316 2.09669 12.5812 2.67726 12.5812 3.2826V5.00433C12.5812 5.22177 12.6575 5.43268 12.7975 5.60316C12.9376 5.77364 13.1332 5.89351 13.3528 5.94346L15.6949 6.15216C16.3787 6.31977 16.9814 6.71002 17.4009 7.25681C17.8203 7.80346 18.0306 8.47311 17.9964 9.15215L17.7933 12.4782C17.6923 13.4468 17.2203 14.3443 16.4692 14.9955C15.7182 15.6467 14.7422 16.0048 13.7319 16H10.1037C9.72767 16.0009 9.35356 15.9482 8.99358 15.8434L6.52965 15.1782C6.18803 15.0744 5.8319 15.0216 5.47368 15.0217V8.69563C6.28563 8.03889 6.92388 7.20555 7.3358 6.26433C7.74773 5.32325 7.92151 4.30116 7.84285 3.2826Z"
							stroke="#080B10" stroke-miterlimit="10" stroke-linecap="round"
							stroke-linejoin="round" />
				</svg>
				<span>
					{_newFront.navbar.tellFriend, [upTo => ($recommendationBonusAmount |amount)] |noescape}
				</span>
			</a>

			<a n:href=":NewFront:Account:Notification:default"
					data-google-interstitial="false"
				class="flex gap-2 text-zinc-950 text-sm font-medium leading-normal px-6 py-3.5 xl:hover:underline">
				<svg xmlns="http://www.w3.org/2000/svg" width="18" height="20" viewBox="0 0 18 20" fill="none">
					{if $unreadNotificationsCount >= 1}
						<path
								d="M12.1387 5.0837C12.1387 5.72828 12.3948 6.34645 12.8505 6.80224C13.3063 7.25802 13.9245 7.51408 14.5691 7.51408C15.2136 7.51408 15.8318 7.25802 16.2876 6.80224C16.7434 6.34645 16.9994 5.72828 16.9994 5.0837C16.9994 4.43912 16.7434 3.82095 16.2876 3.36516C15.8318 2.90938 15.2136 2.65332 14.5691 2.65332C13.9245 2.65332 13.3063 2.90938 12.8505 3.36516C12.3948 3.82095 12.1387 4.43912 12.1387 5.0837Z"
								fill="#F72F49" stroke="#F72F49" stroke-linecap="round" stroke-linejoin="round" />
						<path
								d="M6.67089 17.9834C6.77379 18.3339 6.98745 18.6417 7.2799 18.8606C7.57231 19.0795 7.92788 19.1979 8.29316 19.1979C8.65845 19.1979 9.01401 19.0795 9.30639 18.8606C9.59884 18.6417 9.81256 18.3339 9.91544 17.9834M8.29114 2.79349V0.970703M14.3857 10.053C14.5388 14.7161 15.5823 15.553 15.5823 15.553H1C1 15.553 2.21519 14.0008 2.21519 8.86944C2.2161 7.91753 2.44043 6.97916 2.87013 6.1298C3.29983 5.28044 3.9229 4.5438 4.68921 3.97915C5.45553 3.41451 6.34368 3.03762 7.28221 2.87882C8.22074 2.72002 9.18341 2.78373 10.0929 3.06485"
								stroke="#080B10" stroke-linecap="round" stroke-linejoin="round" />
					{else}
						<path
								d="M6.67089 17.9834C6.77379 18.3339 6.98745 18.6417 7.2799 18.8606C7.57231 19.0795 7.92788 19.1979 8.29316 19.1979C8.65845 19.1979 9.01401 19.0795 9.30639 18.8606C9.59884 18.6417 9.81256 18.3339 9.91544 17.9834M8.29114 2.79349V0.970703M2.87013 6.1298C3.29983 5.28044 3.9229 4.5438 4.68921 3.97915C5.45553 3.41451 6.34368 3.03762 7.28221 2.87882C8.22074 2.72002 9.18341 2.78373 10.0929 3.06485C10.9025 3.33659 11.6456 3.79013 12.2706 4.39754C12.8956 5.00494 13.3794 5.75239 13.6888 6.58311C14.0077 7.44198 14.1901 8.39847 14.2 8.86944C14.2 14.0008 15.4152 15.553 15.4152 15.553H1C1 15.553 2.21519 14.0008 2.21519 8.86944C2.2161 7.91753 2.44043 6.97916 2.87013 6.1298Z"
								stroke="#080B10" stroke-linecap="round" stroke-linejoin="round" />
					{/if}
				</svg>
				{_newFront.accountNavbar.notifications}
				<span n:if="$unreadNotificationsCount >= 1">({$unreadNotificationsCount})</span>
			</a>
			<a n:href=":NewFront:Vouchers:Vouchers:default" n:if="$localization->isSlovak() || $localization->isCzech() || $user->getIdentity()->isAdmin()"
					data-google-interstitial="false"
					class="flex gap-2 text-zinc-950 text-sm font-medium leading-normal px-6 py-3.5 xl:hover:underline">
				<svg xmlns="http://www.w3.org/2000/svg" width="18" height="17" viewBox="0 0 688 686" fill="none">
					<g clip-path="url(#clip0_1529_173)">
						<path d="M57.5996 16V131.2C57.5996 140.037 64.7629 147.2 73.5996 147.2C82.4363 147.2 89.5996 140.037 89.5996 131.2V16C89.5996 7.16328 82.4363 0 73.5996 0C64.7629 0 57.5996 7.16328 57.5996 16Z" fill="black"/>
						<path d="M16 89.6001H131.2C140.037 89.6001 147.2 82.4368 147.2 73.6001C147.2 64.7634 140.037 57.6001 131.2 57.6001H16C7.16328 57.6001 0 64.7634 0 73.6001C0 82.4368 7.16328 89.6001 16 89.6001ZM597.543 253.696V368.896C597.543 377.733 604.706 384.896 613.543 384.896C622.38 384.896 629.543 377.733 629.543 368.896V253.696C629.543 244.859 622.38 237.696 613.543 237.696C604.706 237.696 597.543 244.859 597.543 253.696Z" fill="black"/>
						<path d="M555.943 327.296H671.143C679.98 327.296 687.143 320.133 687.143 311.296C687.143 302.459 679.98 295.296 671.143 295.296H555.943C547.107 295.296 539.943 302.459 539.943 311.296C539.943 320.133 547.107 327.296 555.943 327.296ZM134.4 154.36V234.44C134.4 243.277 141.564 250.44 150.4 250.44C159.237 250.44 166.4 243.277 166.4 234.44V154.36C166.4 145.523 159.237 138.36 150.4 138.36C141.564 138.36 134.4 145.523 134.4 154.36Z" fill="black"/>
						<path d="M110.36 210.4H190.44C199.277 210.4 206.44 203.237 206.44 194.4C206.44 185.563 199.277 178.4 190.44 178.4H110.36C101.524 178.4 94.3604 185.563 94.3604 194.4C94.3604 203.237 101.524 210.4 110.36 210.4ZM315.604 283.739L271.892 197.715C269.188 192.393 273.055 186.091 279.021 186.091H410.428C416.402 186.091 420.268 192.392 417.563 197.716L373.853 283.738C369.849 291.616 372.991 301.248 380.868 305.251C388.745 309.254 398.378 306.112 402.381 298.235L446.092 212.211C459.613 185.597 440.278 154.091 410.428 154.091H279.021C249.176 154.091 229.846 185.599 243.363 212.209L287.076 298.234C291.079 306.112 300.71 309.253 308.588 305.25C316.467 301.247 319.607 291.616 315.604 283.737V283.739Z" fill="black"/>
						<path d="M474.064 653.613C531.659 653.613 578.344 606.929 578.344 549.334V518.558C578.344 405.01 485.371 312.038 371.823 312.038H317.623C204.077 312.038 111.112 405.009 111.112 518.558V549.334C111.112 606.929 157.796 653.613 215.391 653.613H474.064ZM474.064 685.613H215.392C140.123 685.613 79.1123 624.602 79.1123 549.334V518.558C79.1123 387.336 186.405 280.038 317.624 280.038H371.824C503.044 280.038 610.344 387.337 610.344 518.558V549.334C610.344 624.602 549.332 685.613 474.064 685.613Z" fill="black"/>
					</g>
					<defs>
						<clipPath id="clip0_1529_173">
							<rect width="688" height="686" fill="white"/>
						</clipPath>
					</defs>
				</svg>
				{_newFront.accountNavbar.tipliExtra}
			</a>
			<a n:href=":NewFront:Static:addon" n:if="$isMobile === false"
					data-google-interstitial="false"
				class="hidden lg:flex gap-2 text-zinc-950 text-sm font-medium leading-normal px-6 py-3.5 xl:hover:underline">
				<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
					<path
						d="M7.34605 16.7978C5.93404 16.4995 4.62905 15.8247 3.56933 14.845C2.50961 13.8654 1.73464 12.6173 1.32654 11.233C0.918423 9.84871 0.892372 8.37984 1.25114 6.98193C1.60991 5.58406 2.34014 4.30926 3.36445 3.29262C4.38877 2.27598 5.66902 1.55536 7.06955 1.20711C8.47009 0.858862 9.93875 0.895956 11.3199 1.31446C12.7011 1.73296 13.9433 2.5173 14.915 3.58435C15.8867 4.65141 16.5517 5.96144 16.8394 7.37564M4.03937 15.2335L4.75048 11.6423H5.51848C5.68067 11.6428 5.84079 11.6059 5.98635 11.5344C6.13191 11.4629 6.25896 11.3586 6.3576 11.2299C6.45918 11.1041 6.52955 10.9561 6.56292 10.7978C6.59629 10.6396 6.59173 10.4758 6.5496 10.3197L6.01626 8.18635C5.96037 7.95502 5.82826 7.7493 5.64122 7.60224C5.45418 7.4552 5.22308 7.37538 4.98515 7.37565H1.19492M15.4172 4.17565H12.5158C12.2779 4.17538 12.0468 4.2552 11.8597 4.40226C11.6727 4.54931 11.5406 4.75504 11.4847 4.98632L11.2856 5.76854M11.6452 12.1635C11.6452 12.4464 11.7576 12.7177 11.9577 12.9177C12.1577 13.1178 12.429 13.2302 12.7119 13.2302C12.9948 13.2302 13.2661 13.1178 13.4661 12.9177C13.6662 12.7177 13.7785 12.4464 13.7785 12.1635C13.7785 11.8806 13.6662 11.6093 13.4661 11.4092C13.2661 11.2092 12.9948 11.0969 12.7119 11.0969C12.429 11.0969 12.1577 11.2092 11.9577 11.4092C11.7576 11.6093 11.6452 11.8806 11.6452 12.1635ZM13.615 8.03908L13.9563 9.07019C14.0092 9.24036 14.1241 9.3845 14.2781 9.47403C14.4322 9.56363 14.6143 9.592 14.7883 9.55374L15.8337 9.31197C16.0325 9.26624 16.2408 9.28558 16.4278 9.36708C16.6148 9.44857 16.7707 9.58795 16.8726 9.76466C16.9745 9.94137 17.017 10.1462 16.9938 10.3488C16.9706 10.5515 16.883 10.7414 16.7439 10.8906L16.0328 11.68C15.9108 11.8115 15.8431 11.9842 15.8431 12.1635C15.8431 12.3429 15.9108 12.5155 16.0328 12.6471L16.7439 13.4364C16.883 13.5855 16.9706 13.7755 16.9938 13.9781C17.017 14.1808 16.9745 14.3856 16.8726 14.5623C16.7707 14.7391 16.6148 14.8784 16.4278 14.9599C16.2408 15.0414 16.0325 15.0607 15.8337 15.0151L14.7883 14.7733C14.6143 14.735 14.4322 14.7634 14.2781 14.8529C14.1241 14.9425 14.0092 15.0866 13.9563 15.2569L13.6363 16.288C13.5806 16.4845 13.4623 16.6576 13.2993 16.7808C13.1363 16.904 12.9376 16.9707 12.7332 16.9707C12.5289 16.9707 12.3301 16.904 12.1672 16.7808C12.0042 16.6576 11.8858 16.4845 11.8301 16.288L11.5101 15.2569C11.4573 15.0866 11.3424 14.9425 11.1883 14.8529C11.0343 14.7634 10.8522 14.735 10.6781 14.7733L9.63276 15.0151C9.43393 15.0607 9.22572 15.0414 9.03869 14.9599C8.85167 14.8784 8.69572 14.7391 8.59382 14.5623C8.49192 14.3856 8.44947 14.1808 8.47265 13.9781C8.49583 13.7755 8.58337 13.5855 8.72253 13.4364L9.43365 12.6471C9.5556 12.5155 9.62337 12.3429 9.62337 12.1635C9.62337 11.9842 9.5556 11.8115 9.43365 11.68L8.72253 10.8906C8.58337 10.7414 8.49583 10.5515 8.47265 10.3488C8.44947 10.1462 8.49192 9.94137 8.59382 9.76466C8.69572 9.58795 8.85167 9.44857 9.03869 9.36708C9.22572 9.28558 9.43393 9.26624 9.63276 9.31197L10.6781 9.55374C10.8522 9.592 11.0343 9.56363 11.1883 9.47403C11.3424 9.3845 11.4573 9.24036 11.5101 9.07019L11.823 8.03908C11.8826 7.84807 12.0018 7.68117 12.1629 7.56263C12.3241 7.44409 12.5189 7.38017 12.719 7.38017C12.9191 7.38017 13.1139 7.44409 13.2751 7.56263C13.4363 7.68117 13.5554 7.84807 13.615 8.03908Z"
						stroke="#080B10" stroke-linecap="round" stroke-linejoin="round" />
				</svg>
				{_newFront.accountNavbar.addon}
			</a>
			<a n:href=":NewFront:Sign:out"
					data-google-interstitial="false"
				class="flex gap-2 text-zinc-950 text-sm font-medium leading-normal px-6 py-3.5 xl:hover:underline">
				<svg xmlns="http://www.w3.org/2000/svg" width="18" height="21" viewBox="0 0 18 21" fill="none">
					<path
						d="M8.99998 0.970703V9.79282M6.33334 4.14934C4.55411 4.78968 3.05454 6.04968 2.09966 7.70666C1.14479 9.36366 0.796107 11.3109 1.11523 13.2042C1.43435 15.0976 2.40073 16.8152 3.84358 18.0534C5.28642 19.2915 7.11284 19.9707 9.00001 19.9707C10.8871 19.9707 12.7136 19.2915 14.1564 18.0534C15.5993 16.8152 16.5657 15.0976 16.8848 13.2042C17.2039 11.3109 16.8552 9.36366 15.9003 7.70666C14.9455 6.04968 13.4459 4.78968 11.6667 4.14934"
						stroke="black" stroke-linecap="round" stroke-linejoin="round" />
				</svg>
				{_newFront.accountNavbar.signOut}
			</a>

			{if $user->getIdentity()->getPartnerOrganization()}
			<div class="w-full h-px bg-gray-200 rounded-lg mt-3 mb-6"></div>

			<div class="text-zinc-950 text-xs font-normal leading-[21px] px-6 mb-6 pr-8 navbar__partner-table-cell">
				{_newFront.accountNavbar.partner}
				<img src="{$user->getIdentity()->getPartnerOrganization()->getLogo() |image:null,45}"
					class="inline-block ml-1 homepage-header-partner__image">
			</div>
			{/if}

			{if $isThereMobileApp}
				<div class="w-full h-px bg-gray-200 rounded-lg"></div>

				<div class="grid grid-cols-2 gap-2 p-5">
					<a href="{$localization->getAppleUrl()}" target="_blank">
						<div class="border border-bg-light-4 w-full sm:w-[295px] max-w-full relative bg-white rounded-[10px]">
							<div class="relative">
								<img src="{$basePath}/new-design/footer/app-store.svg" loading="lazy" alt="app store" class="m-auto px-4 py-2">
								<span class="text-[8px] leading-[21px] text-dark-1 absolute top-0 left-0 w-full text-center">{_'newFront.phoneApp.getApple'}</span>
							</div>
						</div>
					</a>
					<a href="{$localization->getAndroidUrl()}" target="_blank">
						<div class="border border-bg-light-4 w-full sm:w-[295px] max-w-full relative bg-white rounded-[10px]">
							<div class="relative">
								<img src="{$basePath}/new-design/footer/google-play.svg" loading="lazy" alt="app store" class="m-auto px-4 py-2">
								<span class="text-[8px] leading-[21px] text-dark-1 absolute top-0 left-[-6px] w-full text-center">{_'newFront.phoneApp.getAndroid'}</span>
							</div>
						</div>
					</a>
				</div>
			{/if}
		</div>
	</div>
</div>
{/if}

{if $menuShowShops}

<div
	class="js-shops-menu hidden w-full absolute top-[114px] z-50">
	<div class="fixed w-full h-full bg-blue-950/70 backdrop-blur-[6px]"></div>
	<div class="js-shops-menu-inner relative bg-[image:linear-gradient(to_right,white_0%,white_50%,#f4f4f5_50%,#f4f4f5_100%)]">
		<div class="flex container ">
			<div class="w-[253px] grow-0 shrink-0">
				<div class="pt-8">
					<span class="block text-zinc-950 text-xl font-bold leading-[35px] mb-2 pr-11">
						{_newFront.navbar.title, ['count' => ($countOfCashbackShops |amount)]}
					</span>

					<div class="block text-zinc-950 text-base font-normal leading-7 mb-4 pr-11">
						{_newFront.navbar.text, ['percent' => '26,9']|noescape}
					</div>

					<a n:href=":NewFront:Shops:Shops:default"
						class="flex gap-2 items-center text-orange-500 text-sm font-bold leading-normal xl:hover:underline">
						{_newFront.navbar.link}
						<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
							<path d="M1 11L11 1M10.9984 9.4741L10.9983 1.00032L2.52456 1.00031" stroke="#EF7F1A"
								stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
						</svg>
					</a>

					<img src="{$basePath}/new-design/oslik-menu.png" alt="Donkey" loading="lazy" class="w-[232px] h-[228px] mt-12">
				</div>
			</div>
			<div class="bg-zinc-100 pt-8 pl-12 grow">
				<div class="text-zinc-950 text-base font-medium leading-7 mb-3.5">{_newFront.navbar.shopsTitle}</div>
				{cache 'navbarShopsGrid-' . $localization->getId(), 'expiration' => '6 hours'}
                    <div class="grid grid-cols-2 pb-5 gap-[15px] sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6">
                        {var $navbarShops = $getNavbarShops()}
                        {foreach $navbarShops as $shopItem}
                            {include "../../ShopsModule/Presenters/templates/Shops/shopItemDropdown.latte", shop: $shopItem}
                        {/foreach}
                    </div>
				{/cache}

				<div class="flex items-center justify-center">
					<a n:href=":NewFront:Shops:Shops:default"
						class="text-orange-500 text-sm font-bold leading-normal rounded-xl border border-gray-300 px-9 py-3 xl:hover:bg-pastel-orange-light xl:hover:border-primary-orange">
						{_newFront.navbar.cta, ['count' => ($countOfCashbackShops |amount)]}
					</a>
				</div>
			</div>
		</div>
	</div>
</div>
{/if}
{/define}

{define footer}


<div class="relative w-full bg-[#182B4A]" n:if="$presenter->getName() !== 'NewFront:Sign'">
	<div class="absolute hidden md:block w-full h-[331px] {ifset footerBackgroundColor}{include footerBackgroundColor}{else}bg-white{/ifset}{if $presenter->getName() === 'NewFront:LuckyShops:LuckyShops' || $presenter->getName() === 'NewFront:LuckyShops:Widgets'} md:hidden{/if}"></div>
	<div id="footer-background" class="md:hidden bg-no-repeat bg-center bg-cover relative lazy {if $presenter->getName() === 'NewFront:LuckyShops:LuckyShops' || $presenter->getName() === 'NewFront:LuckyShops:Widgets'} hidden{/if}">
		<div class="footer-sticker text-center leading-[21.5px] font-bold w-[86px] text-white">{_'newFront.footer.badge'}</div>
		<div class="h-[620px] overflow-hidden relative">
			<div class="pt-[61px] px-10">
				<div class="text-[26px] font-bold leading-[39px] text-white mb-[23px]">{_'newFront.footer.app'}</div>
				<a href="{$localization->getAppleUrl()}" target="_blank">
					<div class="border border-bg-light-4 w-full sm:w-[295px] max-w-full relative bg-white mt-6 mb-4 rounded-[10px]">
						<div class="relative">
							<img src="{$basePath}/new-design/footer/app-store.svg" loading="lazy" alt="app store" class="m-auto pt-5 pb-4">
							<div class="text-xs leading-[21px] text-dark-1 absolute top-[11px] left-0 w-full text-center">{_'newFront.phoneApp.getApple'}</div>
						</div>
					</div>
				</a>
				<a href="{$localization->getAndroidUrl()}" target="_blank">
					<div class="border border-bg-light-4 w-full sm:w-[295px] max-w-full relative bg-white mt-6 mb-4 rounded-[10px]">
						<div class="relative">
							<img src="{$basePath}/new-design/footer/google-play.svg" loading="lazy" alt="app store" class="m-auto pt-5 pb-4">
							<span class="text-xs leading-[21px] text-dark-1 absolute top-[15px] left-[-13px] w-full text-center">{_'newFront.phoneApp.getAndroid'}</span>
						</div>
					</div>
				</a>
			</div>
		</div>
	</div>
	<div class="relative hidden md:block bg-no-repeat bg-center z-10 {if $presenter->getName() === 'NewFront:LuckyShops:LuckyShops' || $presenter->getName() === 'NewFront:LuckyShops:Widgets'} md:hidden{/if}" style="background-image: url('{$basePath}/new-design/bg-footer-logged.png')">
		<div class="h-[569px]">
			<section class="container relative flex justify-between items-center">
				<div class="text-white text-[33px] leading-[49.5px] font-bold pt-[166px]">
					{_'newFront.footer.addonPromo.title'}
					<div class="mt-[9px] text-base leading-7 max-w-[302px] font-normal">{_'newFront.footer.addonPromo.text'}</div>
					<div class="flex">
						<a n:href=":NewFront:Static:addon" class="flex items-center gap-3 px-[41px] h-[56px] py-3x rounded-xl bg-white text-black text-base font-bold leading-[28px] mt-[30px] border border-transparent xl:hover:bg-transparent xl:hover:border-white xl:hover:text-white">
							{_'newFront.footer.addonPromo.cta'}
						</a>
					</div>
				</div>

				<div class="hidden xl:block absolute top-[56px] lg:top-[158px] left-[-2px] lg:left-[583px] w-[186px] bg-white p-5 rounded-[10px] rotate-3 z-40 shadow-lg">
					<div class="flex justify-between">
						{if $localization->isHungarian()}
							<img src="{$basePath}/images/tiplino_logo_new_color.svg" title="Tipli" alt="Tiplino" loading="lazy" class="w-[60px] h-[29px]">
						{else}
							<svg width="45" height="22" viewBox="0 0 78 39" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path fill-rule="evenodd" clip-rule="evenodd" d="M21.8419 7.28024H27.4368V27.3415H21.8419V7.28024ZM62.3468 27.1098V7.50599H67.482V27.1103L62.3468 27.1098ZM53.2192 27.1098V0.339844H58.3599V27.1103L53.2192 27.1098ZM44.8271 17.2604C44.8271 16.7853 44.7389 16.2929 44.5681 15.7818C44.3914 15.2713 44.1325 14.808 43.7673 14.3987C43.4003 13.9803 42.9524 13.6383 42.4536 13.3957C41.9294 13.1404 41.3407 13.0155 40.6812 13.0155H36.3999V21.4635H40.6812C41.3407 21.4635 41.9294 21.3268 42.4536 21.0538C43.4112 20.5591 44.1557 19.7269 44.5441 18.7153C44.7326 18.2225 44.8266 17.7415 44.8266 17.2609L44.8271 17.2604ZM30.9054 7.42876H40.6576C42.3655 7.42876 43.8377 7.73174 45.086 8.33724C46.3342 8.94275 47.359 9.72042 48.1658 10.6766C49.6897 12.4899 50.5378 14.7884 50.5573 17.1664C50.536 19.5748 49.6825 21.9042 48.148 23.7493C47.3068 24.76 46.2584 25.5826 45.0801 26.1595C43.8477 26.7477 42.4981 27.0548 41.134 27.0548C40.9878 27.0548 40.8374 27.0516 40.6921 27.0448L36.3999 27.0444V34.0148H30.9054V7.42876ZM9.75216 21.3091C9.28111 21.3091 8.79233 21.2201 8.28585 21.0479C7.24699 20.6845 6.39393 19.9173 5.91879 18.917C5.66577 18.3947 5.54178 17.7951 5.54178 17.136V13.1222H14.216V7.476H5.54131V0.339844H0V17.1123C0 18.828 0.300248 20.3179 0.901213 21.5707C1.50172 22.8294 2.27349 23.8624 3.22149 24.676C4.1695 25.4895 5.21199 26.0891 6.3426 26.4866C7.47912 26.8845 8.58658 27.0862 9.65814 27.0862C10.7297 27.0862 11.843 26.8845 12.9973 26.4866C19.1509 24.3611 19.463 18.6617 19.463 15.8118H13.9689C13.9571 17.0351 13.8631 18.2461 13.5156 18.917C13.2507 19.4335 12.921 19.8728 12.5262 20.2226C11.7576 20.9026 10.7742 21.2882 9.75216 21.3091ZM21.8419 0.339844H27.4368V5.98697H21.8419V0.339844Z" fill="#646C7C"></path>
								<path fill-rule="evenodd" clip-rule="evenodd" d="M62.3465 0.339844H67.4817V5.52319H62.3465V0.339844ZM74.6606 26.552L77.9998 29.7285C73.133 34.8664 68.2344 38.395 61.1623 38.5867C53.9952 38.2737 49.1076 34.5285 44.4961 29.7403L47.8116 26.5342C51.5042 30.3808 55.5742 33.6854 61.1973 33.9375C66.8531 33.7695 70.745 30.6779 74.6606 26.552Z" fill="#EF7F1A"></path>
							</svg>
						{/if}
						<svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none">
							<path d="M1.17053 9.0205L9.32664 1.80457M8.85656 9.4906L1.64064 1.33447" stroke="#80899C" stroke-width="0.888507" stroke-linecap="round"></path>
						</svg>
					</div>

					<div class="text-center bg-pastel-green-light py-[22px] my-[9px] rounded-[10px]">
						<div class="text-[56px] font-black leading-[59px] text-secondary-green tracking-[-8px]">{_'newFront.footer.addonPromo.sale.value'}</div>
						<div class="text-sm leading-[27px] text-dark-1">{_'newFront.footer.addonPromo.sale.text'}</div>
					</div>

					<button class="w-full text-white text-xs font-bold leading-[20px] pt-[13px] pb-[13px] bg-orange-gradient rounded-xl mb-[9px]">
						{_'newFront.footer.addonPromo.sale.getReward'}
					</button>

					<div class="text-center text-[10px] leading-[16px] underline text-dark-2">{_'newFront.footer.addonPromo.sale.show'}</div>
				</div>

				<div class="hidden xl:block absolute right-[625px] top-[186px] z-10">
					<div class="flex flex-col bg-white rounded-2xl relative w-[143px]">
						<div class="ml-[16px] mt-[15px]">
							{if $localization->isHungarian()}
								<img src="{$basePath}/images/tiplino_logo_new_color.svg" title="Tipli" alt="Tiplino" loading="lazy" class="w-[60px] h-[29px]">
							{else}
								<svg width="45" height="22" viewBox="0 0 78 39" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path fill-rule="evenodd" clip-rule="evenodd" d="M21.8419 7.28024H27.4368V27.3415H21.8419V7.28024ZM62.3468 27.1098V7.50599H67.482V27.1103L62.3468 27.1098ZM53.2192 27.1098V0.339844H58.3599V27.1103L53.2192 27.1098ZM44.8271 17.2604C44.8271 16.7853 44.7389 16.2929 44.5681 15.7818C44.3914 15.2713 44.1325 14.808 43.7673 14.3987C43.4003 13.9803 42.9524 13.6383 42.4536 13.3957C41.9294 13.1404 41.3407 13.0155 40.6812 13.0155H36.3999V21.4635H40.6812C41.3407 21.4635 41.9294 21.3268 42.4536 21.0538C43.4112 20.5591 44.1557 19.7269 44.5441 18.7153C44.7326 18.2225 44.8266 17.7415 44.8266 17.2609L44.8271 17.2604ZM30.9054 7.42876H40.6576C42.3655 7.42876 43.8377 7.73174 45.086 8.33724C46.3342 8.94275 47.359 9.72042 48.1658 10.6766C49.6897 12.4899 50.5378 14.7884 50.5573 17.1664C50.536 19.5748 49.6825 21.9042 48.148 23.7493C47.3068 24.76 46.2584 25.5826 45.0801 26.1595C43.8477 26.7477 42.4981 27.0548 41.134 27.0548C40.9878 27.0548 40.8374 27.0516 40.6921 27.0448L36.3999 27.0444V34.0148H30.9054V7.42876ZM9.75216 21.3091C9.28111 21.3091 8.79233 21.2201 8.28585 21.0479C7.24699 20.6845 6.39393 19.9173 5.91879 18.917C5.66577 18.3947 5.54178 17.7951 5.54178 17.136V13.1222H14.216V7.476H5.54131V0.339844H0V17.1123C0 18.828 0.300248 20.3179 0.901213 21.5707C1.50172 22.8294 2.27349 23.8624 3.22149 24.676C4.1695 25.4895 5.21199 26.0891 6.3426 26.4866C7.47912 26.8845 8.58658 27.0862 9.65814 27.0862C10.7297 27.0862 11.843 26.8845 12.9973 26.4866C19.1509 24.3611 19.463 18.6617 19.463 15.8118H13.9689C13.9571 17.0351 13.8631 18.2461 13.5156 18.917C13.2507 19.4335 12.921 19.8728 12.5262 20.2226C11.7576 20.9026 10.7742 21.2882 9.75216 21.3091ZM21.8419 0.339844H27.4368V5.98697H21.8419V0.339844Z" fill="#646C7C"></path>
									<path fill-rule="evenodd" clip-rule="evenodd" d="M62.3465 0.339844H67.4817V5.52319H62.3465V0.339844ZM74.6606 26.552L77.9998 29.7285C73.133 34.8664 68.2344 38.395 61.1623 38.5867C53.9952 38.2737 49.1076 34.5285 44.4961 29.7403L47.8116 26.5342C51.5042 30.3808 55.5742 33.6854 61.1973 33.9375C66.8531 33.7695 70.745 30.6779 74.6606 26.552Z" fill="#EF7F1A"></path>
								</svg>
							{/if}
						</div>
						<div class="mt-[14px]">
							<svg class="m-auto" xmlns="http://www.w3.org/2000/svg" width="29" height="25" viewBox="0 0 29 25" fill="none">
								<path d="M23.2497 14.9626C23.5658 13.3402 25.1391 12.28 26.7615 12.5961L26.9906 11.4205C27.9068 6.71787 26.9602 5.31317 22.2576 4.39695L10.5011 2.1064C5.79853 1.19018 4.39383 2.13677 3.47761 6.83936L3.36308 7.42718C4.98548 7.74328 6.04566 9.31654 5.72956 10.9389C5.41347 12.5613 3.8402 13.6215 2.21781 13.3054L2.10328 13.8932C1.18706 18.5958 2.13365 20.0005 6.83624 20.9168L18.5927 23.2073C23.2953 24.1235 24.7 23.1769 25.6162 18.4743C23.9938 18.1582 22.9337 16.585 23.2497 14.9626Z" stroke="#EC7700" stroke-width="1.59467" stroke-linecap="round" stroke-linejoin="round"/>
								<path d="M14.0264 2.79337L10.3615 21.6037" stroke="#EC7700" stroke-width="1.59467" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="2.74 2.74"/>
							</svg>
							<div class="text-center text-[18px] text-dark-1 font-bold leading-[26px]">{_'newFront.footer.addonPromo.coupon.title'}</div>
							<div class="text-center text-[9px] text-dark-1 leading-[16px]">{_'newFront.footer.addonPromo.coupon.text'}</div>
						</div>

						<img class="absolute -left-[23px] bottom-[80px]" src="/new-design/ellipse-orange.svg" loading="lazy" alt="ellipse">


						<div class="my-4">
							<svg class="m-auto" xmlns="http://www.w3.org/2000/svg" width="81" height="4" viewBox="0 0 81 4" fill="none">
								<path fill-rule="evenodd" clip-rule="evenodd" d="M17.6298 0.566481C17.4988 0.554813 17.3658 0.561627 17.2415 0.586362C17.1172 0.611097 17.0051 0.653063 16.9143 0.708852L14.3518 2.28473L11.6348 0.732868C11.4725 0.640446 11.2569 0.58911 11.0348 0.590026C10.8128 0.590942 10.6024 0.644045 10.4494 0.737768L7.88631 2.31324L5.17029 0.761589C5.0082 0.668902 4.79247 0.61738 4.57031 0.618297C4.34815 0.619213 4.13766 0.672498 3.98489 0.766484L0.847979 2.69579C0.697908 2.79042 0.617954 2.91734 0.625488 3.04897C0.633022 3.1806 0.727436 3.30633 0.888221 3.39888C1.04901 3.49143 1.26319 3.54333 1.4843 3.54331C1.70541 3.54329 1.91559 3.49134 2.06923 3.39876L4.63137 1.82284L7.34841 3.37459C7.51075 3.46698 7.72637 3.51829 7.94837 3.51738C8.17036 3.51646 8.38078 3.46338 8.53383 3.36969L11.0965 1.79419L13.8135 3.34604C13.9759 3.43836 14.1915 3.48961 14.4134 3.48867C14.6353 3.48773 14.8457 3.43466 14.9987 3.34101L17.5606 1.76532L20.2777 3.31705C20.3577 3.36398 20.4523 3.40119 20.5559 3.42653C20.6595 3.45187 20.77 3.46483 20.8811 3.46466C20.9921 3.46449 21.1016 3.45119 21.203 3.42553C21.3044 3.39988 21.3958 3.36238 21.4718 3.31521C21.5479 3.26804 21.6071 3.21214 21.6461 3.15075C21.685 3.08935 21.703 3.02369 21.6988 2.95756C21.6946 2.89143 21.6684 2.82615 21.6218 2.76551C21.5751 2.70486 21.5089 2.65007 21.427 2.60428L18.0998 0.703957C17.9704 0.629971 17.8058 0.581827 17.6298 0.566481Z" fill="#D7DBE0"></path>
								<path fill-rule="evenodd" clip-rule="evenodd" d="M37.0263 0.423023C36.8953 0.411355 36.7623 0.418169 36.638 0.442904C36.5137 0.467639 36.4016 0.509605 36.3108 0.565394L33.7483 2.14128L31.0313 0.589409C30.869 0.496988 30.6533 0.445652 30.4313 0.446568C30.2093 0.447484 29.9989 0.500586 29.8459 0.59431L27.2828 2.16978L24.5668 0.61813C24.4047 0.525443 24.189 0.473921 23.9668 0.474838C23.7446 0.475755 23.5341 0.52904 23.3814 0.623026L20.2445 2.55233C20.0944 2.64697 20.0144 2.77388 20.022 2.90551C20.0295 3.03714 20.1239 3.16288 20.2847 3.25542C20.4455 3.34797 20.6597 3.39988 20.8808 3.39985C21.1019 3.39983 21.3121 3.34789 21.4657 3.2553L24.0279 1.67938L26.7449 3.23113C26.9072 3.32352 27.1229 3.37484 27.3448 3.37392C27.5668 3.373 27.7773 3.31993 27.9303 3.22623L30.493 1.65073L33.21 3.20259C33.3724 3.2949 33.588 3.34615 33.8099 3.34521C34.0318 3.34427 34.2422 3.2912 34.3952 3.19755L36.9571 1.62186L39.6741 3.17359C39.7542 3.22052 39.8488 3.25774 39.9524 3.28307C40.056 3.30841 40.1665 3.32137 40.2776 3.3212C40.3886 3.32103 40.4981 3.30773 40.5995 3.28208C40.7009 3.25642 40.7923 3.21893 40.8683 3.17176C40.9444 3.12459 41.0036 3.06868 41.0426 3.00729C41.0815 2.94589 41.0994 2.88023 41.0953 2.8141C41.0911 2.74797 41.0649 2.68269 41.0183 2.62205C40.9716 2.56141 40.9054 2.50661 40.8235 2.46082L37.4963 0.560499C37.3669 0.486513 37.2023 0.438369 37.0263 0.423023Z" fill="#D7DBE0"></path>
								<path fill-rule="evenodd" clip-rule="evenodd" d="M56.7708 0.566481C56.6399 0.554813 56.5069 0.561627 56.3826 0.586362C56.2583 0.611097 56.1462 0.653063 56.0554 0.708852L53.4928 2.28473L50.7758 0.732868C50.6135 0.640446 50.3979 0.58911 50.1759 0.590026C49.9539 0.590942 49.7434 0.644045 49.5904 0.737768L47.0274 2.31324L44.3113 0.761589C44.1492 0.668902 43.9335 0.61738 43.7114 0.618297C43.4892 0.619213 43.2787 0.672498 43.1259 0.766484L39.989 2.69579C39.839 2.79042 39.759 2.91734 39.7665 3.04897C39.7741 3.1806 39.8685 3.30633 40.0293 3.39888C40.19 3.49143 40.4042 3.54333 40.6253 3.54331C40.8465 3.54329 41.0566 3.49134 41.2103 3.39876L43.7724 1.82284L46.4895 3.37459C46.6518 3.46698 46.8674 3.51829 47.0894 3.51738C47.3114 3.51646 47.5218 3.46338 47.6749 3.36969L50.2375 1.79419L52.9546 3.34604C53.1169 3.43836 53.3325 3.48961 53.5544 3.48867C53.7764 3.48773 53.9867 3.43466 54.1398 3.34101L56.7017 1.76532L59.4187 3.31705C59.4988 3.36398 59.5933 3.40119 59.6969 3.42653C59.8005 3.45187 59.911 3.46483 60.0221 3.46466C60.1332 3.46449 60.2426 3.45119 60.344 3.42553C60.4454 3.39988 60.5368 3.36238 60.6129 3.31521C60.6889 3.26804 60.7482 3.21214 60.7871 3.15075C60.8261 3.08935 60.844 3.02369 60.8398 2.95756C60.8357 2.89143 60.8095 2.82615 60.7628 2.76551C60.7162 2.70486 60.65 2.65007 60.5681 2.60428L57.2408 0.703957C57.1115 0.629971 56.9469 0.581827 56.7708 0.566481Z" fill="#D7DBE0"></path>
								<path fill-rule="evenodd" clip-rule="evenodd" d="M76.1673 0.423023C76.0364 0.411355 75.9034 0.418169 75.7791 0.442904C75.6547 0.467639 75.5427 0.509605 75.4519 0.565394L72.8893 2.14128L70.1723 0.589409C70.01 0.496988 69.7944 0.445652 69.5724 0.446568C69.3504 0.447484 69.1399 0.500586 68.9869 0.59431L66.4238 2.16978L63.7078 0.61813C63.5457 0.525443 63.33 0.473921 63.1078 0.474838C62.8857 0.475755 62.6752 0.52904 62.5224 0.623026L59.3855 2.55233C59.2354 2.64697 59.1555 2.77388 59.163 2.90551C59.1706 3.03714 59.265 3.16288 59.4257 3.25542C59.5865 3.34797 59.8007 3.39988 60.0218 3.39985C60.2429 3.39983 60.4531 3.34789 60.6068 3.2553L63.1689 1.67938L65.8859 3.23113C66.0483 3.32352 66.2639 3.37484 66.4859 3.37392C66.7079 3.373 66.9183 3.31993 67.0714 3.22623L69.634 1.65073L72.351 3.20259C72.5134 3.2949 72.729 3.34615 72.9509 3.34521C73.1729 3.34427 73.3832 3.2912 73.5363 3.19755L76.0981 1.62186L78.8152 3.17359C78.8953 3.22052 78.9898 3.25774 79.0934 3.28307C79.197 3.30841 79.3075 3.32137 79.4186 3.3212C79.5297 3.32103 79.6391 3.30773 79.7405 3.28208C79.8419 3.25642 79.9333 3.21893 80.0094 3.17176C80.0854 3.12459 80.1447 3.06868 80.1836 3.00729C80.2226 2.94589 80.2405 2.88023 80.2363 2.8141C80.2321 2.74797 80.206 2.68269 80.1593 2.62205C80.1127 2.56141 80.0465 2.50661 79.9645 2.46082L76.6373 0.560499C76.5079 0.486513 76.3434 0.438369 76.1673 0.423023Z" fill="#D7DBE0"></path>
							</svg>
						</div>

						<div class="text-center text-dark-1 text-[9px] leading-[15px] mb-[6px] font-bold">
							{_'newFront.footer.addonPromo.coupon.value'}
						</div>

						<div class="px-3 pb-2 w-full">
							<button class="w-full py-2 relative z-20 rounded-xl bg-orange-gradient text-white font-medium text-[9px] leading-[16px] mb-[7px]">
								{_'newFront.footer.addonPromo.sale.getReward'}
							</button>
							<div class="text-center text-[7px] leading-[12px] text-dark-2">{_'newFront.footer.addonPromo.sale.show'}</div>
						</div>
					</div>
				</div>

				<svg class="hidden xl:block absolute left-[317px] top-[260px]" width="156" height="69" viewBox="0 0 156 69" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path opacity="0.7" d="M1.8867 31.3629C1.06061 31.3006 0.441417 30.5805 0.503684 29.7544L1.51838 16.2926C1.58064 15.4665 2.30079 14.8473 3.12688 14.9096C3.95296 14.9718 4.57216 15.692 4.50989 16.5181L3.60794 28.4841L15.574 29.3861C16.4001 29.4483 17.0193 30.1685 16.957 30.9946C16.8947 31.8207 16.1746 32.4399 15.3485 32.3776L1.8867 31.3629ZM115.89 37.77L114.415 38.0452L115.89 37.77ZM88.3997 44.0665L86.939 43.7255L86.939 43.7255L88.3997 44.0665ZM153.612 0.500123C154.441 0.500123 155.112 1.1717 155.112 2.00012C155.112 2.82855 154.441 3.50012 153.612 3.50012L153.612 0.500123ZM1.0215 28.7298C26.4006 6.90841 53.4939 -0.883719 75.2855 1.76254C97.0849 4.40975 113.645 17.5581 117.364 37.4949L114.415 38.0452C110.987 19.6688 95.722 7.26629 74.9238 4.74066C54.1178 2.21409 27.8402 9.62711 2.97738 31.0045L1.0215 28.7298ZM117.364 37.4949C119.412 48.467 118.345 56.3497 115.408 61.502C112.43 66.7264 107.612 68.9802 102.722 68.7298C93.0544 68.2347 83.5883 58.0758 86.939 43.7255L89.8604 44.4076C86.9038 57.07 95.2089 65.3411 102.876 65.7337C106.654 65.9272 110.394 64.2393 112.801 60.0164C115.25 55.7214 116.394 48.6513 114.415 38.0452L117.364 37.4949ZM86.939 43.7255C92.1719 21.3135 115.328 0.500123 153.612 0.500123L153.612 3.50012C116.452 3.50012 94.719 23.5992 89.8604 44.4076L86.939 43.7255Z" fill="white"/>
				</svg>
			</section>
		</div>

		{*<div class="h-[569px]">
			<section class="container flex justify-between items-center">
				<div class="text-white text-[33px] leading-[49.5px] font-bold">
					{_newFront.footer.promo.text}
					<div class="flex">
						<a n:href=":NewFront:Homepage:default"
							class="flex items-center gap-3 px-7 py-3 rounded-xl bg-white text-black text-base font-bold leading-[28px] mt-[30px] border border-transparent xl:hover:bg-transparent xl:hover:border-white xl:hover:text-white">
							{_newFront.footer.promo.cta}
							<svg xmlns="http://www.w3.org/2000/svg" width="19" height="20" viewBox="0 0 19 20" fill="none">
								<path d="M4.19576 15.0328L14.0938 5.13477" stroke="currentColor" stroke-width="2"
									stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
								<path d="M14.0921 13.5224L14.0921 5.13508L5.70477 5.13507" stroke="currentColor" stroke-width="2"
									stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
							</svg>
						</a>
					</div>
				</div>
				<img src="{$basePath}/new-design/hp-footer-donkey.png" class="relative -top-[85px]" alt="donkey">
			</section>
		</div>*}
	</div>

	{var $facebookLink = ('front.link.facebook'|translate)}
	{var $twitterLink = ('front.link.twitter'|translate)}
	{var $instagramLink = ('front.link.instagram'|translate)}

	<div class="container max-w-[1240px]">
		<div class="text-[26px] leading-[39px] text-white font-medium text-center pt-[46px] md:hidden">{_'newFront.footer.help'}</div>
		<div class="leading-[39px] font-bold text-primary-orange text-center text-[26px] md:hidden">
			<span class="support-email" data-email="{_'newFront.footer.helpEmail'}"></span>
		</div>
		<div class="flex justify-center mt-[28px] items-center gap-[80px] md:hidden">
			<a href="{$facebookLink}" data-google-interstitial="false" n:if="$facebookLink" target="_blank" rel="nofollow">
				<img src="{$basePath}/new-design/footer/fb.svg" alt="Facebook" loading="lazy" class="">
			</a>
			<a href="{$twitterLink}" data-google-interstitial="false" n:if="$twitterLink && $localization->isBulgarian() === false && $localization->isSlovenian() === false && $localization->isCroatian() === false" target="_blank" rel="nofollow">
				<img src="{$basePath}/new-design/footer/x.svg" alt="X" loading="lazy" class="">
			</a>
			<a href="{$instagramLink}" data-google-interstitial="false" n:if="$instagramLink && $localization->isSlovenian() === false" target="_blank" rel="nofollow">
				<img src="{$basePath}/new-design/footer/instagram.svg" alt="Instagram" loading="lazy" class="">
			</a>
		</div>
		<div class="md:hidden w-fill h-px opacity-30 bg-white mt-10 mb-4"></div>
		<div class="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-5 gap-3 md:gap-6 md:pt-12 md:mb-[74px] flex-1">
			<div>
				<div class="flex justify-between items-center">
					<h4 id="stores-header"
						class="text-white text-sm md:text-xl font-medium leading-[35px] md:mb-4 w-full text-left cursor-pointer">
						{_'newFront.footer.navbar.shopSection.title'}
					</h4>
					<svg id="stores-arrow" class="md:hidden transition-transform" xmlns="http://www.w3.org/2000/svg"
						width="13" height="7" viewBox="0 0 13 7" fill="none">
						<path d="M11.9961 1L6.49609 6L0.996094 1" stroke="white" stroke-width="2" stroke-linecap="round"
							stroke-linejoin="round" />
					</svg>
				</div>
				<ul id="stores" class="hidden my-6 md:my-0 md:block">
				    {cache 'footerShops-' . $localization->getId(), 'expiration' => '6 hours'}
				        {var $footerShops = $getFooterShops()}

                        <li n:foreach="$footerShops as $footerShop"><a
                                n:href=":NewFront:Shops:Shop:default, shop: $footerShop"
									data-google-interstitial="false"
                                class="text-white text-sm font-normal leading-[35px] xl:hover:text-primary-orange">{$footerShop->getName()}</a>
                        </li>
					{/cache}
				</ul>
			</div>
			<div class="md:hidden w-fill h-px opacity-30 bg-white"></div>
			<div>
				<div class="flex justify-between items-center">
					<h4 id="about-tipli-header"
						class="text-white text-sm md:text-xl font-medium leading-[35px] md:mb-4 w-full text-left cursor-pointer">
						{_'newFront.footer.navbar.about.title'}
					</h4>
					<svg id="about-tipli-arrow" class="md:hidden transition-transform"
						xmlns="http://www.w3.org/2000/svg" width="13" height="7" viewBox="0 0 13 7" fill="none">
						<path d="M11.9961 1L6.49609 6L0.996094 1" stroke="white" stroke-width="2" stroke-linecap="round"
							stroke-linejoin="round" />
					</svg>
				</div>
				<ul id="about-tipli" class="hidden my-6 md:my-0 md:block mb-8">
					<li><a n:href=":NewFront:Static:howItWorks" data-google-interstitial="false"
							class="text-white text-sm font-normal leading-[35px] xl:hover:text-primary-orange">{_'newFront.footer.navbar.about.howItWorks'}</a>
					</li>
					<li n:if="$user->isLoggedIn()"><a n:href=":NewFront:Account:User:guarantee" data-google-interstitial="false"
							class="text-white text-sm font-normal leading-[35px] xl:hover:text-primary-orange">{_'newFront.footer.navbar.about.guarantee'}</a>
					</li>
					<li><a n:href=":NewFront:Static:faq"
							class="text-white text-sm font-normal leading-[35px] xl:hover:text-primary-orange">{_'newFront.footer.navbar.about.faq'}</a>
					</li>
					<li n:if="$isThereAddon === true && $isMobile === false"><a n:href=":NewFront:Static:addon" data-google-interstitial="false"
							class="text-white text-sm font-normal leading-[35px] xl:hover:text-primary-orange">{_'newFront.footer.navbar.about.addon'}</a>
					</li>
					<li n:if="$isThereMobileApp === true"><a n:href=":NewFront:Static:phoneApp" data-google-interstitial="false"
							class="text-white text-sm font-normal leading-[35px] xl:hover:text-primary-orange">{_'newFront.footer.navbar.about.app'}</a>
					</li>
					<li
						n:if="$localization->isBulgarian() === false && $localization->isCroatian() === false && $localization->isSlovenian() === false">
						<a n:href=":NewFront:Articles:Articles:default" data-google-interstitial="false"
							class="text-white text-sm font-normal leading-[35px] xl:hover:text-primary-orange">{_'newFront.footer.navbar.about.articles'}</a>
					</li>
					<li><a n:href=":NewFront:Deals:Deals:default" data-google-interstitial="false"
							class="text-white text-sm font-normal leading-[35px] xl:hover:text-primary-orange">{_'newFront.footer.navbar.about.sales'}</a>
					</li>
					<li n:if="$isThereLeaflets">
						<a n:href=":NewFront:Leaflets:Leaflet:default" data-google-interstitial="false"
								class="text-white text-sm font-normal leading-[35px] xl:hover:text-primary-orange">{_'newFront.footer.navbar.about.leaflets'}</a>
					</li>
				</ul>
				<h4
					class="text-white text-sm md:text-xl font-medium leading-[35px] mb-4 mt-6 w-full text-left hidden md:block">
					{_'newFront.footer.otherCountries.title'}
				</h4>
				<div class="hidden md:flex gap-3 flex-wrap">
					<a href="https://www.tipli.cz" data-google-interstitial="false"><img src="{$basePath}/new-design/flags/cs.svg" alt="cs" class="" loading="lazy"></a>
					<a href="https://www.tipli.sk" data-google-interstitial="false"><img src="{$basePath}/new-design/flags/sk.svg" alt="sk" class="" loading="lazy"></a>
					<a href="https://www.tipli.pl" data-google-interstitial="false"><img src="{$basePath}/new-design/flags/pl.svg" alt="pl" class="" loading="lazy"></a>
					<a href="https://www.tipli.ro" data-google-interstitial="false"><img src="{$basePath}/new-design/flags/ro.svg" alt="ro" class="" loading="lazy"></a>
					<a href="https://www.tiplino.hu" data-google-interstitial="false"><img src="{$basePath}/new-design/flags/hu.svg" alt="hu" class="" loading="lazy"></a>
					<a href="https://www.tipli.hr" data-google-interstitial="false"><img src="{$basePath}/new-design/flags/hr.svg" alt="hr" class="" loading="lazy"></a>
					<a href="https://www.tipli.bg" data-google-interstitial="false"><img src="{$basePath}/new-design/flags/bg.svg" alt="bg" class="" loading="lazy"></a>
					<a href="https://www.tipli.si" data-google-interstitial="false"><img src="{$basePath}/new-design/flags/si.svg" alt="si" class="" loading="lazy"></a>
				</div>
			</div>
			<div class="md:hidden w-fill h-px opacity-30 bg-white"></div>
			<div>
				<div class="flex justify-between items-center">
					<h4 id="more-about-tipli-header"
						class="text-white text-sm md:text-xl font-medium leading-[35px] md:mb-4 w-full text-left cursor-pointer">
						{_'newFront.footer.navbar.info.title'}
					</h4>
					<svg id="more-about-tipli-arrow" class="md:hidden transition-transform"
						xmlns="http://www.w3.org/2000/svg" width="13" height="7" viewBox="0 0 13 7" fill="none">
						<path d="M11.9961 1L6.49609 6L0.996094 1" stroke="white" stroke-width="2" stroke-linecap="round"
							stroke-linejoin="round" />
					</svg>
				</div>
				<ul id="more-about-tipli" class="hidden my-6 md:my-0 md:block">
					<li class="hidden">
						<a n:href=":NewFront:Jobs:Job:jobs" data-google-interstitial="false"
								class="text-white text-sm font-normal leading-[35px] xl:hover:text-primary-orange">{_'newFront.footer.navbar.info.career'}</a>
					</li>
					<li>
						<a n:href=":NewFront:Static:conditions" data-google-interstitial="false"
								class="text-white text-sm font-normal leading-[35px] xl:hover:text-primary-orange">{_'newFront.footer.navbar.info.conditions'}</a>
					</li>
					<li>
						<a n:href=":NewFront:Static:privacyPolicy" data-google-interstitial="false"
								class="text-white text-sm font-normal leading-[35px] xl:hover:text-primary-orange">{_'newFront.footer.navbar.info.privacyPolicy'}</a>
					</li>
					<li>
						<a n:href=":NewFront:Static:cookies" data-google-interstitial="false"
								class="text-white text-sm font-normal leading-[35px] xl:hover:text-primary-orange">{_'newFront.footer.navbar.info.cookies'}</a>
					</li>
					<li>
						<a n:href=":NewFront:Jobs:Job:jobs" data-google-interstitial="false"
								class="text-white text-sm font-normal leading-[35px] xl:hover:text-primary-orange">{_'newFront.footer.navbar.info.career'}</a>
					</li>
				</ul>
			</div>
			<div class="md:hidden w-fill h-px opacity-30 bg-white"></div>
			<div>
				<div class="flex justify-between items-center">
					<h4 id="help-header"
						class="text-white text-sm md:text-xl font-medium leading-[35px] md:mb-4 w-full text-left">
						{_'newFront.footer.navbar.support.title'}
					</h4>
					<svg id="help-arrow" class="md:hidden transition-transform" xmlns="http://www.w3.org/2000/svg"
						width="13" height="7" viewBox="0 0 13 7" fill="none">
						<path d="M11.9961 1L6.49609 6L0.996094 1" stroke="white" stroke-width="2" stroke-linecap="round"
							stroke-linejoin="round" />
					</svg>
				</div>
				<ul id="help" class="hidden my-6 md:my-0 md:block">
					<li>
						<a n:href=":NewFront:Account:Refund:default" data-google-interstitial="false"
							class="text-white text-sm font-normal leading-[35px] xl:hover:text-primary-orange">{_'newFront.footer.navbar.support.reward'}</a>
					</li>
					<li>
						<a n:href=":NewFront:Account:Refund:default#missing-payout" data-google-interstitial="false"
							class="text-white text-sm font-normal leading-[35px] xl:hover:text-primary-orange">{_'newFront.footer.navbar.support.payout'}</a>
					</li>
					<li>
						<a n:href=":NewFront:Static:contact" data-google-interstitial="false"
							class="text-white text-sm font-normal leading-[35px] xl:hover:text-primary-orange">{_'newFront.footer.navbar.support.answer'}</a>
					</li>
					<li>
						<a n:href=":NewFront:Sign:forgottenPassword" data-google-interstitial="false"
							class="text-white text-sm font-normal leading-[35px] xl:hover:text-primary-orange">{_'newFront.footer.navbar.support.forgottenPassword'}</a>
					</li>
					<li>
						<a n:href=":NewFront:Static:contact" data-google-interstitial="false"
								class="text-white text-sm font-normal leading-[35px] xl:hover:text-primary-orange">{_'newFront.footer.navbar.support.contact'}</a>
					</li>
				</ul>
			</div>
			<div class="md:hidden w-fill h-px opacity-30 bg-white"></div>
			<div class="hidden lg:block">
				<h4
					class="text-white text-sm md:text-xl font-medium leading-[35px] md:mb-4 w-full text-left">
					{_'newFront.footer.navbar.application.title'}
				</h4>
				<div class="hidden lg:block">
					<a href="{$localization->getAppleUrl()}" data-google-interstitial="false" target="_blank" style="margin: 10px 0;">
						<div class="border border-bg-light-4 w-[180px] bg-white relative mt-6 mb-4 rounded-[10px]">
							<img src="{$basePath}/new-design/footer/app-store.svg" alt="app store" loading="lazy" class="px-6 pt-3 pb-2">
							<div class="text-xs leading-[21px] text-dark-1 absolute top-[5px] left-[58px]">{_'newFront.phoneApp.getApple'}</div>
						</div>
					</a>

					<a href="{$localization->getAndroidUrl()}" data-google-interstitial="false" target="_blank" style="margin: 10px 0;">
						<div class="border border-bg-light-4 w-[180px] bg-white relative mt-6 mb-4 rounded-[10px]">
							<img src="{$basePath}/new-design/footer/google-play.svg" alt="app store" loading="lazy" class="px-6 pt-3 pb-2">
							<div class="text-xs leading-[21px] text-dark-1 absolute top-[5px] left-[59px]">{_'newFront.phoneApp.getAndroid'}</div>
						</div>
					</a>

				</div>
			</div>
			<div class="md:hidden">
				<div class="flex justify-between items-center">
					<h4 id="tipli-countries-header"
						class="text-white text-sm md:text-xl font-medium leading-[35px] mb-4 w-full text-left">
						{_'newFront.footer.otherCountries.title'}
					</h4>
					<svg id="tipli-countries-arrow" class="md:hidden mb-5 transition-transform"
						xmlns="http://www.w3.org/2000/svg" width="13" height="7" viewBox="0 0 13 7" fill="none">
						<path d="M11.9961 1L6.49609 6L0.996094 1" stroke="white" stroke-width="2" stroke-linecap="round"
							stroke-linejoin="round" />
					</svg>
				</div>
				<div id="tipli-countries" class="hidden my-12 md:my-0 md:flex gap-3">
					<div class="flex gap-2">
						<a href="https://www.tipli.cz" data-google-interstitial="false"><img src="{$basePath}/new-design/flags/cs.svg" alt="cs" class="" loading="lazy"></a>
						<a href="https://www.tipli.sk" data-google-interstitial="false"><img src="{$basePath}/new-design/flags/sk.svg" alt="sk" class="" loading="lazy"></a>
						<a href="https://www.tipli.pl" data-google-interstitial="false"><img src="{$basePath}/new-design/flags/pl.svg" alt="pl" class="" loading="lazy"></a>
						<a href="https://www.tipli.ro" data-google-interstitial="false"><img src="{$basePath}/new-design/flags/ro.svg" alt="ro" class="" loading="lazy"></a>
						<a href="https://www.tiplino.hu" data-google-interstitial="false"><img src="{$basePath}/new-design/flags/hu.svg" alt="hu" class="" loading="lazy"></a>
						<a href="https://www.tipli.hr" data-google-interstitial="false"><img src="{$basePath}/new-design/flags/hr.svg" alt="hr" class="" loading="lazy"></a>
						<a href="https://www.tipli.bg" data-google-interstitial="false"><img src="{$basePath}/new-design/flags/bg.svg" alt="bg" class="" loading="lazy"></a>
						<a href="https://www.tipli.si" data-google-interstitial="false"><img src="{$basePath}/new-design/flags/si.svg" alt="si" class="" loading="lazy"></a>
					</div>
				</div>
			</div>
		</div>

		<div class="w-fill h-px opacity-30 bg-white mb-14"></div>
		<div class="hidden md:flex items-center mb-14 ">
			<div class="flex">
				<div class="text-white text-[40px] font-light leading-[58px]">
					{_'newFront.footer.help'}
				</div>
				<span class="support-email text-orange-500 text-[40px] font-bold underline leading-[58px] ml-4 xl:hover:no-underline" data-email="{_'newFront.footer.helpEmail'}"></span>
			</div>

			<div class="flex gap-10 ml-auto">
				<a href="{$facebookLink}" n:if="$facebookLink" target="_blank" rel="nofollow" data-google-interstitial="false">
					<img src="{$basePath}/new-design/footer/fb.svg" alt="Facebook" loading="lazy" class="">
				</a>
				<a href="{$twitterLink}" data-google-interstitial="false"
					n:if="$twitterLink && $localization->isBulgarian() === false && $localization->isSlovenian() === false && $localization->isCroatian() === false"
					target="_blank" rel="nofollow">
					<img src="{$basePath}/new-design/footer/x.svg" alt="X" loading="lazy" class="">
				</a>
				<a href="{$instagramLink}" n:if="$instagramLink && $localization->isSlovenian() === false" data-google-interstitial="false"
					target="_blank" rel="nofollow">
					<img src="{$basePath}/new-design/footer/instagram.svg" alt="Instagram" loading="lazy" class="">
				</a>
			</div>
		</div>
		<div class="hidden md:block w-fill h-px opacity-30 bg-white mb-14"></div>

		<div class="flex items-center justify-between pb-[49px] md:pb-20">

			{if $localization->isHungarian()}
				<img src="{$basePath}/images/tiplino_logo_new_white.svg" title="Tipli" alt="Tiplino" loading="lazy" class="w-[153px] h-[90px]">
			{else}
				<img src="{$basePath}/new-design/footer/tipli.svg" alt="Tipli" loading="lazy" class="max-w-[110px] md:max-w-[150px]">
			{/if}

			<div class="hidden md:block text-right text-white text-sm font-normal leading-normal">
				{_'newFront.footer.copyright', ['year' => date('Y')]}
			</div>

			{if $localization->isCzech() || $localization->isSlovak() || $localization->isPolish()}
			<img src="{$basePath}/new-design/footer/apek.svg" alt="APEK" loading="lazy" class="">
			{/if}

		</div>
		<div class="md:hidden w-fill h-px opacity-30 bg-white"></div>

		<div class="md:hidden text-center text-white text-sm font-normal pt-[36px] pb-[32px] leading-normal">
			{_'newFront.footer.copyright', ['year' => date('Y')]}
		</div>
	</div>
</div>

<!-- Autocomplete desktop -->
{cache 'navbarShopsDesktop-' . $localization->getId(), 'expiration' => '6 hours'}
<div id="autocomplete-desktop-bg" class="absolute hidden bg-blue-950/70 backdrop-blur-[6px] z-50 top-[64px] w-full h-full left-0"></div>
<div id="autocomplete-desktop-dropdown" class="container absolute hidden top-[66px] left-1/2 transform -translate-x-1/2 z-50">
	<div id="autocomplete-desktop-dropdown-inner" class="relative left-[115px] mt-2 w-[477px] h-auto bg-white rounded-tl-[3px] rounded-tr-2xl rounded-bl-2xl rounded-br-2xl overflow-hidden">
		{foreach $getNavbarShops() as $topShop}
			<a n:href=":NewFront:Shops:Shop:default $topShop" data-google-interstitial="false" class="flex gap-4 items-center py-2 px-5 md:py-4 xl:hover:bg-green-gradient">
				<div
					class="flex justify-center items-center flex-shrink-0 w-[97px] h-[55px] border border-light-5 rounded-xl md:border-0 md:max-h-[30px] md:w-[75px]">
					<img class="max-w-[58px] md:max-w-[75px] max-h-[30px] h-auto" loading="lazy" alt="{$topShop->getName()}" src="{$topShop->getCurrentLogo() |image:116,0,'fit',false,$topShop->getName()}" />
				</div>

				<div class="flex flex-col md:flex-row w-full">
					<div><span class="text-gray-500 text-sm font-normal leading-normal">{$topShop->getName()}</span></div>
					<div id="dr-max" class="search-reward h-7 text-sm md:ml-auto">
						{$topShop |reward:true,'extended'|noescape}
					</div>
				</div>
			</a>
			{breakIf $iterator->counter >= 6}
		{/foreach}
	</div>
</div>
{/cache}
<!-- Addon popup -->
{snippet addonPopup}
{if isset($showAddonPopup) && $showAddonPopup}
{include '../../Presenters/templates/popups/addonPopup.latte'}
<script type="text/javascript" src="{$basePath}/js/popup/addonPopup.js?v=0.1" defer></script>
{/if}
{/snippet}

{if $user->isLoggedIn() && $isAdminReLoginAllowed}
<a n:href=":NewFront:Sign:AdminReLogin" class="footer__sticky-button">
	< Přihásit zpátky do administrace</a>
		{/if}

		{/define}


		{define metaTitle}{if $pageExtension &&
		$pageExtension->getMetaTitle()}{$pageExtension->getMetaTitle()}{else}{ifset title}{include
		title}{else}{_'front.head.title'}{/ifset}{/if}{/define}
		{define metaDescription}{if $pageExtension &&
		$pageExtension->getMetaDescription()}{$pageExtension->getMetaDescription() |striptags}{else}{ifset
		description}{include description}{else}{_'front.head.description'}{/ifset}{/if}{/define}
		{define metaKeywords}{if $pageExtension &&
		$pageExtension->getMetaKeywords()}{$pageExtension->getMetaKeywords()}{else}{ifset keywords}{include
		keywords}{/ifset}{/if}{/define}


<script>
	document.addEventListener("DOMContentLoaded", function () {
		const closeButton = document.getElementById('addon-promo-close-btn');

		if (closeButton) {
			closeButton.addEventListener('click', () => {
				const promoSection = document.getElementById('addon-promo-header');

				if (promoSection) {
					promoSection.style.display = 'none';
				}
			})
		}
	});
</script>

