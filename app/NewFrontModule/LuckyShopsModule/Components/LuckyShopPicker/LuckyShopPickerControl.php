<?php

namespace tipli\NewFrontModule\LuckyShopsModule\Components\LuckyShopPicker;

use Nette\Application\UI\Control;
use Nette\Application\UI\Form;
use Nette\Localization\Translator;
use Nette\Utils\ArrayHash;
use tipli\InvalidArgumentException;
use tipli\Model\Account\Entities\User;
use tipli\Model\Configuration;
use tipli\Model\Images\ImageFilter;
use tipli\Model\Localization\LocalizationFacade;
use tipli\Model\LuckyShop\Entities\UserLuckyShop;
use tipli\Model\LuckyShop\LuckyShopFacade;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Shops\ShopFacade;
use Tracy\Debugger;

class LuckyShopPicker<PERSON>ontrol extends Control
{
	public array $onSuccess = [];

	public array $onCloseModal = [];

	public function __construct(
		private User $user,
		private ?UserLuckyShop $userLuckyShop,
		private bool $modal,
		private LocalizationFacade $localizationFacade,
		private LuckyShopFacade $luckyShopFacade,
		private ShopFacade $shopFacade,
		private ImageFilter $imageFilter,
		private Translator $translator,
		private Configuration $configuration
	) {
	}

	public function render(bool $openModal = false): void
	{
		$localization = $this->localizationFacade->getCurrentLocalization();

		$this->template->countOfCashbackShops = $this->shopFacade->findCountOfShops($localization, true);

		$shopsByPopularity = $this->shopFacade->findShopsForLuckyShopPicker($localization);

		$this->template->veryPopularShop = $shopsByPopularity['very_popular'][array_rand($shopsByPopularity['very_popular'])];

		$this->template->popularShop = null;
		if ($shopsByPopularity['popular']) {
			$this->template->popularShop = $shopsByPopularity['popular'][array_rand($shopsByPopularity['popular'])];
		}

		$this->template->unpopularShop = null;
		if ($shopsByPopularity['unpopular']) {
			$this->template->unpopularShop = $shopsByPopularity['unpopular'][array_rand($shopsByPopularity['unpopular'])];
		}

		$userLuckyShops = $this->luckyShopFacade->findValidUserLuckyShops($this->user);
		$this->template->userLuckyShops = array_map(static fn (UserLuckyShop $userLuckyShop) => $userLuckyShop->getShop(), $userLuckyShops);

		$this->template->addFilter('image', $this->imageFilter);

		$this->template->getShopPopularity = static function (Shop $shop) use ($shopsByPopularity) {
			if (in_array($shop, $shopsByPopularity['very_popular'], true)) {
				return 'very_popular';
			}

			if (in_array($shop, $shopsByPopularity['popular'], true)) {
				return 'popular';
			}

			if (in_array($shop, $shopsByPopularity['unpopular'], true)) {
				return 'unpopular';
			}

			return 'popular';
		};

		$shopsQuery = $this->shopFacade->createShopsQuery($this->localizationFacade->getCurrentLocalization())
			->onlyPublished()
			->optimized()
			->onlyWithCashbackAllowed()
			->onlyActive()
			->sortAlphabetically()
		;

		if ($this->configuration->getMode() === 'normal') {
			$shopsQuery->in(Shop::LUCKY_SHOP_IDS[$localization->getId()]);
		}

		$shops = $this->shopFacade->fetch($shopsQuery);

		$this->template->shops = $shops;
		if ($this->modal) {
			$this->template->setFile(__DIR__ . '/modal.latte');
		} else {
			$this->template->setFile(__DIR__ . '/control.latte');
		}

		$this->template->openModal = $openModal;
		$this->template->render();
	}

	public function createComponentForm(): Form
	{
		$form = new Form();

		$form->addText('shopId')
			->setRequired();

		$form->addSubmit('submit');

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, ArrayHash $values): void
	{
		try {
			$shop = $this->shopFacade->find($values->shopId);

			if ($shop === null) {
				throw new InvalidArgumentException(
					$this->translator->translate('newFront.luckyShops.shopPicker.errors.emptyShop')
				);
			}

			if ($this->userLuckyShop && $this->userLuckyShop->getUser() !== $this->user) {
				throw new InvalidArgumentException('Invalid user');
			}

			if ($shop->getLocalization() !== $this->user->getLocalization()) {
				Debugger::log('User ' . $this->user->getId() . ' tried to pick shop ' . $shop->getId() . ' from different localization', 'lucky-shop-picker');

				throw new InvalidArgumentException('Invalid shop');
			}

			if ($this->luckyShopFacade->findValidUserLuckyShopByShop($this->user, $shop, new \DateTime())) {
				throw new InvalidArgumentException(
					$this->translator->translate('newFront.luckyShops.shopPicker.errors.duplicityShop')
				);
			}

			$defaultUserLuckyShop = $this->luckyShopFacade->findDefaultUserLuckyShopForUser($this->user);

			if ($this->userLuckyShop === null) {
				$source = UserLuckyShop::SOURCE_DEFAULT;
			} else {
				$source = $this->userLuckyShop->getSource();
			}

			if ($source === UserLuckyShop::SOURCE_DEFAULT && $defaultUserLuckyShop === null) {
				$this->luckyShopFacade->createUserLuckyShop(
					$this->user,
					UserLuckyShop::SOURCE_DEFAULT,
					new \DateTime(),
					new \DateTime('2050-01-01'),
					$shop
				);

				if ($this->user->hasInstalledAddon()) {
					$this->luckyShopFacade->createUserLuckyShop(
						$this->user,
						UserLuckyShop::SOURCE_ADDON,
						new \DateTime(),
						new \DateTime('+ 30 days')
					);
				}
			} else {
				$this->luckyShopFacade->updateUserLuckyShop($this->userLuckyShop, $shop);
			}

			$this->onSuccess();
		} catch (InvalidArgumentException $e) {
			$form->addError($e->getMessage());
			$this->redrawControl('luckyShopPickerControlErrors');
			return;
		}
	}

	public function handleClose()
	{
		if ($this->userLuckyShop && $this->userLuckyShop->getShop() === null) {
			$this->onCloseModal();
		}
	}

	public function setShops(array $shops): void
	{
		$this->template->searchShops = $shops;
		$this->redrawControl('searchShops');
	}
}
