<div id="overlay"
     n:class="$openModal === false ? 'hidden', 'fixed inset-0 bg-primary-blue-dark bg-opacity-90 backdrop-blur-lg z-[55]'"></div>
<div id="all-shops-list-modal"
	 n:class="$openModal === false ? 'hidden', 'w-full ml-auto mr-auto fixed inset-x-0 top-0 bottom-0 z-[60] px-3 md:pr-[15px] overflow-y-auto overflow-x-hidden'">
	{form form, class: "ajax relative block"}
	<div n:snippet="luckyShopPickerControlErrors">
		{var $form = $control['form']}
		<div class="bg-secondary-red text-white mt-3 rounded p-2 md:ml-5 md:mr-5" n:foreach="$form->errors as $error">
			{$error |noescape}
		</div>
	</div>

	<div class="js-luckyShop-modal-shop-id text-black hidden">
		{input shopId, value: $popularShop?->getId() ?: 0}
	</div>

	<div class="text-sm text-dark-1 leading-[24.5px] mb-[30px]" n:foreach="$form->getErrors() as $error">
		{$error |noescape}
	</div>

	<div
		class="border border-white/50 pt-10 pb-[30px] px-5 lg:px-[90px] rounded-2xl shadow-lg relative my-10 md:my-20 md:max-w-[1200px] w-full m-auto">
		{if $openModal === true}
		<a n:href="close!" class="ajax block absolute top-[-15px] right-[-20px] close-button hover:cursor-pointer">
			<img src="{$basePath}/new-design/close-modal-wheel.svg" alt="zavriet">
		</a>
		{else}
		<button class="block absolute top-[-15px] right-[-20px] close-button hover:cursor-pointer">
			<img src="{$basePath}/new-design/close-modal-wheel.svg" alt="zavriet">
		</button>
		{/if}

		<div class="text-medium text-white leading-7 mb-[30px]">
			{_newFront.luckyShops.shopPicker.popup.title}
		</div>

		<div id="editPopularInfo"
			 class="md:hidden mb-5 mt-4 text-xs text-white px-[19px] py-[15px] bg-primary-orange/10 backdrop-blur text-center border border-primary-orange rounded-md">
			<div class="text-xs leading-[21px] font-bold">{_newFront.luckyShops.shopPicker.tooltip.title}</div>
			<div class="text-xs leading-[21px]">{_newFront.luckyShops.shopPicker.tooltip.text}</div>
		</div>

		<div id='luckyShopPickerModal' class="datalist">
			<div class="datalist-data grid grid-cols-1 md:grid-cols-2 md:grid-cols-3 gap-5">
				{foreach $shops as $shop}
				{include shopItem, $shop, $getShopPopularity($shop)}
				{/foreach}
			</div>
		</div>
	</div>
	{/form}
</div>

{define shopItem, $shop, $popularity}
    <button {if in_array($shop, $userLuckyShops) === false}type="submit" name="shopId" value="{$shop->getId()}" data-id="{$shop->getId()}"{/if} n:class="in_array($shop, $userLuckyShops) ? 'bg-secondary-green/20 border-secondary-green', 'modalShopItem p-2 flex items-center text-start gap-[15px] hover:cursor-pointer rounded-xl border border-transparent hover:border-secondary-green'">
        <div class="flex items-center justify-center bg-white w-[131px] min-h-[70px] rounded-xl flex-shrink-0">
            <img class="max-w-[100px] max-h-[45px]" src="{$shop->getCurrentLogo() |image:200,0,'fit',false,$shop->getName()}" loading="lazy" alt="obchod">
        </div>
        <div class="text-white">
            <div class="font-bold leading-6">{$shop->getName()}</div>
            <div class="flex items-center gap-[7px] text-sm opacity-50 leading-[24.5px]">
                {_newFront.luckyShops.default.editLuckyShop.popularityValues.$popularity}
                <svg xmlns="http://www.w3.org/2000/svg" class="tooltip-icon" id="popular-shop-tooltip" width="16" height="16"
					 viewBox="0 0 16 16" fill="none">
                    <path d="M8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15Z" stroke="#ADB3BF" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                    <path d="M7.92578 7.16797V11.4609" stroke="#ADB3BF" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                    <circle cx="7.92443" cy="4.62951" r="0.846307" fill="#ADB3BF"></circle>
                </svg>
            </div>
        </div>
    </button>
{/define}

{if $openModal === false}
	<script>
		tippy('#popular-shop-tooltip', {
			content: `
			<div class="text-xs text-white px-[19px] z-50 py-[15px] bg-[#060300] text-center rounded-md">
				<div class="font-bold mb-[5px]">{_newFront.luckyShops.shopPicker.tooltip.title |noescape}</div>
				<p>{_newFront.luckyShops.shopPicker.tooltip.text |noescape}</p>
			</div>
		`,
			allowHTML: true,
			placement: 'bottom',
			theme: 'tooltip-shop',
			arrow: true,
		})

		document.querySelectorAll('.tooltip-icon').forEach(el => {
			el.addEventListener('click', (e) => {
				e.stopPropagation();
				e.preventDefault();
			});
		});
	</script>
<script n:syntax="off">
	const modal = document.getElementById('all-shops-list-modal');
	const overlay = document.getElementById('overlay');
	const closeButton = document.querySelector('.close-button');

	function openModal() {
		document.body.classList.add('hidden');
		modal.classList.remove('hidden');
		overlay.classList.remove('hidden');
	}

	function closeModal() {
		document.body.style.overflow = "";
		modal.classList.add('hidden');
		overlay.classList.add('hidden');
	}

	if (closeButton) closeButton.addEventListener('click', closeModal);
	if (overlay) overlay.addEventListener('click', closeModal);

	if (modal && !modal.classList.contains('hidden')) {
		openModal();
	}
</script>
{/if}
<style>
	.tippy-box[data-theme~='tooltip-shop'] {
		background-color: transparent !important;
		box-shadow: none !important;
		border: none !important;
	}

	.tippy-box[data-theme~='tooltip-shop'] .tippy-content {
		padding: 0 !important;
		background-color: transparent !important;
	}

	.tippy-box[data-theme~='tooltip-shop'][data-placement^='bottom'] > .tippy-arrow::before {
		border-bottom-color: #060300 !important;
	}
</style>
