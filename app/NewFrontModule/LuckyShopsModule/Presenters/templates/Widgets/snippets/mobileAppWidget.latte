<div class="bg-light-6 w-full md:max-w-[335px] rounded-2xl">
    <div class="relative bg-[#15243E] max-w-fit mx-auto">
        <svg width="38" height="38" viewBox="0 0 38 38" xmlns="http://www.w3.org/2000/svg" class="absolute top-0 left-[-1px]">
            <linearGradient id="linearGradient1" x1="63" y1="162.5" x2="63" y2="310.5" gradientUnits="userSpaceOnUse">
                <stop offset="1e-05" stop-color="##F4F4F6" stop-opacity="1"></stop>
                <stop offset="1" stop-color="#ecedf0" stop-opacity="1"></stop>
            </linearGradient>
            <path id="Path" fill="#F4F4F6" fill-rule="evenodd" stroke="none" d="M 14 10 C 14 4.548126 9.570007 0 4.118011 0 L -389 0 C -413.513 0 -425.769592 0 -433.384766 7.615234 C -441 15.230408 -441 27.487 -441 52 L -441 310 C -441 334.513 -441 346.769989 -433.384766 354.38501 C -425.769592 362 -413.513 362 -389 362 L 515 362 C 539.513 362 551.77002 362 559.380005 354.38501 C 567 346.769989 567 334.513 567 310 L 567 52 C 567 27.487 567 15.230408 559.380005 7.615234 C 551.77002 0 539.513 0 515 0 L 121.882019 0 C 116.429993 0 112 4.548126 112 10 C 112 21.235687 112 26.853607 109.304016 30.889099 C 108.135986 32.6362 106.635986 34.1362 104.888977 35.303497 C 100.854004 38 95.236023 38 84 38 L 42 38 C 30.764008 38 25.145996 38 21.110992 35.303497 C 19.364014 34.1362 17.864014 32.6362 16.696014 30.889099 C 14 26.853516 14 21.235687 14 10 Z"></path>
        </svg>

        <svg width="38" height="38" viewBox="0 0 38 38" xmlns="http://www.w3.org/2000/svg" class="absolute top-0 right-[-1px]">
            <linearGradient id="linearGradient1" x1="-29" y1="162.5" x2="-29" y2="310.5" gradientUnits="userSpaceOnUse">
                <stop offset="1e-05" stop-color="#ffffff" stop-opacity="1"></stop>
                <stop offset="1" stop-color="#ecedf0" stop-opacity="1"></stop>
            </linearGradient>
            <path id="Path" fill="#F4F4F6" fill-rule="evenodd" stroke="none" d="M -78 10 C -78 4.548126 -82.429993 0 -87.881989 0 L -481 0 C -505.513 0 -517.769592 0 -525.384766 7.615234 C -533 15.230408 -533 27.487 -533 52 L -533 310 C -533 334.513 -533 346.769989 -525.384766 354.38501 C -517.769592 362 -505.513 362 -481 362 L 423 362 C 447.513 362 459.77002 362 467.380005 354.38501 C 475 346.769989 475 334.513 475 310 L 475 52 C 475 27.487 475 15.230408 467.380005 7.615234 C 459.77002 0 447.513 0 423 0 L 29.882019 0 C 24.429993 0 20 4.548126 20 10 C 20 21.235687 20 26.853607 17.304016 30.889099 C 16.135986 32.6362 14.635986 34.1362 12.888977 35.303497 C 8.854004 38 3.236023 38 -8 38 L -50 38 C -61.235992 38 -66.854004 38 -70.889008 35.303497 C -72.635986 34.1362 -74.135986 32.6362 -75.303986 30.889099 C -78 26.853516 -78 21.235687 -78 10 Z"></path>
        </svg>


        <div class="flex items-center gap-[5px] uppercase text-sm relative text-white leading-7 font-bold h-[38px] px-10">
            {_newFront.luckyShops.widgets.mobileApp.header}
        </div>
    </div>

    <div class="pt-[25px] px-[30px] font-bold leading-7 text-center mb-[37px]">
        {_newFront.luckyShops.widgets.mobileApp.text}
    </div>

    <div class="mb-2 relative">
        <img class="mx-auto" src="/new-design/widget-action-1.png" alt="widget">
        <div class="flex items-center gap-2 bg-white w-full max-w-fit whitespace-nowrap p-2 rounded-lg absolute bottom-[25px] left-1/2 -translate-x-1/2">
            <svg xmlns="http://www.w3.org/2000/svg" width="35" height="35" viewBox="0 0 35 35" fill="none">
                <rect width="35" height="35" rx="6" fill="#EF7F1A"></rect>
                <path d="M17.1087 22.3878C17.421 22.4908 17.7223 22.5441 18.0127 22.5441C18.6429 22.5316 19.2492 22.3009 19.7231 21.8941C19.9665 21.6849 20.1698 21.4221 20.3331 21.1131C20.5473 20.7118 20.6053 19.9873 20.6126 19.2555H24C24 20.9604 23.8076 24.3699 20.0135 25.6413C19.3019 25.8793 18.6155 26 17.9548 26C17.2941 26 16.6113 25.8793 15.9106 25.6413C15.2135 25.4035 14.5707 25.0449 13.9862 24.5582C13.4017 24.0715 12.9259 23.4536 12.5556 22.7006C12.1851 21.9512 12 21.0599 12 20.0335V10H15.4165V14.2689H20.765V17.6466H15.4168V20.0477C15.4168 20.4419 15.4933 20.8006 15.6493 21.1131C15.9422 21.7115 16.4682 22.1705 17.1087 22.3878Z" fill="white"></path>
            </svg>
            <div>
                <div class="flex items-center gap-[37px] justify-between">
                    <div class="text-sm font-bold">{_newFront.luckyShops.widgets.mobileApp.notification.title}</div>
                    <div class="text-[10px] text-dark-4">{_newFront.luckyShops.widgets.mobileApp.notification.timeAgo}</div>
                </div>
                <div class="text-xs text-dark-2">{_newFront.luckyShops.widgets.mobileApp.notification.text}</div>
            </div>
        </div>
    </div>

    <div class="px-5 pb-5">
        <a n:href="openWidget!, widgetTypeId: $userWidget->getWidgetType()->getId()" style="box-shadow: 6px 6px 13.5px 0px rgba(239, 127, 26, 0.51);" class="flex items-center justify-center text-sm w-full h-[56px] relative rounded-xl bg-orange-gradient text-white font-bold md:py-4 leading-[28px] cursor-pointer xl:hover:bg-orange-gradient-hover">
            {_newFront.luckyShops.widgets.mobileApp.cta}
        </a>
    </div>
</div>