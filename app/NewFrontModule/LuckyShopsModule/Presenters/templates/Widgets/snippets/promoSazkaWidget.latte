<div class="bg-white w-full md:max-w-[335px] rounded-2xl">
    <div class="relative bg-[#15243E] max-w-fit mx-auto">
        <svg width="38" height="38" viewBox="0 0 38 38" xmlns="http://www.w3.org/2000/svg" class="absolute top-0 left-[-1px]">
            <linearGradient id="linearGradient1" x1="63" y1="162.5" x2="63" y2="310.5" gradientUnits="userSpaceOnUse">
                <stop offset="1e-05" stop-color="#ffffff" stop-opacity="1"></stop>
                <stop offset="1" stop-color="#ecedf0" stop-opacity="1"></stop>
            </linearGradient>
            <path id="Path" fill="url(#linearGradient1)" fill-rule="evenodd" stroke="none" d="M 14 10 C 14 4.548126 9.570007 0 4.118011 0 L -389 0 C -413.513 0 -425.769592 0 -433.384766 7.615234 C -441 15.230408 -441 27.487 -441 52 L -441 310 C -441 334.513 -441 346.769989 -433.384766 354.38501 C -425.769592 362 -413.513 362 -389 362 L 515 362 C 539.513 362 551.77002 362 559.380005 354.38501 C 567 346.769989 567 334.513 567 310 L 567 52 C 567 27.487 567 15.230408 559.380005 7.615234 C 551.77002 0 539.513 0 515 0 L 121.882019 0 C 116.429993 0 112 4.548126 112 10 C 112 21.235687 112 26.853607 109.304016 30.889099 C 108.135986 32.6362 106.635986 34.1362 104.888977 35.303497 C 100.854004 38 95.236023 38 84 38 L 42 38 C 30.764008 38 25.145996 38 21.110992 35.303497 C 19.364014 34.1362 17.864014 32.6362 16.696014 30.889099 C 14 26.853516 14 21.235687 14 10 Z"></path>
        </svg>

        <svg width="38" height="38" viewBox="0 0 38 38" xmlns="http://www.w3.org/2000/svg" class="absolute top-0 right-[-1px]">
            <linearGradient id="linearGradient1" x1="-29" y1="162.5" x2="-29" y2="310.5" gradientUnits="userSpaceOnUse">
                <stop offset="1e-05" stop-color="#ffffff" stop-opacity="1"></stop>
                <stop offset="1" stop-color="#ecedf0" stop-opacity="1"></stop>
            </linearGradient>
            <path id="Path" fill="url(#linearGradient1)" fill-rule="evenodd" stroke="none" d="M -78 10 C -78 4.548126 -82.429993 0 -87.881989 0 L -481 0 C -505.513 0 -517.769592 0 -525.384766 7.615234 C -533 15.230408 -533 27.487 -533 52 L -533 310 C -533 334.513 -533 346.769989 -525.384766 354.38501 C -517.769592 362 -505.513 362 -481 362 L 423 362 C 447.513 362 459.77002 362 467.380005 354.38501 C 475 346.769989 475 334.513 475 310 L 475 52 C 475 27.487 475 15.230408 467.380005 7.615234 C 459.77002 0 447.513 0 423 0 L 29.882019 0 C 24.429993 0 20 4.548126 20 10 C 20 21.235687 20 26.853607 17.304016 30.889099 C 16.135986 32.6362 14.635986 34.1362 12.888977 35.303497 C 8.854004 38 3.236023 38 -8 38 L -50 38 C -61.235992 38 -66.854004 38 -70.889008 35.303497 C -72.635986 34.1362 -74.135986 32.6362 -75.303986 30.889099 C -78 26.853516 -78 21.235687 -78 10 Z"></path>
        </svg>


        <div class="flex items-center gap-[5px] uppercase text-sm relative text-white leading-7 font-bold h-[38px] px-10">
            {_newFront.luckyShops.widgets.sazka.header}
        </div>
    </div>
    <div class="pt-[25px] px-[30px] font-bold leading-7 text-center">
        {_newFront.luckyShops.widgets.sazka.text}
    </div>

    <img class="mx-auto my-[14px]" src="/new-design/widget-cashback.png" alt="widget">

    <div class="px-5 pb-5">
        <div class="text-sm leading-[24.5px] text-dark-2 text-center mb-[15px]">
            {_newFront.luckyShops.widgets.sazka.promo.text |noescape}

        </div>

        <a target="_blank" style="box-shadow: 6px 6px 13.5px 0px rgba(239, 127, 26, 0.51);" class="flex items-center justify-center text-sm w-full h-[56px] relative rounded-xl bg-orange-gradient text-white font-bold md:py-4 leading-[28px] cursor-pointer xl:hover:bg-orange-gradient-hover" href="#">
            {_newFront.luckyShops.widgets.sazka.cta}
        </a>
    </div>
</div>