{block content}
<div class="bg-[#182B4A]" style="padding: 30px">

    <div  class='gap-5' style="display: grid; grid-template-columns: repeat(5, minmax(0, 1fr));">
    {foreach $userWidgets as $userWidget}
        <div>
            {var $widgetType = $userWidget->getWidgetType()}

            {include "./snippets/" . $widgetType->getCamelCaseType() . "Widget.latte"}

            <div class='bg-white p-2 mt-2 rounded'>
                Priority: {$userWidget->getWidgetType()->getPriority()}
                {if $userWidget->getReasons()}
                    <div>
                        {foreach $userWidget->getReasons() as $reason}
                                {$reason}
                        {/foreach}
                    </div>
                {/if}
            </div>
        </div>
    {/foreach}
</div>
</div>

<style>
    .bg-lucky-shops {
        position: relative;
        width: 100%;
        min-height: 1119px;
        height: auto;
        z-index: 20;
    }

    .bg-image {
        background-image: url(/new-design/bg-lucky-shops.svg);
        background-repeat: no-repeat;
        background-size: cover;
        background-position: bottom;
        width: 100%;
        height: 100%;
    }
</style>
