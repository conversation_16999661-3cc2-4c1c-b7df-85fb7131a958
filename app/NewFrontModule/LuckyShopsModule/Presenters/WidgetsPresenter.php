<?php

namespace tipli\NewFrontModule\LuckyShopsModule\Presenters;

use Nette\DI\Attributes\Inject;
use tipli\Model\LuckyShop\UserWidgetProvider;
use tipli\Model\LuckyShop\UserWidgetTestProvider;
use tipli\Model\LuckyShop\WidgetConditions\StreakWidgetCondition;
use tipli\NewFrontModule\Presenters\BasePresenter;

class WidgetsPresenter extends BasePresenter
{
	#[Inject]
	public UserWidgetProvider $userWidgetProvider;

	#[Inject]
	public UserWidgetTestProvider $userWidgetTestProvider;

	#[Inject]
	public StreakWidgetCondition $streakWidgetCondition;

	public function renderDefault()
	{
		$userWidgets = $this->userWidgetTestProvider->provideUserWidgets($this->getUserIdentity(), new \DateTime());

		$this->template->userWidgets = $userWidgets;
	}
}
