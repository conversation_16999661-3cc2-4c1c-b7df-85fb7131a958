<?php

namespace tipli\NewFrontModule\LuckyShopsModule\Presenters;

use Nette\DI\Attributes\Inject;
use tipli\Model\LuckyShop\Entities\WidgetType;
use tipli\Model\LuckyShop\Repositories\WidgetTypeRepository;
use tipli\Model\LuckyShop\UserWidgetProvider;
use tipli\Model\LuckyShop\UserWidgetTestProvider;
use tipli\Model\LuckyShop\WidgetConditions\StreakWidgetCondition;
use tipli\Model\LuckyShop\WidgetFacade;
use tipli\NewFrontModule\Presenters\BasePresenter;

class WidgetsPresenter extends BasePresenter
{
	#[Inject]
	public UserWidgetProvider $userWidgetProvider;

	#[Inject]
	public UserWidgetTestProvider $userWidgetTestProvider;

	#[Inject]
	public StreakWidgetCondition $streakWidgetCondition;

	#[Inject]
	public WidgetFacade $widgetFacade;

	#[Inject]
	public WidgetTypeRepository $widgetTypeRepository;

	public function renderDefault()
	{
		$userWidgets = $this->userWidgetTestProvider->provideUserWidgets($this->getUserIdentity(), new \DateTime());

		$this->template->userWidgets = $userWidgets;
	}

	public function actionOpenWidget(int $widgetTypeId): void
	{
		$user = $this->getUserIdentity();
		if (!$user) {
			$this->error('User not logged in', 401);
		}

		$widgetType = $this->widgetTypeRepository->find($widgetTypeId);
		if (!$widgetType) {
			$this->error('Widget type not found', 404);
		}

		$this->widgetFacade->saveUserWidgetOpen($user, $widgetType);

		$this->redirectWidget($widgetType);
	}

	private function redirectWidget(WidgetType $widgetType): void
	{
		switch ($widgetType->getType()) {
			case WidgetType::TYPE_DEFAULT:
			case WidgetType::TYPE_TIP:
				$this->redirect(':NewFront:Shops:Shops:default');
				break;
			case WidgetType::TYPE_STREAK:
				$this->redirect(':NewFront:LuckyShops:LuckyShops:default');
				break;
			case WidgetType::TYPE_ADDON:
				$this->redirect(':NewFront:Static:addon');
				break;
			case WidgetType::TYPE_MOBILE_APP:
				$this->redirect(':NewFront:Static:mobileApp');
				break;
			case WidgetType::TYPE_PROMO_SAZKA:
				$this->redirect(':NewFront:Shops:Shop:', ['shop' => 'sazka']);
				break;
			case WidgetType::TYPE_ADVERTISEMENT:
				$this->redirect(':NewFront:Homepage:default');
				break;
			default:
				$this->redirect(':NewFront:Homepage:default');
		}
	}
}
