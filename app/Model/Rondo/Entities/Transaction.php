<?php

namespace tipli\Model\Rondo\Entities;

use Doctrine\ORM\Mapping as ORM;
use tipli\Model\Shops\Entities\Redirection;

/**
 * @ORM\Entity(repositoryClass="tipli\Model\Rondo\Repositories\TransactionRepository")
 * @ORM\Table(name="tipli_rondo_transaction", uniqueConstraints={
 *      @ORM\UniqueConstraint(name="transaction_id", columns={"transaction_id"})
 * })
 */
class Transaction
{
	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Transactions\Entities\Transaction", inversedBy="rondoTransaction")
	 * @ORM\JoinColumn(name="transaction_id", referencedColumnName="id")
	 */
	private $transaction;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Shops\Entities\Redirection")
	 * @ORM\JoinColumn(name="redirection_id", referencedColumnName="id", nullable=true)
	 */
	private $redirection;

	/**
	 * @ORM\Column(type="string")
	 */
	private $subId;

	/**
	 * @param \tipli\Model\Transactions\Entities\Transaction $transaction
	 * @param Redirection $redirection
	 */
	public function __construct(\tipli\Model\Transactions\Entities\Transaction $transaction, Redirection $redirection)
	{
		$this->transaction = $transaction;
		$this->redirection = $redirection;
		$this->subId = $redirection->getSubId();
	}

	/** @return \tipli\Model\Transactions\Entities\Transaction */
	public function getTransaction()
	{
		return $this->transaction;
	}

	/** @return string */
	public function getSubId()
	{
		return $this->subId;
	}
}
