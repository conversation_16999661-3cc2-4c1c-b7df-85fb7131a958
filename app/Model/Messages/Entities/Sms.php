<?php

namespace tipli\Model\Messages\Entities;

use DateTime;
use Doctrine\ORM\Mapping as ORM;
use tipli\InvalidArgumentException;
use tipli\Model\Account\Entities\User;

/**
 * @ORM\Entity(repositoryClass="tipli\Model\Messages\Repositories\SmsRepository")
 * @ORM\Table(name="tipli_messages_sms", indexes={
 *  @ORM\Index(name="scheduled_at_idx", columns={"scheduled_at"}),
 *  @ORM\Index(name="protection_duplicity_hashx", columns={"protection_duplicity_hash"}),
 *  @ORM\Index(name="sent_at_idx", columns={"sent_at"}),
 *  @ORM\Index(name="aborted_at_idx", columns={"aborted_at"})
 * })
 */
class Sms
{
	public const PRIORITY_SEND_ALWAYS = 500;
	public const PRIORITY_SEND_IMMEDIATELY = 100;
	public const PRIORITY_HIGH = 60;
	public const PRIORITY_MEDIUM = 30;
	public const PRIORITY_LOW = 10;

	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\Column(type="string", nullable=true, length=32)
	 * @var string
	 */
	private $protectionDuplicityHash;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	private $messageId;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Account\Entities\User")
	 * @ORM\JoinColumn(name="user_id", referencedColumnName="id", nullable=true)
	 */
	private $user;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	private $toPhoneNumber;

	/**
	 * @ORM\Column(type="string")
	 */
	private $campaign;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 */
	private $text;

	/**
	 * @ORM\Column(type="integer")
	 */
	private $priority = 1;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $scheduledAt;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $sentAt;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private $abortedAt;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	private ?string $abortedReason;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $createdAt;

	public function __construct(
		$to,
		$text,
		$campaign,
		$scheduledAt = null,
		$priority = self::PRIORITY_MEDIUM
	) {
		if ($to instanceof User) {
			$this->user = $to;
			$this->toPhoneNumber = $to->getPhoneNumber();
		} else {
			$this->toPhoneNumber = $to;
		}

		$this->text = $text;
		$this->campaign = $campaign;
		$this->scheduledAt = $scheduledAt ?: new DateTime();
		$this->protectionDuplicityHash = md5($this->toPhoneNumber . $campaign . $text);
		$this->priority = $priority;
		$this->createdAt = new DateTime();
	}

  /**
   * @return mixed
   */
	public function getId()
	{
		return $this->id;
	}

	public function send()
	{
		$this->sentAt = new DateTime();
	}

	/**
	 * @return mixed
	 */
	public function getUser()
	{
		return $this->user;
	}

	/**
	 * @return string
	 */
	public function getToPhoneNumber()
	{
		return $this->toPhoneNumber;
	}

	/**
	 * @return string
	 */
	public function getText()
	{
		return $this->text;
	}

  /**
   * @return string
   */
	public function getProtectionDuplicityHash(): string
	{
		return $this->protectionDuplicityHash;
	}

	public function getPriorities()
	{
		return [
			self::PRIORITY_LOW,
			self::PRIORITY_MEDIUM,
			self::PRIORITY_HIGH,
			self::PRIORITY_SEND_ALWAYS,
			self::PRIORITY_SEND_IMMEDIATELY,
		];
	}

  /**
   * @return int
   */
	public function getPriority(): int
	{
		return $this->priority;
	}

	public function setPriority($priority)
	{
		$priorities = $this->getPriorities();

		if (in_array($priority, $priorities)) {
			$this->priority = $priority;
			return $this;
		}

		throw new InvalidArgumentException('Priority ' . $priority . ' not found.');
	}

	public function abort(?string $reason = null)
	{
		$this->setAbortedAt(new \DateTime());
		$this->setAbortedReason($reason);
	}

	public function sent()
	{
		$this->setSentAt(new \DateTime());
	}

  /**
   * @param DateTime $abortedAt
   */
	public function setAbortedAt(\DateTime $abortedAt)
	{
		$this->abortedAt = $abortedAt;
	}

	public function setAbortedReason(?string $reason = null): void
	{
		$this->abortedReason = $reason;
	}

  /**
   * @param DateTime $sentAt
   */
	public function setSentAt(\DateTime $sentAt)
	{
		$this->sentAt = $sentAt;
	}

	public function getCampaign()
	{
		return $this->campaign;
	}

	public function getScheduledAt()
	{
		return $this->scheduledAt;
	}

	public function getSentAt()
	{
		return $this->sentAt;
	}

	public function isScheduled(): bool
	{
		return $this->getSentAt() === null && $this->abortedAt == null && $this->scheduledAt !== null;
	}

	public function clearUserData()
	{
		$this->text = null;
		$this->toPhoneNumber = null;
	}
}
