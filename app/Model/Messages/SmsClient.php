<?php

namespace tipli\Model\Messages;

use GuzzleHttp\Client;
use Nette\Utils\Strings;
use tipli\Model\Configuration;
use tipli\Model\Messages\Entities\Sms;

class SmsClient implements ISmsSender
{
	/** @var Configuration */
	private $configuration;

	private $smsbranaCredentials;

	public function __construct(Configuration $configuration)
	{
		$this->configuration = $configuration;
		$this->smsbranaCredentials = (object) $this->configuration->getSmsbranaCredentials();
	}

	public function sendSms(Sms $sms): void
	{
		$url = 'https://api.smsbrana.cz/smsconnect/http.php?login=' . $this->smsbranaCredentials->login . '&password=' . $this->smsbranaCredentials->password . '&action=send_sms&number=' . $sms->getToPhoneNumber() . '&message=' . Strings::toAscii($sms->getText());

		$client = new Client();
		$response = $client->request('GET', $url);

		$body = (string)$response->getBody();
		$xml = simplexml_load_string($body);

		if ($xml === false) {
			throw new \InvalidArgumentException('Invalid XML response');
		}

		if ((int)$xml->err !== 0) {
			throw new \InvalidArgumentException($this->getSmsErrorMessage((int)$xml->err));
		}
	}

	public function getRemainingCredit()
	{
		$url = 'https://api.smsbrana.cz/smsconnect/http.php?login=' . $this->smsbranaCredentials->login . '&password=' . $this->smsbranaCredentials->password . '&action=credit_info';

		$client = new Client();
		$response = $client->request('GET', $url);

		return (float) simplexml_load_string($response->getBody()->getContents())->credit;
	}

	private function getSmsErrorMessage(int $code): string
	{
		$errors = [
			-1 => 'Duplicitní user_id – stejně označená SMS byla odeslaná již v minulosti',
			1 => 'Neznámá chyba',
			2 => 'Neplatný login',
			3 => 'Neplatný hash nebo heslo (podle varianty zabezpečení přihlášení)',
			4 => 'Neplatný čas – větší odchylka času mezi servery než maximálně akceptovaná',
			5 => 'Nepovolená IP – viz nastavení služby SMS Connect',
			6 => 'Neplatný název akce',
			7 => 'Tato sůl byla již jednou za daný den použita',
			8 => 'Nebyla navázána komunikace s databází',
			9 => 'Nedostatečný kredit',
			10 => 'Neplatné číslo příjemce SMS',
			11 => 'Prázdný text zprávy',
			12 => 'SMS je delší než povolených 459 znaků',
		];

		return $errors[$code] ?? 'Neznámý chybový kód';
	}
}
