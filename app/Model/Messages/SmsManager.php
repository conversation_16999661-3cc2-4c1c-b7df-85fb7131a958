<?php

namespace tipli\Model\Messages;

use tipli\Model\Doctrine\EntityManager;
use InvalidArgumentException;
use tipli\Model\Messages\Entities\Sms;
use tipli\Model\Messages\Queries\SmsQuery;
use tipli\Model\Messages\Repositories\SmsRepository;

class SmsManager
{
	public const COUNT_OF_SMS_IN_BATCH = 600;

	/** @var EntityManager */
	private $em;

	/** @var SmsRepository */
	private $smsRepository;

	/** @var SmsFactory */
	private $smsFactory;

	/** @var ISmsSender */
	private $smsSender;

	public function __construct(ISmsSender $smsSender, EntityManager $em, SmsRepository $smsRepository, SmsFactory $smsFactory)
	{
		$this->em = $em;
		$this->smsRepository = $smsRepository;
		$this->smsFactory = $smsFactory;
		$this->smsSender = $smsSender;
	}

	public function getSms()
	{
		return $this->smsRepository->getSms();
	}

	/**
	 * @param Sms $sms
	 * @return Sms|null
	 */
	public function scheduleSms(Sms $sms)
	{
		if ($sms->getProtectionDuplicityHash() && $this->smsRepository->findOneBy(['protectionDuplicityHash' => $sms->getProtectionDuplicityHash()])) {
			return null;
		}

		return $this->saveSms($sms);
	}

	public function sendSms(Sms $sms)
	{
		if (
			$sms->getUser()
			&& !$sms->getUser()->hasAllowedSendingSms()
			&& $sms->getPriority() !== Sms::PRIORITY_SEND_ALWAYS
			&& $sms->getPriority() !== Sms::PRIORITY_SEND_IMMEDIATELY
		) {
			$sms->abort();

			$this->saveSms($sms);
			return;
		}

		try {
			$this->smsSender->sendSms($sms);
			$sms->sent();
		} catch (InvalidArgumentException $e) {
			$sms->abort($e->getMessage());
		}

		$this->saveSms($sms);
		return;
	}

	public function sendScheduledSms()
	{
		$smsQuery = $this->createSmsQuery()
			->onlyScheduled()
			->topPriorityFirst()
			->oldestScheduledFirst();

		$scheduledSms = $this->fetch($smsQuery)
			->applyPaging(0, self::COUNT_OF_SMS_IN_BATCH);

		$count = 0;
		foreach ($scheduledSms as $sms) {
			$count++;

			if ($count === (int) round(self::COUNT_OF_SMS_IN_BATCH / 2)) {
				sleep(4);
			}

			$this->sendSms($sms);
		}
	}

	public function saveSms(Sms $sms)
	{
		$this->em->persist($sms);
		$this->em->flush($sms);

		return $sms;
	}

	public function createSmsQuery()
	{
		return new SmsQuery();
	}

	public function fetch(SmsQuery $query)
	{
		return $this->smsRepository->fetch($query);
	}

	public function clearSmsUserData(Sms $sms)
	{
		$sms->clearUserData();

		if ($sms->isScheduled()) {
			$sms->abort();
		}

		$this->saveSms($sms);
	}
}
