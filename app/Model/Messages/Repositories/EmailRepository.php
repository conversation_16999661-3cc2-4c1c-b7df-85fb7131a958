<?php

namespace tipli\Model\Messages\Repositories;

use tipli\Model\Doctrine\BaseRepository;
use Nette\Utils\DateTime;
use tipli\Model\Account\Entities\User;
use tipli\Model\Messages\Entities\Email;

class EmailRepository extends BaseRepository
{
	private function getEmails()
	{
		return $this->createQueryBuilder('e');
	}

	public function getSentEmails()
	{
		return $this->getEmails()
			->leftJoin('e.user', 'u')
			->andWhere('e.sentAt IS NOT NULL');
	}

	public function getCountSentEmailsByDatetimeRange(DateTime $since, DateTime $till)
	{
		return $this->getEmails()
			->select('COUNT(e.id)')
			->andWhere('e.sentAt >= :since')
			->setParameter('since', $since->format('Y-m-d H:i:s'))
			->andWhere('e.sentAt <= :till')
			->setParameter('till', $till->format('Y-m-d H:i:s'));
	}

	public function findUserEmails(User $user)
	{
		$qb = $this->getEmails()
			->andWhere('e.user = :user')
			->setParameter('user', $user);

		return $qb->getQuery()->getResult();
	}

	public function findEmailsByIds(array $ids)
	{
		return $this->getEmails()
			->andWhere('e.id IN (:ids)')
			->setParameter('ids', $ids)
			->getQuery()
			->getResult();
	}

	public function findScheduledLuckyShopEmail(User $user)
	{
		return $this->getEmails()
			->andWhere('e.user = :user')
			->setParameter('user', $user)
			->andWhere('e.campaign = :campaign')
			->setParameter('campaign', Email::LUCKY_SHOP_CAMPAIGN_NAME)
			->andWhere('e.scheduledAt IS NOT NULL')
			->andWhere('e.sentAt IS NULL')
			->andWhere('e.enqueuedAt IS NULL')
			->andWhere('e.scheduledAt > :now')
			->setParameter('now', new DateTime())
			->addOrderBy('e.scheduledAt', 'DESC')
			->getQuery()
			->getOneOrNullResult();
	}

	public function findTodayLuckyShopEmailForUser(User $user)
	{
		return $this->getEmails()
			->andWhere('e.user = :user')
			->setParameter('user', $user)
			->andWhere('e.campaign = :campaign')
			->setParameter('campaign', Email::LUCKY_SHOP_CAMPAIGN_NAME)
			->andWhere('e.createdAt >= :now')
			->setParameter('now', (new DateTime())->setTime(12, 0, 0))
			->setMaxResults(1)
			->getQuery()
			->getOneOrNullResult();
	}

	public function findLastAccountNumberVerificationEmail(User $user): ?Email
	{
		return $this->getEmails()
			->andWhere('e.user = :user')
			->setParameter('user', $user)
			->andWhere('e.campaign = :campaign')
			->setParameter('campaign', Email::ACCOUNT_NUMBER_VERIFICATION_CAMPAIGN_NAME)
			->addOrderBy('e.createdAt', 'DESC')
			->setMaxResults(1)
			->getQuery()
			->getOneOrNullResult();
	}
}
