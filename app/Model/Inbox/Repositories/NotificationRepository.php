<?php

namespace tipli\Model\Inbox\Repositories;

use tipli\Model\Doctrine\BaseRepository;
use tipli\Model\Account\Entities\User;
use tipli\Model\Inbox\Entities\Notification;
use tipli\Model\Inbox\Entities\NotificationCampaign;

class NotificationRepository extends BaseRepository
{
	public function getNotifications()
	{
		return $this->createQueryBuilder('n')
			->leftJoin('n.user', 'u')
			->leftJoin('n.notificationCampaign', 'nc');
	}

	public function findNotificationsToOpen(User $user): array
	{
		$qb = $this->createQueryBuilder('n')
			->where('n.user = :user')
			->andWhere('n.openedAt IS NULL')
			->andWhere('n.scheduledAt <= :now')
			->setParameter('now', new \DateTime())
			->setParameter('user', $user)
		;

		return $qb->getQuery()->getResult();
	}

	public function findNotOpenedNotificationsCountForUser(User $user)
	{
		$query = $this->createQueryBuilder('n')
			->select('COUNT(n.id)')
			->leftJoin(NotificationCampaign::class, 'nc', 'WITH', 'n.notificationCampaign = nc')
			->where('n.user = :user')
			->andWhere('n.openedAt IS NULL')
			->andWhere('n.scheduledAt <= :now')
			->andWhere('(n.validTill IS NOT NULL AND n.validTill > :now) OR (n.validTill IS NULL AND (nc.validTill IS NULL OR nc.validTill > :now)) OR (n.validTill IS NULL AND nc.validTill IS NULL)')
			->setParameter('user', $user)
			->setParameter('now', new \DateTime())
			->getQuery();

		return $query->getSingleScalarResult();
	}

	public function findNotificationForUser(User $user, NotificationCampaign $notificationCampaign)
	{
		return $this->createQueryBuilder('n')
			->andWhere('n.notificationCampaign = :notificationCampaign')
			->andWhere('n.user = :user')
			->setParameter('notificationCampaign', $notificationCampaign)
			->setParameter('user', $user)
			->setMaxResults(1)
			->getQuery()
			->getOneOrNullResult();
	}

	public function findTodayLuckyShopNotificationForUser(User $user)
	{
		return $this->createQueryBuilder('n')
			->andWhere('n.user = :user')
			->setParameter('user', $user)
			->andWhere('n.type = :type')
			->setParameter('type', Notification::TYPE_LUCKY_SHOP)
			->andWhere('n.createdAt >= :now')
			->setParameter('now', (new \DateTime())->setTime(12, 0, 0))
			->setMaxResults(1)
			->getQuery()
			->getOneOrNullResult();
	}
}
