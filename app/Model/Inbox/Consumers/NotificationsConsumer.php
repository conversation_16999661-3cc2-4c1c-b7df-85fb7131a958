<?php

namespace tipli\Model\Inbox\Consumers;

use Bunny\Message;
use Contributte\RabbitMQ\Consumer\IConsumer;
use Nette\Caching\Cache;
use Nette\Caching\IStorage;
use tipli\Model\Account\Entities\User;
use tipli\Model\Account\UserFacade;
use tipli\Model\Inbox\Entities\Notification;
use tipli\Model\Inbox\Entities\NotificationCampaign;
use tipli\Model\Inbox\Messages\NotificationMessage;
use tipli\Model\Inbox\NotificationFacade;
use tipli\Model\RabbitMq\BaseConsumer;
use Tracy\Debugger;

class NotificationsConsumer extends BaseConsumer
{
	private Cache $cache;

	public function __construct(
		private NotificationFacade $notificationFacade,
		private UserFacade $userFacade,
		IStorage $storage,
	) {
		$this->cache = new Cache($storage, 'NotificationsConsumer');
	}

	public function consume(array $messages): array
	{
		Debugger::timer('bulk');
		$result = [];

		$campaigns = [];

		echo "start\n";
		echo "consuming messages: " . count($messages) . "\n";

		echo "foreach start\n";
		/** @var Message $message */
		foreach ($messages as $message) {
			$result[$message->deliveryTag] = IConsumer::MESSAGE_ACK;

			$message = NotificationMessage::fromJson($message->content);

			echo $message->getAction() . "\n";

			if ($message->getAction() === NotificationMessage::ACTION_CREATE_NOTIFICATIONS) {
//				echo "create notifications\n";
				/** @var NotificationCampaign $notificationCampaign */
				$notificationCampaign = $this->notificationFacade->findNotificationCampaign($message->getNotificationCampaignId());

				echo "A\n";
				$this->notificationFacade->createNotificationsFromCampaign($notificationCampaign, $message->getUsersIds());
				echo "B\n";
			} elseif ($message->getAction() === NotificationMessage::ACTION_PUSH_NOTIFICATIONS) {
				if ($message->getType() === Notification::TYPE_LUCKY_SHOP) {
					$cacheKey = sha1(implode(',', [$message->getTitle(), $message->getBody(), $message->getPushScheduledAt(), implode(',', $message->getDeviceTokens())]));
				} else {
					$cacheKey = sha1(implode(',', [$message->getTitle(), $message->getBody(), implode(',', $message->getDeviceTokens())]));
				}

				if ($this->cache->load($cacheKey) !== null) {
					continue;
				}

				$this->cache->save($cacheKey, true, [Cache::EXPIRATION => '1 month']);

//				if ($message->getNotificationCampaignId() === null) {
					$this->notificationFacade->pushNotifications(
						$message->getTitle(),
						$message->getBody(),
						$message->getDeviceTokens(),
						(string) $message->getNotificationId(),
						$message->getWebviewAllowed(),
						$message->getUrl(),
						$message->getShowReviewPopup(),
						$message->getReviewPopupTitle(),
						$message->getReviewPopupText(),
						$message->getPlatform() === 'android'
					);
//				} else {
//					if (isset($campaigns[$message->getNotificationCampaignId()]['deviceTokens']) === false) {
//						$campaigns[$message->getNotificationCampaignId()] = [
//							'message' => $message,
//							'deviceTokens' => $message->getDeviceTokens(),
//						];
//					} else {
//						$campaigns[$message->getNotificationCampaignId()]['deviceTokens'] = array_merge($campaigns[$message->getNotificationCampaignId()]['deviceTokens'], $message->getDeviceTokens());
//					}
//				}
			} elseif ($message->getAction() === NotificationMessage::ACTION_MARK_NOTIFICATIONS) {
				$user = $this->userFacade->find($message->getUserId());
				if ($user instanceof User) {
					$this->notificationFacade->markAllAsOpenedForUser($user);
				}
			}
		}

		echo "foreach end\n";

//		foreach ($campaigns as $campaignData) {
//			/** @var NotificationMessage $message */
//			$message = $campaignData['message'];
//
//			$deviceTokens = $campaignData['deviceTokens'];
//
//			$this->notificationFacade->pushNotifications(
//				$message->getTitle(),
//				$message->getBody(),
//				$deviceTokens,
//				'c' . $message->getNotificationCampaignId(),
//				$message->getWebviewAllowed(),
//				$message->getUrl(),
//				$message->getShowReviewPopup(),
//				$message->getReviewPopupTitle(),
//				$message->getReviewPopupText()
//			);
//		}

//		Debugger::log(count($messages) . ' zaznamu - ' . Debugger::timer('bulk'), 'NotificationConsumerLog');

//		sleep(1);

		echo "result: " . json_encode($result);

		return $result;
	}
}
