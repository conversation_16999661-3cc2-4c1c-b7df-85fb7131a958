<?php

namespace tipli\Model\Inbox\Messages;

use Nette\Utils\Json;
use tipli\Model\RabbitMq\IMessage;

class NotificationMessage implements IMessage
{
	public const ACTION_CREATE_NOTIFICATIONS = 'createNotifications';
	public const ACTION_PUSH_NOTIFICATIONS = 'pushNotifications';
	public const ACTION_MARK_NOTIFICATIONS = 'markNotifications';

	/** @var string */
	private $action;

	/** @var int|null */
	private $notificationCampaignId;

	/** @var array|null */
	private $usersIds;

	/** @var string|null */
	private $title;

	/** @var string|null */
	private $body;

	/** @var array|null */
	private $deviceTokens;

	/** @var string|null */
	private $notificationId;

	/** @var bool|null */
	private $webviewAllowed;

	/** @var string|null */
	private $url;

	/** @var bool|null */
	private $showReviewPopup;

	/** @var string|null */
	private $reviewPopupTitle;

	/** @var string|null */
	private $reviewPopupText;

	/** @var int|null */
	private $userId;

	/** @var string|null */
	private $platform;

	/** @var string|null */
	private $type;

	/** @var string|null */
	private $pushScheduledAt;

	public function __construct(
		string $action,
		?int $notificationCampaignId,
		?array $usersIds,
		?string $title = null,
		?string $body = null,
		?array $deviceTokens = null,
		?string $notificationId = null,
		?bool $webviewAllowed = null,
		?string $url = null,
		?bool $showReviewPopup = null,
		?string $reviewPopupTitle = null,
		?string $reviewPopupText = null,
		?int $userId = null,
		?string $platform = null,
		?string $type = null,
		?string $pushScheduledAt = null,
	) {
		$this->action = $action;
		$this->notificationCampaignId = $notificationCampaignId;
		$this->usersIds = $usersIds;
		$this->title = $title;
		$this->body = $body;
		$this->deviceTokens = $deviceTokens;
		$this->notificationId = $notificationId;
		$this->webviewAllowed = $webviewAllowed;
		$this->url = $url;
		$this->showReviewPopup = $showReviewPopup;
		$this->reviewPopupTitle = $reviewPopupTitle;
		$this->reviewPopupText = $reviewPopupText;
		$this->userId = $userId;
		$this->platform = $platform;
		$this->type = $type;
		$this->pushScheduledAt = $pushScheduledAt;
	}

	public function getAction(): string
	{
		return $this->action;
	}

	public function getNotificationCampaignId(): ?int
	{
		return $this->notificationCampaignId;
	}

	public function getUsersIds(): ?array
	{
		return $this->usersIds;
	}

	public function getTitle(): ?string
	{
		return $this->title;
	}

	public function getBody(): ?string
	{
		return $this->body;
	}

	public function getDeviceTokens(): ?array
	{
		return $this->deviceTokens;
	}

	public function getNotificationId(): ?string
	{
		return $this->notificationId;
	}

	public function getWebviewAllowed(): ?bool
	{
		return $this->webviewAllowed;
	}

	public function getUrl(): ?string
	{
		return $this->url;
	}

	public function getShowReviewPopup(): ?bool
	{
		return $this->showReviewPopup;
	}

	public function getReviewPopupTitle(): ?string
	{
		return $this->reviewPopupTitle;
	}

	public function getReviewPopupText(): ?string
	{
		return $this->reviewPopupText;
	}

	public function getUserId(): ?int
	{
		return $this->userId;
	}

	public function getType(): ?string
	{
		return $this->type;
	}

	public function getPushScheduledAt(): ?string
	{
		return $this->pushScheduledAt;
	}

	public function __toString(): string
	{
		return Json::encode([
			'action' => $this->getAction(),
			'notificationCampaignId' => $this->getNotificationCampaignId(),
			'usersIds' => $this->getUsersIds(),
			'title' => $this->getTitle(),
			'body' => $this->getBody(),
			'deviceTokens' => $this->getDeviceTokens(),
			'notificationId' => $this->getNotificationId(),
			'webviewAllowed' => $this->getWebviewAllowed(),
			'url' => $this->getUrl(),
			'showReviewPopup' => $this->getShowReviewPopup(),
			'reviewPopupTitle' => $this->getReviewPopupTitle(),
			'reviewPopupText' => $this->getReviewPopupText(),
			'userId' => $this->getUserId(),
			'platform' => $this->getPlatform(),
			'type' => $this->getType(),
			'pushScheduledAt' => $this->getPushScheduledAt(),
		]);
	}

	public static function fromJson(string $data): self
	{
		$data = Json::decode($data);

		return new self(
			$data->action,
			$data->notificationCampaignId,
			$data->usersIds ? (array) $data->usersIds : null,
			$data->title,
			$data->body,
			$data->deviceTokens ? (array) $data->deviceTokens : [],
			$data->notificationId,
			$data->webviewAllowed,
			$data->url,
			$data->showReviewPopup,
			$data->reviewPopupTitle,
			$data->reviewPopupText,
			$data->userId,
			$data->platform ?? null,
			$data->type ?? null,
			$data->pushScheduledAt ?? null
		);
	}

	public function getPlatform()
	{
		return $this->platform;
	}
}
