<?php

namespace tipli\Model\Inbox;

use tipli\Model\Account\Entities\User;
use tipli\Model\Firebase\FirebaseClient;
use tipli\Model\Groups\Entities\Group;
use tipli\Model\Images\Entities\Image;
use tipli\Model\Inbox\Entities\Notification;
use tipli\Model\Inbox\Entities\NotificationCampaign;
use tipli\Model\Inbox\Producers\NotificationsProducer;
use tipli\Model\Inbox\Queries\NotificationCampaignsQuery;
use tipli\Model\Inbox\Queries\NotificationsQuery;
use tipli\Model\Inbox\Repositories\NotificationCampaignRepository;
use tipli\Model\Inbox\Repositories\NotificationRepository;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Payouts\Entities\Payout;
use tipli\Model\Queues\SqlQueryScheduler;
use tipli\Model\Rewards\Entities\MoneyRewardCampaign;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Templates\DataObjects\NotificationTemplateDataObject;

class NotificationFacade
{
	public function __construct(
		private NotificationManager $notificationManager,
		private NotificationCampaignRepository $notificationCampaignRepository,
		private NotificationRepository $notificationRepository,
		private NotificationFactory $notificationFactory,
		private NotificationsProducer $notificationsProducer,
		private FirebaseClient $firebaseClient,
		private SqlQueryScheduler $sqlQueryScheduler
	) {
	}

	public function createNotificationCampaign(Localization $localization, $name, ?Group $group, ?MoneyRewardCampaign $moneyRewardCampaign, Image $image, $title, $content, $url, bool $pushAllowed, \DateTime $scheduledAt)
	{
		return $this->notificationManager->createNotificationCampaign($localization, $name, $group, $moneyRewardCampaign, $image, $title, $content, $url, $pushAllowed, $scheduledAt);
	}

	public function createNotificationCampaignsQuery()
	{
		return new NotificationCampaignsQuery();
	}

	public function fetchNotificationCampaigns(NotificationCampaignsQuery $notificationCampaignsQuery)
	{
		return $this->notificationCampaignRepository->fetch($notificationCampaignsQuery);
	}

	public function saveNotificationCampaign(NotificationCampaign $notificationCampaign)
	{
		return $this->notificationManager->saveNotificationCampaign($notificationCampaign);
	}

	public function removeNotificationCampaign(NotificationCampaign $notificationCampaign)
	{
		$this->notificationManager->removeNotificationCampaign($notificationCampaign);
	}

	public function findNotificationCampaign($id)
	{
		return $this->notificationCampaignRepository->find($id);
	}

	public function getNotificationCampaigns()
	{
		return $this->notificationCampaignRepository->getNotificationCampaigns();
	}

	public function processNotificationCampaign(NotificationCampaign $notificationCampaign)
	{
		$this->notificationManager->processNotificationCampaign($notificationCampaign);
	}

	public function markAllAsOpenedForUser(User $user)
	{
		$notifications = $this->notificationRepository->findNotificationsToOpen($user);

		/** @var Notification $notification */
		foreach ($notifications as $notification) {
			$this->notificationManager->markNotificationAsOpened($notification);
		}
	}

	public function scheduleMarkAllAsOpenedForUser(User $user): void
	{
		$this->notificationsProducer->scheduleMarkAllAsOpenedNotifications($user);
	}

	public function notificationClicked(Notification $notification)
	{
		$this->notificationManager->notificationClicked($notification);
	}

	public function notificationMobileClicked(Notification $notification)
	{
		$this->notificationManager->notificationMobileClicked($notification);
	}

	public function findNotOpenedNotificationsCountForUser(User $user)
	{
		return $this->notificationRepository->findNotOpenedNotificationsCountForUser($user);
	}

	public function fetch(NotificationsQuery $notificationsQuery)
	{
		return $this->notificationRepository->fetch($notificationsQuery);
	}

	/**
	 * @return NotificationsQuery
	 */
	public function createNotificationsQuery()
	{
		return new NotificationsQuery();
	}

	/**
	 * @return \Doctrine\ORM\QueryBuilder
	 */
	public function getNotifications()
	{
		return $this->notificationRepository->getNotifications();
	}

	/**
	 * @param User $user
	 * @param Shop $shop
	 * @param array $transactions
	 */
	public function sendTransactionsRegistrationNotification(User $user, Shop $shop, array $transactions)
	{
		$this->notificationManager->sendNotification(
			$this->notificationFactory->createTransactionsRegistrationNotification($user, $shop, $transactions)
		);
	}

	/**
	 * @param Payout $payout
	 */
	public function sendPayoutConfirmationNotification(Payout $payout)
	{
		$this->notificationManager->sendNotification(
			$this->notificationFactory->createPayoutConfirmationNotification($payout)
		);
	}

	public function sendDealsNotificationByDTO(User $user, NotificationTemplateDataObject $notificationTemplateDataObject, ?Image $image)
	{
		$this->notificationManager->sendNotification(
			$this->notificationFactory->createNotificationFromNotificationTemplateDataObject($user, $notificationTemplateDataObject, null, null, $image)
		);
	}

	/**
	 * @param Notification $notification
	 */
	public function saveNotification(Notification $notification)
	{
		$this->notificationManager->saveNotification($notification);
	}

	/**
	 * @param NotificationCampaign $notificationCampaign
	 * @param array $usersIds
	 */
	public function createNotificationsFromCampaign(NotificationCampaign $notificationCampaign, array $usersIds)
	{
		echo "A001\n";
		$this->notificationManager->createNotificationsFromCampaign($notificationCampaign, $usersIds);
	}

	public function pushNotifications(
		string $title,
		string $body,
		array $deviceTokens,
		?string $notificationId,
		bool $webviewAllowed,
		?string $url,
		bool $showReviewPopup,
		?string $reviewPopupTitle,
		?string $reviewPopupText,
		?bool $isAndroid = false
	): bool {
		$result = $this->firebaseClient->pushNotificationToDeviceToken(
			$title,
			$body,
			$deviceTokens,
			$notificationId,
			$webviewAllowed,
			$url,
			$showReviewPopup,
			$reviewPopupTitle,
			$reviewPopupText,
			$isAndroid
		);

		$this->sqlQueryScheduler->markNotificationAsPushed($notificationId);

		return $result;
	}

	/**
	 * @param User $user
	 * @param int $id
	 */
	public function findUserNotificationById(User $user, int $id)
	{
		return $this->notificationRepository->findOneBy([
			'user' => $user,
			'id' => $id,
		]);
	}

	public function findUserNotificationByCampaignNotification(User $user, NotificationCampaign $notificationCampaign)
	{
		return $this->notificationRepository->findNotificationForUser($user, $notificationCampaign);
	}

	public function findTodayLuckyShopNotificationForUser(User $user)
	{
		return $this->notificationRepository->findTodayLuckyShopNotificationForUser($user);
	}
}
