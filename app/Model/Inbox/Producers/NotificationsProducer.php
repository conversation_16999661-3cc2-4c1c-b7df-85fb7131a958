<?php

namespace tipli\Model\Inbox\Producers;

use tipli\Model\Account\Entities\User;
use tipli\Model\Inbox\Entities\Notification;
use tipli\Model\Inbox\Entities\NotificationCampaign;
use tipli\Model\Inbox\Messages\NotificationMessage;
use tipli\Model\RabbitMq\BaseProducer;

class NotificationsProducer extends BaseProducer
{
	public const ROUTING_KEY_CREATE = 'create';
	public const ROUTING_KEY_MARK = 'mark';
	public const ROUTING_KEY_PUSH_PRIORITY = 'push_priority';
	public const ROUTING_KEY_PUSH_DEFAULT = 'push_default';
	public const ROUTING_KEY_PUSH_LUCKY_SHOPS = 'push_lucky_shops';

	public function scheduleMarkAllAsOpenedNotifications(User $user): void
	{
		$this->producer->publish(
			new NotificationMessage(
				NotificationMessage::ACTION_MARK_NOTIFICATIONS,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				null,
				$user->getId()
			),
			[],
			self::ROUTING_KEY_MARK
		);
	}

	public function scheduleCreateNotifications(NotificationCampaign $notificationCampaign, array $usersIds)
	{
		$this->producer->publish(
			new NotificationMessage(
				NotificationMessage::ACTION_CREATE_NOTIFICATIONS,
				$notificationCampaign->getId(),
				$usersIds
			),
			[],
			self::ROUTING_KEY_CREATE
		);
	}

	public function schedulePushNotifications(
		string $title,
		string $body,
		array $deviceTokens,
		?string $notificationId,
		bool $webviewAllowed,
		?string $url,
		bool $showReviewPopup,
		?string $reviewPopupTitle = null,
		?string $reviewPopupText = null,
		?int $notificationCampaignId = null,
		?string $platform = null,
		?string $type = null,
		?string $pushScheduledAt = null,
	) {
		$routingKey = $notificationCampaignId !== null ? self::ROUTING_KEY_PUSH_DEFAULT : self::ROUTING_KEY_PUSH_PRIORITY;

		if ($type === Notification::TYPE_LUCKY_SHOP) {
			$routingKey = self::ROUTING_KEY_PUSH_LUCKY_SHOPS;
		}

		$this->producer->publish(
			new NotificationMessage(
				NotificationMessage::ACTION_PUSH_NOTIFICATIONS,
				$notificationCampaignId,
				null,
				$title,
				$body,
				$deviceTokens,
				$notificationId,
				$webviewAllowed,
				$url,
				$showReviewPopup,
				$reviewPopupTitle,
				$reviewPopupText,
				null,
				$platform,
				$type,
				$pushScheduledAt
			),
			[],
			$routingKey
		);
	}
}
