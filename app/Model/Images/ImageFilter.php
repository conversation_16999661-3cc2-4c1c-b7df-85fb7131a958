<?php

namespace tipli\Model\Images;

use Nette\Caching\Cache;
use Nette\Caching\Storage;
use Nette\Http\Request;
use Nette\Http\Url;
use Nette\Utils\Strings;
use tipli\InvalidArgumentException;
use tipli\Model\Configuration;
use tipli\Model\Images\Entities\Image;
use tipli\Model\Localization\LocalizationFacade;
use Tracy\Debugger;

class ImageFilter
{
	private const VSHCDN_KEY = '3617e3c1c56f0e53';
	private const VSHCDN_SALT = 'b692f6108ae4db75';

	public const FLAG_FIT = 'fit';
	public const FLAG_EXACT = 'exact';
	public const FLAG_FIT_MIDDLE = 'fitMiddle';
	public const FLAG_FIT_MIDDLE_WITHOUT_BORDERS = 'fitMiddleWithoutBorders';
	public const FLAG_EXACT_TOP = 'exactTop';

	/**
	 * @var ImageStorage
	 */
	private $imageStorage;

	/**
	 * @var Configuration
	 */
	private $configuration;

	/**
	 * @var Request
	 */
	private $httpRequest;

	/**
	 * @var Cache
	 */
	private $cache;

	/**
	 * @var LocalizationFacade
	 */
	private $localizationFacade;

	public function __construct(ImageStorage $imageStorage, Configuration $configuration, Request $httpRequest, Storage $storage, LocalizationFacade $localizationFacade)
	{
		$this->imageStorage = $imageStorage;
		$this->configuration = $configuration;
		$this->httpRequest = $httpRequest;
		$this->cache = new Cache($storage, self::class);
		$this->localizationFacade = $localizationFacade;
	}

	/**
	 * @param Image|string|null $image
	 * @param int $width
	 * @param int $height
	 * @param string $flag
	 *
	 * @param bool $absolute
	 * @return string|null
	 */
	public function __invoke($image = null, $width = null, $height = null, $flag = 'fit', $absolute = false, $name = null, $webp = false, bool $useProxy = false, $quality = 90)
	{
		bdump($webp);
		if ($image === null) {
			Debugger::log('Image is null', 'image-filter-null-image');

			return null;
		}

		if ($this->configuration->getMode() === 'dev') {
			$useProxy = false;
		} elseif (!$useProxy && $this->useProxy()) {
			$useProxy = true;
		}

		if (PHP_SAPI === 'cli') {
			if ($image instanceof Image) {
				$imageUrl = 'https://www.' . $this->getBaseDomain() . '/upload/images/' . $image->getNamespace() . '/' . $image->getIdentifier();
//				Debugger::log($imageUrl . ' ### width: ' . $width, 'imageFilter-generate-cli');
			} else {
//				Debugger::log($image . ' ### width: ' . $width, 'imageFilter-generate-cli');
			}
		}

		if ($image instanceof Image && $this->imageStorage->getImageContentType($image) === 'image/svg+xml') {
			return $this->getBasePath() . '/upload/images/' . $image->getNamespace() . '/' . $image->getIdentifier();
		}

		if ($image instanceof Image) {
			$image = 'https://www.' . $this->getBaseDomain() . '/upload/images/' . $image->getNamespace() . '/' . $image->getIdentifier();
		}

//		if (is_string($image)) {
		return $this->invokeNewCdn($image, $width, $height, $flag, ($webp ? 'webp' : null), $quality);
//		}
//		return $this->invokeAbsolute($image, $width ? (float) $width : null, $height ? (float) $height : null, $flag);
/**
		if (!($image instanceof Image)) {
			throw new InvalidArgumentException('$image is not instance of tipli\Model\Images\Entities\Image', 'invalid-image');
		}

		$cacheKey = $this->getCacheKey($image, $width, $height, $flag, $absolute, $name, $webp);

		if ($cachedFilePath = $this->cache->load($cacheKey)) {
			return $cachedFilePath;
		}

		if (!$width && !$height) {
			$filePath = $this->getBasePath() . '/upload/images/' . $image->getNamespace() . '/' . $image->getIdentifier();
			$this->cache->save($cacheKey, $filePath);
			return $filePath;
		}

		$thumbnailFileName = $this->getThumbnailFileName($image, $width, $height, $flag, $name, $webp);

		$thumbnailFilePath = $this->imageStorage->getThumbnailPath($image) . 'o' . DIRECTORY_SEPARATOR;
		$compressedThumbnailFilePath = $this->imageStorage->getThumbnailPath($image);

		if (!file_exists($this->configuration->getThumbnailsPath() . $thumbnailFilePath)) {
			FileSystem::createDir($this->configuration->getThumbnailsPath() . $thumbnailFilePath);
		}

		if (!file_exists($this->configuration->getThumbnailsPath() . $compressedThumbnailFilePath)) {
			FileSystem::createDir($this->configuration->getThumbnailsPath() . $compressedThumbnailFilePath);
		}

		if ($webp && !$this->isThumbnailExists($compressedThumbnailFilePath, $thumbnailFileName)) {
			$this->imageStorage->resetCompressionMark($image);

			return null;
		}

		if ($this->isThumbnailExists($compressedThumbnailFilePath, $thumbnailFileName)) {
			$filePath = $this->getBasePath($absolute) . '/upload/thumbnails/' . $compressedThumbnailFilePath . $thumbnailFileName;
			$this->cache->save($cacheKey, $filePath, [Cache::EXPIRATION => '15 minutes']);
			return $filePath;
		}

		if ($this->isThumbnailExists($thumbnailFilePath, $thumbnailFileName)) {
			$filePath = $this->getBasePath($absolute) . '/upload/thumbnails/' . $thumbnailFilePath . $thumbnailFileName;
			$this->cache->save($cacheKey, $filePath, [Cache::EXPIRATION => '15 minutes']);
			return $filePath;
		}

		$originalImage = $this->imageStorage->getImageFile($image);

		if (!$originalImage) {
			bdump($image);
			$originalImage = \Nette\Utils\Image::fromFile(__DIR__ . '/../../../www/images/missing-image.png');
		}

		$thumbnailImage = $this->generateThumbnail($originalImage, $width, $height, $flag);
		$thumbnailImage->save($this->configuration->getThumbnailsPath() . $thumbnailFilePath . $thumbnailFileName);

		$this->imageStorage->resetCompressionMark($image);

		$filePath = $this->getBasePath($absolute) . '/upload/thumbnails/' . $thumbnailFilePath . $thumbnailFileName;
		$this->cache->save($cacheKey, $filePath, [Cache::EXPIRATION => '15 minutes']);
		return $filePath;
		**/
	}

	public function invokeAbsolute(?string $imageUrl, float $width = null, float $height = null, string $flag = 'fit', string $extension = null)
	{
		if (!$imageUrl) {
			$imageUrl = "/images/placeholder.png";
		}

		if (Strings::contains($imageUrl, 'farnell')) {
			return $imageUrl;
		}

		$url = new Url($imageUrl);
		$urlAuthority = str_replace('www.', '', $url->getAuthority());

		if ($urlAuthority === 'letaky.tipli.cz') {
			$cdnPrefix = 's'; // steve
		} elseif ($urlAuthority === 'kaufino.com' || $urlAuthority === '') {
			$cdnPrefix = 'k'; // kaufino
		} elseif (
			$urlAuthority === 'tipli.cz'
			|| $urlAuthority === 'tipli.czlocal'
			|| $urlAuthority === 'tipli.sk'
			|| $urlAuthority === 'tipli.bg'
			|| $urlAuthority === 'tipli.si'
			|| $urlAuthority === 'tipli.hr'
			|| $urlAuthority === 'tipli.pl'
			|| $urlAuthority === 'tipli.ro'
			|| $urlAuthority === 'tiplino.hu'
		) {
			$cdnPrefix = 't'; // tipli
		} else {
			$cdnPrefix = 'a'; // absolutni externi adresa
		}

		if ($cdnPrefix === 'a') {
			$urlPaths = [];
			$urlPaths[] = $cdnPrefix;
			$urlPaths[] = rawurlencode($imageUrl);

			$extension = $extension ? : 'jpg';
			$fileName = Strings::substring(sha1($imageUrl), 0, 16) . '.' . $extension;

			$proxyToken = Strings::substring(
				sha1('osel' . $imageUrl . $fileName . ($width ?? 0) . ($height ?? 0) . $flag),
				0,
				4
			);

			bdump(('osel' . $imageUrl . $fileName . ($width ?? 0) . ($height ?? 0) . $flag));
		} else {
			$urlPath = trim($url->getPath(), '/');
			$urlPaths = explode('/', $urlPath);
			$fileName = $urlPaths[count($urlPaths) - 1];

			$originalExtension = explode('.', $fileName);
			$originalExtension = end($originalExtension);

			if ($extension === 'webp') {
				$fileName = mb_substr($fileName, 0, -1 - strlen($originalExtension)) . '.webp'; // with .webp
			}

			if (Strings::lower($originalExtension) === 'gif') {
				return $imageUrl;
			}

			array_pop($urlPaths); // remove last value - filename
			array_unshift($urlPaths, $cdnPrefix); // add prefix to first position of array

			$originalFileName = isset($originalExtension)
				? str_replace('.webp', '.' . $originalExtension, $fileName)
				: $fileName
			;

			$proxyToken = Strings::substring(
				sha1('osel' . $originalFileName . ($width ?? 0) . ($height ?? 0) . $flag),
				0,
				4
			);
		}

		$urlPaths[] = $proxyToken;
		$urlPaths[] = ($width ?: '') . 'x' . ($height ?: '');
		$urlPaths[] = $flag;
		$urlPaths[] = $fileName;

		$cdnBaseUrl = 'static.' . $this->getBaseDomain();

		$proxyUrl = 'https://' . $cdnBaseUrl . '/' . implode('/', $urlPaths);
		$proxyUrl .= '?v=13.0';

		if (isset($originalExtension) && $originalExtension !== 'jpg') {
			$proxyUrl .= '&from=' . $originalExtension;
		} elseif (isset($originalExtension) && $originalExtension === 'webp') {
			$proxyUrl .= '&from=webp';
		}

		return $proxyUrl;
	}

	private function isThumbnailExists($filePath, $fileName): bool
	{
		return file_exists($this->configuration->getThumbnailsPath() . $filePath . $fileName);
	}

	private function getCacheKey(Image $image, $width = null, $height = null, $flag = 'fit', $absolute = false, $name = null, $webp = false)
	{
		return ($image->getId() . '-' . $width . '-' . $height . '-' . $flag . '-' . ($absolute ? 1 : 0 . '-' . $name) . '-' . ($webp ? 1 : 0));
	}

	private function getThumbnailFileName(Image $image, $width, $height, $flag, $name = null, $webp = false)
	{
		if ($webp) {
			$extension = 'webp';
		} else {
			$extension = explode('.', $image->getIdentifier());
			$extension = end($extension);
		}

		if ($name) {
			$name = Strings::webalize($name);

			return $name . '-' . $width . 'x' . $height . '-' . $flag . '.' . $extension;
		} else {
			return 'i-' . $width . 'x' . $height . '-' . $flag . '.' . $extension;
		}
	}

	private function generateThumbnail(\Nette\Utils\Image $originalImage, $width, $height, $flag): \Nette\Utils\Image
	{
		if ($flag === self::FLAG_FIT) {
			return $originalImage->resize($width, $height, \Nette\Utils\Image::FIT);
		} elseif ($flag === self::FLAG_EXACT) {
			return $originalImage->resize($width, $height, \Nette\Utils\Image::EXACT);
		} elseif ($flag === self::FLAG_FIT_MIDDLE) {
			$originalImage->resize($width - 30, $height - 30, \Nette\Utils\Image::FIT);

			$blank = \Nette\Utils\Image::fromBlank($width, $height, \Nette\Utils\Image::rgb(255, 255, 255));
			$blank->place($originalImage, '50%', '50%');

			return $blank;
		} elseif ($flag === self::FLAG_FIT_MIDDLE_WITHOUT_BORDERS) {
			$originalImage->resize($width, $height, \Nette\Utils\Image::FIT);

			$blank = \Nette\Utils\Image::fromBlank($width, $height, \Nette\Utils\Image::rgb(255, 255, 255));
			$blank->place($originalImage, '50%', '50%');

			return $blank;
		} elseif ($flag === self::FLAG_EXACT_TOP) {
			$originalImage->resize($width, null, \Nette\Utils\Image::FIT);
			$originalImage->crop(0, 0, $width, $height);

			return $originalImage;
		} else {
			throw new InvalidArgumentException('Flag ' . $flag . ' is not supported.');
		}
	}

	private function getBasePath($absolute = false)
	{
		$baseUri = $this->httpRequest ? rtrim($this->httpRequest->getUrl()->getBaseUrl(), '/') : null;

		if ($absolute) {
			return $baseUri;
		}

		if (PHP_SAPI === 'cli') {
			return 'https://www.tipli.cz';
		}

		return preg_replace('#https?://[^/]+#A', '', $baseUri);
	}

	private function getBaseDomain(): string
	{
		if (PHP_SAPI === 'cli') {
			return 'tipli.cz';
		}

		return str_replace('https://www.', '', $this->localizationFacade->getCurrentLocalization()->getBaseUrl());
	}

	private function useProxy(): bool
	{
		return true;
	}

	public static function preloadImageOnUrl(string $url)
	{
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
		curl_setopt($ch, CURLOPT_TIMEOUT, 1);
		curl_setopt($ch, CURLOPT_NOSIGNAL, 1);
		curl_exec($ch);
		curl_close($ch);
	}

	private function invokeNewCdn(?string $imageUrl, ?float $width, ?float $height, string $flag, ?string $extension = null, ?int $quality = 100, float $maxBytes = null): string
	{
		$ttl = 7200; // nacachování (browser + CDN) na x sekund minimalni hodnota 3600
		$width = $width !== null ? $width : 0;
		$height = $height !== null ? $height : 0;
		$enlarge = 1;

//		if (Strings::startsWith($imageUrl, '/upload')) {
//			$imageUrl = 'https://kaufino.com' . $imageUrl;
//		}

		if ($flag === 'exactTop' || $flag === 'exact') {
			$flag = 'fill';
		}


		if ($extension === null) {
			$extension = explode('.', $imageUrl);
			$extension = end($extension);

			if (in_array($extension, ['png', 'jpg', 'jpeg', 'avif', 'webp', 'gif', 'bmp', 'svg']) === false) {
				$extension = "png";
			}
		}

		$keyBin = pack("H*", self::VSHCDN_KEY);
		if (empty($keyBin)) {
			return $imageUrl;
		}

		$saltBin = pack("H*", self::VSHCDN_SALT);
		if (empty($saltBin)) {
			return $imageUrl;
		}

		$encodedUrl = rtrim(strtr(base64_encode($imageUrl), '+/', '-_'), '=');

		if ($flag === self::FLAG_FIT_MIDDLE || $flag === self::FLAG_FIT_MIDDLE_WITHOUT_BORDERS) {
			$parameters = [];

			// Step 1: Resize the image to fit within the reduced dimensions (width-30, height-30)
			$resizeWidth = $width - 30;
			$resizeHeight = $height - 30;
			$parameters[] = 'resize:fit:' . $resizeWidth . ':' . $resizeHeight . ':' . $enlarge;

			// Step 2: Set gravity to center the image
			$parameters[] = 'gravity:ce';

			// Step 3: Use the extend parameter to extend the image to fit the full canvas size
			$parameters[] = 'extend:1';
		} else {
			$parameters = [
				'resize:' . $flag . ':' . $width . ':' . $height . ':' . $enlarge,
				'gravity:no',
			];
		}

		if ($maxBytes !== null) {
			$parameters[] = 'max_bytes:' . $maxBytes;
		}

		if ($quality !== null) {
			$parameters[] = 'quality:' . $quality;
		}

		$path = '/' . implode('/', $parameters) . '/' . $encodedUrl . ($extension !== null ? ('.' . $extension) : null);
		$signature = rtrim(strtr(base64_encode(hash_hmac('sha256', $saltBin . '/' . $ttl . '/' . $path, $keyBin, true)), '+/', '-_'), '=');

		return 'https://img.tiplicdn.com/zoh4eiLi/IMG/' . $ttl . '/' . $signature . $path;
	}
}
