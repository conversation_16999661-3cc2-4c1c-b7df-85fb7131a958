<?php

namespace tipli\Model\PartnerSystems\Networks;

use GuzzleHttp\Client;
use Nette\Http\Url;
use Nette\Utils\Json;
use Nette\Utils\Strings;
use tipli\InvalidArgumentException;
use tipli\Model\Currencies\Currency;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\PartnerSystems\PartnerSystemProcessResult;
use tipli\Model\Transactions\ImportedTransaction;

interface IProfitShareFactory
{
	/**
	 * @return ProfitShare
	 */
	public function create(PartnerSystem $partnerSystem);
}

class ProfitShare extends Network
{
	private const RESPONSE_FORMAT = 'json';

	public function createRedirectionLink($url, $userId, $redirectionId, $deepUrl = null)
	{
		$redirectionUrl = new Url($url);
		$redirectionUrl->setPath(Strings::trim($redirectionUrl->getPath(), '/') . '/' . $userId . 'x' . $redirectionId);

		return $redirectionUrl->getAbsoluteUrl();
	}

	public function processTransactions(\DateTime $from = null, \DateTime $to = null): PartnerSystemProcessResult
	{
		$nowFrom = (new \DateTime())->modify('-15 day');
		$nowTo = (new \DateTime())->modify('+1 day');

		$records = [];
		if ($from && $to) {
			$records = array_merge($records, $this->getRecords($from, $to));
		}

		$records = array_merge($records, $this->getRecords($nowFrom, $nowTo));
		foreach ($records as $record) {
			$userId = $record->hash;
			$transactionId = $record->order_id;
			$partnerSystemKey = Strings::webalize($record->advertiser_id);

			$commissionAmount = 0;
			$orderAmount = 0;
			$itemCommissionValue = explode('|', $record->items_commision_value);

			foreach (explode('|', $record->items_commision) as $key => $itemCommissionAmount) {
				$commissionAmount += (float) $itemCommissionAmount;
				$orderAmount += (float) $itemCommissionAmount * (100 / (float) $itemCommissionValue[$key]);
			}

			$currency = Currency::RON;

			$orderAmount = (float) $orderAmount;
			$registeredAt = (new \DateTime($record->order_date));
			$registeredAt->modify('- 1 hour');
			$currencyFixDate = (new \DateTime('2024-11-06 17:00:00'));

			if ($registeredAt > $currencyFixDate) {
				// delete this after all transactions older than 2024-11-06 17:00:00 are confirmed
				$currency = $this->partnerSystem->getOption('currency');
			}

			$status = $this->resolveStatus($record->order_status);

			$importedTransaction = new ImportedTransaction($this->partnerSystem, $partnerSystemKey, $userId, $transactionId, $commissionAmount, $orderAmount, $currency, $status, $registeredAt, null, null, ImportedTransaction::CHANNEL_API);

			$this->scheduleImportedTransaction($importedTransaction);
		}

		return new PartnerSystemProcessResult(count($records));
	}

	private function resolveStatus(?string $status = null): string
	{
		if (empty($status)) {
			return ImportedTransaction::STATUS_REGISTERED;
		}

		switch ($status) {
			case 'pending':
				return ImportedTransaction::STATUS_REGISTERED;
			case 'canceled':
				return ImportedTransaction::STATUS_CANCELLED;
			case 'approved':
				return ImportedTransaction::STATUS_CONFIRMED;
		}

		throw new InvalidArgumentException('unknown status ' . $status);
	}

	private function getRecords(\DateTime $from, \DateTime $to)
	{
		$dateTime = (new \DateTime())->setTimezone(new \DateTimeZone('Europe/Bucharest'))->format('D, j M Y H:i:s O');

		$result = [];

		for ($i = 1; $i <= 20; $i++) {
			$apiUrl = new Url($this->partnerSystem->getOption('apiUrl') . '/affiliate-commissions/');
			$apiUrl->setQueryParameter('filters[date_from]', $from->format('Y-m-d'));
			$apiUrl->setQueryParameter('filters[date_to]', $to->format('Y-m-d'));
			$apiUrl->setQueryParameter('page', $i);

			$client = new Client(['verify' => false]);
			$response = $client->request('GET', urldecode($apiUrl->getAbsoluteUrl()), [
				'headers' => [
					'X-PS-Auth' => $this->getApiToken($dateTime, urldecode($apiUrl->getQuery())),
					'X-PS-Accept' => self::RESPONSE_FORMAT,
					'X-PS-Client' => $this->partnerSystem->getOption('apiUser'),
					'Date' => $dateTime,
				],
			]);

			$records = Json::decode($response->getBody()->getContents());

			if ($i > $records->result->current_page) {
				break;
			}

			$result = array_merge($result, $records->result->commissions);
		}

		return $result;
	}

	private function getApiToken($dateTime, $queryString)
	{
		$apiUser = $this->partnerSystem->getOption('apiUser');
		$apiKey = $this->partnerSystem->getOption('apiKey');

		$signatureString = 'GETaffiliate-commissions/?' . $queryString . '/' .
			$apiUser . $dateTime;

		$signatureStringEncrypted = hash_hmac('sha1', $signatureString, $apiKey);

		return $signatureStringEncrypted;
	}
}
