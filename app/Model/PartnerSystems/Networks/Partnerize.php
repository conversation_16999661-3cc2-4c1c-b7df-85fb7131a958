<?php

namespace tipli\Model\PartnerSystems\Networks;

use GuzzleHttp\Client;
use League\Csv\Reader;
use Nette\Http\Url;
use Nette\Utils\Json;
use Nette\Utils\Strings;
use tipli\InvalidArgumentException;
use tipli\InvalidImportException;
use tipli\Model\Currencies\Currency;
use tipli\Model\Files\Entities\File;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\PartnerSystems\PartnerSystemProcessResult;
use tipli\Model\Transactions\ImportedTransaction;

interface IPartnerizeFactory
{
	/** @return Partnerize */
	public function create(PartnerSystem $partnerSystem);
}

class Partnerize extends Network
{
	public function createRedirectionLink($url, $userId, $redirectionId, $deepUrl = null)
	{
		$url = new Url($url);

		$url .= '/pubref:' . $userId . 'x' . $redirectionId;

		if ($deepUrl) {
			$url .= '/destination:' . $deepUrl;
		}

		return $url;
	}

	public function import(File $file)
	{
		$reader = Reader::createFromPath($this->fileStorage->getFile($file));
		$reader->setDelimiter(',');

		$headers = [];
		foreach ($reader->fetchOne() as $columnIndex => $columnName) {
			$headers[] = $columnName;
		}

		$requiredColumns = ['conversion_id', 'campaign_title', 'conversion_date_time', 'currency', 'publisher_reference', 'conversion_status', 'publisher_commission', 'value'];
		if (!count(array_intersect($headers, $requiredColumns)) == count($requiredColumns)) {
			throw new InvalidImportException('Partnerize import není ve správném formátu..');
		}

		$results = $reader->getRecords($headers);

		foreach ($results as $index => $row) {
			// skip header
			if ($index === 0) {
				continue;
			}

			$transactionId = $row['conversion_id'];
			$partnerSystemKey = Strings::webalize($row['campaign_title']);
			$userId = $row['publisher_reference'];
			$commissionAmount = (float)$row['publisher_commission'];
			$orderAmount = (float)$row['value'];
			$registeredAt = new \DateTime($row['conversion_date_time']);
			$currency = mb_substr($row['currency'], 0, 3);
			$currency = Currency::getCurrencyByName($currency);

			$status = $this->resolveStatus($row['conversion_status']);

			$importedTransaction = new ImportedTransaction($this->partnerSystem, $partnerSystemKey, $userId, $transactionId, $commissionAmount, $orderAmount, $currency, $status, $registeredAt, null, null, ImportedTransaction::CHANNEL_IMPORT);

			$this->scheduleImportedTransaction($importedTransaction);
		}
	}

	public function processTransactions(\DateTime $from = null, \DateTime $to = null): PartnerSystemProcessResult
	{
		$nowFrom = (new \DateTime())->modify('-1 day');
		$nowTo = (new \DateTime())->modify('+1 day');

		$records = [];
		if ($from && $to) {
			$records = array_merge($records, $this->getRecords($from, $to));
		}
		$records = array_merge($records, $this->getRecords($nowFrom, $nowTo));

		foreach ($records as $record) {
			$data = $record->conversion_data;
			$currency = $data->currency;

			if (!$currency || !Currency::getCurrencyByName($currency)) {
				throw new InvalidArgumentException('Undefined partner system currency.');
			}

			if (Strings::contains($data->publisher_reference, 'x') === false) {
				continue;
			}

			$userId = $data->publisher_reference;
			$partnerSystemKey = $data->campaign_id;
			$transactionId = $data->conversion_id;

			$commissionAmount = (float) $data->conversion_value->publisher_commission;
			$orderAmount = (float) $data->conversion_value->value;
			$registeredAt = new \DateTime($data->conversion_time);
			$registeredAt->setTimezone(new \DateTimeZone('Europe/Prague'));
			$status = $this->resolveStatus($data->conversion_value->conversion_status);

			if ($data->conversion_value->conversion_status === 'mixed' && isset($data->conversion_items)) {
				$finalCommissionAmount = 0;
				$status = ImportedTransaction::STATUS_REGISTERED;

				foreach ($data->conversion_items as $conversionItem) {
					if (!in_array($conversionItem->item_status, ['rejected', 'approved', 'confirmed'])) { // pokud se v seznamu objevi nejaka nerozhodnuta transakce, tak se proces groupovani prerusi
						$status = ImportedTransaction::STATUS_REGISTERED;
						$finalCommissionAmount = $commissionAmount;
						break;
					}

					if (in_array($conversionItem->item_status, ['approved', 'confirmed'])) {
						$finalCommissionAmount += (float) $conversionItem->item_publisher_commission;
					}

					$status = ImportedTransaction::STATUS_CONFIRMED;
				}

				if ($status === ImportedTransaction::STATUS_CONFIRMED && $finalCommissionAmount === 0) {
					$status = ImportedTransaction::STATUS_CANCELLED;
				}

				$commissionAmount = $finalCommissionAmount;
			}

			$importedTransaction = new ImportedTransaction($this->partnerSystem, $partnerSystemKey, $userId, $transactionId, $commissionAmount, $orderAmount, $currency, $status, $registeredAt, null, null, ImportedTransaction::CHANNEL_API);

			$this->scheduleImportedTransaction($importedTransaction);
		}

		return new PartnerSystemProcessResult(count($records));
	}

	private function getRecords(\DateTime $from, \DateTime $to)
	{
		$from = $from->setTime(0, 0, 0)->format('Y-m-d+H:i:s');
		$to = $to->setTime(0, 0, 0)->format('Y-m-d+H:i:s');

		$client = new Client();
		$request = $client->request(
			'GET',
			'https://api.performancehorizon.com/reporting/report_publisher/publisher/1101l13194/conversion.json?start_date=' . $from . '&end_date=' . $to . '&ref_conversion_metric_id%5B%5D=2',
			[
				'headers' => [
					'Authorization' => $this->partnerSystem->getOption('authorization'),
				],
			]
		);

		return Json::decode($request->getBody()->getContents())->conversions;
	}

	private function resolveStatus($status)
	{
		$status = Strings::lower($status);

		switch ($status) {
			case 'mixed':
			case 'pending':
				return ImportedTransaction::STATUS_REGISTERED;
			case 'rejected':
				return ImportedTransaction::STATUS_CANCELLED;
			case 'approved':
			case 'confirmed':
				return ImportedTransaction::STATUS_CONFIRMED;
		}

		throw new InvalidArgumentException('unknown status ' . $status);
	}
}
