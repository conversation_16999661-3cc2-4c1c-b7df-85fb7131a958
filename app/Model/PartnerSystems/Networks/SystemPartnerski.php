<?php

namespace tipli\Model\PartnerSystems\Networks;

use GuzzleHttp\Client;
use Nette\Http\Url;
use Nette\Utils\Json;
use Nette\Utils\Strings;
use tipli\InvalidArgumentException;
use tipli\Model\Currencies\Currency;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\PartnerSystems\PartnerSystemProcessResult;
use tipli\Model\Transactions\ImportedTransaction;

interface ISystemPartnerskiFactory
{
	/**
	 * @param PartnerSystem $partnerSystem
	 * @return SystemPartnerski
	 */
	public function create(PartnerSystem $partnerSystem);
}


class SystemPartnerski extends Network
{
	private const API_TOKEN_URL = 'https://apiv2.systempartnerski.pl/partner-api/token';
	private const API_TRANSACTIONS_URL = 'https://apiv2.systempartnerski.pl/partner-api/wnioski?data_zaraportowania=%dateFrom&status_wniosku_id=PTW&status_wniosku_id=ZRP&status_wniosku_id=ZKC&status_wniosku_id=WTR&status_wniosku_id=RZL&status_wniosku_id=ODR';

	public function createRedirectionLink($url, $userId, $redirectionId, $deepUrl = null)
	{
		$redirectionUrl = new Url($url);
		$redirectionUrl->setQueryParameter('label', $userId . 'x' . $redirectionId);

		if ($deepUrl) {
			$redirectionUrl->setQueryParameter('dp_url', $deepUrl);
		}

		return $redirectionUrl->getAbsoluteUrl();
	}

	public function processTransactions(\DateTime $from = null, \DateTime $to = null): PartnerSystemProcessResult
	{
		$token = $this->getToken();
		$nowFrom = (new \DateTime())->modify('-1 day');
		$nowTo = (new \DateTime())->modify('+1 day');

		$records = [];

		if ($from && $to) {
			$records = array_merge($records, $this->getRecords($token, $from, $to));
		}

		$records = array_merge($records, $this->getRecords($token, $nowFrom, $nowTo));

		foreach ($records as $record) {
			$userId = $record->etykieta;
			$partnerSystemKey = $record->domain_id;
			$transactionId = $record->numer_wniosku;
			$commissionAmount = (float) $record->prowizja_partner;
			$orderAmount = (float) $record->wartosc_produktu;
			$registeredAt = new \DateTime($record->data_otwarcia);
			$currency = Currency::PLN;

			$status = $this->resolveStatus($record->status_wniosku_id);

			$importedTransaction = new ImportedTransaction($this->partnerSystem, $partnerSystemKey, $userId, $transactionId, $commissionAmount, $orderAmount, $currency, $status, $registeredAt, null, null, ImportedTransaction::CHANNEL_API);

			$this->scheduleImportedTransaction($importedTransaction);
		}

		return new PartnerSystemProcessResult(count($records));
	}

	private function getToken()
	{
		$client = new Client();

		$response = $client->request(
			'GET',
			self::API_TOKEN_URL,
			['auth' => [
				$this->partnerSystem->getOption('user'),
				$this->partnerSystem->getOption('apiKey'),
			],
			]
		);

		return Json::decode($response->getBody()->getContents())->token;
	}

	private function getRecords($token, \DateTime $from, \DateTime $to)
	{
		$client = new Client();
		$response = $client->request(
			'GET',
			strtr(self::API_TRANSACTIONS_URL, ['%dateFrom' => $from->format('Y-m-d')]),
			['headers' => [
				'X-Auth-Token' => $token,
			],
			]
		);

		return Json::decode($response->getBody()->getContents())->wniosek;
	}

	private function resolveStatus(string $status): string
	{
		$status = Strings::lower($status);

		switch ($status) {
			case 'odr':
				return ImportedTransaction::STATUS_CANCELLED;
			case 'wtr':
			case 'rzl':
			case 'zkc':
				return ImportedTransaction::STATUS_CONFIRMED;
		}

		throw new InvalidArgumentException('unknown status ' . $status);
	}
}
