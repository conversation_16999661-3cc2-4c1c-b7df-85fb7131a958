<?php

namespace tipli\Model\PartnerSystems\Networks;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\ConnectException;
use League\Csv\Reader;
use Nette\Http\Url;
use Nette\Utils\Json;
use Nette\Utils\Strings;
use tipli\InvalidImportException;
use tipli\Model\Currencies\Currency;
use tipli\Model\Files\Entities\File;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\PartnerSystems\PartnerSystemProcessResult;
use tipli\Model\Transactions\ImportedTransaction;

interface IAliexpressFactory
{
	/**
	 * @return Aliexpress
	 */
	public function create(PartnerSystem $partnerSystem);
}

class Aliexpress extends Network
{
	public function createRedirectionLink($url, $userId, $redirectionId, $deepUrl = null)
	{
		$oldUrl = $url;
		$url = new Url($url);
		$url->setQueryParameter('dp', $userId . 'x' . $redirectionId);

		if ($deepUrl && Strings::contains($deepUrl, 'click')) {
			$url = new Url($deepUrl);
			$url->setQueryParameter('dp', $userId . 'x' . $redirectionId);
		}

		return $url->getAbsoluteUrl();
	}

	public function import(File $file)
	{
		$reader = Reader::createFromPath($this->fileStorage->getFile($file));

		// get the header
		$headers = $reader->fetchOne();
		$headers = array_map(static function ($item) {
			return Strings::webalize(str_replace('"', '', $item));
		}, $headers);


		$requiredColumns = ['ordernumber', 'ordertime', 'orderstatus', 'paymentamount', 'estimatedcommission', 'commission', 'dp'];
		if (!count(array_intersect($headers, $requiredColumns)) == count($requiredColumns)) {
			throw new InvalidImportException('AliExpress import není ve správném formátu..');
		}

		$results = $reader->getRecords($headers);

		foreach ($results as $index => $row) {
			// skip header
			if ($index === 0) {
				continue;
			}

			$transactionId = $row['ordernumber'];
			$userId = $row['dp'];
			$orderAmount = (float) $row['paymentamount'];
			$orderAmount *= 0.95;

			if (isset($row['ordertime']) && new \DateTime($row['ordertime'], new \DateTimeZone('America/Los_Angeles'))) {
				$registeredAt = new \DateTime($row['ordertime'], new \DateTimeZone('America/Los_Angeles'));
				$registeredAt->setTimezone(new \DateTimeZone('Europe/Prague'));
			} else {
				$registeredAt = new \DateTime();
			}

			if (isset($row['transactiontime']) && new \DateTime($row['transactiontime'], new \DateTimeZone('America/Los_Angeles'))) {
				$confirmedAt = new \DateTime($row['transactiontime'], new \DateTimeZone('America/Los_Angeles'));
				$confirmedAt->setTimezone(new \DateTimeZone('Europe/Prague'));
			} else {
				$confirmedAt = null;
			}

			$status = $this->resolveStatus($row['orderstatus'], $registeredAt, (float) $row['estimatedcommission']);
			$commissionAmount = $status == ImportedTransaction::STATUS_CONFIRMED ? (float) $row['commission'] : (float) $row['estimatedcommission'];

			$importedTransaction = new ImportedTransaction($this->partnerSystem, null, $userId, $transactionId, $commissionAmount, $orderAmount, Currency::USD, $status, $registeredAt, null, null, ImportedTransaction::CHANNEL_IMPORT);

			$this->scheduleImportedTransaction($importedTransaction);
		}
	}

	public function processTransactions(\DateTime $from = null, \DateTime $to = null): PartnerSystemProcessResult
	{
		//        $from = (new \DateTime())->modify('-3 days');
//        $to = (new \DateTime())->modify('+1 day');
		$nowFrom = (new \DateTime())->modify('-1 day');
		$nowTo = (new \DateTime())->modify('+1 day');

		$records = [];
		if ($from && $to) {
			//$records = array_merge($records, $this->getRecordsToConfirm($from, $to));
		}
		$records = array_merge($records, $this->getRecords($nowFrom, $nowTo));

		$this->processRecords($records);

		return new PartnerSystemProcessResult(count($records));
	}

	private function processRecords($records)
	{
		foreach ($records as $record) {
			$transactionId = (string) $record->orderNumber;
			$userId = (isset($record->extraParams) && Strings::contains($record->extraParams, 'dp')) ? Json::decode($record->extraParams)->dp : null;
			$orderAmount = (float) $record->paymentAmount;
			$orderAmount *= 0.95;

			if (isset($record->orderTime) && \DateTime::createFromFormat('m-d-Y H:i', $record->orderTime)) {
				$registeredAt = \DateTime::createFromFormat('m-d-Y H:i', $record->orderTime, new \DateTimeZone('America/Los_Angeles'));
				$registeredAt->setTimezone(new \DateTimeZone('Europe/Prague'));
			} else {
				$registeredAt = new \DateTime();
			}

			if (isset($record->transactionTime) && \DateTime::createFromFormat('m-d-Y H:i', $record->transactionTime)) {
				$confirmedAt = \DateTime::createFromFormat('m-d-Y H:i', $record->transactionTime, new \DateTimeZone('America/Los_Angeles')) ?: new \DateTime();
				$confirmedAt->setTimezone(new \DateTimeZone('Europe/Prague'));
			} else {
				$confirmedAt = null;
			}

			$status = $this->resolveStatus((string) $record->orderStatus, $registeredAt, (float) $record->estimatedCommission);
			$commissionAmount = $status == ImportedTransaction::STATUS_CONFIRMED ? (float) $record->commission : (float) $record->estimatedCommission;

			if ($status == ImportedTransaction::STATUS_CONFIRMED && $commissionAmount == 0) {
				$status = ImportedTransaction::STATUS_CANCELLED;
			}

			$importedTransaction = new ImportedTransaction($this->partnerSystem, null, $userId, $transactionId, $commissionAmount, $orderAmount, Currency::USD, $status, $registeredAt, null, null, ImportedTransaction::CHANNEL_API);

			$this->scheduleImportedTransaction($importedTransaction);
		}
	}

	private function resolveStatus($orderStatus, $date, $amount)
	{
		$statuses = [
			'Completed Payments' => ImportedTransaction::STATUS_REGISTERED,
			'Completed Orders' => ImportedTransaction::STATUS_CONFIRMED,
			'Refund Orders' => ImportedTransaction::STATUS_CANCELLED,
		];

		$status = $statuses[$orderStatus];

		$amountConfirmBorder = 20;
		$dateConfirmBorder = 60; // in days

		$dateLimit = clone $date;
		$dateLimit->modify('+ ' . $dateConfirmBorder . 'days');

		if ($status == ImportedTransaction::STATUS_CONFIRMED) {
			if ($date->diff(new \DateTime())->days < 30 || ($amount > $amountConfirmBorder && new \DateTime() < $dateLimit)) {
				$status = ImportedTransaction::STATUS_REGISTERED;
			}
		}

		return $status;
	}

	private function getRecords(\DateTime $from, \DateTime $to, $pagesLimit = 5)
	{
		$countOfItemsPerPage = 20;
		$incompletedPages = ceil($this->getCountOfResults($from, $to) / $countOfItemsPerPage);
		$completedPages = ceil($this->getCountOfResults($from, $to, true) / $countOfItemsPerPage);

		if ($incompletedPages > $pagesLimit) {
			$incompletedPages = $pagesLimit;
		}

		if ($completedPages > $pagesLimit) {
			$completedPages = $pagesLimit;
		}

		$items = [];
		for ($i = 1; $i <= $incompletedPages; $i++) {
			$items = array_merge($items, $this->getRecordsPerPage($from, $to, false, $i));
		}

		for ($i = 1; $i <= $completedPages; $i++) {
			$items = array_merge($items, $this->getRecordsPerPage($from, $to, true, $i));
		}

		return $items;
	}

	private function getRecordsPerPage(\DateTime $from, \DateTime $to, $completed = false, $page = 1, $countOfItemsPerPage = 20)
	{
		$result = $this->fetchRecordsToRegister($from, $to, $completed, $page, $countOfItemsPerPage);

		if ($result->errorCode != 20010000) {
			return [];
		}

		return $result->result->orders;
	}

	private function getCountOfResults(\DateTime $from, \DateTime $to, $completed = false, $countOfItemsPerPage = 20)
	{
		$result = $this->fetchRecordsToRegister($from, $to, $completed, 1, $countOfItemsPerPage);

		if ($result->errorCode != 20010000) {
//			Debugger::log('Aliexpress error code pri volani: ' . $result->errorCode, 'aliexpress');
			return 0;
		}

		$totalResults = $result->result->totalResults;

		return $totalResults == '-' ? 100 : $totalResults;
	}

	private function fetchRecordsToRegister(\DateTime $from, \DateTime $to, $completed = false, $page = 1, $countOfItemsPerPage = 20)
	{
		$status = $completed ? 'success' : 'pay';
		$apiUrl = 'https://gw.api.alibaba.com/openapi/param2/2/portals.open/api.getCompletedOrders/' . $this->partnerSystem->getOption('appKey') .
		sprintf('?appSignature=%s', $this->partnerSystem->getOption('appSignature')) .
		sprintf('&liveOrderStatus=%s', $status) .
		sprintf('&startDate=%s', $from->format('Y-m-d')) .
		sprintf('&endDate=%s', $to->format('Y-m-d')) .
		sprintf('&pageSize=%s', $countOfItemsPerPage) .
		sprintf('&pageNo=%d', $page);

		return $this->sendRequest($apiUrl);
	}

	private function sendRequest($url)
	{
		$client = new Client();

		try {
			$response = $client->request('GET', $url);
		} catch (ConnectException $e) {
//			Debugger::log('Aliexpress problem s pripojenim pri volani requestu', 'aliexpress');
			return [];
		}

		return Json::decode($response->getBody()->getContents());
	}
}
