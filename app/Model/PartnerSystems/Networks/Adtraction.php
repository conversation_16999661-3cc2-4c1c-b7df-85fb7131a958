<?php

namespace tipli\Model\PartnerSystems\Networks;

use GuzzleHttp\Client;
use Nette\Http\Url;
use Nette\Utils\Json;
use tipli\InvalidArgumentException;
use tipli\Model\Deals\Entities\Deal;
use tipli\Model\Deals\ImportedDeal;
use tipli\Model\Deals\Producers\DealImporterProducer;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use League\Csv\Reader;
use Nette\Utils\Strings;
use tipli\InvalidImportException;
use tipli\Model\Currencies\Currency;
use tipli\Model\PartnerSystems\Producers\PartnerSystemsDealProducer;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Transactions\ImportedTransaction;
use tipli\Model\Files\Entities\File;
use Tracy\Debugger;

interface IAdtractionFactory
{
	/**
	 * @return Adtraction
	 */
	public function create(PartnerSystem $partnerSystem);
}

class Adtraction extends Network
{
	/** @var DealImporterProducer $dealImporterProducer @inject */
	public $dealImporterProducer;

	/** @var PartnerSystemsDealProducer $partnerSystemsDealProducer @inject */
	public PartnerSystemsDealProducer $partnerSystemsDealProducer;

	public function createRedirectionLink($url, $userId, $redirectionId, $deepUrl = null)
	{
		$url = new Url($url);
		$url->setQueryParameter('epi', $userId . 'x' . $redirectionId);
		$url->setQueryParameter('epi2', $userId . 'x' . $redirectionId);

		$absoluteUrl = $url->getAbsoluteUrl();

		return $absoluteUrl;
	}

	public function import(File $file)
	{
		$reader = Reader::createFromPath($this->fileStorage->getFile($file));

		// get the header
		$headers = $reader->fetchOne();
		$headers = array_map(static function ($item) {
			return Strings::webalize(str_replace('"', '', $item));
		}, $headers);

		$importedCurrency = explode('-', $headers[13])[1];

		$orderColumn = 'order-value-' . $importedCurrency;
		$commissionColumn = 'commission-' . $importedCurrency;
		$currency = Currency::getCurrencyByName(Strings::upper($importedCurrency));

		$requiredColumns = ['channel', 'epi', 'id', 'event', $orderColumn, $commissionColumn, 'status'];

		if (!count(array_intersect($headers, $requiredColumns)) == count($requiredColumns)) {
			throw new InvalidImportException('Adtraction import není ve správném formátu..');
		}

		$results = $reader->getRecords($headers);

		foreach ($results as $index => $row) {
			// skip header
			if ($index === 0) {
				continue;
			}

			//skip last result row
			if ($row['id'] == '') {
				continue;
			}

			$channel = Strings::webalize(Strings::lower($row['channel']));
			if (($channel == 'tipli.cz' && $currency != Currency::CZK) || ($channel == 'tipli' && $currency != Currency::PLN)) {
				throw new InvalidArgumentException('Adtraction - currency is not CZK or PLN');
			}

			$partnerSystemKey = Strings::webalize(Strings::lower($row['advertiser']));
			$userId = $row['epi'];
			$commissionAmount = (float) $row[$commissionColumn];
			$orderAmount = (float) str_replace(',', '', $row[$orderColumn]);
			$registeredAt = new \DateTime($row['event']);

			$transactionId = $row['order-id'] ?? null;

			if ($registeredAt < new \DateTime('2019-01-03 00:00:00') || !$transactionId) {
				$transactionId = $row['id'];
			}

			$status = $this->resolveStatus(Strings::lower($row['status']));

			$importedTransaction = new ImportedTransaction($this->partnerSystem, $partnerSystemKey, $userId, $transactionId, $commissionAmount, $orderAmount, $currency, $status, $registeredAt, null, null, ImportedTransaction::CHANNEL_IMPORT);

			$this->scheduleImportedTransaction($importedTransaction);
		}
	}

	private function resolveStatus($status)
	{
		if (is_int($status)) {
			switch ($status) {
				case 1:
					return ImportedTransaction::STATUS_CONFIRMED;
				case 3:
				case 4:
				case 2:
					return ImportedTransaction::STATUS_REGISTERED;
				case 5:
					return ImportedTransaction::STATUS_CANCELLED;
			}
		} else {
			switch ($status) {
				case 'invoiced':
				case 'paid to affiliate':
				case 'confirmed':
					return ImportedTransaction::STATUS_CONFIRMED;
				case 'lead':
				case 'claim':
				case 'pending':
					return ImportedTransaction::STATUS_REGISTERED;
				case 'rejected':
					return ImportedTransaction::STATUS_CANCELLED;
			}
		}

		throw new InvalidArgumentException('unknown status ' . $status);
	}

	public function processTransactions(\DateTime $from = null, \DateTime $to = null)
	{
		$nowFrom = (new \DateTime())->modify('-1 day');
		$nowTo = (new \DateTime())->modify('+1 day');
		$records = [];

		if ($from && $to) {
			$records = array_merge($records, $this->getRecords($from, $to));
		}
		$records = array_merge($records, $this->getRecords($nowFrom, $nowTo));

		foreach ($records as $record) {
			$currency = Currency::getCurrencyByName($record->currency);

			list($userId, $redirectId) = explode('x', $record->click->epi);
			$partnerSystemKey = $record->click->programId;
			$transactionId = $record->uniqueId;
			$commissionAmount = (float) str_replace(',', '', $record->commission);
			$orderAmount = (float) str_replace(',', '', $record->orderValue);
			$registeredAt = new \DateTime($record->transactionDate);
			$registeredAt->setTimezone(new \DateTimeZone('Europe/Prague'));
			$status = $this->resolveStatus($record->transactionStatus);

			$confirmedAt = null;
			if ($status == ImportedTransaction::STATUS_CONFIRMED) {
				$confirmedAt = new \DateTime($record->lastUpdated ?? 'now');
			}

			$importedTransaction = new ImportedTransaction($this->partnerSystem, $partnerSystemKey, $userId, $transactionId, $commissionAmount, $orderAmount, $currency, $status, $registeredAt, $confirmedAt, null, ImportedTransaction::CHANNEL_API);
			$this->scheduleImportedTransaction($importedTransaction);
		}
	}

	private function getRecords(\DateTime $from, \DateTime $to)
	{
		$records = $this->request($this->partnerSystem->getOption('apiUrl'), [
			'fromDate' => $from->format('Y-m-d\TH:i:sO'),
			'toDate' => $to->format('Y-m-d\TH:i:sO'),
			'transactionStatus' => 0,
		]);

		if (!is_array($records)) {
			return [];
		}

		return $records;
	}

	public function processSales()
	{
		foreach ($this->partnerSystem->getOption('salesLocales') as $locale) {
			$sales = $this->request($this->partnerSystem->getOption('salesUrl'), ['market' => Strings::upper($locale)]);

			if (!is_array($sales)) {
				continue;
			}

			$localization = $this->localizationFacade->findOneByLocale($locale);

			foreach ($sales as $sale) {
				$uniqueId = $sale->programId . $sale->offerId;
				$type = $this->resolveStatus($sale->offerType);
				$name = Strings::truncate($sale->offerDescription, 255);
				$slug = Strings::webalize($name);
				$couponCode = $sale->offerCoupon ?? null;
				$rules = $sale->offerTerms ?? null;
				$url = $sale->offerPage;
				$validSince = new \DateTime($sale->validFrom);
				$validTill = new \DateTime($sale->validTo);
				$couponDiscountValue = null;
				$couponDiscountUnit = null;

				/** @var Shop|null $shop */
				$shop = $this->shopFacade->findByPartnerSystem($this->partnerSystem, $sale->programId, $localization);

				if (!$shop) {
					Debugger::log('cant identify shop by campaignId: ' . $sale->programId, 'Adtraction-processSales');
					continue;
				}

				if ($validTill < new \DateTime()) {
					continue;
				}

				if (!$name) {
					continue;
				}

				if (!$type) {
					Debugger::log('unknown offer type ' . $sale->offerType, 'Adtraction-processSales');
					continue;
				}

				if ($couponCode) {
					if (preg_match('/([0-9,.]+)\s?([zł|Kč|%]{1,2})/iu', $name, $matches)) {
						if (count($matches) < 3) {
							Debugger::log('unknown voucher value ' . $name, 'Adtraction-processSales');
							continue;
						}

						list ($fullMatch, $couponDiscountValue, $couponDiscountUnit) = $matches;

						if ($couponDiscountUnit === '%') {
							$couponDiscountUnit = Deal::UNIT_PERCENTAGE;
						} else {
							$couponDiscountUnit = Currency::getCurrencyFromSymbol($couponDiscountUnit);
						}
					}

					if (!$couponDiscountValue || !$couponDiscountUnit) {
						Debugger::log('can\'t resolve coupon discount unit or value ' . $name, 'Adtraction-processSales');
						continue;
					}
				}

				/** @var ImportedDeal $importedDeal */
				$importedDeal = new ImportedDeal($localization->getId(), $shop->getId(), null, $name, $slug, $validSince, $validTill);

				$importedDeal->setUniqueId($uniqueId);
				$importedDeal->setSourceName($this->partnerSystem->getFeedName());
				$importedDeal->setSourceType(Deal::SOURCE_TYPE_API);
				$importedDeal->setValidSince($validSince);
				$importedDeal->setValidTill($validTill);
				$importedDeal->setName($name);
				$importedDeal->setDeepUrl($url);
				$importedDeal->setDescription($rules);

				if ($couponCode) {
					$importedDeal->setType(Deal::TYPE_COUPON);
					$importedDeal->setCode($couponCode);
					$importedDeal->setUnit($couponDiscountUnit);
					$importedDeal->setValue($couponDiscountValue);
				} else {
					$importedDeal->setType(Deal::TYPE_TIP);
				}

				//$this->dealImporterProducer->scheduleImportedDeal($importedDeal);
				$this->partnerSystemsDealProducer->scheduleDeal($importedDeal);
			}
		}
	}

	public function resolveOfferType($type)
	{
		switch ($type) {
			case 1:
				return Deal::TYPE_COUPON;
			case 2:
				return Deal::TYPE_SALE;
		}

		return null;
	}

	private function request(string $endpointUrl, array $params)
	{
		$client = new Client();

		$options = [
			'headers' => [
				'Content-Type' => 'application/json',
				'X-Token' => $this->partnerSystem->getOption('apiKey'),
			],
		];

		if ($params) {
			$options['body'] = Json::encode($params);
		}

		$response = $client->request('POST', $endpointUrl, $options);

		return Json::decode($response->getBody()->getContents());
	}
}
