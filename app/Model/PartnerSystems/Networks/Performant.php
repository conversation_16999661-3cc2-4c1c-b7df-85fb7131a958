<?php

namespace tipli\Model\PartnerSystems\Networks;

use GuzzleHttp\Client;
use GuzzleHttp\RequestOptions;
use League\Csv\Reader;
use Nette\Http\Url;
use Nette\Utils\Json;
use Nette\Utils\Strings;
use tipli\InvalidArgumentException;
use tipli\InvalidImportException;
use tipli\Model\Currencies\Currency;
use tipli\Model\Files\Entities\File;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\PartnerSystems\PartnerSystemProcessResult;
use tipli\Model\Transactions\ImportedTransaction;

interface IPerformantFactory
{
	/** @return Performant */
	public function create(PartnerSystem $partnerSystem);
}

class Performant extends Network
{
	/** @var Client */
	private $client;

	/** @var string */
	private $apiAccessToken;

	/** @var string */
	private $apiTokenType;

	/** @var string */
	private $apiClient;

	/** @var int */
	private $apiUid;

	public const PROGRAM_UNIQUE_IDS = [132 => "8fa76d04d", 284 => "ed6e28c6e", 345 => "087e21d9f", 383 => "d5050c390", 389 => "4ad33db48", 411 => "d4f678b43", 433 => "00e4f4d54", 446 => "90bc72e35", 458 => "be2b49965", 480 => "d401268fb", 488 => "a1880c1fe", 505 => "4ae47c7ef", 512 => "8f3acdb11", 525 => "2e49eab4f", 576 => "79a6773ee", 591 => "0a55073f0", 624 => "ac2bffaf7", 628 => "9a6f02fef", 633 => "7e537fc0d", 636 => "67024d20f", 690 => "9617003d6", 699 => "f2d6169d1", 773 => "e3ce46cfa", 794 => "9a7c9ac43", 825 => "da1148931", 850 => "b9385e46f", 856 => "e518db993", 873 => "0a8a2c4d9", 882 => "184f69294", 907 => "a5e9e1225", 929 => "4f3a0c67c", 935 => "cb588f07f", 937 => "f659ee6d5", 958 => "9aab15f10", 1055 => "39849df4a", 1118 => "32a6cdf55", 1130 => "53818b507", 1212 => "040f7e27b", 1235 => "fd031aed4", 1270 => "94d225abf", 1275 => "5a920d82c", 1327 => "90e5f8c54", 1368 => "0abf54346", 1440 => "4450ef117", 1462 => "2761e6fd1", 1464 => "36460d3fc", 1471 => "8bc6d7111", 1486 => "f936b1b54", 1498 => "1409267f5", 1546 => "ffa0a469f", 1567 => "77ba9e67f", 1573 => "5baacfa1f", 1581 => "706637522", 1606 => "50a81426a", 1637 => "0a69f5ba2", 1652 => "212d9fcf5", 1659 => "e4eab12b6", 1664 => "80ef38da6", 1677 => "07b5f0fed", 1711 => "c3bdfda38", 1716 => "ecb652a5e", 1722 => "b61a210a8", 1756 => "3ab624794", 1757 => "5de68c36f", 1793 => "4dc37856e", 1815 => "f8a99eac2", 1850 => "15b6bd058", 1860 => "444575266", 1938 => "f04dc0f30", 1961 => "861f9e301", 1969 => "f87fbfcc4", 2000 => "646cd29ac", 2021 => "59bb5f58d", 2028 => "a9a91cea2", 2059 => "cf9dbf315", 2070 => "b102aee32", 2112 => "c7b418b04", 2154 => "a41bf974c", 2184 => "0e0a9347f", 2243 => "367ede5cd", 2294 => "05a8c81a1", 2384 => "4f0c269be", 2405 => "884f13c4f", 2428 => "c8bec220d", 2472 => "b09908f08", 2572 => "0edbd1504", 2615 => "bb3071a7d", 2625 => "424485ac5", 2650 => "a9ea8a546", 2662 => "313128750", 2676 => "60b800055", 2678 => "09779f562", 2722 => "3565a82b0", 2748 => "8cf043bc6", 2754 => "a46311f72", 2786 => "309209d66", 2822 => "3a1746ee7", 2891 => "28a0dd759", 3010 => "cde040759", 3035 => "6f33f2a39", 3089 => "3085d2457", 3151 => "1f8e1c390", 3160 => "400c6069c", 3271 => "754ea50b5", 3385 => "75488ec7b", 3409 => "c6dae5faa", 3473 => "f5dd9a46a", 3530 => "46f8cd5eb", 3532 => "ffa0d55af", 3556 => "7cc138816", 3612 => "7b0460236", 3637 => "fa7843c33", 3754 => "98cc861a7", 3893 => "2396fef36", 3989 => "cc08e2536", 4035 => "14d232641", 4036 => "0c1a7057d", 4060 => "adf01d61b", 4063 => "b25659819", 4069 => "2fa33259c", 4083 => "8e6000c80", 4098 => "28d837221", 4182 => "dbe21092c", 4185 => "2173f05f3", 4209 => "41db07e03", 4224 => "3fd6221b1", 4257 => "67f11dee9", 4311 => "bb24d9ce5", 4312 => "e085cdb83", 4470 => "c1e2e9f3f", 4495 => "0a282cf97", 4610 => "e661a4b76", 4686 => "9918fab64", 4789 => "1c1576ba9", 4825 => "731158c82", 4869 => "f394d2a00", 4871 => "2466c613d", 4888 => "d404a783d", 4939 => "0999f10ff", 5142 => "9a7ef9b40", 5151 => "12a5c4900", 5176 => "a14aa39cc", 5192 => "4b9a6d5ea", 5223 => "f44fd3c8f", 5233 => "e71662392", 5265 => "6d7b342e3", 5271 => "0069528b6", 5277 => "f6b801b06", 5287 => "245708582", 5312 => "87d77c7b9", 5378 => "56a090d7a", 5413 => "19f149258", 5434 => "58a6e453f", 5450 => "ca0e67b7f", 5503 => "4c4885d8b", 5519 => "5493f0b92", 5522 => "04b14ad56", 5546 => "7e5ed3049", 5550 => "1dfbc028f", 5572 => "05d8a5892", 5625 => "5e58b4e80", 5664 => "92c053035", 5672 => "48a30b7c9", 5718 => "4a61a7588", 5767 => "ffde15e2f", 5771 => "fc11297bd", 5809 => "4401106cd", 5818 => "3da8a1b48", 5925 => "568090529", 6135 => "d42a9ce58", 6154 => "8d5662e9f", 6245 => "ddd596cc4", 6268 => "4eed54264", 6282 => "05898874d", 6316 => "1bf67dbe4", 6330 => "864699797", 6334 => "94045f09d", 6364 => "421244207", 6401 => "50416786e", 6405 => "f2e28353b", 6427 => "f15a58da6", 6440 => "f8461feff", 6526 => "50e9adcf3", 6599 => "a5e857cd9", 6625 => "216e8f76d", 6674 => "413c802af", 6851 => "d598c8ac6", 6872 => "9ecd0126b", 6878 => "e9b684613", 6913 => "c889f94a6", 7014 => "c7b68464c", 7070 => "60a8b63d2", 7082 => "fae7c7679", 7115 => "da7d360c1", 7131 => "8b83ae69a", 7156 => "431c2ef95", 7157 => "ca38c9368", 7175 => "2ff4d2991", 7183 => "478cb16f4", 7184 => "d6fcd64c1", 7187 => "ed360ebe2", 7201 => "f919a2dce", 7235 => "de929938e", 7347 => "d8da403ea", 7381 => "2246d2918", 7492 => "c43368d27", 7539 => "c739b2e3b", 7594 => "a554914db", 7616 => "2a0abc2c5", 7627 => "299b97f2d", 7682 => "0e39a2519", 7781 => "74683eea3", 7860 => "2d2273411", 7875 => "9abbbd0e9", 7884 => "44bb0e642", 7894 => "27ec4b078", 7905 => "772e5083a", 7933 => "21cf9abb0", 7990 => "9659b7651", 8013 => "55df46338", 8016 => "fbf67d9a2", 8025 => "51d11393c", 8036 => "501fc252b", 8054 => "2eb995be1", 8062 => "54d396a54", 8079 => "ab2c0c015", 8103 => "8360baa1b", 8108 => "07e65acfe", 8139 => "6390e3cfb", 8144 => "51f00ce0d", 8153 => "20db4b4e7", 8155 => "3ec20f3dc", 8194 => "fef3ef7dc", 8219 => "01a9a0f26", 8268 => "0ea3346dd", 8280 => "bdabf16d9", 8286 => "e245b26bc", 8326 => "bb9cb6802", 8339 => "790f6cd79", 8347 => "3f2fc358a", 8349 => "cda550516", 8350 => "a11b7b0d7", 8359 => "e59380926", 8360 => "9b1d818eb", 8397 => "d4a607fd3", 8427 => "573b44539", 8436 => "4039ee84a", 8445 => "229754a2c", 8458 => "281d476d8", 8485 => "aea2041fa", 8497 => "3b0b5a0bf", 8502 => "322bda0fb", 8519 => "6a8af3dcc", 8529 => "de9651a1f", 8536 => "ca75a401c", 8560 => "d7287555a", 8562 => "6146dfc5f", 8563 => "477ec7520", 8564 => "0e7e08583", 8607 => "37f55ab99", 8609 => "e06ef5ee2", 8666 => "0b5fcbb96", 8673 => "f756cc32b", 8685 => "bfc4c2dce", 8813 => "c03d5cda1", 8818 => "f116921ab", 8878 => "cf0c66246", 8886 => "e7bc77dba", 8890 => "c257eeead", 8920 => "71dda413a", 8928 => "3efdbc6c8", 8935 => "190c2e3b3", 8958 => "91ddf66cb", 8961 => "75667dd72", 8964 => "ff599b873", 8967 => "604687bb7", 8979 => "9c4727498", 9000 => "d5075b651", 9100 => "82845fa70", 9163 => "a94e29437", 9204 => "dec236cad", 9246 => "fc562044c", 9264 => "cbab37232", 9284 => "bd1301cce", 9293 => "732aae773", 9315 => "e7a1eb999", 9341 => "171ec84ee", 9353 => "ccbf91def", 9404 => "7b2bb25ec", 9439 => "513164aff", 9455 => "8c98d9341", 9458 => "8c28ab2d2", 9501 => "fd0d8a0c3", 9505 => "025fbaa5d", 9618 => "2642c34a4", 9638 => "506706e53", 9657 => "17a3613f8", 9660 => "d6129cd00", 9693 => "94d8b3e3b", 9705 => "522f2a38e", 9717 => "c09bcacc2", 9736 => "fec8438a9", 9743 => "c6211f604", 9746 => "4dffec7e8", 9791 => "d404f3ff0", 9812 => "5abfb2edd", 9823 => "1ec3596e6", 9834 => "4aff2c394", 9839 => "b09284244", 9848 => "d2c0e8ca6", 9850 => "1f2342012", 9851 => "5990d52aa", 9887 => "5a7af5822", 9899 => "82ec146ca", 9909 => "fcc8c8536", 9938 => "938e46537", 9967 => "42bc4def7", 9970 => "b22a2e173", 10010 => "5a34fd138", 10017 => "3562da932", 10019 => "e56a09878", 10020 => "401ba6e73", 10029 => "e3d889555", 10044 => "6d2ab7cb4", 10047 => "fab21dcf7", 10051 => "c79ca7e72", 10054 => "fea2bee89", 10056 => "90d2775e0", 10062 => "c34fa7e29", 10070 => "d8d0bdbba", 10071 => "ec567488a", 10073 => "deeba38e6", 10077 => "29fc0be8e", 10081 => "42ec8bef4", 10082 => "dcdc38903", 10083 => "5c95c3c58", 10085 => "4ef912004", 10090 => "e62532c33", 10091 => "3ad30e42b", 10097 => "9bdfaba23", 10099 => "35241b78e", 10104 => "f708f5534", 10106 => "bea2df30f", 10114 => "75ac352f9", 10116 => "07a2ce605", 10117 => "5ed7b2553", 10122 => "799e8fadd", 10130 => "2fbf8acd0", 10132 => "754430051", 10134 => "d19151f4f", 10135 => "75ac897f4", 10136 => "1cf328111", 10139 => "ea7857c9c", 10153 => "07ee67319", 10154 => "490eefa08", 10155 => "68ca2c51b", 10159 => "6a453a2a5", 10170 => "989f060e9", 10175 => "8ded2f65b", 10181 => "7818b420c", 10184 => "b52835caa", 10186 => "be42df24f", 10187 => "083d30a66", 10190 => "7c693c981", 10192 => "5cfa464b4", 10193 => "0be5d72ae", 10194 => "e0338fcc3", 10197 => "b30959199", 10199 => "4480e459f", 10201 => "ef793b37d", 10205 => "e540eff8e", 10207 => "156d14d4f", 10215 => "52fd78c2c", 10219 => "655dd2374", 10229 => "43661af18", 10240 => "67b9ce22d", 10241 => "95ff9b01a", 10243 => "c6016eb67", 10247 => "a369272b3", 10248 => "436288837", 10251 => "96d5da3c4", 10255 => "dd18899e5", 10260 => "90cc26df2", 10264 => "bf08cf29b", 10268 => "94832153f", 10270 => "e148bd087", 10271 => "5e4135c1c", 10273 => "93bdc07c4", 10274 => "ced005319", 10282 => "e2b21b98d", 10295 => "7589b7933", 10299 => "6e0b0b0df", 10303 => "5cc980d88", 10306 => "eca048411", 10307 => "9bf544b4b", 10310 => "a71311b5a", 10312 => "718e76f5b", 10318 => "4b1d83db1", 10323 => "f91a8f988", 10324 => "075dc3544", 10325 => "8e59c17b0", 10326 => "f4e470e8d", 10328 => "b8c7b2037", 10334 => "c5408677d", 10337 => "09f902c5c", 10345 => "ce9f8cb2c", 10347 => "0c3054359", 10349 => "91a708b99", 10350 => "16c52c962", 10356 => "4c379db42", 10360 => "6e7fc3433", 10361 => "8dad9830f", 10362 => "bb621a3f4", 10374 => "eb863d0d2", 10375 => "f1bb3ae59", 10376 => "d911e9d71", 10378 => "c94ca04f3", 10379 => "560980d8f", 10382 => "a0689c44a", 10383 => "09008a698", 10384 => "1e304e22f", 10385 => "90a06114e", 10387 => "8268bf1be", 10389 => "460d73101", 10402 => "2482e4cbe", 10406 => "0b33481b4", 10408 => "d3020cc56", 10412 => "23dea9330", 10413 => "73799b837", 10414 => "2c4eb8772", 10415 => "6f12561aa", 10416 => "50fd66d67", 10417 => "69cf32ac9", 10418 => "ace674f62", 10419 => "6ced40826", 10420 => "768232f44", 10421 => "ee120ed49", 10423 => "938c02434", 10427 => "f6cf397df", 10429 => "582d446a6", 10435 => "3570dc974", 10437 => "f4ff1138e", 10439 => "7d4493402", 10440 => "191366864", 10445 => "204b87b24", 10454 => "b590653ca", 10464 => "9f144b2da", 10466 => "24adc4186", 10472 => "a88fd794e", 10473 => "bdcb96b79", 10474 => "e3d91d33e", 10476 => "1bd3131c9", 10481 => "beb29d722", 10489 => "0383ce0be", 10499 => "4358c241d", 10500 => "41327da9d", 10504 => "e26c2f295", 10507 => "28b5f50b1", 10508 => "31a870f64", 10510 => "3bfa0ec2d", 10512 => "44ad2ee96", 10513 => "aeeb7cb5a", 10514 => "a4077a25f", 10515 => "00eef474b", 10518 => "1805e7205", 10525 => "56d1489f9", 10526 => "b8e07a1e9", 10531 => "0f73de8fd", 10532 => "ce1c50262", 10536 => "855cf8071", 10544 => "556733a1b", 10549 => "2fe0c2868", 10550 => "612db5fe6", 10553 => "7d79cad05", 10558 => "3d2339dd9", 10579 => "c459e147f", 10582 => "dd2ae14d4", 10583 => "f548deb79", 10584 => "cf30301a1", 10590 => "5ec0bc36d", 10596 => "805f0a1a3", 10602 => "2db3ac442", 10639 => "b47697fd7", 10643 => "96b5f5b45", 10653 => "ecffdf120", 10669 => "d131c7dfb", 10675 => "a968e1cf3", 10683 => "9f1263709", 10686 => "6d1c0e5ce", 10692 => "84908191c", 10717 => "3952fcb60", 10719 => "4612c4c58", 10728 => "8e4c40bdd", 10736 => "853fff54b", 10738 => "cd7d02fae", 10739 => "d9bff9b61", 10740 => "486f0ff5d", 10744 => "dea41dcfa", 10748 => "5327f8aa9", 10755 => "cc99055d6", 10758 => "9f7684a55", 10764 => "77dec1824", 10766 => "03bb372c7", 10775 => "5e10088d0", 10776 => "85ce49221", 10778 => "89d26b6ec", 10779 => "ae38f094b", 10785 => "eb291f606", 10786 => "deb836f21", 10787 => "b4bc451bd", 10789 => "22c4a6f2a", 10791 => "2111b7ebd", 10792 => "66c68d2a4", 10793 => "e122617a7", 10797 => "1ff42075f", 10811 => "69db7d852", 10813 => "6855dadab", 10814 => "a620be36b", 10820 => "2688ac1d3", 10823 => "da6e4d718", 10824 => "b3648c174", 10829 => "bb7f88924", 10832 => "a11314b24", 10835 => "bea48bd79", 10838 => "9dd5272cf", 10841 => "8ca096430", 10848 => "8bb172815", 10850 => "6c9dd9bd3", 10853 => "dc53a764d", 10856 => "6c053035f", 10862 => "ef2621c0a", 10864 => "70ed36e79", 10871 => "7568196a4", 10872 => "14b3ee455", 10875 => "1741b8e36", 10884 => "3c9d0299d", 10887 => "28805d89b", 10894 => "892843614", 10900 => "0f9f28b38", 10902 => "c12f187b0", 10907 => "80aa6e7e1", 10909 => "1ca38f3e2", 10910 => "ccb7a5fad", 10911 => "d1b11793e", 10921 => "24e0dd8bd", 10922 => "539ab1e68", 10930 => "cb9acd60b", 10932 => "f4dd1f868", 10944 => "47ecc4496", 10960 => "4c7b27b30", 10972 => "0e4f00934", 10973 => "2728ee619", 10974 => "2c77849ab", 10975 => "060d02986", 10984 => "b07ea10ae", 10986 => "8f6d14453", 10988 => "36d49d322", 10992 => "81a821097", 10995 => "01b1af9da", 10997 => "121e85469", 10999 => "d425323c9", 11000 => "ee0d045d3", 11001 => "7aafa644b", 11002 => "055b7648a", 11005 => "61e0a7127", 11012 => "6576430e9", 11020 => "8d8621a47", 11021 => "88de869b7", 11024 => "1145db170", 11025 => "f5379b5d9", 11026 => "c5d6670d6", 11028 => "d5c6d7f9b", 11034 => "09d32fa9f", 11035 => "c7b717d58", 11038 => "2cfadce36", 11041 => "adb202c7d", 11077 => "ec4470863", 11080 => "083b6205a", 11084 => "14732dbbb", 11096 => "c76ac25ef", 11097 => "6b3d99933", 11112 => "131408c23", 11113 => "70f622281", 11115 => "be99b912f", 11123 => "8e2b627c4", 11135 => "4d2b03098", 11169 => "b83e731f2"];

	public function createRedirectionLink($url, $userId, $redirectionId, $deepUrl = null): string
	{
		$url = new Url($url);
		$url .= '&st=' . $userId . 'x' . $redirectionId;

		if ($deepUrl) {
			$url .= '&redirect_to=' . $deepUrl;
		}

		return $url;
	}

	public function processTransactions(\DateTime $from = null, \DateTime $to = null): PartnerSystemProcessResult
	{
		$this->login();

		$records = [];

		if ($from && $to) {
			$countOfPages = $this->getRecordsCountOfPages($from, $to);

			for ($page = 1; $page <= $countOfPages; $page++) {
				$records = array_merge($records, $this->getRecords($from, $to, $page));
			}
		}

		$countOfPages = $this->getRecordsCountOfPages(new \DateTime(), new \DateTime());
		for ($page = 1; $page <= $countOfPages; $page++) {
			$records = array_merge($records, $this->getRecords(new \DateTime(), new \DateTime(), $page));
		}

		foreach ($records as $record) {
			if (!$record->stats_tags) {
				continue;
			}

			$currency = $record->currency;
			$commissionAmount = (float) $record->amount;
			$orderAmount = (float) $record->public_action_data->amount;

			if (isset($record->public_action_data->amount_in_working_currency)) {
				$currency = $record->working_currency_code;

				$commissionAmount = (float) $record->amount_in_working_currency;
				$orderAmount = (float) $record->public_action_data->amount_in_working_currency;
			}

			if (!$currency || !Currency::getCurrencyByName($currency)) {
				throw new InvalidArgumentException('Undefined partner system currency.');
			}

			$userId = Strings::trim($record->stats_tags, ',');
			$partnerSystemKey = $record->program_id;
			$transactionId = $record->id;
			$registeredAt = new \DateTime($record->created_at);
			$registeredAt->setTimezone(new \DateTimeZone('Europe/Prague'));
			$status = $this->resolveStatus($record->status);

			$importedTransaction = new ImportedTransaction($this->partnerSystem, $partnerSystemKey, $userId, $transactionId, $commissionAmount, $orderAmount, $currency, $status, $registeredAt, null, null, ImportedTransaction::CHANNEL_API);

			$this->scheduleImportedTransaction($importedTransaction);
		}

		return new PartnerSystemProcessResult(count($records));
	}

	/**
	 * @throws \Nette\Utils\JsonException
	 */
	private function login(): void
	{
		$params = [
			'user' => [
				'email' => $this->partnerSystem->getOption('email'),
				'password' => $this->partnerSystem->getOption('password'),
			],
		];

		$options = [
			RequestOptions::HEADERS => ['Content-Type' => 'application/json'],
			RequestOptions::BODY => Json::encode($params),
		];

		$this->client = new Client();
		$response = $this->client->request('POST', 'https://api.2performant.com/users/sign_in', $options);
		$contents = Json::decode($response->getBody()->getContents());

		$this->apiAccessToken = current($response->getHeader('access-token'));
		$this->apiTokenType = current($response->getHeader('token-type'));
		$this->apiClient = current($response->getHeader('client'));
		$this->apiUid = $contents->user->email;
	}

	private function getRecords(\DateTime $startDate, \DateTime $endDate, int $page = 1): array
	{
		$params = [
			'page' => $page,
			'perpage' => 50,
			'filter' => [
				'start_date' => $startDate->format('Y-m-d'),
				'end_date' => $endDate->format('Y-m-d'),
			],
		];

		$options = [
			RequestOptions::HEADERS => [
				'Accept' => '*/*',
				'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36',
				'Content-Type' => 'application/json',
				'access-token' => $this->apiAccessToken,
				'client' => $this->apiClient,
				'uid' => $this->apiUid,
			],
			RequestOptions::BODY => Json::encode($params),
		];

		$response = $this->client->request('GET', 'https://api.2performant.com/affiliate/commissions', $options);
		$contents = Json::decode($response->getBody()->getContents());

		return $contents->commissions;
	}

	private function getRecordsCountOfPages(\DateTime $startDate, \DateTime $endDate)
	{
		$params = [
			'perpage' => 50,
			'filter' => [
				'start_date' => $startDate->format('Y-m-d'),
				'end_date' => $endDate->format('Y-m-d'),
			],
		];

		$options = [
			RequestOptions::HEADERS => [
				'Accept' => '*/*',
				'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36',
				'Content-Type' => 'application/json',
				'access-token' => $this->apiAccessToken,
				'client' => $this->apiClient,
				'uid' => $this->apiUid,
			],
			RequestOptions::BODY => Json::encode($params),
		];

		$response = $this->client->request('GET', 'https://api.2performant.com/affiliate/commissions', $options);
		$contents = Json::decode($response->getBody()->getContents());

		return $contents->metadata->pagination->pages;
	}

	public function import(File $file)
	{
		$reader = Reader::createFromPath($this->fileStorage->getFile($file));
		$reader->setDelimiter(',');

		// get the header
		$headers = $reader->fetchOne();
		$headers = array_map(static function ($item) {
			return Strings::webalize(str_replace('"', '', $item));
		}, $headers);

		$requiredColumns = ['id', 'program', 'transaction-date', 'commission-amount-ron', 'sale-amount-ron', 'click-tag', 'status'];

		if (!count(array_intersect($headers, $requiredColumns)) == count($requiredColumns)) {
			throw new InvalidImportException('Shopee import není ve správném formátu..');
		}

		$results = $reader->getRecords($headers);

		foreach ($results as $index => $row) {
			if ($index === 0) {
				continue;
			}

			$status = $this->resolveStatus($row['status']);

			$transactionId = $row['id'];
			$commissionAmount = (float) $row['commission-amount-ron'];
			$orderAmount = (float) $row['sale-amount-ron'];
			$registeredAt = new \DateTime($row['transaction-date']);

			if ($commissionAmount === 0.0) {
				continue;
			}

			$confirmedAt = null;
			if ($status == ImportedTransaction::STATUS_CONFIRMED) {
				$confirmedAt = new \DateTime();
			}

			$importedTransaction = new ImportedTransaction($this->partnerSystem, null, $row['click-tag'], $transactionId, $commissionAmount, $orderAmount, Currency::RON, $status, $registeredAt, $confirmedAt, null, ImportedTransaction::CHANNEL_IMPORT);

			$this->scheduleImportedTransaction($importedTransaction);
		}
	}

	private function resolveStatus($status): string
	{
		$status = Strings::lower($status);

		switch ($status) {
			case 'pending':
				return ImportedTransaction::STATUS_REGISTERED;
			case 'rejected':
				return ImportedTransaction::STATUS_CANCELLED;
			case 'paid':
			case 'accepted':
				return ImportedTransaction::STATUS_CONFIRMED;
		}

		throw new InvalidArgumentException('unknown status ' . $status);
	}

	public function getPrograms()
	{
		$this->login();

		$options = [
			RequestOptions::HEADERS => [
				'Accept' => '*/*',
				'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36',
				'Content-Type' => 'application/json',
				'access-token' => $this->apiAccessToken,
				'client' => $this->apiClient,
				'uid' => $this->apiUid,
			],
		];

		$page = 1;
		$perPage = 50;
		$result = [];

		while (true) {
			$response = $this->client->request('GET', 'https://api.2performant.com/affiliate/programs?perpage=' . $perPage . '&page=' . $page, $options);
			$contents = Json::decode($response->getBody()->getContents());

			$programs = $contents->programs;

			foreach ($programs as $program) {
				$result[$program->id] = $program->unique_code;
			}

			if (count($programs) < $perPage) {
				break;
			}

			$page++;
		}

		echo Json::encode($result);

		exit;
	}
}
