<?php

namespace tipli\Model\PartnerSystems\Networks;

use Admitad\Api\Api;
use GuzzleHttp\Client;
use League\Csv\Reader;
use Nette\Database\Context;
use Nette\Http\Url;
use Nette\Utils\DateTime;
use Nette\Utils\Json;
use Nette\Utils\Strings;
use tipli\InvalidArgumentException;
use tipli\InvalidImportException;
use tipli\Model\Currencies\Currency;
use tipli\Model\Deals\Entities\Deal;
use tipli\Model\Deals\ImportedDeal;
use tipli\Model\Files\Entities\File;
use tipli\Model\OpsGenie\OpsGenieClient;
use tipli\Model\OpsGenie\Producers\OpsGenieProducer;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\PartnerSystems\PartnerSystemProcessResult;
use tipli\Model\PartnerSystems\Producers\PartnerSystemsDealProducer;
use tipli\Model\Transactions\ImportedTransaction;
use Tracy\Debugger;

interface IAdmitadFactory
{
	/**
	 * @return Admitad
	 */
	public function create(PartnerSystem $partnerSystem);
}

class Admitad extends Network
{
	private const COUPONS_URL  = 'https://api.admitad.com/coupons/';

	/** @var OpsGenieProducer @inject */
	public $opsGenieProducer;

	/** @var DealImporterProducer $dealImporterProducer @inject */
	public PartnerSystemsDealProducer $partnerSystemsDealProducer;

	/** @var Context @inject */
	public Context $context;

	public function createRedirectionLink($url, $userId, $redirectionId, $deepUrl = null)
	{
		$url = new Url($url);
		$url->setQueryParameter('subid', $userId . 'x' . $redirectionId);

		if ($deepUrl !== null && Strings::contains($deepUrl, 's.click') === false) {
			$url->setQueryParameter('ulp', $deepUrl);
		}

		// Swarovski wants subid4 to contain a redirection link
		if (Strings::contains($url, 'dwetll09hf26a75d30f118744b8d18') || Strings::contains($url, 'dwetll09hfc350453fa418744b8d18')) {
			$url->setQueryParameter('subid4', $url->getAbsoluteUrl());
		}

		return $url->getAbsoluteUrl();
	}

	public function import(File $file)
	{
		$reader = Reader::createFromPath($this->fileStorage->getFile($file));
		$reader->setDelimiter(',');

		$headers = [];
		foreach ($reader->fetchOne(0) as $columnIndex => $columnName) {
			$columnName = Strings::webalize($columnName);
			$headers[] = $columnName;
		}

		$requiredColumns = ['adv-campaign', 'action-time', 'order-sum', 'status', 'order-id', 'subid', 'payment', 'date-of-processing'];
		if (!count(array_intersect($headers, $requiredColumns)) == count($requiredColumns)) {
			throw new InvalidImportException('Admitad import není ve správném formátu..');
		}

		$results = $reader->getRecords($headers);

		foreach ($results as $offset => $row) {
			// skip header
			if ($offset === 0) {
				continue;
			}

			$currency = explode(' ', $row['payment']);
			$currency = end($currency);

			$transactionId = $row['order-id'];
			$partnerSystemKey = Strings::webalize($row['adv-campaign']);
			$userId = $row['subid'];
			$orderAmount = (float) $row['order-sum'];
			$commissionAmount = (float) $row['payment'];
			$registeredAt = new \DateTime($row['action-time']);

//			PRO LETNÍ ČAS:
//			if ($registeredAt) {
//				$registeredAt->modify('- 1 hours');
//			}

			//$confirmedAt = new \DateTime($row['date-of-processing']);
//			$currency = Currency::USD;

			$status = $this->resolveStatus($row['status']);

			if ($status !== ImportedTransaction::STATUS_CANCELLED && $commissionAmount <= 0) {
				Debugger::log(Json::encode($row), 'admitad-zero-commission-import');
				continue;
			}

//			if ($partnerSystemKey != '6115') {
//				$status = ImportedTransaction::STATUS_REGISTERED;
//			}

			$importedTransaction = new ImportedTransaction($this->partnerSystem, $partnerSystemKey, $userId, $transactionId, $commissionAmount, $orderAmount, $currency, $status, $registeredAt, null, null, ImportedTransaction::CHANNEL_IMPORT);

			$this->scheduleImportedTransaction($importedTransaction);
		}
	}

	public function processTransactions(\DateTime $from = null, \DateTime $to = null): PartnerSystemProcessResult
	{
		$this->refreshToken();

		$nowFrom = (new \DateTime())->modify('- 1 day');
		$nowTo = (new \DateTime());

		$records = [];
		if ($from && $to) {
			$records = array_merge($records, $this->downloadTransactions($from, $to));
		}

		if ($from > new \DateTime('- 2 years') && count($records) === 0) {
			$this->opsGenieProducer->scheduleAlert(
				OpsGenieClient::PRIORITY_HIGH,
				'#acta',
				'Admitad API nevrací žádné transakce.'
			);
		}

//        $records = array_merge($records, $this->downloadTransactions($nowFrom, $nowTo));
		foreach ($records as $record) {
			$transactionId = (string) $record->order_id;
			$userId = $record->subid;

			$orderAmount = $record->cart;
			$commissionAmount = $record->payment;

			$status = $this->resolveStatus($record->status);

			if ($status !== ImportedTransaction::STATUS_CANCELLED && $commissionAmount <= 0) {
//				Debugger::log(Json::encode($record), 'admitad-zero-commission-api');
				continue;
			}

//            $registeredAt = \DateTime::createFromFormat('Y-m-d H:i:s', $record->action_date)->modify('- 1 hour'); LETNÍ ČAS
			$registeredAt = \DateTime::createFromFormat('Y-m-d H:i:s', $record->action_date)->modify('- 2 hours');

			$partnerSystemKey = $record->advcampaign_id;

//            if ($partnerSystemKey != '6115') {
//            	$status = ImportedTransaction::STATUS_REGISTERED;
//			}

			$importedTransaction = new ImportedTransaction($this->partnerSystem, $partnerSystemKey, $userId, $transactionId, $commissionAmount, $orderAmount, Currency::getCurrencyByName($record->currency), $status, $registeredAt, null, null, ImportedTransaction::CHANNEL_API);

			$this->scheduleImportedTransaction($importedTransaction);
		}

		return new PartnerSystemProcessResult(count($records));
	}

	public function processWebhook($record)
	{
		$record = (object) $record;

		foreach (['order_sum', 'payment_status', 'website_id', 'currency', 'payment_sum', 'order_id', 'offer_id', 'type', 'subid', 'time', 'admitad_id'] as $field) {
			if (!isset($record->{$field})) {
				throw new InvalidArgumentException('Field ' . $field . ' is missing in webhook.');
			}
		}

//        Debugger::log(json_encode($record), 'admitad-webhook-record-log');

		$transactionId = (string) $record->order_id;
		$userId = $record->subid;

		$orderAmount = $record->order_sum;
		$commissionAmount = $record->payment_sum;
		$status = $this->resolveStatus($record->payment_status);

		if ($status !== ImportedTransaction::STATUS_CANCELLED && $commissionAmount <= 0) {
			Debugger::log(Json::encode($record), 'admitad-zero-commission-webhook');
			return;
		}

		$registeredAt = (new \DateTime())->setTimestamp($record->time);

		$partnerSystemKey = $record->offer_id;

//		if ($partnerSystemKey != '6115') {
//			$status = ImportedTransaction::STATUS_REGISTERED;
//		}

		try {
			$currency = Currency::getCurrencyByName($record->currency);
		} catch (\Exception $e) {
			Debugger::log(json_encode($record), 'admitad-unkown-currency');
			return;
		}

		$importedTransaction = new ImportedTransaction($this->partnerSystem, $partnerSystemKey, $userId, $transactionId, $commissionAmount, $orderAmount, $currency, $status, $registeredAt, null, null, ImportedTransaction::CHANNEL_WEBHOOK);

		$this->scheduleImportedTransaction($importedTransaction);
	}

	private function downloadTransactions(\DateTime $from, \DateTime $to): array
	{
//    	$applyOffset = $from->format('dmY') !== (new \DateTime)->format('dmY');

		$api = new Api($this->partnerSystem->getOption('accessToken'));

//        if ($applyOffset) {
//        	offset only for pivot transactions
//			$nextOffset = $this->partnerSystem->getOption('nextOffset');
//
//			if ($nextOffset > 20000) {
//				$nextOffset = 0;
//			}
//
//			$this->partnerSystem->setOption('nextOffset', $nextOffset + 500);
//		} else {
//        	$nextOffset = 0;
//		}

		$records = [];
		$offset = 0;
		$pages = 15;

		if (
			$from->format('d.m') === '10.11'
			|| $from->format('d.m') === '11.11'
			|| $from->format('d.m') === '12.11'
			|| $from->format('d.m') === '13.11'
		) { // aliexpress day
			$pages = 100;
		}

		if ($from->format('d.m.Y') === (new \DateTime())->format('d.m.Y')) {
			$pages = 3;
		}

		for ($c = 0; $c <= $pages; $c++) {
			$data = $api->get('/statistics/actions/', [
				'date_start' => $from->format('d.m.Y'),
				'date_end' => $to->format('d.m.Y'),
				'limit' => 500,
				'offset' => $offset,
			]);

			$offset += 500;

			foreach ($data->getResult('results')->getArrayCopy() as $record) {
				$records[] = $record;
			}
		}

		return $records;
	}

	private function refreshToken()
	{
		$nextRefreshDate = DateTime::createFromFormat('Y-m-d H:i:s', $this->partnerSystem->getOption('nextRefreshDate'));
		$now = new \DateTime();

		if ($nextRefreshDate < $now) {
			$this->partnerSystem->setOption('nextRefreshDate', $now->modify('+ 4 days')->format('Y-m-d H:i:s'));
		} else {
			return;
		}

		$api = new Api();

		$request = $api->refreshToken(
			$this->partnerSystem->getOption('clientId'),
			$this->partnerSystem->getOption('clientSecret'),
			$this->partnerSystem->getOption('refreshToken')
		);

		$result = $request->getResult();

		Debugger::log($result, 'admitad-refresh-token');

		if ($result->refresh_token && $result->access_token) {
			$this->partnerSystem->setOption('refreshToken', $result->refresh_token);
			$this->partnerSystem->setOption('accessToken', $result->access_token);

			return;
		}

		throw new InvalidArgumentException('Refresh token failed.');
	}

	private function resolveStatus($status)
	{
		if ($status === 'pending') {
			return ImportedTransaction::STATUS_REGISTERED;
		}

		if ($status === 'new') {
			return ImportedTransaction::STATUS_REGISTERED;
		}

		if ($status === 'approved') {
			return ImportedTransaction::STATUS_CONFIRMED;
		}

		if ($status === 'approved_but_stalled') {
			return ImportedTransaction::STATUS_CONFIRMED;
		}

		if ($status === 'declined') {
			return ImportedTransaction::STATUS_CANCELLED;
		}

		throw new InvalidArgumentException('unknown status ' . $status);
	}

	public function processSales()
	{
		$countOfDeals = 0;
		$countOfProcessedDeals = 0;
		$countOfPairedDeals = 0;

		Debugger::log('STARTED', 'admitad-process-sales');

		$token = $this->getToken('coupons');

		$importedIds = [];
		$missingCampaigns = [];
		$limit = 500;

		for ($offset = 0; $offset < 10000; $offset += 500) {
			$data = $this->fetchSales($token, $limit, $offset);

			Debugger::log('Fetched ' . count($data->results) . ' coupons. Limit:' . $limit . ' Offset:' . $offset, 'admitad-process-sales');
			if (count($data->results) === 0) {
				break;
			}

			foreach ($data->results as $coupon) {
				$countOfDeals++;
				try {
					if ($coupon->status !== "active") {
						continue;
					}

					if (in_array($coupon->campaign->id, $missingCampaigns)) {
						continue;
					}

					/** @var Shop|null $shop */
					$shops = $this->shopFacade->findShopsByPartnerSystemKey($coupon->campaign->id, $this->partnerSystem);

					if (empty($shops)) {
						$missingCampaigns[] = $coupon->campaign->id;
						Debugger::log('Shop ' . $coupon->campaign->name . ' not found for coupon. CampaignId: ' . $coupon->campaign->id, 'admitad-process-sales');
						continue;
					}

					$countOfPairedDeals++;

					foreach ($shops as $shop) {
						$uniqueId = $coupon->id . $shop->getId();

						if (in_array($uniqueId, $importedIds)) {
							continue;
						}

						$validSince = new DateTime($coupon->date_start);
						$validTill = new DateTime($coupon->date_end);

						if ($validTill < new DateTime()) {
							continue;
						}

						if (str_contains($coupon->discount, '%')) {
							$value = str_replace('%', '', $coupon->discount);
							$unit = Deal::UNIT_PERCENTAGE;
						} else {
							list($value, $unit) = explode(' ', $coupon->discount);
							$unit = Currency::getCurrencyFromSymbol(trim($unit));
						}

						if (empty(trim($value)) === true) {
							$type = Deal::TYPE_TIP;
						} else {
							$type = Deal::TYPE_SALE;
						}

						$url = $coupon->campaign->site_url;

						$importedDeal = new ImportedDeal($shop->getLocalization()->getId(), $shop->getId(), null, null, $url, $validSince, $validTill, null, null);
						$importedDeal->setUniqueId($uniqueId);
						$importedDeal->setDescription($coupon->description);
						$importedDeal->setSourceName($this->partnerSystem->getFeedName());
						$importedDeal->setSourceType(Deal::SOURCE_TYPE_API);
						$importedDeal->setValidSince($validSince);
						$importedDeal->setValidTill($validTill);
						$importedDeal->setName($coupon->name);
						$importedDeal->setDeepUrl($url);
						$importedDeal->setValue($value);
						$importedDeal->setType($type);
						$importedDeal->setUnit($unit);
						$importedDeal->setExclusive($coupon->exclusive);

						$this->partnerSystemsDealProducer->scheduleDeal($importedDeal);

						$countOfProcessedDeals++;

						echo $uniqueId . PHP_EOL;

						$importedIds[] = $uniqueId;
					}
				} catch (\InvalidArgumentException $e) {
					Debugger::log($e->getMessage(), 'admitad-process-sales');
					continue;
				}
			}
		}

		$this->context->query('INSERT INTO partner_systems_deal_import', [
			'partner_system_id' => $this->partnerSystem->getId(),
			'count_of_deals' => $countOfDeals,
			'count_of_processed_deals' => $countOfProcessedDeals,
			'count_of_paired_deals' => $countOfPairedDeals,
			'created_at' => new \DateTime(),
		]);
	}

	private function fetchSales(string $token, int $limit, int $offset): \stdClass
	{
		$client = new Client(['verify' => false]);

		$response = $client->request('GET', 'https://api.admitad.com/coupons/', [
			'headers' => [
				'Authorization' => 'Bearer ' . $token,
			],
			'query' => [
				'region' => 'CZ',
				'language' => 'en',
				'limit' => $limit,
				'offset' => $offset,
			],
		]);

		return Json::decode($response->getBody()->getContents());
	}

	private function getToken(string $scope): string
	{
		$clientId = $this->partnerSystem->getOption('clientId');
		$clientSecret = $this->partnerSystem->getOption('clientSecret');

		$credentials = base64_encode($clientId . ":" . $clientSecret);

		$client = new Client(['verify' => false]);

		$response = $client->request('POST', 'https://api.admitad.com/token/', [
			'headers' => [
				'Authorization' => 'Basic '	. $credentials,
				'Content-Type' => 'application/x-www-form-urlencoded',
			],
			'form_params' => [
				'grant_type' => 'client_credentials',
				'scope'      => $scope,
				'client_id'  => $clientId,
			],
		]);

		$data =  Json::decode($response->getBody()->getContents());

		if (isset($data->access_token)) {
			return $data->access_token;
		} else {
			throw new InvalidArgumentException('Token not found.');
		}
	}
}
