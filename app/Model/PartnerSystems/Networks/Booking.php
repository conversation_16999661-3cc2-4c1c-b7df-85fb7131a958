<?php

namespace tipli\Model\PartnerSystems\Networks;

use GuzzleHttp\Client;
use League\Csv\Reader;
use Nette\Http\Url;
use Nette\Utils\Json;
use Nette\Utils\Strings;
use tipli\InvalidArgumentException;
use tipli\InvalidImportException;
use tipli\Model\Currencies\Currency;
use tipli\Model\Files\Entities\File;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\PartnerSystems\PartnerSystemProcessResult;
use tipli\Model\Transactions\ImportedTransaction;

interface IBookingFactory
{
	/**
	 * @return Booking
	 */
	public function create(PartnerSystem $partnerSystem);
}

class Booking extends Network
{
	public const API_TRANSACTIONS_URL = 'https://secure-distribution-xml.booking.com/2.0/json/bookingDetails?created_from=%s;created_until=%s;rows=1000';
	private const BOOKING_TWISTO_KEY = "BOOKING_TWISTO";

	public function createRedirectionLink($url, $userId, $redirectionId, $deepUrl = null)
	{
//		if ($this->clientLayer->isMobileDetected()) {
//			$url = str_replace('index.html', 'accommodations.html', $url);
//		}

		$redirectionUrl = new Url($url);
		$redirectionUrl->setQueryParameter('label', $userId . 'x' . $redirectionId);

		if ($deepUrl) {
			$deepUrlFactory = new Url($deepUrl);
			$deepUrlFactory->setQueryParameter('aid', $redirectionUrl->getQueryParameter('aid'));
			$deepUrlFactory->setQueryParameter('label', $redirectionUrl->getQueryParameter('label'));

			$redirectionUrl = $deepUrlFactory;
		}

		return $redirectionUrl->getAbsoluteUrl();
	}

	private function importFromCopyPasteSheet(array $rows)
	{
		foreach ($rows as $row) {
			$item = explode(',', $row);

			$redirection = end($item);

			if (!Strings::contains($redirection, 'x')) {
				continue;
			}

			$transactionId = $item[1];
			$registeredAt = \DateTime::createFromFormat('d/m/Y', $item[0]);
			$commissionAmount = (float) str_replace([',', '€'], ['.', ''], $item[6]);

			$status = $this->resolveStatus(Strings::lower((string) $item[5]), $registeredAt);

			if (!$commissionAmount && $status === ImportedTransaction::STATUS_CANCELLED) {
				$commissionAmount = 0;
			}

			$importedTransaction = new ImportedTransaction($this->partnerSystem, null, $redirection, $transactionId, $commissionAmount, 0, Currency::EUR, $status, $registeredAt, null, null, ImportedTransaction::CHANNEL_IMPORT);

			$this->scheduleImportedTransaction($importedTransaction);
		}
	}

	public function import(File $file)
	{
		$reader = Reader::createFromPath($this->fileStorage->getFile($file));

		// get the header
		$headers = $reader->fetchOne();
		$headers = array_map(static function ($item) {
			return Strings::webalize(str_replace('"', '', $item));
		}, $headers);

		$requiredColumns = ['booking-number', 'label', 'your-commission', 'booking-date', 'status', 'total-commission'];

		if (count(array_intersect($headers, $requiredColumns)) === 0) {
			$this->importFromCopyPasteSheet(explode("\n", file_get_contents($this->fileStorage->getFile($file))));
			return;
		}

		if (!count(array_intersect($headers, $requiredColumns)) == count($requiredColumns)) {
			throw new InvalidImportException('Booking import není ve správném formátu..');
		}

		$results = $reader->getRecords($headers);

		foreach ($results as $index => $row) {
			// skip header
			if ($index === 0) {
				continue;
			}

			$partnerSystemKey = null;

			$transactionId = $row['booking-number'];

			$userId = $row['label'];
			$commissionAmount = (float) str_replace(',', '.', $row['your-commission']);
			$registeredAt = new \DateTime($row['booking-date']);

			$status = $this->resolveStatus(Strings::lower((string) $row['status']), $registeredAt);

			if (!$commissionAmount && $status === ImportedTransaction::STATUS_CANCELLED) {
				$commissionAmount = 0;
			}

			if ($this->partnerSystem->getId() === 225) {
				$partnerSystemKey = 'BOOKING_TWISTO';
			}

			$importedTransaction = new ImportedTransaction($this->partnerSystem, $partnerSystemKey, $userId, $transactionId, $commissionAmount, 0, Currency::EUR, $status, $registeredAt, null, null, ImportedTransaction::CHANNEL_IMPORT);

			$this->scheduleImportedTransaction($importedTransaction);
		}
	}

	private function resolveStatus($importedTransactionStatus, \DateTime $transactionCreatedAt)
	{
		$status = ImportedTransaction::STATUS_REGISTERED;

		if ($importedTransactionStatus == 'Dokončené' || $importedTransactionStatus == 'final' || $importedTransactionStatus == 'finalised' || $importedTransactionStatus == 'stayed') {
			$status = ImportedTransaction::STATUS_CONFIRMED;
		} elseif ($importedTransactionStatus == 'cancelled' || $importedTransactionStatus == 'Zrušené' || $importedTransactionStatus == 'cancelled by guest') {
			$status = ImportedTransaction::STATUS_CANCELLED;
		}

		if ($status != ImportedTransaction::STATUS_REGISTERED) {
			$dateLimit = clone $transactionCreatedAt;
			$dateLimit->modify('+ 30 days');

			if (new \DateTime() < $dateLimit) {
				$status = ImportedTransaction::STATUS_REGISTERED;
			}
		}

		return $status;
	}

	public function processTransactions(\DateTime $from = null, \DateTime $to = null): PartnerSystemProcessResult
	{
		$nowFrom = (new \DateTime())->modify('-36 hours');
		$nowTo = (new \DateTime());

		$records = [];
		if ($from && $to && $from >= new \DateTime('- 12 months')) {
			$records = array_merge($records, $this->fetchRecords($from, $to));
		}

		$records = array_merge($records, $this->fetchRecords($nowFrom, $nowTo));

		foreach ($records as $record) {
			$transactionId = $record->reservation_id;
			$userId = $record->affiliate_label;
			$commissionAmount = (float) $record->euro_fee;
			$orderAmount = (float) $record->price_euro;
			$registeredAt = new \DateTime($record->created);

			$confirmationDateLimit = clone $registeredAt;
			$confirmationDateLimit->modify('+ 30 days');

			$confirmationAllowed = new \DateTime() > $confirmationDateLimit;
			$status = ImportedTransaction::STATUS_REGISTERED;

			if ($record->status == 'stayed' && $confirmationAllowed) {
				$status = ImportedTransaction::STATUS_CONFIRMED;
			} elseif ($record->status == 'cancelled' && $confirmationAllowed) {
				$status = ImportedTransaction::STATUS_CANCELLED;
			}

			if ($status === ImportedTransaction::STATUS_REGISTERED && $commissionAmount <= 0 && $registeredAt >= new \DateTime('2022-07-21 00:00:00')) {
				$commissionAmount = ($orderAmount / 100) * 4;
			}

			if ($status === ImportedTransaction::STATUS_CONFIRMED) {
				$status = ImportedTransaction::STATUS_REGISTERED;
			}

			$importedTransaction = new ImportedTransaction($this->partnerSystem, null, $userId, $transactionId, $commissionAmount, $orderAmount, Currency::EUR, $status, $registeredAt, null, null, ImportedTransaction::CHANNEL_API);

			$this->scheduleImportedTransaction($importedTransaction);
		}

		return new PartnerSystemProcessResult(count($records));
	}

	public function processWebhookTransactions(array $transactions, PartnerSystem $partnerSystem)
	{
		foreach ($transactions as $record) {
			$transactionId = $record->bookingNumber;
			$userId = $record->label;

			if (isset($record->affiliateFeeAmount->min)) {
				$commissionAmount = (float) $record->affiliateFeeAmount->min;
			} else {
				continue;
			}

			$orderAmount = (float) $record->totalTransactionValue;

			$registeredAt = new \DateTime();
			$registeredAt = $registeredAt->setTimestamp($record->bookingDate / 1000);
			$status = ImportedTransaction::STATUS_REGISTERED;

			$importedTransaction = new ImportedTransaction($partnerSystem, self::BOOKING_TWISTO_KEY, $userId, $transactionId, $commissionAmount, $orderAmount, Currency::EUR, $status, $registeredAt, null, null, ImportedTransaction::CHANNEL_WEBHOOK);

			$this->scheduleImportedTransaction($importedTransaction);
		}
	}

	private function fetchRecords(\DateTime $from, \DateTime $to)
	{
		$url = sprintf(self::API_TRANSACTIONS_URL, $from->format('Y-m-d'), $to->format('Y-m-d'));

		$client = new Client();
		$response = $client->request('GET', $url, ['auth' => [$this->partnerSystem->getOption('login'), $this->partnerSystem->getOption('password')]])->getBody()->getContents();
		$data = Json::decode($response);

		if (!isset($data->result)) {
			throw new InvalidArgumentException('No data');
		}

		return $data->result;
	}
}
