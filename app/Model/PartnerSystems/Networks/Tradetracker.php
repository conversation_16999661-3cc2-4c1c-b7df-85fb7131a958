<?php

namespace tipli\Model\PartnerSystems\Networks;

use League\Csv\Reader;
use Nette\Http\Url;
use Nette\Utils\Strings;
use SoapClient;
use tipli\InvalidArgumentException;
use tipli\Model\Currencies\Currency;
use tipli\Model\Files\Entities\File;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\PartnerSystems\PartnerSystemProcessResult;
use tipli\Model\Refunds\Entities\RefundSolution;
use tipli\Model\Refunds\RefundSolutionObject;
use tipli\Model\Transactions\ImportedTransaction;

interface ITradetrackerFactory
{
	/**
	 * @return Tradetracker
	 */
	public function create(PartnerSystem $partnerSystem);
}

class Tradetracker extends Network
{
	public function createRedirectionLink($url, $userId, $redirectionId, $deepUrl = null)
	{
		$url = new Url($url);

		if ($url->getQueryParameter('tt') && Strings::endsWith($url->getQueryParameter('tt'), '_')) {
			$url->setQueryParameter('tt', $url->getQueryParameter('tt') . $userId . 'x' . $redirectionId);
		} else {
			$url->setQueryParameter('r', $userId . 'x' . $redirectionId);
		}

		$absoluteUrl = $url->getAbsoluteUrl();

		return $absoluteUrl;
	}

	public function processTransactions(\DateTime $from = null, \DateTime $to = null): PartnerSystemProcessResult
	{
		$nowFrom = (new \DateTime())->modify('-1 day');
		$nowTo = (new \DateTime())->modify('+1 day');

		$transactions = $this->downloadTransactions($nowFrom, $nowTo);
		if ($from && $to) {
			$transactions = array_merge($transactions, $this->downloadTransactions($from, $to));
		}

		foreach ($transactions as $transaction) {
			if ($transaction->transactionType != 'sale') {
				continue;
//				throw new InvalidArgumentException('Unknown transactionType - ' . $transaction->transactionType);
			}

			$transactionId = $transaction->ID;
			$partnerSystemKey = $transaction->campaign->ID;
			$userId = $transaction->reference;
			$commissionAmount = (float) $transaction->commission;
			$orderAmount = (float) $transaction->orderAmount;
			$currency = Currency::getCurrencyByName($transaction->currency);
			$registeredAt = new \DateTime($transaction->registrationDate);
			$status = $this->resolveStatus($transaction->transactionStatus);

			$importedTransaction = new ImportedTransaction(
				$this->partnerSystem,
				$partnerSystemKey,
				$userId,
				$transactionId,
				$commissionAmount,
				$orderAmount,
				$currency,
				$status,
				$registeredAt,
				null,
				null,
				ImportedTransaction::CHANNEL_API
			);

			$this->scheduleImportedTransaction($importedTransaction);
		}

		return new PartnerSystemProcessResult(count($transactions));
	}

	private function downloadTransactions(\DateTime $from, \DateTime $to)
	{
		$client = new SoapClient('http://ws.tradetracker.com/soap/affiliate?wsdl', ['compression' => SOAP_COMPRESSION_ACCEPT | SOAP_COMPRESSION_GZIP]);
		$client->authenticate(
			$this->partnerSystem->getOption('clientId'),
			$this->partnerSystem->getOption('clientKey'),
			false,
			'en_GB',
			false
		);

		$options = [
			'registrationDateFrom' => $from->format('Y-m-d'),
			'registrationDateTo' => $to->format('Y-m-d'),
		];

		$transactions = [];
		foreach ($client->getConversionTransactions($this->partnerSystem->getOption('affiliateSiteId'), $options) as $transaction) {
			$transactions[] = $transaction;
		}

		return $transactions;
	}

	private function resolveStatus($status)
	{
		if ($status == 'accepted') {
			return ImportedTransaction::STATUS_CONFIRMED;
		}

		if ($status == 'rejected') {
			return ImportedTransaction::STATUS_CANCELLED;
		}

		if ($status == 'pending') {
			return ImportedTransaction::STATUS_REGISTERED;
		}

		throw new InvalidArgumentException('unknown status ' . $status);
	}

	public function importRefunds(File $file): array
	{
		$file = $this->fileStorage->getFile($file);
		$contents = file_get_contents($file);

		$contents = mb_convert_encoding($contents, 'UTF-8', 'UTF-16');

		$reader = Reader::createFromString($contents)
			->setDelimiter(',');

		$reader->setOutputBOM(Reader::BOM_UTF8);

		$headers = $reader->fetchOne();
		$headers = array_map(static function ($item) {
			return Strings::webalize(str_replace('"', '', $item));
		}, $headers);

		$results = $reader->getRecords($headers);

		$refundsToProcess = [];

		foreach ($results as $index => $row) {
			if ($index === 0) {
				continue;
			}

			$status = $this->resolveRefundSolutionStatus(Strings::lower($row['status']));

			if ($status === null) {
				continue;
			}

			$refundsToProcess[] = new RefundSolutionObject(
				$this->partnerSystem,
				str_replace('#', '', $row['id']),
				$status,
				null
			);
		}

		return $refundsToProcess;
	}

	private function resolveRefundSolutionStatus(string $status): ?string
	{
		$status = Strings::webalize($status);

		return match ($status) {
			'prijaty' => RefundSolution::STATE_PARTNER_APPROVED,
			default => null,
		};
	}
}
