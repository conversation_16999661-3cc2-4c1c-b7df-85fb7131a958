<?php

namespace tipli\Model\PartnerSystems\Networks;

use GuzzleHttp\Client;
use Nette\Http\Url;
use Nette\Utils\Json;
use tipli\InvalidArgumentException;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\PartnerSystems\PartnerSystemProcessResult;
use tipli\Model\Transactions\ImportedTransaction;

interface IKwankoFactory
{
	/** @return Kwanko */
	public function create(PartnerSystem $partnerSystem);
}

class Kwanko extends Network
{
	private const TRANSACTIONS_ENDPOINT = 'https://api.kwanko.com/publishers/conversions';
	private const API_KEY = 'Bq{vNp)Ve]dcm{9rd0,Ns(F1Q}+fMci+xQ_[}l-I./|0,oetQ0_ej/k6vrd}s7)6H)bL';

	public function createRedirectionLink($url, $userId, $redirectionId, $deepUrl = null)
	{
		$redirectionUrl = new Url($url);
		$redirectionUrl->setQueryParameter('argsite', $userId . 'x' . $redirectionId);

		if ($deepUrl) {
			$redirectionUrl->setQueryParameter('redir', $deepUrl);
		}

		return $redirectionUrl->getAbsoluteUrl();
	}

	public function processTransactions(\DateTime $from = null, \DateTime $to = null): PartnerSystemProcessResult
	{
		$nowFrom = (new \DateTime())->modify('-1 day');
		$nowTo = (new \DateTime())->modify('+1 day');

		$records = [];
		if ($from && $to) {
			$records = array_merge($records, $this->getRecords($from, $to));
		}
		$records = array_merge($records, $this->getRecords($nowFrom, $nowTo));

		foreach ($records as $record) {
			$partnerSystemKey = $record->campaign->id;
			$userId = $record->websites_per_language[0]->argsites->argsite;
			$transactionId = $record->campaign->id . '_' . $record->kwanko_id;
			$commissionAmount = $record->websites_per_language[0]->earnings->value;
			$orderAmount = 0;
			$currency = $record->campaign->currency;
			$status = $this->resolveStatus($record->state);
			$registeredAt = new \DateTime($record->completed_at);

			$importedTransaction = new ImportedTransaction($this->partnerSystem, $partnerSystemKey, $userId, $transactionId, $commissionAmount, $orderAmount, $currency, $status, $registeredAt, null, null, ImportedTransaction::CHANNEL_API);

			$this->scheduleImportedTransaction($importedTransaction);
		}

		return new PartnerSystemProcessResult(count($records));
	}

	private function resolveStatus(string $status)
	{
		switch ($status) {
			case 'pending_approval':
				return ImportedTransaction::STATUS_REGISTERED;
			case 'approved':
				return ImportedTransaction::STATUS_CONFIRMED;
			case 'declined':
				return ImportedTransaction::STATUS_CANCELLED;
		}

		throw new InvalidArgumentException('unknown status ' . $status);
	}

	private function getRecords(\DateTime $from, \DateTime $to)
	{
		$from = $from->setTime(0, 0, 0)->format('Y-m-d\TH:i:s');
		$to = $to->setTime(0, 0, 0)->format('Y-m-d\TH:i:s');

		$apiUrl = new Url(self::TRANSACTIONS_ENDPOINT);
		$apiUrl->setQueryParameter('api_key', self::API_KEY);
		$apiUrl->setQueryParameter('date_type', 'modified_date');
		$apiUrl->setQueryParameter('start_date', $from);
		$apiUrl->setQueryParameter('end_date', $to);

		$client = new Client();
		$request = $client->request('GET', $apiUrl->getAbsoluteUrl());

		return Json::decode($request->getBody()->getContents())->data;
	}
}
