<?php

namespace tipli\Model\PartnerSystems\Networks;

use DateTime;
use GuzzleHttp\Client;
use League\Csv\Reader;
use Nette\Http\Url;
use Nette\Utils\Json;
use Nette\Utils\Strings;
use tipli\InvalidArgumentException;
use tipli\InvalidImportException;
use tipli\Model\Currencies\Currency;
use tipli\Model\Files\Entities\File;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\PartnerSystems\PartnerSystemProcessResult;
use tipli\Model\Refunds\Entities\RefundSolution;
use tipli\Model\Refunds\RefundSolutionObject;
use tipli\Model\Transactions\ImportedTransaction;

interface IAffiliateWindowFactory
{
	/**
	 * @return AffiliateWindow
	 */
	public function create(PartnerSystem $partnerSystem);
}

class AffiliateWindow extends Network
{
	public function createRedirectionLink($url, $userId, $redirectionId, $deepUrl = null)
	{
		$url = new Url($url);
		$url->setQueryParameter('clickref', $userId . 'x' . $redirectionId);

		if ($deepUrl) {
			$url->setQueryParameter('p', $deepUrl);
		}

		return $url->getAbsoluteUrl();
	}

	public function import(File $file)
	{
		$reader = Reader::createFromPath($this->fileStorage->getFile($file));

		// get the header
		$headers = $reader->fetchOne();
		$headers = array_map(static function ($item) {
			return Strings::webalize(str_replace('"', '', $item));
		}, $headers);


		$requiredColumns = ['id', 'advertiser_id', 'sale_amount', 'commission', 'date', 'validation_date', 'commission_status', 'click_ref'];
		if (!count(array_intersect($headers, $requiredColumns)) == count($requiredColumns)) {
			throw new InvalidImportException('AffiliateWindow import není ve správném formátu..');
		}

		$results = $reader->getRecords($headers);

		foreach ($results as $index => $row) {
			// skip header
			if ($index === 0) {
				continue;
			}

			$partnerSystemKey = $row['advertiser-id'];
			$transactionId = $row['id'];
			$userId = !empty($row['click-ref']) ? $this->resolveUserId($row['click-ref']) : $row['click-ref2'];
			$orderAmount = (float) $row['sale-amount'];

			$registeredAt = new \DateTime($row['date']);

			$commissionAmount = (float) $row['commission'];

			$status = $this->resolveStatus($row['commission-status'], $commissionAmount);
			$currency = $this->resolveCurrency($partnerSystemKey);

			$importedTransaction = new ImportedTransaction($this->partnerSystem, $partnerSystemKey, $userId, $transactionId, $commissionAmount, $orderAmount, $currency, $status, $registeredAt, null, null, ImportedTransaction::CHANNEL_IMPORT);

			$this->scheduleImportedTransaction($importedTransaction);
		}
	}

	public function processWebhookTransaction($webhookTransaction)
	{
		$requiredColumns = ['clickRef', 'transactionId', 'merchantId', 'transactionDate', 'transactionAmount', 'transactionCurrency'];
		if (count(array_intersect(array_keys((array) $webhookTransaction), $requiredColumns)) !== count($requiredColumns)) {
			throw new InvalidArgumentException('Data z webhooku pro sit AffiliateWindow nejsou validni.');
		}

		$userId = $webhookTransaction->clickRef;
		$transactionId = $webhookTransaction->transactionId;
		$partnerSystemKey = $webhookTransaction->merchantId;
		$registeredAt = DateTime::createFromFormat('Y-m-d H:i:s', $webhookTransaction->transactionDate);
		$commissionAmount = $webhookTransaction->commission;
		$orderAmount = $webhookTransaction->transactionAmount;
		$currency = Currency::getCurrencyByName($webhookTransaction->transactionCurrency);

		$importedTransaction = new ImportedTransaction($this->partnerSystem, $partnerSystemKey, $userId, $transactionId, $commissionAmount, $orderAmount, $currency, ImportedTransaction::STATUS_REGISTERED, $registeredAt, null, null, ImportedTransaction::CHANNEL_WEBHOOK);

		$this->scheduleImportedTransaction($importedTransaction);
	}

	public function processTransactions(\DateTime $from = null, \DateTime $to = null): PartnerSystemProcessResult
	{
		$nowFrom = (new \DateTime())->modify('-1 day');
		$nowTo = (new \DateTime())->modify('+1 day');

		//Please note: the maximum date range between startDate and endDate currently supported is 31 days.
		//http://wiki.awin.com/index.php/API_get_transactions_list

		$records = [];
		if ($from && $to) {
			$records = array_merge($records, $this->getRecords($from, $to));
		}
		$records = array_merge($records, $this->getRecords($nowFrom, $nowTo));

		foreach ($records as $record) {
			$partnerSystemKey = $record->advertiserId;
			$transactionId = $record->id;

			if ($record->type === 'TQS') {
				continue;
			}

			$userId = $record->clickRefs->clickRef ?? null;
			$commissionAmount = (float) $record->commissionAmount->amount;
			$orderAmount = (float) $record->saleAmount->amount;
			$currency = $record->commissionAmount->currency;
			$registeredAt = new \DateTime($record->transactionDate);
			$registeredAt->setTimezone(new \DateTimeZone('Europe/Prague'));

			$status = $this->resolveStatus($record->commissionStatus, $commissionAmount);

			$confirmedAt = null;
			if ($record->validationDate && ($status == ImportedTransaction::STATUS_CONFIRMED || $status == ImportedTransaction::STATUS_CANCELLED)) {
				$confirmedAt = new \DateTime($record->validationDate);
				$confirmedAt->setTimezone(new \DateTimeZone('Europe/Prague'));
			}

			$importedTransaction = new ImportedTransaction($this->partnerSystem, $partnerSystemKey, $userId, $transactionId, $commissionAmount, $orderAmount, $currency, $status, $registeredAt, $confirmedAt, null, ImportedTransaction::CHANNEL_API);

			$this->scheduleImportedTransaction($importedTransaction);
		}

		return new PartnerSystemProcessResult(count($records));
	}

	private function resolveStatus($status, $commissionAmount)
	{
		$status = Strings::lower($status);
		if ($status == 'deleted' || $status == 'declined' || ($status == 'approved' && $commissionAmount == 0)) {
			return ImportedTransaction::STATUS_CANCELLED;
		}

		if ($status == 'approved') {
			return ImportedTransaction::STATUS_CONFIRMED;
		}

		if ($status == 'pending') {
			return ImportedTransaction::STATUS_REGISTERED;
		}

		throw new InvalidArgumentException('unknown status ' . $status);
	}

	private function resolveCurrency($partnerSystemKey)
	{
		$shopsCurrency = $this->partnerSystem->getOption('shopsCurrency');
		if (isset($shopsCurrency->$partnerSystemKey)) {
			return $shopsCurrency->$partnerSystemKey;
		}

		throw new InvalidArgumentException('PartnerSystemKey: ' . $partnerSystemKey . ', currency does not exists in currency list.');
	}

	private function resolveUserId($userId)
	{
		if ($this->partnerSystem->getOption('ignoreUserIds') && in_array($userId, $this->partnerSystem->getOption('ignoreUserIds'))) {
			return null;
		}

		return $userId;
	}

	private function getRecords(\DateTime $from, \DateTime $to)
	{
		$nextPublisherId = $this->partnerSystem->getOption('nextPublisherId') ? $this->partnerSystem->getOption('nextPublisherId') : 0;
		$publisherId = $this->partnerSystem->getOption('publisherIds')[$nextPublisherId];

		$client = new Client();
		$response = $client->request('GET', 'https://api.awin.com/publishers/' . $publisherId . '/transactions/', [
			'query' => [
				'accessToken' => $this->partnerSystem->getOption('token'),
				'startDate' => $from->format('Y-m-d') . 'T' . $to->format('H:i:s'),
				'endDate' => $to->format('Y-m-d') . 'T' . $to->format('H:i:s'),
				'timezone' => 'UTC',
			],
		]);

		$nextPublisherId = $nextPublisherId == 1 ? 0 : 1;

		$this->partnerSystem->setOption('nextPublisherId', $nextPublisherId);

		$contents = $response->getBody()->getContents();

		$records = Json::decode($contents);

		return $records;
	}

	public function importRefunds(File $file): array
	{
		$reader = Reader::createFromPath($this->fileStorage->getFile($file))
			->setDelimiter(',');

		$headers = $reader->fetchOne();
		$headers = array_map(static function ($item) {
			return Strings::webalize(str_replace('"', '', $item));
		}, $headers);

		$results = $reader->getRecords($headers);

		$refundsToProcess = [];

		foreach ($results as $index => $row) {
			if ($index === 0) {
				continue;
			}

			$status = Strings::lower($row['enquiry-status']);

			if ($status === 'pending') {
				continue;
			}

			$refundsToProcess[] = new RefundSolutionObject(
				$this->partnerSystem,
				$row['order-reference'],
				$this->resolveRefundSolutionStatus($status)
			);
		}

		return $refundsToProcess;
	}

	private function resolveRefundSolutionStatus(string $status): string
	{
		switch ($status) {
			case 'declined':
				return RefundSolution::STATE_PARTNER_DECLINED;
			case 'approved':
				return RefundSolution::STATE_PARTNER_APPROVED;
		}

		throw new \InvalidArgumentException('undefined status ' . $status);
	}
}
