<?php

namespace tipli\Model\PartnerSystems\Networks;

use GuzzleHttp\Client;
use League\Csv\Reader;
use Nette\Http\Url;
use Nette\Utils\Json;
use Nette\Utils\Strings;
use tipli\InvalidArgumentException;
use tipli\InvalidImportException;
use tipli\Model\Currencies\Currency;
use tipli\Model\Files\Entities\File;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\PartnerSystems\PartnerSystemProcessResult;
use tipli\Model\Refunds\Entities\RefundSolution;
use tipli\Model\Refunds\RefundSolutionObject;
use tipli\Model\Transactions\ImportedTransaction;

interface IScaleoFactory
{
	public function create(PartnerSystem $partnerSystem): Scaleo;
}

class Scaleo extends Network
{
	private const API_KEY = 'e14c127709a9d920d275c6935323c6b309481705';

	public function createRedirectionLink($url, $userId, $redirectionId, $deepUrl = null, ?string $tipliDomain = null): string
	{
		$url = new Url($url);
		$url->setQueryParameter('sub_id1', $userId . 'x' . $redirectionId);

		if ($deepUrl) {
			if (Strings::contains($deepUrl, 'tchibo.')) {
				$url->setQueryParameter('link_id', null);
				$url->setQueryParameter('sub_id5', $deepUrl);
			} else {
				$url->setQueryParameter('deep_link', $deepUrl);
			}
		}

		// nike.ro
		if ($url->getQueryParameter('o') && $url->getQueryParameter('o') === '1800') {
			$url->setQueryParameter('sub_id4', rawurlencode(rawurlencode($tipliDomain)));
		}

		return $url->getAbsoluteUrl();
	}

	public function import(File $file)
	{
		$reader = Reader::createFromPath($this->fileStorage->getFile($file))
			->setDelimiter(',');

		// get the header
		$headers = $reader->fetchOne();
		$headers = array_map(static function ($item) {
			return Strings::webalize(str_replace('"', '', $item));
		}, $headers);

		$requiredColumns = ['transaction-id', 'currency', 'payout', 'conversion-status', 'date', 'offer-id', 'affiliate-sub-id-1', 'sale-amount'];
		$missingColumns = [];

		foreach ($requiredColumns as $requiredColumn) {
			if (!in_array($requiredColumn, $headers)) {
				$missingColumns[] = $requiredColumn;
			}
		}

		if (!empty($missingColumns)) {
			throw new InvalidImportException(
				'Scaleo import není ve správném formátu. Chybějící sloupce: ' . implode(', ', $missingColumns)
			);
		}

		$results = $reader->getRecords($headers);

		foreach ($results as $index => $row) {
			if ($index === 0) {
				continue;
			}

			$transactionId = $row['transaction-id'];
			[$userId, $redirectionId] = explode('x', $row['affiliate-sub-id-1']);
			$commissionAmount = $row['payout'];
			$orderAmount = $row['sale-amount'];
			$currency = Currency::getCurrencyByName($row['currency']);
			$status = $this->resolveStatus($row['conversion-status']);
			$partnerSystemKey = $row['offer-id'];
			$registeredAt = new \DateTime($row['date']);

			$confirmedAt = null;
			if ($status === ImportedTransaction::STATUS_CONFIRMED) {
				$confirmedAt = new \DateTime();
			}

			$importedTransaction = new ImportedTransaction($this->partnerSystem, $partnerSystemKey, $userId, $transactionId, $commissionAmount, $orderAmount, $currency, $status, $registeredAt, $confirmedAt, null, ImportedTransaction::CHANNEL_IMPORT);

			$this->scheduleImportedTransaction($importedTransaction);
		}
	}

	public function processTransactions(\DateTime $from = null, \DateTime $to = null): PartnerSystemProcessResult
	{
		$nowFrom = (new \DateTime())->modify('- 1 day');
		$nowTo = (new \DateTime());

		$records = [];
		if ($from && $to) {
			$records = array_merge($records, $this->getRecords($from, $to));
		}

		$records = array_merge($records, $this->getRecords($nowFrom, $nowTo));

		foreach ($records as $transaction) {
			if ($transaction->sub_id1 !== null && Strings::contains($transaction->sub_id1, 'x')) {
				[$userId, $redirectionId] = explode('x', $transaction->sub_id1);
			} else {
				$userId = (int) $transaction->sub_id1;
			}

			$partnerSystemKey = $transaction->offer->id;
			$transactionId = $transaction->transaction_id;
			$commissionAmount = $transaction->payout;
			$orderAmount = $transaction->advertiser_amount;
			$currency = Currency::getCurrencyByName($transaction->currency);
			$status = $this->resolveStatus($transaction->conversion_status);
			$registeredAt = new \DateTime($transaction->added_timestamp);

			$confirmedAt = null;
			if ($status === ImportedTransaction::STATUS_CONFIRMED) {
				$confirmedAt = new \DateTime();
			}

			$importedTransaction = new ImportedTransaction(
				$this->partnerSystem,
				$partnerSystemKey,
				(int) $userId,
				$transactionId,
				$commissionAmount,
				$orderAmount,
				$currency,
				$status,
				$registeredAt,
				$confirmedAt,
				null,
				ImportedTransaction::CHANNEL_API
			);

			$this->scheduleImportedTransaction($importedTransaction);
		}

		return new PartnerSystemProcessResult(count($records));
	}

	private function getRecords(\DateTime $from, \DateTime $to)
	{
		$client = new Client(['verify' => false]);
		$response = $client->request('POST', 'https://affiliateport.scaletrk.com/api/v2/affiliate/reports/conversions?api-key=' . self::API_KEY . '&perPage=1000', [
			'form_params' => [
				'rangeFrom' => $from->format('Y-m-d'),
				'rangeTo' => $to->format('Y-m-d'),
				'columns' => 'transaction_id,conversion_status,added_timestamp,time_difference,payout,sub_id1,sub_id2,sub_id3,sub_id4,sub_id5,aff_param1,aff_param2,aff_param3,aff_param4,aff_param5,aff_click_id,deep_link_url,source,advertiser_order_id,advertiser_user_id,advertiser_amount,offer,goal,goal_type,link,creative,language,connection_type,mobile_operator,idfa,gaid,ip,geo,device_type,device_brand,device_model,device_os,device_os_version,browser,browser_version',
			],
		]);

		return Json::decode($response->getBody()->getContents())->info->transactions;
	}

	public function importRefunds(File $file): array
	{
		$reader = Reader::createFromPath($this->fileStorage->getFile($file))
			->setDelimiter(',');

		$headers = $reader->fetchOne();
		$headers = array_map(static function ($item) {
			return Strings::webalize(str_replace('"', '', $item));
		}, $headers);

		$results = $reader->getRecords($headers);

		$refundsToProcess = [];

		foreach ($results as $index => $row) {
			if ($index === 0) {
				continue;
			}

			$status = Strings::webalize($row['status']);

			if ($status === 'nova' || $status === 'nedoresena') {
				continue;
			}

			$refundsToProcess[] = new RefundSolutionObject(
				$this->partnerSystem,
				$row['order-number'],
				$this->resolveRefundSolutionStatus($status),
				$row['note']
			);
		}

		return $refundsToProcess;
	}

	private function resolveRefundSolutionStatus(string $status): string
	{
		switch ($status) {
			case 'zamitnuto':
				return RefundSolution::STATE_PARTNER_DECLINED;
			case 'vyrizeno':
				return RefundSolution::STATE_PARTNER_APPROVED;
		}

		throw new \InvalidArgumentException('undefined status ' . $status);
	}

	public function resolveStatus($status): string
	{
		$status = Strings::lower($status);

		switch ($status) {
			case 'pending':
				return ImportedTransaction::STATUS_REGISTERED;
			case 'approved':
				return ImportedTransaction::STATUS_CONFIRMED;
			case 'rejected':
			case 'trash':
				return ImportedTransaction::STATUS_CANCELLED;
		}

		throw new InvalidArgumentException('unknown status ' . $status);
	}
}
