<?php

namespace tipli\Model\PartnerSystems\Networks;

use League\Csv\Reader;
use Nette\Http\Url;
use Nette\Utils\Strings;
use tipli\InvalidImportException;
use tipli\Model\Currencies\Currency;
use tipli\Model\Files\Entities\File;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\PartnerSystems\PartnerSystemProcessResult;
use tipli\Model\Transactions\ImportedTransaction;
use Tracy\Debugger;

interface IPostAffiliateProFactory
{
  /**
   * @param PartnerSystem $partnerSystem
   * @return PostAffiliatePro
   */
	public function create(PartnerSystem $partnerSystem);
}

class PostAffiliatePro extends Network
{
	public function createRedirectionLink($url, $userId, $redirectionId, $deepUrl = null)
	{
		$url = new Url($url);
		if ($deepUrl) {
			$url = new Url($deepUrl);
		}

		$useFragment = $this->partnerSystem->getOption('useFragment');

		if ($useFragment) {
			$fragment = $url->getFragment() . '&data1=' . $userId . 'x' . $redirectionId;
			$url->setFragment($fragment);
		} else {
			$url->setQueryParameter('data1', $userId . 'x' . $redirectionId);
			$url->setQueryParameter('data2', $userId . 'x' . $redirectionId);
		}

		return $url->getAbsoluteUrl();
	}

	public function processTransactions(\DateTime $from = null, \DateTime $to = null): PartnerSystemProcessResult
	{
		//        $from = (new \DateTime())->modify('-90 days');
//        $to = new \DateTime();

		$nowFrom = (new \DateTime())->modify('-1 day');
		$nowTo = (new \DateTime())->modify('+1 day');

		$records = [];
		if ($from && $to) {
			$records = array_merge($records, $this->getRecords($from, $to));
		}
		$records = array_merge($records, $this->getRecords($nowFrom, $nowTo));

		bdump(count($records));

		foreach ($records as $record) {
			$partnerSystemKey = $record->get('campaignid');
			$transactionId = $record->get('id');

			bdump($transactionId);

			$commissionAmount = (float) $record->get('commission');
			$registeredAt = new \DateTime($record->get('dateinserted'), new \DateTimeZone('Europe/Prague'));
			$currency = $this->getCurrency();

			if ($this->partnerSystem->getId() === 278) {
				$utcTime = $record->get('dateinserted');
				$utcTimezone = new \DateTimeZone('UTC');
				$pragueTimezone = new \DateTimeZone('Europe/Prague');

				$registeredAt = new \DateTime($utcTime, $utcTimezone);
				$registeredAt->setTimezone($pragueTimezone);

				$userId = str_replace('A6etp', '', $record->get('firstclickdata1'));
				$orderAmount = (float) $record->get('totalcost');

				if ($currency === Currency::EUR) {
					$commissionAmount *= 25;
					$orderAmount *= 25;
					$currency = Currency::CZK;
				}

				if ($registeredAt < (new \DateTime())->modify('-30 days')) {
					$status = ImportedTransaction::STATUS_CONFIRMED;
				} else {
					$status = ImportedTransaction::STATUS_REGISTERED;
				}
			} else {
				$userId = $record->get('lastclickdata1');
				$orderAmount = (float) $record->get('totalcost');
				$status = $this->resolveStatus($record->get('rstatus'));
			}

			$importedTransaction = new ImportedTransaction($this->partnerSystem, $partnerSystemKey, $userId, $transactionId, $commissionAmount, $orderAmount, $currency, $status, $registeredAt, null, null, ImportedTransaction::CHANNEL_API);

			bdump($importedTransaction);

			$this->scheduleImportedTransaction($importedTransaction);
		}

		return new PartnerSystemProcessResult(count($records));
	}

	public function import(File $file)
	{
		$reader = Reader::createFromPath($this->fileStorage->getFile($file))
			->setDelimiter(';');

		// get the header
		$headers = [];
		foreach ($reader->fetchOne() as $item) {
			$name = Strings::webalize(str_replace('"', '', $item));
			$headers[] = !in_array($name, $headers) ? $name : uniqid();
		}

		// en version
		if (in_array('commission', $headers)) {
			foreach ($headers as $headerId => $headerName) {
				if ($headerName == 'campaign-id') {
					$headers[$headerId] = 'campaignid';
				}
				if ($headerName == 'commission') {
					$headers[$headerId] = 'provize';
				}
				if ($headerName == 'total-cost') {
					$headers[$headerId] = 'celkova-suma';
				}
				if ($headerName == 'created') {
					$headers[$headerId] = 'vytvoreno';
				}
				if ($headerName == 'last-click-data-1') {
					$headers[$headerId] = 'data-1-pro-posledni-kliknuti';
				}
				if ($headerName == 'approved') {
					$headers[$headerId] = 'dateapproved';
				}
			}
		}

		$requiredColumns = ['campaignid', 'provize', 'celkova-suma', 'vytvoreno', 'status', 'data-1-pro-posledni-kliknuti', 'dateapproved'];
		if (!count(array_intersect($headers, $requiredColumns)) == count($requiredColumns)) {
			throw new InvalidImportException('pap import není ve správném formátu..');
		}

		$results = $reader->getRecords($headers);

		foreach ($results as $index => $row) {
			// skip header
			if ($index === 0) {
				Debugger::log('bad r', 'pap-errors');
				continue;
			}

			if (empty($row['data-1-pro-posledni-kliknuti'])) {
				Debugger::log('data-1-pro-posledni-kliknuti is missing', 'pap-errors');
				continue;
			}

			$campaignId = null;
			if (isset($row['campaignid'])) {
				$campaignId = $row['campaignid'];
			} elseif (isset($row['id-kampane'])) {
				$campaignId = $row['id-kampane'];
			}

			if (!$campaignId) {
				Debugger::log('campaignId is missing', 'pap-errors');
				continue;
			}

			$transactionId = $row['id'];
			$userId = $row['data-1-pro-posledni-kliknuti'];
			$orderAmount = (float) str_replace([',', ' '], ['.', ''], $row['celkova-suma']);

			$registeredAt = new \DateTime($row['vytvoreno']);

			$status = $this->resolveStatus(Strings::upper(Strings::webalize($row['status'])));

			$commissionAmount = (float) str_replace([',', ' '], ['.', ''], $row['provize']);
			$partnerSystemKey = $campaignId;

			$currency = Currency::CZK;

			if ($this->partnerSystem->getId() === 121) {
				$currency = Currency::EUR;
			}

			$importedTransaction = new ImportedTransaction($this->partnerSystem, $partnerSystemKey, $userId, $transactionId, $commissionAmount, $orderAmount, $currency, $status, $registeredAt, null, null, ImportedTransaction::CHANNEL_IMPORT);

			$this->scheduleImportedTransaction($importedTransaction);
		}
	}

	private function resolveStatus($status)
	{
		switch ($status) {
			case 'P':
				return ImportedTransaction::STATUS_REGISTERED;
			case 'A':
				return ImportedTransaction::STATUS_CONFIRMED;
			case 'D':
				return ImportedTransaction::STATUS_CANCELLED;
		}

		throw new \InvalidArgumentException('undefined status ' . $status);
	}

	private function getCurrency()
	{
		return $this->partnerSystem->getOption('currency') ? $this->partnerSystem->getOption('currency') : Currency::CZK;
	}

	private function getRecords(\DateTime $from, \DateTime $to)
	{
		// Kod je založen na základě dostupného dema v dokumentaci knihovny
		$session = $this->getSession($this->partnerSystem->getOption('apiUrl'), $this->partnerSystem->getOption('username'), $this->partnerSystem->getOption('password'));

		if (empty($session)) { // @phpstan-ignore-line
			return [];
		}

		$grid = $this->getGrid($from, $to, $session);
		$recordset = $grid->getRecordset();

		$records = [];
		foreach ($recordset as $recordsetItem) {
			$records[] = $recordsetItem;
		}

		return $records;
	}

	/**
	 * @param string $apiUrl
	 * @param string $username
	 * @param string $password
	 * @return \Gpf_Api_Session
	 */
	private function getSession($apiUrl, $username, $password) /** @phpstan-ignore-line */
	{
//		error_reporting(E_ERROR | E_WARNING | E_PARSE | E_NOTICE); // disable Deprecated Error
		include __DIR__ . '/../../../../vendor/tipli/tipli-vendor/PapApi/PapApi.class.php';
		$session = new \Gpf_Api_Session($apiUrl);
		if (!$session->login($username, $password, \Gpf_Api_Session::AFFILIATE)) {
			Debugger::log('Nelze se prihlasit. Zprava: ' . $session->getMessage());
			return null;
		}

		return $session;
	}

  /**
   * @param \DateTime $from
   * @param \DateTime $to
   * @param string $session
   * @return \Gpf_Data_Grid
   */
	private function getGrid(\DateTime $from, \DateTime $to, $session)
	{
		$request = new \Pap_Api_TransactionsGrid($session);

		$request->addFilter('dateinserted', 'D>=', $from->format('Y-m-d H:i:s'));
		$request->addFilter('dateinserted', 'D<=', $to->format('Y-m-d H:i:s'));

		$request->addParam(
			'columns',
			new \Gpf_Rpc_Array(
				[
					['id'],
					['userid'],
					['transid'],
					['campaignid'],
					['orderid'],
					['commission'],
					['totalcost'],
					['rstatus'],
					['lastclickdata1'],
					['lastclicktime'],
					['dateinserted'],
					['fixedcost'],
					['firstclickdata1'],
				]
			)
		);

		$request->setLimit(0, 100);
		$request->setSorting('orderid', false);
		$request->sendNow();

		return $request->getGrid();
	}
}
