<?php

namespace tipli\Model\PartnerSystems\Networks;

use GuzzleHttp\Client;
use Nette\Caching\Cache;
use Nette\Database\Context;
use Nette\Http\Url;
use Nette\Utils\Json;
use Nette\Utils\Strings;
use tipli\Model\Currencies\Currency;
use tipli\Model\Deals\Entities\Deal;
use tipli\Model\Deals\ImportedDeal;
use tipli\Model\Deals\Producers\DealImporterProducer;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\PartnerSystems\PartnerSystemProcessResult;
use tipli\Model\PartnerSystems\Producers\PartnerSystemsDealProducer;
use tipli\Model\Transactions\ImportedTransaction;
use Tracy\Debugger;

interface IHasOffersFactory
{
	/**
	 * @return HasOffers
	 */
	public function create(PartnerSystem $partnerSystem);
}

class HasOffers extends Network
{
	/** @var DealImporterProducer $dealImporterProducer @inject */
	public $dealImporterProducer;

	/** @var PartnerSystemsDealProducer $partnerSystemsDealProducer @inject */
	public PartnerSystemsDealProducer $partnerSystemsDealProducer;

	/** @var Context $context @inject */
	public Context $context;

	private const PURCHASE_IN_SHOP_TRANSLATE = [
		'cs' => ' na nákup v obchodě ',
		'sk' => ' na nákup v obchode ',
		'pl' => 'zniżka na zakupy w sklepie ',
	];

	private const COUPON_NAME_REPLACEMENTS = [
		'cs' => ['Sleva', 'Kód'],
		'sk' => ['Zľava', 'Kod'],
		'pl' => ['Zniżka', 'Kod'],
	];

	private const COUPON_NAME_REPLACE_WITH = [
		'cs' => 'Slevový kupon',
		'sk' => 'Zľavový kupón',
		'pl' => 'Kupon rabatowy',
	];

	public function createRedirectionLink($url, $userId, $redirectionId, $deepUrl = null, ?string $tipliDomain = null): string
	{
		$url = new Url($url);
		$url->setQueryParameter('aff_sub', $userId . 'x' . $redirectionId);
		$url->setQueryParameter('aff_sub3', $tipliDomain);

		if ($deepUrl) {
			if ($this->partnerSystem->getId() === 152 || Strings::contains($deepUrl, 'answear.pl')) {
				$url->setQueryParameter('aff_sub5', $deepUrl);

				return $url->getAbsoluteUrl();
			}

			// performers
			if ($this->partnerSystem->getId() === 126) {
				$cleanUrl = str_replace(['http://', 'https://'], '', $deepUrl);

				if (Strings::contains($cleanUrl, '?')) {
					$cleanUrl .= '&';
				} else {
					$cleanUrl .= '?';
				}

				$url->setQueryParameter('url_id', 38401);

				return $url->getAbsoluteUrl() . '&deeplink=' . $cleanUrl;
			}

			$deepUrl = new Url($deepUrl);
			$deepUrl->setQueryParameter('utm_campaign', '{affiliate_id}');
			$deepUrl->setQueryParameter('utm_medium', 'affiliate');
			$deepUrl->setQueryParameter('utm_source', 'affilport');
			$deepUrl->setQueryParameter('transaction_id', '{transaction_id}');

			$url->setQueryParameter('url', urldecode($deepUrl->getAbsoluteUrl()));
		}

		return $url->getAbsoluteUrl();
	}

	public function processTransactions(\DateTime $from = null, \DateTime $to = null): PartnerSystemProcessResult
	{
				//$from = (new \DateTime())->modify('-180 days');
		//$to = new \DateTime();

		$nowFrom = (new \DateTime())->modify('-1 day');
		$nowTo = (new \DateTime())->modify('+1 day');

		$records = [];
		if ($from && $to) {
			$records = array_merge($records, $this->getRecords($from, $to));
		}
		$records = array_merge($records, $this->getRecords($nowFrom, $nowTo));

		foreach ($records as $record) {
			$item = $record->Stat;
			$partnerSystemKey = (string) $item->offer_id;
			$transactionId = (string) $item->conversion_id;
			$userId = $item->affiliate_info1;
			$commissionAmount = (float) $item->payout;
			if (isset($item->conversion_sale_amount)) {
				$orderAmount = (float) $item->conversion_sale_amount;
			} else {
				$orderAmount = (float) 0;
			}
			$registeredAt = new \DateTime($item->datetime);
			$registeredAt->modify('- 1 hour'); // ZIMNÍ ČAS

			$status = $this->resolveStatus((string) $item->conversion_status, $registeredAt);

			/*
			 * 7.11.2022
			 * Při aktuální úpravě trackingu na straně inzerenta došlo k drobné technické chybě v nastavení způsobující duplicitní záznam vybraných objednávek.Vybrané objednávky
			 * se v systému duplikují skrze přidružený znak ")" na konci ID objednávky.
			 */
			if (Strings::endsWith($transactionId, '}')) {
				continue;
			}

			if (!$commissionAmount > 0) {
				continue;
			}

			if ($orderAmount > 0 && ($commissionAmount / $orderAmount) < 0.001) {
				$orderAmount = 0;
			}

			if ($partnerSystemKey === '1704' && $registeredAt->diff(new \DateTime())->days < 70) {
				$status = ImportedTransaction::STATUS_REGISTERED;
			}

			if ($this->partnerSystem->getId() === 126 && $registeredAt->diff(new \DateTime())->days < 65) {
				$status = ImportedTransaction::STATUS_REGISTERED;
			}

			$importedTransaction = new ImportedTransaction($this->partnerSystem, $partnerSystemKey, $userId, $transactionId, $commissionAmount, $orderAmount, $this->getCurrency(), $status, $registeredAt, null, null, ImportedTransaction::CHANNEL_API);

			bdump($importedTransaction);

			$this->scheduleImportedTransaction($importedTransaction);
		}

		return new PartnerSystemProcessResult(count($records));
	}

	public function processSales()
	{
		$countOfDeals = 0;
		$countOfProcessedDeals = 0;
		$countOfPairedDeals = 0;

		$apiUrl = $this->partnerSystem->getOption('salesUrl');

		$data = Json::decode(file_get_contents($apiUrl));

		foreach ($data as $coupon) {
			$countOfDeals++;

			$id = $coupon->id;
			$uniqueId = Strings::webalize($this->partnerSystem->getName() . '--' . $id);
			$locale = $coupon->locale;
			$type = $coupon->type;
			$name = $coupon->name;
			$code = $coupon->code;
			$value = $coupon->value !== null ? Strings::lower($coupon->value) : null;
			$conditions = $coupon->conditions;
			$url = $coupon->url;
			$validSince = (new \DateTime($coupon->validFrom))->setTime(0, 0);
			$validTill = (new \DateTime($coupon->validTill))->setTime(23, 59, 59);

			if (Strings::endsWith($name, '%') && isset(self::PURCHASE_IN_SHOP_TRANSLATE[$locale])) {
				if ($locale === 'pl') {
					preg_match('/(\d+\s?%)/', $name, $matchesDiscount);
					$discount = str_replace(' ', '', $matchesDiscount[0]);

					$name = self::PURCHASE_IN_SHOP_TRANSLATE[$locale] . $coupon->offerName . ' ' . $discount;
				} else {
					$name .= self::PURCHASE_IN_SHOP_TRANSLATE[$locale] . $coupon->offerName;
				}
			}

			if (!in_array($type, ['offer', 'coupon'])) {
				Debugger::log('cant identify type: ' . $type, 'HasOffers-processSales');
				continue;
			}

			if ($type === 'offer' && empty($conditions)) {
				Debugger::log('no conditions for offer id: ' . $id, 'HasOffers-processSales');
				continue;
			}

			if ($type === 'coupon') {
				preg_match('/([0-9]*\.[0-9]+|[0-9]+)\s?(kč|€|\$|zł|£|%)/i', $value, $matches);
				if (count($matches) !== 3) {
					Debugger::log('cant identify value of: ' . $value, 'HasOffers-processSales');
					continue;
				}

				list ($match, $matchedValue, $matchedSymbol) = $matches;
				$unit = Deal::getUnitBySymbol($matchedSymbol);
				if (!isset($unit)) {
					Debugger::log('cant identify unit value of: ' . $value, 'HasOffers-processSales');
					continue;
				}

				$name = str_replace(self::COUPON_NAME_REPLACEMENTS[$locale], self::COUPON_NAME_REPLACE_WITH[$locale], $name);
			}

			$shop = null;
			$slug = Strings::webalize($name);
			$localization = $this->localizationFacade->findOneByLocale($locale);

			if (!$localization) {
				Debugger::log('cant identify localization by locale ' . $locale, 'HasOffers-processSales');
				continue;
			}

			$url = new Url($url);
			$domain = $url->getDomain();
			$shop = $this->shopFacade->findShopByDomain($localization, $domain);

			if (!$shop) {
				Debugger::log('[' . $this->partnerSystem->getName() .  '][' . $coupon->offerName . '] shop not found', 'deals-not-paired-shops');
				Debugger::log('cant identify shop by url: ' . $url->getAbsoluteUrl(), 'HasOffers-processSales');
				continue;
			}

			$countOfPairedDeals++;

			if ($shop->getPartnerSystem()->getId() !== $this->partnerSystem->getId()) {
				Debugger::log('Found shop ' . $shop->getId() . ' has different partner system, locale: ' . $localization->getLocale() . ' | domain:' . $domain, 'HasOffers-processSales');
				continue;
			}

			if ($this->cache->load($uniqueId)) {
				continue;
			}

			/** @var ImportedDeal $deal */
			$importedDeal = new ImportedDeal($localization->getId(), $shop->getId(), null, $name, $slug, $validSince, $validTill, null, null);

			$importedDeal->setUniqueId($uniqueId);
			$importedDeal->setSourceName($this->partnerSystem->getFeedName());
			$importedDeal->setSourceType(Deal::SOURCE_TYPE_API);
			$importedDeal->setValidSince($validSince);
			$importedDeal->setValidTill($validTill);
			$importedDeal->setName($name);
			$importedDeal->setDeepUrl($url->getAbsoluteUrl());

			if ($type === 'offer') {
				if (Strings::contains(Strings::lower($name), 'doprava zdarma') || Strings::contains(Strings::lower($name), 'poštovné zdarma')) {
					$importedDeal->setType(Deal::TYPE_FREE_SHIPPING);

					preg_match('/nad ([0-9]*\.[0-9]+|[0-9]+)\s(kč|€|\$|zł|£)/', Strings::lower($conditions), $matches);
					if (count($matches) === 3) {
						list ($match, $matchedMinimalOrder, $matchedMinimalOrderSymbol) = $matches;
						$minimalOrderUnit = Deal::getUnitBySymbol(Strings::lower($matchedMinimalOrderSymbol));

						if ($minimalOrderUnit) {
							$importedDeal->setFreeShippingMinimalOrder($matchedMinimalOrder);
							$importedDeal->setFreeShippingMinimalOrderCurrency($minimalOrderUnit);
						}
					}
				} else {
					$importedDeal->setType(Deal::TYPE_TIP);
				}
			} elseif ($type === 'coupon' && isset($matchedValue) && isset($unit)) {
				$importedDeal->setType(Deal::TYPE_COUPON);
				$importedDeal->setCode($code);
				$importedDeal->setValue($matchedValue);
				$importedDeal->setUnit($unit);
			}

			$importedDeal->setDescription($conditions);

			//$this->dealImporterProducer->scheduleImportedDeal($importedDeal);
			$this->partnerSystemsDealProducer->scheduleDeal($importedDeal);

			$countOfProcessedDeals++;

			$this->cache->save($uniqueId, true, [Cache::EXPIRATION => '4 hours']);
		}

		$this->context->query('INSERT INTO partner_systems_deal_import', [
			'partner_system_id' => $this->partnerSystem->getId(),
			'count_of_deals' => $countOfDeals,
			'count_of_processed_deals' => $countOfProcessedDeals,
			'count_of_paired_deals' => $countOfPairedDeals,
			'created_at' => new \DateTime(),
		]);
	}

	private function resolveStatus($status, \DateTime $registeredAt)
	{
		if ($this->partnerSystem->getId() === 127) {
			$registeredAt = clone $registeredAt;
			if ($status === 'approved' && $registeredAt->modify('+40 days') > new \DateTime()) {
				return ImportedTransaction::STATUS_REGISTERED;
			}
		}

		switch ($status) {
			case 'pending':
				return ImportedTransaction::STATUS_REGISTERED;
			case 'approved':
				return ImportedTransaction::STATUS_CONFIRMED;
			case 'rejected':
				return ImportedTransaction::STATUS_CANCELLED;
		}

		throw new \InvalidArgumentException('undefined status ' . $status);
	}

	private function getRecords(\DateTime $from, \DateTime $to)
	{
		$client = new Client();
		$response = $client->request('GET', $this->createApiUrl($from, $to));

		$contents = $response->getBody()->getContents();
		$jsonStructure = Json::decode($contents);

		if (isset($jsonStructure->response->status) && $jsonStructure->response->status == 1) {
			return $jsonStructure->response->data->data;
		}

		return [];
	}

	private function getCurrency()
	{
		return $this->partnerSystem->getOption('currency') ? $this->partnerSystem->getOption('currency') : Currency::CZK;
	}

	/**
	 * @param \DateTime $from
	 * @param \DateTime $to
	 * @return string
	 */
	private function createApiUrl(\DateTime $from, \DateTime $to)
	{
		return 'http://api.hasoffers.com/v3/Affiliate_Report.json' .
			sprintf('?limit=%d', 5000) .
			sprintf('&page=%d', 1) .
			'&Method=getConversions' .
			sprintf('&api_key=%s', $this->partnerSystem->getOption('apiKey')) .
			sprintf('&NetworkId=%s', $this->partnerSystem->getOption('networkId')) .
			'&fields[]=Stat.offer_id' .
			'&fields[]=Stat.datetime' .
			'&fields[]=Stat.conversion_id' .
			'&fields[]=Stat.conversion_status' .
			'&fields[]=Stat.conversion_sale_amount' .
			'&fields[]=Stat.payout' .
			'&fields[]=Stat.affiliate_info1' .
			'&sort[Stat.datetime]=asc' .
			'&filters[Stat.date][conditional]=BETWEEN' .
			sprintf('&filters[Stat.date][values][]=%s', $from->format('Y-m-d')) .
			sprintf('&filters[Stat.date][values][]=%s', $to->format('Y-m-d')) .
			sprintf('&data_start=%s', $from->format('Y-m-d')) .
			sprintf('&data_end=%s', $to->format('Y-m-d'));
	}
}
