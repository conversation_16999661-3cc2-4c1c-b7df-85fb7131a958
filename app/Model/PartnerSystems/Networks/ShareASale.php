<?php

namespace tipli\Model\PartnerSystems\Networks;

use Nette\Http\Url;
use SimpleXMLElement;
use tipli\Model\Currencies\Currency;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\PartnerSystems\PartnerSystemProcessResult;
use tipli\Model\Transactions\ImportedTransaction;

interface IShareASaleFactory
{
	/**
	 * @return ShareASale
	 */
	public function create(PartnerSystem $partnerSystem);
}

class ShareASale extends Network
{
	public function createRedirectionLink($url, $userId, $redirectionId, $deepUrl = null)
	{
		$url = new Url($url);
		$url->setQueryParameter('afftrack', $userId . 'x' . $redirectionId);

		if ($deepUrl) {
			$url->setQueryParameter('urllink', $deepUrl);
		}

		return $url->getAbsoluteUrl();
	}

	public function processTransactions(\DateTime $from = null, \DateTime $to = null): PartnerSystemProcessResult
	{
		$records = [];
		if ($from && $to) {
			$records = array_merge($records, $this->parseXml($this->downloadXml($from, $to)));
		}
		$records = array_merge($records, $this->parseXml($this->downloadXml((new \DateTime())->modify('- 7 days'), new \DateTime())));

		$this->processRecords($records);

		return new PartnerSystemProcessResult(count($records));
	}

	private function processRecords($records)
	{
		foreach ($records as $record) {
			$importedTransaction = new ImportedTransaction(
				$this->partnerSystem,
				$record->shopId,
				$record->userId,
				$record->transactionId,
				$record->commissionAmount,
				$record->orderAmount,
				Currency::USD,
				$record->status,
				$record->registeredAt,
				null,
				null,
				ImportedTransaction::CHANNEL_API
			);

			$this->scheduleImportedTransaction($importedTransaction);
		}
	}

	private function parseXml($xmlData)
	{
		$xml = new SimpleXMLElement($xmlData);

		$records = [];

		if (count($xml)) {
			foreach ($xml as $record) {
				$record = (object) $record;

				$locked = ((string) $record->locked == 1);
				$voided = ((string) $record->voided == 1);
				$status = ($locked ? ($voided ? ImportedTransaction::STATUS_CANCELLED : ImportedTransaction::STATUS_CONFIRMED) : ImportedTransaction::STATUS_REGISTERED);

				$transactionId = (string) $record->transid;
				$records[$transactionId] = (object) [
					'transactionId' => $transactionId,
					'userId' => (string) $record->affcomment,
					'shopId' => (string) $record->merchantid,
					'orderAmount' => (float) $record->transamount,
					'commissionAmount' => (float) $record->commission,
					'registeredAt' => new \DateTime($record->transdate),
					'status' => $status,
				];
			}
		}

		return $records;
	}

	private function downloadXml($fromDate, $toDate)
	{
		$affiliateId = $this->partnerSystem->getOption('affiliateId');
		$token = $this->partnerSystem->getOption('token');
		$secretKey = $this->partnerSystem->getOption('secretKey');

		$actionVerb = 'activity';

		$myTimeStamp = gmdate(DATE_RFC1123);

		$sigHash = hash('sha256', $token . ':' . $myTimeStamp . ':' . $actionVerb . ':' . $secretKey);

		$myHeaders = ["x-ShareASale-Date: $myTimeStamp", "x-ShareASale-Authentication: $sigHash"];

		$ch = curl_init();

		curl_setopt($ch, CURLOPT_URL, 'https://api.shareasale.com/x.cfm?action=activity&affiliateId=' . $affiliateId . '&token=' . $token . '&dateStart=' . $fromDate->format('m/d/Y') . '&dateEnd=' . $toDate->format('m/d/Y') . '&sortCol=commission&sortDir=desc&version=2.1&format=xml&sortcol=transdate');
		curl_setopt($ch, CURLOPT_HTTPHEADER, $myHeaders);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_HEADER, 0);

		$result = curl_exec($ch);

		curl_close($ch);

		if ($result) {
			if (stripos($result, 'Error Code ')) {
				throw new \Exception('ShareASale error code: ' . $result);
			} else {
				return $result;
			}
		} else {
			throw new \Exception('ShareASale connection error');
		}
	}


	/**
	}
	 */
}
