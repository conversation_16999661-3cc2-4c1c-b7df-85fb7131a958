<?php

namespace tipli\Model\PartnerSystems\Producers;

use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\PartnerSystems\Messages\PartnerSystemProcessLogMessage;
use tipli\Model\RabbitMq\BaseProducer;
use DateTime;

class PartnerSystemsProcessLogProducer extends BaseProducer
{
	public function scheduleProcessLog(
		?PartnerSystem $partnerSystem = null,
		?DateTime $fromDate = null,
		?DateTime $toDate = null,
		?DateTime $finishedAt = null,
		?int $duration = null,
		?string $errorMessage = null,
		DateTime $createdAt = null,
		$routingKey = 'default',
		?int $countOfTransactions = null
	): void {

		$this->producer->publish(
			new PartnerSystemProcessLogMessage(
				$partnerSystem?->getId(),
				$fromDate,
				$toDate,
				$finishedAt,
				$duration,
				$errorMessage,
				$createdAt,
				$countOfTransactions
			),
			[],
			$routingKey
		);
	}
}
