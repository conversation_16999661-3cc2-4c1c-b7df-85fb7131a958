<?php

namespace tipli\Model\PartnerSystems\Messages;

use Nette\Utils\Json;
use tipli\Model\RabbitMq\IMessage;
use DateTime;

final class PartnerSystemProcessLogMessage implements IMessage
{
	public function __construct(
		private ?int $partnerSystemId,
		private ?DateTime $fromDate,
		private ?DateTime $toDate,
		private ?DateTime $finishedAt,
		private ?int $duration,
		private ?string $errorMessage,
		private DateTime $createdAt,
		private ?int $countOfTransactions
	) {
	}

	public function getPartnerSystemId(): ?int
	{
		return $this->partnerSystemId;
	}

	public function getFromDate(): ?DateTime
	{
		return $this->fromDate;
	}

	public function getToDate(): ?DateTime
	{
		return $this->toDate;
	}

	public function getFinishedAt(): ?DateTime
	{
		return $this->finishedAt;
	}

	public function getDuration(): ?int
	{
		return $this->duration;
	}

	public function getErrorMessage(): ?string
	{
		return $this->errorMessage;
	}

	public function getCreatedAt(): DateTime
	{
		return $this->createdAt;
	}

	public function getCountOfTransactions(): ?int
	{
		return $this->countOfTransactions;
	}

	public function __toString(): string
	{
		return Json::encode([
			'partnerSystemId' => $this->getPartnerSystemId(),
			'fromDate' => $this->getFromDate()?->format('Y-m-d H:i:s'),
			'toDate' => $this->getToDate()?->format('Y-m-d H:i:s'),
			'finishedAt' => $this->getFinishedAt()?->format('Y-m-d H:i:s'),
			'duration' => $this->getDuration(),
			'errorMessage' => $this->getErrorMessage(),
			'createdAt' => $this->getCreatedAt()->format('Y-m-d H:i:s'),
			'countOfTransactions' => $this->getCountOfTransactions(),
		]);
	}

	public static function fromJson(string $data): self
	{
		$data = Json::decode($data);
		return new self(
			$data->partnerSystemId,
			$data->fromDate !== null ? new DateTime($data->fromDate) : null,
			$data->toDate !== null ? new DateTime($data->toDate) : null,
			$data->finishedAt !== null ? new DateTime($data->finishedAt) : null,
			$data->duration,
			$data->errorMessage,
			$data->createdAt !== null ? new DateTime($data->createdAt) : null,
			$data->countOfTransactions
		);
	}
}
