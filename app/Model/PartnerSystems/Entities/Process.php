<?php

namespace tipli\Model\PartnerSystems\Entities;

use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity
 * @ORM\Table(name="tipli_partner_systems_partner_system_process")
 */
class Process
{
	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\PartnerSystems\Entities\PartnerSystem")
	 * @ORM\JoinColumn(name="partner_system_id", referencedColumnName="id")
	 * @var \tipli\Model\PartnerSystems\Entities\PartnerSystem
	 */
	private $partnerSystem;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 * @var \DateTime|null
	 */
	private $fromDate;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 * @var \DateTime|null
	 */
	private $toDate;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 * @var \DateTime
	 */
	private $finishedAt;

	/**
	 * @ORM\Column(type="integer", nullable=true)
	 * @var integer
	 */
	private $duration;

	/**
	 * @ORM\Column(type="text", nullable=true)
	 * @var string|null
	 */
	private $errorMessage;

	/**
	 * @ORM\Column(type="integer", nullable=true)
	 */
	private ?int $countOfTransactions = null;

	/**
	 * @ORM\Column(type="datetime")
	 * @var \DateTime
	 */
	private $createdAt;

	/**
	 * @param PartnerSystem $partnerSystem
	 * @param \DateTime|null $fromDate
	 * @param \DateTime|null $toDate
	 */
	public function __construct(PartnerSystem $partnerSystem, ?\DateTime $fromDate, ?\DateTime $toDate)
	{
		$this->partnerSystem = $partnerSystem;
		$this->fromDate = $fromDate;
		$this->toDate = $toDate;
		$this->createdAt = new \DateTime();
	}

	/**
	 * @return mixed
	 */
	public function getFinishedAt()
	{
		return $this->finishedAt;
	}

	/**
	 * @param mixed $finishedAt
	 */
	public function setFinishedAt($finishedAt): void
	{
		$this->finishedAt = $finishedAt;
	}

	/**
	 * @return mixed
	 */
	public function getDuration()
	{
		return $this->duration;
	}

	/**
	 * @param mixed $duration
	 */
	public function setDuration($duration): void
	{
		$this->duration = $duration;
	}

	/**
	 * @param string|null $errorMessage
	 */
	public function setErrorMessage(?string $errorMessage): void
	{
		$this->errorMessage = $errorMessage;
	}
}
