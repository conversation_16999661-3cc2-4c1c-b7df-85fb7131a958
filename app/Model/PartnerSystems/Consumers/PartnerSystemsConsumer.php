<?php

namespace tipli\Model\PartnerSystems\Consumers;

use Bunny\Message;
use Contributte\RabbitMQ\Consumer\IConsumer;
use Nette\Caching\Cache;
use Nette\Caching\IStorage;
use Nette\Utils\Strings;
use tipli\Model\Datadog\DatadogProducer;
use tipli\Model\PartnerSystems\Producers\PartnerSystemsProcessLogProducer;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\PartnerSystems\Messages\PartnerSystemMessage;
use tipli\Model\PartnerSystems\PartnerSystemFacade;
use tipli\Model\RabbitMq\BaseConsumer;

class PartnerSystemsConsumer extends BaseConsumer implements IConsumer
{
	/** @var Cache */
	private Cache $cache;

	public function __construct(
		private PartnerSystemFacade $partnerSystemFacade,
		private DatadogProducer $datadogProducer,
		private PartnerSystemsProcessLogProducer $partnerSystemsProcessLogProducer,
		IStorage $storage
	) {
		$this->cache = new Cache($storage, self::class);
	}

	public function consume(Message $data): int
	{
		$message = PartnerSystemMessage::fromJson($data->content);

		/** @var PartnerSystem $partnerSystem */
		$partnerSystem = $this->partnerSystemFacade->find($message->getPartnerSystemId());

		echo "zpracovavam: " . $partnerSystem->getName() . "\n";

		$fromDate = $message->getFromDate();
		$toDate = $message->getToDate();

		$network = $this->partnerSystemFacade->getNetwork($partnerSystem);

		$cacheKey = 'processed-' . $partnerSystem->getId();

		if ($this->cache->load($cacheKey) !== null) {
			return IConsumer::MESSAGE_ACK;
		}

		if ($partnerSystem->isAffilbox() === true) {
			$this->cache->save($cacheKey, true, [Cache::EXPIRATION => '1 minute']);
		}

		try {
			$time = time();
//			if ($partnerSystem->isAffilbox()) {
//				Debugger::log($partnerSystem->getId() . ' - affilbox processTransactions', 'partner-systems-consumer');
//			}

			$result = $network->processTransactions($fromDate, $toDate);

			$partnerSystemProcessDuration = time() - $time;

			if ($partnerSystem->isAdmitad()) {
				// save refreshed admitad token
				$this->partnerSystemFacade->save($partnerSystem);
			}

			$this->partnerSystemsProcessLogProducer->scheduleProcessLog(
				$partnerSystem,
				$fromDate === null ? null : $fromDate,
				$toDate === null ? null : $toDate,
				new \DateTime(),
				$partnerSystemProcessDuration,
				null,
				new \DateTime(),
				countOfTransactions: $result ? $result->getCountOfTransactions() : null
			);

			$this->datadogProducer->scheduleSendEvent('partnerSystem.process.' . strtolower($partnerSystem->getType()) . '.success');
		} catch (\Exception $e) {
			$exceptionName = get_class($e);
			$partnerSystemProcessErrorMessage = $e->getMessage();

			$this->partnerSystemsProcessLogProducer->scheduleProcessLog(
				$partnerSystem,
				$fromDate === null ? null : $fromDate,
				$toDate === null ? null : $toDate,
				new \DateTime(),
				null,
				$partnerSystemProcessErrorMessage,
				new \DateTime()
			);

			if (Strings::contains(Strings::lower($exceptionName), 'doctrine')) {
				throw $e;
			}

			$partnerSystem->processError();
			$this->partnerSystemFacade->save($partnerSystem);

			$this->datadogProducer->scheduleSendEvent('partnerSystem.process.' . strtolower($partnerSystem->getType()) . '.error');

			$message = $exceptionName . ': ' . $e->getCode();
			$this->partnerSystemFacade->trackPartnerSystemError($partnerSystem, $message . "\n" . $e->getMessage());
		}

		return IConsumer::MESSAGE_ACK;
	}
}
