<?php

declare(strict_types=1);

namespace tipli\Model\PartnerSystems\Consumers;

use Contributte\RabbitMQ\Consumer\IConsumer;
use Doctrine\DBAL\Connection;
use Nette\Database\Context;
use tipli\Model\PartnerSystems\Messages\PartnerSystemProcessLogMessage;
use tipli\Model\RabbitMq\BaseConsumer;
use Tracy\Debugger;

class PartnerSystemsProcessLogConsumer extends BaseConsumer
{
	public function __construct(private Connection $connection, private Context $context)
	{
	}

	public function consume(array $messages): array
	{
		$values = [];
//		$types = [];
		$result = [];

		foreach ($messages as $message) {
			$result[$message->deliveryTag] = IConsumer::MESSAGE_ACK;
			$message = PartnerSystemProcessLogMessage::fromJson($message->content);

			$values[] = [
				'partner_system_id' => $message->getPartnerSystemId(),
				'from_date' => $message->getFromDate()?->format('Y-m-d H:i:s'),
				'to_date' => $message->getToDate()?->format('Y-m-d H:i:s'),
				'finished_at' => $message->getFinishedAt()?->format('Y-m-d H:i:s'),
				'duration' => $message->getDuration(),
				'error_message' => $message->getErrorMessage(),
				'created_at' => $message->getCreatedAt()->format('Y-m-d H:i:s'),
				'count_of_transactions' => $message->getCountOfTransactions(),
			];
//			$types[] = Connection::PARAM_STR_ARRAY;
		}
		$this->context->query(
			'INSERT INTO tipli_partner_systems_partner_system_process ?',
			$values
		);
		Debugger::log(count($messages) . ' zaznamu', 'PartnerSystemsProcessLogConsumer');
//		$this->connection->executeStatement(
//			'INSERT INTO tipli_partner_systems_partner_system_process (partner_system_id, from_date, to_date, finished_at, duration, error_message, created_at) VALUES ' . implode(", ", $placeholders),
//			$values,
//			$types
//		);

		sleep(20);

		return $result;
	}
}
