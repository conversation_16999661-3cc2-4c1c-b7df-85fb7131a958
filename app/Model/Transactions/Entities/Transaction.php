<?php

namespace tipli\Model\Transactions\Entities;

use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Nette\Utils\Random;
use Nette\Utils\Strings;
use tipli\InvalidArgumentException;
use tipli\Model\Account\Entities\User;
use tipli\Model\Deals\Entities\Deal;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Utm\Entities\Utm;

/**
 * @ORM\Entity(repositoryClass="tipli\Model\Transactions\Repositories\TransactionRepository")
 * @ORM\Table(name="tipli_transactions_transaction", indexes={
 *  @ORM\Index(name="transaction_id_idx", columns={"transaction_id"}),
 *  @ORM\Index(name="type_idx", columns={"type"}),
 *  @ORM\Index(name="currency_idx", columns={"currency"}),
 *  @ORM\Index(name="billable_idx", columns={"billable"}),
 *  @ORM\Index(name="registered_at_idx", columns={"registered_at"}),
 *  @ORM\Index(name="confirmed_at_idx", columns={"confirmed_at"}),
 *  @ORM\Index(name="created_at_idx", columns={"created_at"})
 * }, uniqueConstraints={
 *      @ORM\UniqueConstraint(name="unique_id", columns={"unique_id"})
 * })
 */
class Transaction
{
	public const TYPE_COMMISSION = 'commission';
	public const TYPE_COMMISSION_ERABAT = 'commission_erabat';
	public const TYPE_BONUS = 'bonus';
	public const TYPE_BONUS_ENTIRE = 'bonus_entire';
	public const TYPE_BONUS_ADDON = 'bonus_addon';
	public const TYPE_BONUS_RECOMMENDATION = 'bonus_recommendation';
	public const TYPE_BONUS_FRIEND = 'bonus_friend';
	public const TYPE_BONUS_PARTNER_ORGANIZATION = 'bonus_partner_organization';
	public const TYPE_BONUS_REFUND = 'bonus_refund';
	public const TYPE_BONUS_ERABAT = 'bonus_erabat';
	public const TYPE_BONUS_MARKETING = 'bonus_marketing';
	public const TYPE_BONUS_CAMPAIGN = 'bonus_campaign';
	public const TYPE_BONUS_LUCKY_SHOP = 'bonus_lucky_shop';
	public const TYPE_BONUS_VOUCHER = 'bonus_voucher';
	public const TYPE_BONUS_DEAL = 'bonus_deal';
	public const TYPE_BONUS_MONEY_REWARD = 'bonus_money_reward';
	public const TYPE_BONUS_REWARD = 'bonus_reward';
	public const TYPE_BONUS_FILLED_PROFILE = 'bonus_filled_proflie';
	public const TYPE_BONUS_SURVEY = 'bonus_survey';
	public const TYPE_BONUS_EMAIL_VERIFICATION = 'bonus_email_verification';
	public const TYPE_PAYOUT = 'payout';

	public const ADDON_PROMO_BONUS_AMOUNT = [
		Localization::LOCALE_CZECH => 50,
		Localization::LOCALE_SLOVAK => 2,
		Localization::LOCALE_POLISH => 10,
		Localization::LOCALE_ROMANIAN => 10,
		Localization::LOCALE_ENGLISH => 0,
		Localization::LOCALE_HUNGARIAN => 1000,
		Localization::LOCALE_CROATIA => 2,
		Localization::LOCALE_SLOVENIA => 2,
		Localization::LOCALE_BULGARIA => 10,
	];

	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 * @var int
	 */
	private $id;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Account\Entities\User", inversedBy="transactions")
	 * @ORM\JoinColumn(name="user_id", referencedColumnName="id", nullable=true)
	 * @var \tipli\Model\Account\Entities\User|null
	 */
	private $user;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Shops\Entities\Shop", inversedBy="transactions")
	 * @ORM\JoinColumn(name="shop_id", referencedColumnName="id", nullable=true)
	 * @var \tipli\Model\Shops\Entities\Shop|null
	 */
	private $shop;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\PartnerSystems\Entities\PartnerSystem")
	 * @ORM\JoinColumn(name="partner_system_id", referencedColumnName="id", nullable=true)
	 * @var \tipli\Model\PartnerSystems\Entities\PartnerSystem|null
	 */
	private $partnerSystem;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 * @var string
	 */
	private $transactionId;

	/**
	 * @ORM\Column(type="string", nullable=true, length=24)
	 * @var string|null
	 */
	private $uniqueId;

	/**
	 * @ORM\Column(type="string")
	 * @var string
	 */
	private $type;

	/**
	 * @ORM\Column(type="string", length=3)
	 * @var string
	 */
	private $currency;

	/**
	 * @ORM\Column(type="decimal", precision=10, scale=3)
	 * @var float
	 */
	private float $commissionAmount = 0;

	/**
	 * @ORM\Column(type="decimal", precision=10, scale=3)
	 * @var float
	 */
	private $userCommissionAmount = 0;

	/**
	 * @ORM\Column(type="decimal", precision=10, scale=3)
	 * @var float
	 */
	private $bonusAmount = 0;

	/**
	 * @ORM\OneToOne(targetEntity="\tipli\Model\Transactions\Entities\TransactionData", mappedBy="transaction", cascade={"persist"})
	 * @var \tipli\Model\Transactions\Entities\TransactionData
	 */
	private $transactionData;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Transactions\Entities\Comment", mappedBy="transaction")
	 * @var ArrayCollection|Comment[]
	 */
	private $comments;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Transactions\Entities\Log", mappedBy="transaction", cascade={"persist"})
	 */
	private $logs;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Utm\Entities\Utm")
	 * @ORM\JoinColumn(name="utm_id", referencedColumnName="id")
	 * @var \tipli\Model\Utm\Entities\Utm
	 */
	private $utm;

	/**
	 * @ORM\Column(type="boolean")
	 * @var boolean
	 */
	private $billable = true;

	/**
	 * @ORM\Column(type="datetime")
	 * @var \DateTime
	 */
	private $registeredAt;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 * @var \DateTime|null
	 */
	private $confirmedAt;

	/**
	 * @ORM\Column(type="datetime")
	 * @var \DateTime
	 */
	private $createdAt;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\Rondo\Entities\Transaction", mappedBy="transaction")
	 * @var Collection
	 */
	private $rondoTransaction;

	/**
	 * @ORM\OneToMany(targetEntity="\tipli\Model\HomeCredit\Entities\Transaction", mappedBy="transaction")
	 * @var Collection
 */
	private $homeCreditTransaction;

	/**
	 * @var string
	 */
	private $clientId;

	public function __construct(
		User $user = null,
		Shop $shop = null,
		$transactionId,
		$name,
		$type,
		$currency,
		$commissionAmount,
		$userCommissionAmount,
		$bonusAmount = 0,
		Utm $utm = null,
		DateTime $registeredAt = null,
		?string $uniqueId = null
	) {
		$this->user = $user;
		$this->shop = $shop;

		if ($shop) {
			$this->partnerSystem = $shop->getPartnerSystem();
		}

		$this->transactionId = $transactionId;
		$this->setType($type);

		$this->transactionData = new TransactionData($this);
		$this->transactionData->setName($name);

		$this->setCurrency($currency);
		$this->setCommissionAmount($commissionAmount);
		$this->setUserCommissionAmount($userCommissionAmount);
		$this->setBonusAmount($bonusAmount);

		$this->utm = $utm;

		if ($type === self::TYPE_COMMISSION) {
			$this->billable = $shop ? $shop->isCashbackAllowed() : true;
		} else {
			$this->billable = true;
		}

		if ($shop !== null && $shop->isNonProfitShop()) {
			$this->billable = false;
		}

		$this->registeredAt = $registeredAt ?: new DateTime();
		$this->createdAt = new DateTime();

		$this->comments = new ArrayCollection();
		$this->logs = new ArrayCollection();

		$this->uniqueId = Strings::substring($uniqueId ?? $this->generateUniqueId(), 0, 24);

		$this->rondoTransaction = new ArrayCollection();
		$this->homeCreditTransaction = new ArrayCollection();
	}

	/**
	 * @return int
	 */
	public function getId()
	{
		return $this->id;
	}

	/**
	 * @return string
	 */
	public function getName()
	{
		return $this->transactionData->getName();
	}

	/**
	 * @return float
	 */
	public function getAmount()
	{
		return $this->bonusAmount + $this->userCommissionAmount;
	}

  /**
   * @return float
   */
	public function getUserCommissionAmount(): float
	{
		return $this->userCommissionAmount;
	}

  /**
   * @return Utm
   */
	public function getUtm()
	{
		return $this->utm;
	}

  /**
   * @return DateTime
   */
	public function getConfirmedAt()
	{
		return $this->confirmedAt;
	}

  /**
   * @param string $type
   */
	public function setType(string $type)
	{
		$this->type = $type;
	}

  /**
   * @param string $currency
   */
	public function setCurrency(string $currency)
	{
		$this->currency = $currency;
	}

	public function confirm($commissionAmount = null, $confirmedByScoring = false)
	{
		$this->confirmedAt = new DateTime();

		if ($commissionAmount !== null) {
			$this->setCommissionAmount($commissionAmount);
		}

		if ($confirmedByScoring) {
			$this->transactionData->confirmByScoring();
		}
	}

	public function unConfirm()
	{
		$this->confirmedAt = null;
	}

	public function cancel()
	{
		$this->confirmedAt = new DateTime();

		$this->setCommissionAmount(0);
		$this->setUserCommissionAmount(0);
		$this->setBonusAmount(0);
	}

	public function setConfirmationTreshold($confirmationTreshold)
	{
		$this->transactionData->setConfirmationTreshold($confirmationTreshold);
	}

	public function recomputeShare($shareCoefficient = null)
	{
		if ($shareCoefficient !== null) {
			$this->transactionData->setShareCoefficient($shareCoefficient);
		}

		$this->setUserCommissionAmount($this->getShareCoefficient() * $this->getCommissionAmount());
	}

	/**
	 * @return bool
	 */
	public function hasUser()
	{
		return $this->user ? true : false;
	}

	/**
	 * @return bool
	 */
	public function isConfirmed()
	{
		return $this->confirmedAt ? true : false;
	}

	/**
	 * @return bool
	 */
	public function isCommission()
	{
		return $this->type === self::TYPE_COMMISSION;
	}

	public function isBonusLuckyShop(): bool
	{
		return $this->type === self::TYPE_BONUS_LUCKY_SHOP;
	}

	/**
	 * @return bool
	 */
	public function isCommissionOrRefund()
	{
		return $this->isCommission() || $this->isBonusRefund();
	}

	public function isBonusAddon()
	{
		return $this->type === self::TYPE_BONUS_ADDON;
	}

	public function isBonusRefund()
	{
		return $this->type === self::TYPE_BONUS_REFUND;
	}

	public function isBonusDeal()
	{
		return $this->type === self::TYPE_BONUS_DEAL;
	}

	public function isBonusCampaign()
	{
		return $this->type === self::TYPE_BONUS_CAMPAIGN;
	}

	public function isBonusVoucher()
	{
		return $this->type === self::TYPE_BONUS_VOUCHER;
	}

	/**
	 * @param User $user
	 */
	public function setRegisteredByUser(User $user = null)
	{
		$this->transactionData->setRegisteredByUser($user);
	}

	/**
	 * @param User $user
	 */
	public function setConfirmedByUser(User $user = null)
	{
		$this->transactionData->setConfirmedByUser($user);
	}

	/**
	 * @param bool $billable
	 */
	public function setBillable(bool $billable)
	{
		$this->billable = $billable;
	}

	/**
	 * @param User $user
	 */
	public function setRelatedRecommendedUser(User $user = null)
	{
		$this->transactionData->setRelatedRecommendedUser($user);
	}

	public function getRelatedRecommendedUser()
	{
		return $this->transactionData->getRelatedRecommendedUser();
	}

	public function getShareCoefficient()
	{
		return $this->transactionData->getShareCoefficient();
	}

	public function getOrderAmount(): float
	{
		return $this->transactionData->getOrderAmount();
	}

	public function getUserCommissionInPercent()
	{
		if ($this->getOrderAmount()) {
			return round($this->userCommissionAmount / $this->getOrderAmount() * 100, 2);
		}

		return 0;
	}

	/**
	 * @return bool
	 */
	public function isPayout()
	{
		return $this->type === self::TYPE_PAYOUT;
	}

	/**
	 * @return bool
	 */
	public function isBonus(): bool
	{
		return Strings::startsWith($this->type, 'bonus');
	}

	/**
	 * @return bool
	 */
	public function isBonusEntire()
	{
		return $this->type == self::TYPE_BONUS_ENTIRE;
	}

	/**
	 * @return bool
	 */
	public function isBonusRecommendation()
	{
		return $this->type == self::TYPE_BONUS_RECOMMENDATION;
	}

	public function isFriendBonus()
	{
		return $this->type == self::TYPE_BONUS_FRIEND;
	}

	/**
	 * @return bool
	 */
	public function isBillable()
	{
		return $this->billable;
	}

	public function prepareForConfirm()
	{
		$this->transactionData->prepareForConfirm();
	}

	public function unPrepareForConfirm()
	{
		$this->transactionData->setPreparedForConfirm(false);
	}

	public function isPreparedForConfirm()
	{
		return $this->transactionData->isPreparedForConfirm();
	}

	public function getConfirmationTreshold()
	{
		return $this->transactionData->getConfirmationTreshold();
	}

	public function getRecommendationBonusTreshold()
	{
		return $this->transactionData->getRecommendationBonusTreshold();
	}

	public function setRecommendationBonusTreshold($recommendationBonusTreshold)
	{
		return $this->transactionData->setRecommendationBonusTreshold($recommendationBonusTreshold);
	}

	/**
	 * @param float $orderAmount
	 */
	public function setOrderAmount($orderAmount)
	{
		$this->transactionData->setOrderAmount($orderAmount);
	}

	public function setAccounting($turnover, $income)
	{
		$this->transactionData->setTurnover($turnover);
		$this->transactionData->setIncome($income);
	}

	public function setOriginalAccounting($turnover, $income)
	{
		$this->transactionData->setOriginalTurnover($turnover);
		$this->transactionData->setOriginalIncome($income);
	}

	/**
	 * @param mixed $commissionAmount
	 */
	public function setCommissionAmount($commissionAmount)
	{
		if ($commissionAmount < 0) {
			throw new InvalidArgumentException('Commission amount is less than zero.');
		}

		$this->commissionAmount = round($commissionAmount, 3);

		$this->recomputeShare();
	}

	/**
	 * @param mixed $userCommissionAmount
	 */
	public function setUserCommissionAmount($userCommissionAmount)
	{
		$this->userCommissionAmount = round($userCommissionAmount, 3);
	}

	/**
	 * @param mixed $bonusAmount
	 */
	public function setBonusAmount($bonusAmount)
	{
		$this->bonusAmount = round($bonusAmount, 3);
	}

	public function isCancelled()
	{
		return $this->getAmount() == 0 && $this->isConfirmed();
	}

	public function getEstimatedConfirmedAt(): ?DateTime
	{
		if ($this->shop && $this->isCommission()) {
			$estimatedConfirmedAt = clone $this->registeredAt;
			return $estimatedConfirmedAt->modify('+ ' . ($this->shop->getAverageConfirmationPeriod() ?: 60) . ' days');
		}

		return null;
	}

	public function isConfirmationInDelay(): bool
	{
		$estimatedConfirmedDate = $this->getEstimatedConfirmedAt();
		$currentDate = new DateTime();

		if ($estimatedConfirmedDate && $estimatedConfirmedDate < $currentDate) {
			return true;
		}

		return false;
	}

	public function getTurnover()
	{
		return $this->transactionData->getTurnover();
	}

	public function getOriginalTurnover()
	{
		return $this->transactionData->getOriginalTurnover();
	}

	public function getOriginalCommissionAmount()
	{
		return $this->transactionData->getOriginalCommissionAmount();
	}

	public function getOriginalConfirmedCommissionAmount()
	{
		return $this->transactionData->getOriginalConfirmedCommissionAmount();
	}

	public function setOriginalConfirmedCommissionAmount($commissionAmount)
	{
		$this->transactionData->setOriginalConfirmedCommissionAmount($commissionAmount);
	}

	public function setOriginalCommissionAmount($commissionAmount)
	{
		$this->transactionData->setOriginalCommissionAmount($commissionAmount);
	}

	public function setOriginalCurrency($currency)
	{
		$this->transactionData->setOriginalCurrency($currency);
	}

	public function isAllowedConfirm()
	{
		return ($this->isCommission() || $this->isFriendBonus()) && !$this->isConfirmed();
	}

	public function isAllowedCancel()
	{
		return $this->isAllowedConfirm() || ($this->isBonus() && !$this->isCancelled());
	}

	/**
	 * @return string
	 */
	public function getClientId()
	{
		return $this->clientId;
	}

	/**
	 * @param string $clientId
	 */
	public function setClientId($clientId)
	{
		$this->clientId = $clientId;
	}

	public function getCommissionAmount(): float
	{
		return $this->commissionAmount;
	}

  /**
   * @return string
   */
	public function getType()
	{
		return $this->type;
	}

  /**
   * @return string
   */
	public function getTransactionId()
	{
		return $this->transactionId;
	}

  /**
   * @return ArrayCollection|TransactionData
   */
	public function getComments()
	{
		return $this->comments;
	}

  /**
   * @return DateTime
   */
	public function getCreatedAt(): DateTime
	{
		return $this->createdAt;
	}

  /**
   * @return DateTime
   */
	public function getRegisteredAt(): DateTime
	{
		return $this->registeredAt;
	}

	/**
	 * @return mixed
	 */
	public function getCurrency()
	{
		return $this->currency;
	}

  /**
   * @return null|Shop
   */
	public function getShop()
	{
		return $this->shop;
	}

	public function hasShop(): bool
	{
		return $this->shop !== null;
	}

	/**
	 * @return User|null
	 */
	public function getUser()
	{
		return $this->user;
	}

  /**
   * @param User $user
   */
	public function setUser($user)
	{
		$this->user = $user;
	}

	/**
	 * @return int
	 */
	public function getCountOfDaysRegistered()
	{
		$interval = (new DateTime())->diff($this->registeredAt);
		return $interval->days;
	}

	public function getOriginalCurrency()
	{
		return $this->transactionData->getOriginalCurrency();
	}

	public static function getTypes()
	{
		return [
			self::TYPE_COMMISSION => 'odměna',
			self::TYPE_BONUS_REFUND => 'reklamace',
			self::TYPE_BONUS_DEAL => 'bonus za nahlášení dealu',
			self::TYPE_PAYOUT => 'výplata',
			self::TYPE_COMMISSION_ERABAT => 'provize-erabat',
			self::TYPE_BONUS_ADDON => 'bonus za instalaci doplňku',
			self::TYPE_BONUS_RECOMMENDATION => 'bonus za doporučení',
			self::TYPE_BONUS_FRIEND => 'bonus doporučeného',
			self::TYPE_BONUS_PARTNER_ORGANIZATION => 'bonus partnerské organizace',
			self::TYPE_BONUS_MARKETING => 'marketingový bonus',
			self::TYPE_BONUS_CAMPAIGN => 'bonus z 200 kč kampaně',
			self::TYPE_BONUS_VOUCHER => 'voucher bonus',
			self::TYPE_BONUS_MONEY_REWARD => 'bonus z kampaně',
			self::TYPE_BONUS_REWARD => 'bonus z kampaně (nový)',
			self::TYPE_BONUS_FILLED_PROFILE => 'bonus za vyplněná profilu',
			self::TYPE_BONUS_SURVEY => 'survey bonus',
			self::TYPE_BONUS_ENTIRE => 'entire bonus',
			self::TYPE_BONUS_LUCKY_SHOP => 'lucky shop bonus',
		];
	}

	public function getTypeLabel()
	{
		return self::getTypes()[$this->type];
	}

	/**
	 * @return float
	 */
	public function getBonusAmount(): float
	{
		return $this->bonusAmount;
	}

	public function setUtm(Utm $utm = null)
	{
		$this->utm = $utm;
	}

	/**
	 * @return null|\tipli\Model\PartnerSystems\Entities\PartnerSystem
	 */
	public function getPartnerSystem()
	{
		return $this->partnerSystem;
	}

	/**
	 * @param null|\tipli\Model\PartnerSystems\Entities\PartnerSystem $partnerSystem
	 */
	public function setPartnerSystem($partnerSystem)
	{
		$this->partnerSystem = $partnerSystem;
	}

	public function getRegisteredByUser()
	{
		return $this->transactionData->getRegisteredByUser();
	}

	public function getRondoTransaction(): ?\tipli\Model\Rondo\Entities\Transaction
	{
		if ($this->rondoTransaction->isEmpty()) {
			return null;
		}

		return $this->rondoTransaction->first();
	}

	public function getHomeCreditTransaction(): ?\tipli\Model\HomeCredit\Entities\Transaction
	{
		if ($this->homeCreditTransaction->isEmpty()) {
			return null;
		}

		return $this->homeCreditTransaction->first();
	}

	public function getTransactionData(): TransactionData
	{
		return $this->transactionData;
	}

	/**
	 * @return string|null
	 */
	public function getPlatform(): ?string
	{
		return $this->transactionData->getPlatform();
	}

	/**
	 * @param string|null $platform
	 */
	public function setPlatform(?string $platform): void
	{
		$this->transactionData->setPlatform($platform);
	}

	public function getDeal(): ?Deal
	{
		return $this->transactionData->getDeal();
	}

	public function setDeal(?Deal $deal)
	{
		$this->transactionData->setDeal($deal);
	}

	public function isAliexpressTransaction()
	{
		$shop = $this->getShop();
		if (!$shop) {
			return false;
		}

		return Strings::lower($shop->getSlug()) === 'aliexpress' || in_array($shop->getId(), [1, 298, 617]);
	}

	/**
	 * @return float|null
	 */
	public function getShopConfirmationRate(): ?float
	{
		return $this->transactionData->getShopConfirmationRate();
	}

	/**
	 * @param float|null $shopConfirmationRate
	 */
	public function setShopConfirmationRate(?float $shopConfirmationRate): void
	{
		$this->transactionData->setShopConfirmationRate($shopConfirmationRate);
	}

	public function setImportedAt(\DateTime $importedAt = null)
	{
		$this->transactionData->setImportedAt($importedAt);
	}

	public function isPaid(): bool
	{
		return $this->transactionData->isPaid();
	}

	public function generateUniqueId(): string
	{
		$uniqueId = Random::generate(24, '0-9a-zA-Z');

		if (
			in_array($this->getType(), [self::TYPE_COMMISSION, self::TYPE_COMMISSION_ERABAT], true)
			&& $this->getTransactionId() !== null
		) {
			$partnerSytemId = $this->getPartnerSystem() ? $this->getPartnerSystem()->getId() : 0;

			$uniqueId = implode('-', [
				$partnerSytemId,
				Strings::substring(sha1($this->getTransactionId()), 0, 8),
				$this->getTransactionId(),
			]);
		}

		if (
			$this->getType() === self::TYPE_BONUS_ADDON
			&& $this->getUser() !== null
		) {
			$uniqueId = $this->getUser()->getId() . '-bonus_addon';
		}

		if (
			$this->getType() === self::TYPE_BONUS_RECOMMENDATION
			&& $this->getUser() !== null
			&& $this->getRelatedRecommendedUser() !== null
		) {
			$uniqueId = $this->getUser()->getId() . '-' . $this->getRelatedRecommendedUser()->getId();
		}

		return Strings::substring($uniqueId, 0, 24);
	}

	public function getUniqueId(): ?string
	{
		return $this->uniqueId;
	}

	public function expire(): void
	{
		$this->billable = false;
		$this->transactionData->expire();
	}

	public function isExpired(): bool
	{
		return $this->transactionData->isExpired();
	}

	public function getExpiredAt(): ?\DateTime
	{
		return $this->transactionData->getExpiredAt();
	}

	public function setUniqueId($uniqueId): void
	{
		$this->uniqueId = $uniqueId;
	}

	public static function getUniqueIdForLuckyShopBonusTransaction(int $userId, int $luckyShopId): string
	{
		$uniqueId = 'ls-' . $luckyShopId . '-' . $userId;

		return Strings::substring(sha1($uniqueId), 0, 24);
	}

	public function setRedirectedAt(DateTime $getCreatedAt)
	{
		$this->transactionData->setRedirectedAt($getCreatedAt);
	}

	public function getRedirectedAt(): ?DateTime
	{
		return $this->transactionData->getRedirectedAt();
	}
}
