<?php

namespace tipli\Model\Transactions\Repositories;

use tipli\Model\Currencies\Currency;
use tipli\Model\Doctrine\BaseRepository;
use Nette\Utils\Strings;
use tipli\Model\Account\Entities\User;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\Refunds\Entities\Refund;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Transactions\Entities\Transaction;

class TransactionRepository extends BaseRepository
{
	public function getTransactions()
	{
		return $this->createQueryBuilder('t')
			->leftJoin('t.user', 'u')
			->leftJoin('t.shop', 's');
	}

	public function getTransactionsByUser(User $user)
	{
		return $this->createQueryBuilder('t')
			->andWhere('t.user = :user')
			->setParameter('user', $user)
			->andWhere('t.currency = :currency')
			->setParameter('currency', $user->getLocalization()->getCurrency())
			->andWhere('t.billable = true');
	}

	public function getRegisteredCommissionBalance(User $user)
	{
		return $this->getTransactionsByUser($user)
			->select('sum(t.userCommissionAmount) AS balance')
			->andWhere('t.confirmedAt IS NULL')
			->getQuery()
			->getSingleScalarResult();
	}

	public function getCommissionBalance(User $user)
	{
		return $this->getTransactionsByUser($user)
			->select('sum(t.userCommissionAmount) AS balance')
			->andWhere('t.type = :commission')
			->setParameter('commission', Transaction::TYPE_COMMISSION)
			->getQuery()
			->getSingleScalarResult();
	}

	public function getRegisteredBonusBalance(User $user)
	{
		return $this->getTransactionsByUser($user)
			->select('sum(t.bonusAmount) AS balance')
			->andWhere('t.confirmedAt IS NULL')
			->getQuery()
			->getSingleScalarResult();
	}

	public function getRegisteredBalance(User $user, array $types = [])
	{
		$qb = $this->getTransactionsByUser($user)
			->select('(sum(t.userCommissionAmount) + sum(t.bonusAmount)) AS balance')
			->andWhere('t.confirmedAt IS NULL');

		if (!empty($types)) {
			$qb->andWhere('t.type IN (:types)')
				->setParameter('types', $types);
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getConfirmedCommissionBalance(User $user)
	{
		return $this->getTransactionsByUser($user)
			->select('IFNULL(sum(t.userCommissionAmount),0) AS balance')
			->andWhere('t.confirmedAt IS NOT NULL')
			->getQuery()
			->getSingleScalarResult();
	}

	public function getConfirmedLifetimeCommissionBalance(User $user)
	{
		return $this->getTransactionsByUser($user)
			->select('sum(t.userCommissionAmount) AS balance')
			->andWhere('t.confirmedAt IS NOT NULL')
			->andWhere('t.type != :payout')
			->setParameter('payout', Transaction::TYPE_PAYOUT)
			->getQuery()
			->getSingleScalarResult();
	}

	public function getConfirmedBonusBalance(User $user)
	{
		return $this->getTransactionsByUser($user)
			->select('sum(t.bonusAmount) AS balance')
			->andWhere('t.confirmedAt IS NOT NULL')
			->getQuery()
			->getSingleScalarResult();
	}

	public function getConfirmedBalance(User $user, \DateTime $fromDate = null, $types = [])
	{
		$qb = $this->getTransactionsByUser($user)
			->select('(sum(t.userCommissionAmount) + sum(t.bonusAmount)) AS balance')
			->andWhere('t.confirmedAt IS NOT NULL');

		if ($fromDate) {
			$qb->andWhere('t.createdAt >= :fromDate')
				->setParameter('fromDate', $fromDate);
		}

		if (!empty($types)) {
			$qb->andWhere('t.type IN (:types)')
				->setParameter('types', $types);
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function getBalance(User $user, \DateTime $fromDate = null, $types = [])
	{
		$qb = $this->getTransactionsByUser($user)
			->select('(sum(t.userCommissionAmount) + sum(t.bonusAmount)) AS balance');

		if ($fromDate) {
			$qb->andWhere('t.createdAt >= :fromDate')
				->setParameter('fromDate', $fromDate);
		}

		if (!empty($types)) {
			$qb->andWhere('t.type IN (:types)')
				->setParameter('types', $types);
		}

		return $qb->getQuery()->getSingleScalarResult();
	}

	private function getLifetimeBalanceQueryBuilder(User $user)
	{
		return $this->getTransactionsByUser($user)
			->select('(sum(t.userCommissionAmount) + sum(t.bonusAmount)) AS balance')
			->andWhere('t.type != :payout')
			->setParameter('payout', Transaction::TYPE_PAYOUT);
	}

	public function getLifetimeBalance(User $user)
	{
		return $this->getLifetimeBalanceQueryBuilder($user)
			->getQuery()
			->getSingleScalarResult();
	}

	public function getLifetimeConfirmedBalance(User $user)
	{
		return $this->getLifetimeBalanceQueryBuilder($user)
			->andWhere('t.confirmedAt IS NOT NULL')
			->getQuery()
			->getSingleScalarResult();
	}

	public function getPreparedForConfirmTransactions($user, $confirmationTreshold)
	{
		return $this->getTransactionsByUser($user)
			->innerJoin('t.transactionData', 'data')
			->andWhere('t.type LIKE :bonus')
			->setParameter('bonus', 'bonus%')
			->andWhere('t.confirmedAt IS NULL')
			->andWhere('data.preparedForConfirm = true')
			->andWhere('data.confirmationTreshold <= :confirmationTreshold')
			->setParameter('confirmationTreshold', $confirmationTreshold)
			->getQuery()
			->getResult();
	}

	public function findRecommendationBonus(User $relatedRecommendedUser)
	{
		return $this->createQueryBuilder('t')
			->innerJoin('t.transactionData', 'data')
			->andWhere('data.relatedRecommendedUser = :relatedRecommendedUser')
			->setParameter('relatedRecommendedUser', $relatedRecommendedUser)
			->getQuery()
			->getOneOrNullResult();
	}

	public function findAddonBonus(User $user)
	{
		return $this->createQueryBuilder('t')
			->andWhere('t.user = :user')
			->setParameter('user', $user)
			->andWhere('t.currency = :currency')
			->setParameter('currency', $user->getLocalization()->getCurrency())
			->andWhere('t.type = :type')
			->setParameter('type', Transaction::TYPE_BONUS_ADDON)
			->setMaxResults(1)
			->getQuery()
			->getOneOrNullResult();
	}

	public function findSurveyBonus($user)
	{
		return $this->getTransactionsByUser($user)
			->andWhere('t.type = :type')
			->setParameter('type', Transaction::TYPE_BONUS_SURVEY)
			->getQuery()
			->getOneOrNullResult();
	}

	public function findFilledProfileBonus(User $user)
	{
		return $this->getTransactionsByUser($user)
			->andWhere('t.type = :type')
			->setParameter('type', Transaction::TYPE_BONUS_FILLED_PROFILE)
			->getQuery()
			->getOneOrNullResult();
	}

	public function findEntireBonus(User $user)
	{
		return $this->getTransactionsByUser($user)
			->andWhere('t.type = :type')
			->setParameter('type', Transaction::TYPE_BONUS_ENTIRE)
			->getQuery()
			->getOneOrNullResult();
	}

	public function findPartnerOrganizationBonus(User $user)
	{
		return $this->getTransactionsByUser($user)
			->andWhere('t.type = :type')
			->setParameter('type', Transaction::TYPE_BONUS_PARTNER_ORGANIZATION)
			->getQuery()
			->getOneOrNullResult();
	}

	public function findByTransactionId($transactionId, $partnerSystem)
	{
		return $this->getTransactions()
			->andWhere('t.partnerSystem = :partnerSystem')
			->setParameter('partnerSystem', $partnerSystem)
			->andWhere('t.transactionId = :transactionId')
			->setParameter('transactionId', Strings::substring($transactionId, 0, 255))
			->getQuery()
			->getOneOrNullResult();
	}

	public function findById(int $id, $partnerSystem)
	{
		return $this->getTransactions()
			->andWhere('t.partnerSystem = :partnerSystem')
			->setParameter('partnerSystem', $partnerSystem)
			->andWhere('t.id = :id')
			->setParameter('id', $id)
			->getQuery()
			->getOneOrNullResult();
	}

	public function findPendingTransactions($from, $to, $currency)
	{
		return $this->getTransactions()
			->andWhere('t.user IS NOT NULL')
			->andWhere('t.createdAt >= :from')
			->andWhere('t.createdAt <= :to')
			->andWhere('t.currency = :currency')
			->andWhere('t.billable = true')
			->setParameter('from', $from)
			->setParameter('to', $to)
			->setParameter('currency', $currency)
			->getQuery()
			->getResult();
	}

	public function findLastRegisteredTransactionByShop(Shop $shop): ?Transaction
	{
		return $this->getTransactions()
			->andWhere('t.type = :type')
			->setParameter('type', Transaction::TYPE_COMMISSION)
			->andWhere('t.shop = :shop')
			->setParameter('shop', $shop)
			->setMaxResults(1)
			->addOrderBy('t.id', 'desc')
			->getQuery()
			->getOneOrNullResult();
	}

	public function findUnconfirmedTransactions(PartnerSystem $partnerSystem, \DateTime $from, \DateTime $to)
	{
		return $this->getTransactions()
			->andWhere('t.confirmedAt IS NULL')
			->andWhere('t.partnerSystem = :partnerSystem')
			->andWhere('t.registeredAt >= :from')
			->andWhere('t.registeredAt <= :to')
			->setParameter('partnerSystem', $partnerSystem)
			->setParameter('from', $from)
			->setParameter('to', $to)
			->getQuery()
			->getResult();
	}

	public function findShopsWhereUserHasTransaction(User $user, ?\DateTime $fromDate = null)
	{
		$qb = $this->createQueryBuilder();

		$qb->select('s')
			->from(Shop::class, 's')
			->innerJoin('s.transactions', 't');

		if ($fromDate) {
			$qb->andWhere('t.createdAt >= :fromDate')
				->setParameter('fromDate', $fromDate);
		}

		$qb->andWhere('t.user = :user');
		$qb->setParameter('user', $user);

		$qb->addGroupBy('s.id');

		return $qb->getQuery()->getResult();
	}

	public function findCommissionTransactions(\DateTime $from, \DateTime $to, $confirmedOnly = false, $withUserOnly = true, $exceptCancelled = true, User $user = null, Shop $shop = null)
	{
		$qb = $this->createQueryBuilder('t')
			->andWhere('t.type = :type')
			->setParameter('type', Transaction::TYPE_COMMISSION);

		$qb->andWhere($confirmedOnly ? 't.confirmedAt >= :from' : 't.createdAt >= :from');
		$qb->andWhere($confirmedOnly ? 't.confirmedAt <= :to' : 't.createdAt <= :to');

		if ($withUserOnly && !$user) {
			$qb->andWhere('t.user IS NOT NULL');
		}

		if ($exceptCancelled) {
			$qb->andWhere('t.userCommissionAmount > 0');
		}

		if ($user) {
			$qb->andWhere('t.user = :user')
				->setParameter('user', $user);
		}

		if ($shop) {
			$qb->andWhere('t.shop = :shop')
				->setParameter('shop', $shop);
		}

		$qb->andWhere('t.billable = :true')
			->setParameter('true', true);

		$qb->setParameter('from', $from)
			->setParameter('to', $to);

		return $qb->getQuery()->getResult();
	}

	public function findCountOfUserPayoutsFrom(User $user, \DateTime $fromDate, $minimalUserCommissionAmount)
	{
		$qb = $this->getTransactions()
			->select('count(t.id)')
			->andWhere('t.user = :user')
			->andWhere('t.confirmedAt >= :fromDate')
			->andWhere('t.userCommissionAmount <= :minimalUserCommissionAmount')
			->andWhere('t.type = :payout')
			->setParameter('user', $user)
			->setParameter('fromDate', $fromDate)
			->setParameter('minimalUserCommissionAmount', ($minimalUserCommissionAmount * -1))
			->setParameter('payout', Transaction::TYPE_PAYOUT);

		return $qb->getQuery()->getSingleScalarResult();
	}

	public function findCountOfUserCommissionTransactions(User $user)
	{
		return $this->getTransactionsByUser($user)
			->select('count(t.id)')
			->andWhere('t.type = :type')
			->setParameter('type', Transaction::TYPE_COMMISSION)
			->getQuery()
			->getSingleScalarResult();
	}

	public function findCountOfCommissionTransactionsByShopIds(array $shopIds, User $user): int
	{
		return (int) $this->createQueryBuilder('t')
			->select('count(t.id)')
			->andWhere('t.type = :type')
			->setParameter('type', Transaction::TYPE_COMMISSION)
			->andWhere('t.shop IN (:shopIds)')
			->setParameter('shopIds', $shopIds)
			->andWhere('t.user = :user')
			->setParameter('user', $user)
			->getQuery()
			->getSingleScalarResult();
	}

	public function findEhubActivationTransactions(\DateTime $fromDate)
	{
		$qb = $this->getTransactions()
			->andWhere('t.type = :commission')
			->andWhere('t.confirmedAt IS NOT NULL')
			->andWhere('t.confirmedAt >= :fromDate')
				->innerJoin('u.utm', 'utm')
			->andWhere('utm.utmSource = :utmSource')
			->andWhere('t.userCommissionAmount > 0');

		$qb->setParameters([
			'commission' => Transaction::TYPE_COMMISSION,
			'fromDate' => $fromDate,
			'utmSource' => 'ehub',
		]);

		return $qb->getQuery()->getResult();
	}

	public function findTransactionsByUserWithShop(User $user, Shop $shop)
	{
		return $this->getTransactionsByUser($user)
			->andWhere('t.shop = :shop')
			->setParameter('shop', $shop)
			->getQuery()
			->getResult();
	}

	public function findUnconfirmedCommissionTransactionByUser(User $user, \DateTime $from, \DateTime $to)
	{
		return $this->createQueryBuilder('t')
			->andWhere('t.type = :type')
			->setParameter('type', Transaction::TYPE_COMMISSION)
			->andWhere('t.user = :user')
			->setParameter('user', $user)
			->andWhere('t.createdAt >= :from')
			->setParameter('from', $from)
			->andWhere('t.createdAt <= :to')
			->setParameter('to', $to)
			->andWhere('t.confirmedAt IS NULL')
			->addOrderBy('t.id', 'DESC')
			->getQuery()
			->getResult();
	}

	public function findUnconfirmedCommissionOrRefundTransactionsByUser(User $user, \DateTime $from, \DateTime $to)
	{
		return $this->createQueryBuilder('t')
			->andWhere('t.type IN (:types)')
			->setParameter('types', [Transaction::TYPE_COMMISSION, Transaction::TYPE_BONUS_REFUND, Transaction::TYPE_BONUS_REWARD])
			->andWhere('t.user = :user')
			->setParameter('user', $user)
			->andWhere('t.createdAt >= :from')
			->setParameter('from', $from)
			->andWhere('t.createdAt <= :to')
			->setParameter('to', $to)
			->andWhere('t.confirmedAt IS NULL')
			->addOrderBy('t.id', 'DESC')
			->getQuery()
			->getResult();
	}

	public function findCanceledCommissionByUser(User $user, ?\DateTime $from)
	{
		$qb = $this->createQueryBuilder('t')
			->andWhere('t.type = \'commission\'')
			->andWhere('t.user = :user')
			->setParameter('user', $user)
			->andWhere('t.confirmedAt IS NOT NULL')
			->andWhere('t.userCommissionAmount = 0')
			->andWhere('t.bonusAmount = 0')
			->addOrderBy('t.id', 'DESC')
		;

		if ($from) {
			$qb->andWhere('t.createdAt >= :from')
				->setParameter('from', $from)
				->andWhere('t.createdAt <= :to')
				->setParameter('to', new \DateTime())
			;
		}

		return $qb->getQuery()->getResult();
	}

	public function findCountOfBonusRefundTransactions(User $user, \DateTime $from, \DateTime $to): int
	{
		return (int) $this->getTransactionsByUser($user)
			->select('count(t.id)')
			->andWhere('t.createdAt >= :from')
			->setParameter('from', $from)
			->andWhere('t.createdAt <= :to')
			->setParameter('to', $to)
			->andWhere('t.type = :type')
			->setParameter('type', Transaction::TYPE_BONUS_REFUND)
			->getQuery()
			->getSingleScalarResult();
	}

	public function findCountOfTransactionByUserInShop(User $user, Shop $shop, \DateTime $from, \DateTime $to): int
	{
		return (int) $this->getTransactionsByUser($user)
			->select('count(t.id)')
			->andWhere('t.shop = :shop')
			->setParameter('shop', $shop)
			->andWhere('t.createdAt >= :from')
			->setParameter('from', $from)
			->andWhere('t.createdAt <= :to')
			->setParameter('to', $to)
			->andWhere('(t.type = :refund OR t.type = :commission)')
			->setParameter('refund', Transaction::TYPE_BONUS_REFUND)
			->setParameter('commission', Transaction::TYPE_COMMISSION)
			->andWhere('t.billable = true')
			->getQuery()
			->getSingleScalarResult();
	}

	public function getUserBonusRefundTransactionsWithoutRefund(User $user)
	{
		return $this->getTransactionsByUser($user)
			->leftJoin(Refund::class, 'r', 'WITH', 'r.refundTransaction = t')
			->andWhere('r.id IS NULL')
			->andWhere('t.type = :type')
			->setParameter('type', Transaction::TYPE_BONUS_REFUND);
	}

	public function findTransactionsToExpire($limit = 1000)
	{
		$qb = $this->createQueryBuilder('t')
			->innerJoin('t.transactionData', 'td')
			->innerJoin('t.user', 'u')
			->innerJoin('u.segmentData', 'sd')
			->leftJoin('u.partnerOrganization', 'p')
			->andWhere('td.isPaid = 0 AND t.billable = 1 AND td.expiredAt IS NULL')
			->andWhere('t.type != :payoutType')
			->andWhere('(t.currency = :eur OR t.currency = :pln OR t.currency = :czk)')
			->andWhere('(u.partnerOrganization IS NULL OR p.isWhiteLabel = 0)')
			->setParameter('czk', Currency::CZK)
			->setParameter('eur', Currency::EUR)
			->setParameter('pln', Currency::PLN)
			->setParameter('payoutType', Transaction::TYPE_PAYOUT)
			->andWhere('sd.lastTransactionAt <= :dateTime AND t.createdAt <= :dateTime AND t.registeredAt <= :dateTime')
			->setParameter('dateTime', (new \DateTime())->modify('-2 years'))
			->addOrderBy('t.id')
			->setMaxResults($limit)
		;

		return $qb->getQuery()->getResult();
	}

	public function getNonProfitTransactionsAmountForUser(User $user)
	{
		return (float) $this->createQueryBuilder('t')
			->select('sum(t.userCommissionAmount) AS balance')
			->andWhere('t.user = :user')
			->setParameter('user', $user)
			->andWhere('t.shop IN (:shopIds)')
			->setParameter('shopIds', Shop::NON_PROFIT_SHOP_IDS)
			->getQuery()
			->getSingleScalarResult()
		;
	}

	public function findByUniqueId(string $uniqueId)
	{
		return $this->createQueryBuilder('t')
			->andWhere('t.uniqueId = :uniqueId')
			->setParameter('uniqueId', $uniqueId)
			->getQuery()
			->getOneOrNullResult()
		;
	}

	public function findEmailVerificationBonusTransaction(User $user): ?Transaction
	{
		return $this->getTransactionsByUser($user)
			->andWhere('t.type = :type')
			->setParameter('type', Transaction::TYPE_BONUS_EMAIL_VERIFICATION)
			->setMaxResults(1)
			->getQuery()
			->getOneOrNullResult()
		;
	}

	public function findUserTransactionsByShopIds(int $userId, array $shopIds): array
	{
		$qb = $this->createQueryBuilder();

		$qb->select('DISTINCT s.id')
			->from(Transaction::class, 't')
			->innerJoin('t.shop', 's', 'WITH', 's.id = t.shop')
			->innerJoin('t.user', 'u', 'WITH', 'u.id = t.user')
			->where($qb->expr()->in('t.shop', ':shopIds'))
			->andWhere('t.user = :userId')
			->setParameter('shopIds', $shopIds)
			->setParameter('userId', $userId);

		return array_map('current', $qb->getQuery()->getArrayResult());
	}

	public function findTransactionsByShopForUser(Shop $shop, User $user)
	{
		return $this->createQueryBuilder('t')
			->andWhere('t.shop = :shop')
			->setParameter('shop', $shop)
			->andWhere('t.user = :user')
			->setParameter('user', $user)
			->andWhere('t.type IN (:types)')
			->setParameter('types', [Transaction::TYPE_COMMISSION, Transaction::TYPE_BONUS_REFUND])
			->andWhere('t.billable = true')
			->getQuery()
			->getResult();
	}

	public function findSazkaTransaction(User $user)
	{
		return $this->createQueryBuilder('t')
			->andWhere('t.user = :user')
			->setParameter('user', $user)
			->andWhere('t.shop = :shop')
			->setParameter('shop', 5283)
			->andWhere('t.type IN (:types)')
			->setParameter('types', [Transaction::TYPE_COMMISSION, Transaction::TYPE_BONUS_REFUND])
			->getQuery()
			->getOneOrNullResult();
	}
}
