<?php

namespace tipli\Model\Transactions;

use Nette\Database\Context;
use tipli\Model\Account\Entities\User;
use tipli\Model\Queues\SqlQueryScheduler;
use tipli\Model\PartnerSystems\Entities\PartnerSystem;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Transactions\Entities\ImportedTransaction as ImportedTransactionEntity;
use tipli\Model\Transactions\Entities\Transaction;
use tipli\Model\Transactions\Repositories\ImportedTransactionRepository;
use tipli\Model\Transactions\Repositories\TransactionRepository;
use tipli\Model\Utm\Entities\Utm;
use tipli\Shops\Queries\TransactionsQuery;

class TransactionFacade
{
	/** @var TransactionManager */
	private $transactionManager;

	/** @var TransactionRepository */
	private $transactionRepository;

	/** @var CommentManager */
	private $commentManager;

	/** @var Context */
	private $context;

	/** @var ImportedTransactionManager */
	private $importedTransactionManager;

	/** @var ImportedTransactionRepository */
	private $importedTransactionRepository;

	public function __construct(
		TransactionManager $transactionManager,
		TransactionRepository $transactionRepository,
		CommentManager $commentManager,
		Context $context,
		ImportedTransactionManager $importedTransactionManager,
		ImportedTransactionRepository $importedTransactionRepository,
		private SqlQueryScheduler $sqlQueryScheduler
	) {
		$this->transactionRepository = $transactionRepository;
		$this->transactionManager = $transactionManager;
		$this->commentManager = $commentManager;
		$this->context = $context;
		$this->importedTransactionManager = $importedTransactionManager;
		$this->importedTransactionRepository = $importedTransactionRepository;
	}

	public function confirm(Transaction $transaction, $commissionAmount = null, $currency = null)
	{
		return $this->transactionManager->confirmTransaction($transaction, $commissionAmount, $currency);
	}

	public function createTransactionsQuery(User $user = null)
	{
		$transactionsQuery = new TransactionsQuery();

		if ($user) {
			$transactionsQuery->withUser($user);
		}

		return $transactionsQuery;
	}

	public function fetch(TransactionsQuery $transactionsQuery)
	{
		return $this->transactionRepository->fetch($transactionsQuery);
	}

	public function cancel(Transaction $transaction)
	{
		return $this->transactionManager->cancelTransaction($transaction);
	}

	public function createEmailVerificationBonusTransaction(User $user, string $name): Transaction
	{
		return $this->transactionManager->createEmailVerificationBonusTransaction($user, $name);
	}

	public function createRefundBonusTransaction(User $user, Shop $shop = null, $name, $amount, $confirmationTreshold)
	{
		return $this->transactionManager->createRefundBonusTransaction($user, $shop, $name, $amount, $confirmationTreshold);
	}

	public function createDealBonusTransaction(User $user, Shop $shop, $amount, $confirmationTreshold)
	{
		return $this->transactionManager->createDealBonusTransaction($user, $shop, $amount, $confirmationTreshold);
	}

	public function createMarketingBonusTransaction(User $user, $name, $amount)
	{
		return $this->transactionManager->createMarketingBonusTransaction($user, $name, $amount);
	}

	public function createEntireBonusTransaction(User $user)
	{
		return $this->transactionManager->createEntireBonusTransaction($user);
	}

	public function createMoneyRewardBonusTransaction(User $user, $name, $amount, ?float $confirmationTreshold = null)
	{
		return $this->transactionManager->createMoneyRewardBonusTransaction($user, $name, $amount, $confirmationTreshold);
	}

	public function createCampaignBonusTransaction(User $user, $name, $amount, $confirmationTreshold = null)
	{
		return $this->transactionManager->createCampaignBonusTransaction($user, $name, $amount, $confirmationTreshold);
	}

	public function createLuckyShopBonusTransaction(User $user, $name, $amount, string $uniqueId): Transaction
	{
		return $this->transactionManager->createLuckyShopBonusTransaction($user, $name, $amount, $uniqueId);
	}

	public function createRewardBonusTransaction(User $user, $name, $amount)
	{
		return $this->transactionManager->createRewardBonusTransaction($user, $name, $amount);
	}

	public function createPartnerOrganizationBonusTransaction(User $user)
	{
		return $this->transactionManager->createPartnerOrganizationBonusTransaction($user);
	}

	public function createAddonBonusTransaction(User $user, Utm $utm = null, string $name, string $currency, float $amount, float $confirmationTreshold = null)
	{
		return $this->transactionManager->createAddonBonusTransaction($user, $utm, $name, $currency, $amount, $confirmationTreshold);
	}

	public function createOrReturnCommissionTransaction($user, Shop $shop, $transactionId, $currency, $commissionAmount, $orderAmount = 0, $registeredAt = null, \DateTime $importedAt = null, ?PartnerSystem $partnerSystem = null, bool $billable = true)
	{
		return $this->transactionManager->createOrReturnCommissionTransaction($user, $shop, $transactionId, $currency, $commissionAmount, $orderAmount, $registeredAt, $importedAt, $partnerSystem, $billable);
	}

	public function createCustomPayoutTransaction($user, float $amount, float $bonusAmount = 0)
	{
		return $this->transactionManager->createCustomPayoutTransaction($user, $amount, $bonusAmount);
	}

	public function getBalance(User $user, \DateTime $fromDate = null, $types = [])
	{
		return $this->transactionManager->getBalance($user, $fromDate, $types);
	}

	public function getRegisteredBalance(User $user, array $types = [])
	{
		return $this->transactionManager->getRegisteredBalance($user, $types);
	}

	public function getRegisteredCommissionBalance(User $user)
	{
		return $this->transactionManager->getRegisteredCommissionBalance($user);
	}

	public function getCommissionBalance(User $user)
	{
		return $this->transactionManager->getCommissionBalance($user);
	}

	public function getRegisteredBonusBalance(User $user)
	{
		return $this->transactionManager->getRegisteredBonusBalance($user);
	}

	public function getConfirmedBalance(User $user, \DateTime $fromDate = null, $types = [])
	{
		return $this->transactionManager->getConfirmedBalance($user, $fromDate, $types);
	}

	public function getConfirmedCommissionBalance(User $user)
	{
		return $this->transactionManager->getConfirmedCommissionBalance($user);
	}

	public function getConfirmedBonusBalance(User $user)
	{
		return $this->transactionManager->getConfirmedBonusBalance($user);
	}

	public function getLifetimeBalance(User $user)
	{
		return $this->transactionManager->getLifetimeBalance($user);
	}

	public function getLifetimeConfirmedBalance(User $user)
	{
		return $this->transactionManager->getLifetimeConfirmedBalance($user);
	}

	public function getTransactions()
	{
		return $this->transactionRepository->getTransactions();
	}

	public function getTransactionsByUser(User $user)
	{
		return $this->transactionRepository->getTransactionsByUser($user);
	}

	public function find($id)
	{
		return $this->transactionRepository->find($id);
	}

	public function findByTransactionId($transactionId, PartnerSystem $partnerSystem)
	{
		return $this->transactionRepository->findByTransactionId($transactionId, $partnerSystem);
	}

	public function findById(int $id, PartnerSystem $partnerSystem)
	{
		return $this->transactionRepository->findById($id, $partnerSystem);
	}

	public function findUnconfirmedTransactions(PartnerSystem $partnerSystem, \DateTime $from, \DateTime $to)
	{
		return $this->transactionRepository->findUnconfirmedTransactions($partnerSystem, $from, $to);
	}

	public function findFilledProfileBonus(User $user)
	{
		return $this->transactionRepository->findFilledProfileBonus($user);
	}

	public function findShopsWhereUserHasTransaction(User $user)
	{
		return $this->transactionRepository->findShopsWhereUserHasTransaction($user);
	}

	public function confirmPreparedForConfirmTransactions(User $user)
	{
		$this->transactionManager->confirmPreparedForConfirmTransactions($user);
	}

	public function recomputeShare(Transaction $transaction, $shareCoefficient)
	{
		return $this->transactionManager->recomputeShare($transaction, $shareCoefficient);
	}

	public function setTransactionRecommendationBonusTreshold(Transaction $transaction, $recommendationBonusTreshold)
	{
		return $this->transactionManager->setTransactionRecommendationBonusTreshold($transaction, $recommendationBonusTreshold);
	}

	public function setTransactionConfirmationTreshold(Transaction $transaction, $confirmationTreshold)
	{
		$this->transactionManager->setTransactionConfirmationTreshold($transaction, $confirmationTreshold);
	}

	public function unConfirmTransaction(Transaction $transaction)
	{
		return $this->transactionManager->unConfirmTransaction($transaction);
	}

	public function findAddonBonus(User $user)
	{
		return $this->transactionRepository->findAddonBonus($user);
	}

	public function createComment(Transaction $transaction, User $user, $text)
	{
		return $this->commentManager->createComment($transaction, $user, $text);
	}

	public function fixTransaction(ImportedTransaction $importedTransaction, Transaction $transaction)
	{
		if (!$transaction->getUser() || !$transaction->getShop()) {
			return;
		}

		$oldUserCommissionAmount = $transaction->getUserCommissionAmount();

		if (!$this->context->query('SELECT transaction_id from transaction_fix where transaction_id = ?', $transaction->getId())->fetch()) {
			if (!$transaction->isPaid()) {
				$this->transactionManager->fixTransaction(
					$transaction,
					$importedTransaction->getCurrency(),
					$importedTransaction->getCommissionAmount(),
					$importedTransaction->getOrderAmount()
				);
			}

//            $user = $transaction->getUser();
//            $shop = $transaction->getShop();
//			$baseCurrency = ($user instanceof User) ? $user->getLocalization()->getCurrency() : $shop->getLocalization()->getCurrency();
//			$commissionAmount = $this->currencyFacade->convert($importedTransaction->getCommissionAmount(), $importedTransaction->getCurrency(), $baseCurrency);
//			$newUserCommissionAmount = $transaction->getShareCoefficient() * $commissionAmount;
//			$orderAmount = $this->currencyFacade->convert($importedTransaction->getOrderAmount(), $importedTransaction->getCurrency(), $baseCurrency);

			$this->sqlQueryScheduler->createFixTransaction(
				$transaction->getId(),
				$oldUserCommissionAmount,
				$transaction->getUserCommissionAmount(),
				$transaction->isPaid(),
				$transaction->getCurrency()
			);
		}
	}

	public function updateCommissionTransaction(Transaction $transaction, $currency, $originalCommissionAmount, $orderAmount)
	{
		$this->transactionManager->updateCommissionTransaction($transaction, $currency, $originalCommissionAmount, $orderAmount);
	}

	public function assignUserToTransaction(Transaction $transaction, User $user): void
	{
		$this->transactionManager->assignUserToTransaction($transaction, $user);
	}

	public function saveTransaction(Transaction $transaction)
	{
		$this->transactionManager->saveTransaction($transaction);
	}

	public function findEhubActivationTransactions(\DateTime $fromDate)
	{
		return $this->transactionRepository->findEhubActivationTransactions($fromDate);
	}

	public function setTransactionBonusAmount(Transaction $transaction, $bonusAmount)
	{
		$this->transactionManager->setTransactionBonusAmount($transaction, $bonusAmount);
	}

	public function findRecommendationBonus(User $relatedRecommendedUser)
	{
		return $this->transactionRepository->findRecommendationBonus($relatedRecommendedUser);
	}

	public function getConfirmedLifetimeCommissionBalance(User $user)
	{
		return $this->transactionManager->getConfirmedLifetimeCommissionBalance($user);
	}

	public function findCommissionTransactions(\DateTime $from, \DateTime $to, $confirmedOnly = false, $withUserOnly = true, $exceptCancelled = true, User $user = null, Shop $shop = null)
	{
		return $this->transactionRepository->findCommissionTransactions($from, $to, $confirmedOnly, $withUserOnly, $exceptCancelled, $user, $shop);
	}

	public function findUnconfirmedCommissionTransactionByUser(User $user, \DateTime $from, \DateTime $to)
	{
		return $this->transactionRepository->findUnconfirmedCommissionTransactionByUser($user, $from, $to);
	}

	public function findUnconfirmedCommissionOrRefundTransactionsByUser(User $user, \DateTime $from, \DateTime $to)
	{
		return $this->transactionRepository->findUnconfirmedCommissionOrRefundTransactionsByUser($user, $from, $to);
	}

	public function findCanceledCommissionTransactionsByUser(User $user, ?\DateTime $from = null)
	{
		return $this->transactionRepository->findCanceledCommissionByUser($user, $from);
	}

	public function findCountOfBonusRefundTransactions(User $user, \DateTime $from, \DateTime $to): int
	{
		return $this->transactionRepository->findCountOfBonusRefundTransactions($user, $from, $to);
	}

	public function findCountOfTransactionByUserInShop(User $user, Shop $shop, \DateTime $from, \DateTime $to): int
	{
		return $this->transactionRepository->findCountOfTransactionByUserInShop($user, $shop, $from, $to);
	}

	public function findCountOfUserCommissionTransactions(User $user): int
	{
		return $this->transactionRepository->findCountOfUserCommissionTransactions($user);
	}

	public function findCountOfCommissionTransactionsByShopIds(array $shopIds, User $user): int
	{
		return $this->transactionRepository->findCountOfCommissionTransactionsByShopIds($shopIds, $user);
	}

	public function logCjLockTransaction(int $partnerSystemId, string $transactionId, \DateTime $lockedDate)
	{
		$this->sqlQueryScheduler->logCjLockTransaction($partnerSystemId, $transactionId, $lockedDate);
	}

	public function getUserBonusRefundTransactionsWithoutRefund(User $user)
	{
		return $this->transactionRepository->getUserBonusRefundTransactionsWithoutRefund($user);
	}

	public function findByUniqueIdentifier(string $uniqueIdentifier): ?ImportedTransactionEntity
	{
		return $this->importedTransactionRepository->findByUniqueIdentifier($uniqueIdentifier);
	}

	public function findImportedTransactions(int $limit, int $offset = 0)
	{
		return $this->importedTransactionRepository->findImportedTransactions($limit, $offset);
	}

	public function findDuplicityByUniqueIdentifier(string $uniqueIdentifier, int $withoutId)
	{
		return $this->importedTransactionRepository->findDuplicityByUniqueIdentifier($uniqueIdentifier, $withoutId);
	}

	public function saveImportedTransaction(ImportedTransactionEntity $importedTransaction)
	{
		$this->importedTransactionManager->saveImportedTransaction($importedTransaction);
	}

	public function removeImportedTransaction(ImportedTransactionEntity $importedTransaction)
	{
		$this->importedTransactionManager->removeImportedTransaction($importedTransaction);
	}

	public function createImportedTransaction(?Transaction $transaction, ?PartnerSystem $partnerSystem, string $uniqueIdentifier, string $transactionId, $commissionAmount, $orderAmount, string $currency, string $status, \DateTime $registeredAt, ?\DateTime $confirmedAt, ?string $shopDomain, ?string $channel, ?string $userId, ?string $partnerSystemKey): ImportedTransactionEntity
	{
		return $this->importedTransactionManager->createImportedTransaction(
			$transaction,
			$partnerSystem,
			$uniqueIdentifier,
			$transactionId,
			$commissionAmount,
			$orderAmount,
			$currency,
			$status,
			$registeredAt,
			$confirmedAt,
			$shopDomain,
			$channel,
			$userId,
			$partnerSystemKey
		);
	}

	public function createImportedTransactions(array $importedTransactions)
	{
		$this->importedTransactionManager->createMultipleImportedTransactions($importedTransactions);
	}

	public function expireTransaction(Transaction $transaction): void
	{
		$this->transactionManager->expireTransaction($transaction);
	}

	public function findTransactionsToExpire($limit = 1000)
	{
		return $this->transactionRepository->findTransactionsToExpire($limit);
	}

	public function getNonProfitTransactionsAmountForUser(User $user): float
	{
		return $this->transactionRepository->getNonProfitTransactionsAmountForUser($user);
	}

	public function findEmailVerificationBonusTransaction(User $user): ?Transaction
	{
		return $this->transactionRepository->findEmailVerificationBonusTransaction($user);
	}

	public function findTransactionsByShopForUser(Shop $shop, User $user)
	{
		return $this->transactionRepository->findTransactionsByShopForUser($shop, $user);
	}

	public function findSazkaTransaction(User $user)
	{
		return $this->transactionRepository->findSazkaTransaction($user);
	}
}
