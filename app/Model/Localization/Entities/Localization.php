<?php

namespace tipli\Model\Localization\Entities;

use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="tipli\Model\Localization\Repositories\LocalizationRepository")
 * @ORM\Table(name="tipli_localization_localization")
 */
class Localization
{
	public const LOCALE_CZECH = 'cs';
	public const LOCALE_SLOVAK = 'sk';
	public const LOCALE_POLISH = 'pl';
	public const LOCALE_ROMANIAN = 'ro';
	public const LOCALE_ENGLISH = 'en';
	public const LOCALE_HUNGARIAN = 'hu';
	public const LOCALE_SLOVENIA = 'si';
	public const LOCALE_CROATIA = 'hr';
//	public const LOCALE_SERBIA = 'rs';
	public const LOCALE_BULGARIA = 'bg';

	private const TIPLI_BASE_URL = 'https://www.tipli.';
	private const TIPLINO_BASE_URL = 'https://www.tiplino.';

	private const DATE_FORMAT = [
		6 => [
			'd.m.Y H:i' => 'Y.m.d H:i',
			'd.m.Y' => 'Y.m.d',
			'j. n.' => 'Y.m.d.',
			'j. n. Y' => 'm.d.',
		],
	];

	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 */
	private $id;

	/**
	 * @ORM\Column(type="string")
	 */
	private $name;

	/**
	 * @ORM\Column(type="string", unique=true)
	 */
	private $locale;

	/**
	 * @ORM\Column(type="string", length=3)
	 */
	private $currency;

  /**
   * @var array
   */
	private $bonusConfirmationTresholds = [
		self::LOCALE_CZECH => 100,
		self::LOCALE_SLOVAK => 4,
		self::LOCALE_POLISH => 20,
		self::LOCALE_ROMANIAN => 20,
		self::LOCALE_HUNGARIAN => 1400,
		self::LOCALE_SLOVENIA => 4, // @todo
		self::LOCALE_CROATIA => 4, // @todo
//		self::LOCALE_SERBIA => 1000, // @todo
		self::LOCALE_BULGARIA => 4, // @todo
	];

	public function __construct($name, $locale, $currency)
	{
		$this->name = $name;
		$this->locale = $locale;
		$this->currency = $currency;
	}

  /**
   * @return mixed
   */
	public function getId()
	{
		return $this->id;
	}

  /**
   * @return mixed
   */
	public function getName()
	{
		return $this->name;
	}

	/**
	 * @return string
	 */
	public function getLocale()
	{
		return $this->locale;
	}

	/**
	 * @return string
	 */
	public function getCurrency()
	{
		return $this->currency;
	}

	public function getDomain()
	{
		if ($this->isCzech()) {
			return 'cz';
		}

		if ($this->isEnglish()) {
			return 'com';
		}

		return $this->getLocale();
	}

	public function getBaseUrl()
	{
		if ($this->isHungarian()) {
			return self::TIPLINO_BASE_URL . $this->getDomain();
		}

		return self::TIPLI_BASE_URL . $this->getDomain();
	}

	public function getDomainName(): string
	{
		if ($this->isHungarian()) {
			return 'Tiplino.' . $this->getDomain();
		}

		return 'Tipli.' . $this->getDomain();
	}

	public function getBaseDomain(): string
	{
		return str_replace('https://www.', '', $this->getBaseUrl());
	}

  /**
	 * @return boolean
	 */
	public function isCzech()
	{
		return $this->locale === self::LOCALE_CZECH;
	}

	/**
	 * @return boolean
	 */
	public function isSlovak()
	{
		return $this->locale === self::LOCALE_SLOVAK;
	}

	/**
	 * @return boolean
	 */
	public function isPolish()
	{
		return $this->locale === self::LOCALE_POLISH;
	}

	public function isRomanian()
	{
		return $this->locale === self::LOCALE_ROMANIAN;
	}

	public function isEnglish()
	{
		return $this->locale === self::LOCALE_ENGLISH;
	}

	public function isHungarian()
	{
		return $this->locale === self::LOCALE_HUNGARIAN;
	}

	public function isSlovenian()
	{
		return $this->locale === self::LOCALE_SLOVENIA;
	}

	public function isCroatian()
	{
		return $this->locale === self::LOCALE_CROATIA;
	}

//	public function isSerbian()
//	{
//		return $this->locale === self::LOCALE_SERBIA;
//	}

	public function isBulgarian()
	{
		return $this->locale === self::LOCALE_BULGARIA;
	}

	public function getDefaultBonusConfirmationTreshold()
	{
		return $this->bonusConfirmationTresholds[$this->getLocale()];
	}

	public function getCountryNameInCzech()
	{
		$names = [
			'cs' => 'Czechia',
			'sk' => 'Slovakia',
			'pl' => 'Poland',
			'ro' => 'Romania',
			'en' => 'English',
			'hu' => 'Hungary',
			'si' => 'Slovenia',
			'hr' => 'Croatia',
//			'rs' => 'Serbia',
			'bg' => 'Bulgaria',
		];

		return $names[$this->getLocale()];
	}

	public function isLiteVersion()
	{
		if (isset($_COOKIE['disable_lite_checking']) && $_COOKIE['disable_lite_checking'] === 'TV1WFGYxsKdOzY7A4') {
			return false;
		}

//        return $this->isRomanian() || $this->isEnglish();
		return $this->isEnglish();
	}

	public function getDateFormat(string $format)
	{
		if (isset(self::DATE_FORMAT[$this->id][$format])) {
			return self::DATE_FORMAT[$this->id][$format];
		}

		return $format;
	}

	public static function getLocales(): array
	{
		return [
			self::LOCALE_CZECH,
			self::LOCALE_SLOVAK,
			self::LOCALE_POLISH,
			self::LOCALE_ROMANIAN,
			self::LOCALE_HUNGARIAN,
			self::LOCALE_SLOVENIA,
			self::LOCALE_CROATIA,
			self::LOCALE_BULGARIA,
		];
	}

	public function getLocaleCode(string $countryCode): string
	{
		$locales = [
			'cz' => 'cs-CZ',
			'sk' => 'sk-SK',
			'pl' => 'pl-PL',
			'ro' => 'ro-RO',
			'hu' => 'hu-HU',
			'bg' => 'bg-BG',
			'hr' => 'hr-HR',
			'si' => 'sl-SI',
		];

		$key = strtolower($countryCode);

		return $locales[$key] ?? 'cs-CZ';
	}

	public function hasPercentagesWithoutSpace(): bool
	{
		return $this->isBulgarian();
	}

	public function getGoogleLocale(): string
	{
		$googleLocales = [
			self::LOCALE_CZECH => 'cs',
			self::LOCALE_SLOVAK => 'sk',
			self::LOCALE_POLISH => 'pl',
			self::LOCALE_ROMANIAN => 'ro',
			self::LOCALE_HUNGARIAN => 'hu',
			self::LOCALE_SLOVENIA => 'sl',
			self::LOCALE_CROATIA => 'hr',
			self::LOCALE_BULGARIA => 'bg',
		];

		return $googleLocales[$this->getLocale()];
	}

	public function getAppleUrl(): string
	{
		if ($this->isHungarian()) {
			return 'https://apps.apple.com/hu/app/tiplino/id1579873267';
		} else {
			$locale = $this->getLocale();

			if ($locale === 'cs') {
				$locale = 'cz';
			}

			return 'https://apps.apple.com/' . $locale . '/app/tipli/id1492288796';
		}
	}

	public function getAndroidUrl(): string
	{
		if ($this->isHungarian()) {
			return 'https://play.google.com/store/apps/details?id=cz.tiplino.hu.android.app&hl=hu';
		} else {
			return 'https://play.google.com/store/apps/details?id=cz.tipli.android.app&hl=' . $this->getLocale();
		}
	}

	public function getChromeAddonUrl()
	{
		if ($this->isCzech()) {
			return "https://chrome.google.com/webstore/detail/tipli-do-prohl%C3%AD%C5%BEe%C4%8De/dbnfnbehhjknomdbfhcobpgpphnlnikp";
		} elseif ($this->isSlovak()) {
			return "https://chrome.google.com/webstore/detail/tipli-do-prehliada%C4%8Da/mpijoellhiljjmeeloljbehhhjkpijpb";
		} elseif ($this->isPolish()) {
			return "https://chrome.google.com/webstore/detail/wtyczka-tipli/ejocgomhimjbhmpbjphkikodfplbemjb";
		} elseif ($this->isRomanian()) {
			return "https://chrome.google.com/webstore/detail/tipli-%C3%AEn-browser/ijdolajanhahfboifbajbgiofkmgkljn";
		} elseif ($this->isHungarian()) {
			return "https://chrome.google.com/webstore/detail/tiplino-a-b%C3%B6ng%C3%A9sz%C5%91be/opicconnaaeilbjfnebkobanjobmnife";
		} elseif ($this->isBulgarian()) {
			return "https://chromewebstore.google.com/detail/%D0%B4%D0%BE%D0%B1%D0%B0%D0%B2%D0%B5%D0%BD-%D0%B2-%D0%B1%D1%80%D0%B0%D1%83%D0%B7%D1%8A%D1%80%D0%B0-%D0%B2%D0%B8-tip/okjkmgpcolfjdkceogdmblgcijkgcjli";
		} elseif ($this->isCroatian()) {
			return "https://chromewebstore.google.com/detail/tipli-na-va%C5%A1-preglednik/bjkanngacobjmdfgpdegpgdnmpedajag";
		} elseif ($this->isSlovenian()) {
			return "https://chromewebstore.google.com/detail/tipli-v-brskalniku/cennkggkkhlnecdpchjnhangkgpgioee";
		}
		return null;
	}

	public function getFirefoxAddonUrl()
	{
		if ($this->isCzech()) {
			return "https://addons.mozilla.org/cs/firefox/addon/tipli-do-prohl%C3%AD%C5%BEe%C4%8De/";
		} elseif ($this->isSlovak()) {
			return "https://addons.mozilla.org/sk/firefox/addon/tipli-do-prehliada%C4%8Da/";
		} elseif ($this->isPolish()) {
			return "https://addons.mozilla.org/pl/firefox/addon/wtyczka-tipli/";
		} elseif ($this->isRomanian()) {
			return "https://addons.mozilla.org/ro/firefox/addon/tipli-în-browser/";
		} elseif ($this->isHungarian()) {
			return "https://addons.mozilla.org/cs/firefox/addon/tiplino-a-b%C3%B6ng%C3%A9sz%C5%91be/";
		} elseif ($this->isCroatian()) {
			return "https://addons.mozilla.org/hr/firefox/addon/tipli-do-prohl%C3%AD%C5%BEe%C4%8De/";
		}
		return null;
	}

	public function getSafariAddonUrl()
	{
		if ($this->isCzech()) {
			return "https://apps.apple.com/app/apple-store/id6477296916";
		}
		return null;
	}
}
