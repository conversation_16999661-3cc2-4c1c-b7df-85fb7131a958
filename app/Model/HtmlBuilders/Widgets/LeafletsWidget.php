<?php

namespace tipli\Model\HtmlBuilders\Widgets;

use Nette\Localization\Translator;
use Nette\Application\LinkGenerator;
use tipli\Model\Images\ImageFilter;
use tipli\Model\Leaflets\Entities\Leaflet;
use tipli\Model\Leaflets\LeafletFacade;
use tipli\Model\Shops\ShopFacade;

class LeafletsWidget extends Widget
{
	/** @var ShopFacade */
	private $shopFacade;

	/** @var ImageFilter */
	private $imageFilter;

	/** @var Translator */
	private $translator;

	/** @var LinkGenerator */
	private $linkGenerator;

	/** @var LeafletFacade */
	private $leafletFacade;

	public function __construct(ShopFacade $shopFacade, ImageFilter $imageFilter, Translator $translator, LinkGenerator $linkGenerator, LeafletFacade $leafletFacade)
	{
		parent::__construct();

		$this->shopFacade = $shopFacade;
		$this->imageFilter = $imageFilter;
		$this->translator = $translator;
		$this->linkGenerator = $linkGenerator;
		$this->leafletFacade = $leafletFacade;
	}

	public function render()
	{
		$shopIds = $this->node->attributes()['shopIds'];
		$shops = [];
		foreach (explode(',', $shopIds) as $shopId) {
			$shops[] = $this->shopFacade->find($shopId);
		}

		$leafletsQuery = $this->leafletFacade->createLeafletsQuery();
		$leafletsQuery->withShops($shops)
			->onlyValid()
			->sortByTopShops()
			->newestFirst();

		$leaflets = $this->leafletFacade->fetch($leafletsQuery)->applyPaging(0, 4);

		$code = null;
		$code .= '<div class="leaflet-list__wrapper">';

		foreach ($leaflets as $leaflet) {
			$code .= $this->getLeafletItem($leaflet);
		}

//        $code .= $this->getNextLeaflets();
		$code .= '</div>';

		return $code;
	}

	private function getLeafletItem(Leaflet $leaflet): string
	{
		$imageFilter = $this->imageFilter;
		$shop = $leaflet->getShop();

		return sprintf(
			'<a href="%s" class="leaflet-list__item">
            <span class="leaflet-list__image"><img src="%s" alt="%s"></span>
            <span class="leaflet-list__content">
                <span class="leaflet-list__item-logo"><span class="leaflet-list__item-logo-image"><img src="%s" alt="%s"></span></span>
                <span class="leaflet-list__item-title">%s</span>
                <span class="leaflet-list__item-small"><strong>%s</strong> %s</span>
            </span>
        </a>',
			$this->linkGenerator->link('NewFront:Leaflets:Leaflet:leaflet', ['leaflet' => $leaflet]),
			$imageFilter->__invoke($leaflet->getFirstLeafletPage()->getDownloadUrl(), 190, 190, 'exact'),
			$leaflet->getTitle(),
			$imageFilter->__invoke($shop->getLogo(), 200),
			$shop->getName(),
			$leaflet->getTitle(),
			$this->translator->translate('front.leaflet.valid'),
			$leaflet->getValidTill()->format('d.m.Y')
		);
	}


//    private function getNextLeaflets(Shop $shop) {
//	    return sprintf('<a href="%s" class="sale-compact-list__more-link">%s</a>',
//            $this->linkGenerator->link('Front:Shops:Sale:sales', [$shop]),
//            $this->translator->translate('front.homepage.default.topSales.btnNextSales')
//        );
//    }
}

interface ILeafletsWidgetFactory
{
	/**
	 * @return LeafletsWidget
	 */
	public function create();
}
