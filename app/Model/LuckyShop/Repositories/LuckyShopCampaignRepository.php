<?php

namespace tipli\Model\LuckyShop\Repositories;

use Doctrine\ORM\QueryBuilder;
use tipli\Model\Doctrine\BaseRepository;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\LuckyShop\Entities\LuckyShopCampaign;

class LuckyShopCampaignRepository extends BaseRepository
{
	public function getLuckyShopCampaigns(): QueryBuilder
	{
		return $this->createQueryBuilder('lsc');
	}

	public function findLuckyShopCampaignByName(Localization $localization, string $name): ?LuckyShopCampaign
	{
		return $this->getLuckyShopCampaigns()
			->andWhere('lsc.name = :name')
			->setParameter('name', $name)
			->andWhere('lsc.localization = :localization')
			->setParameter('localization', $localization)
			->getQuery()
			->getOneOrNullResult();
	}

	public function findLuckyShopCampaignToProcess()
	{
		return $this->getLuckyShopCampaigns()
			->where('lsc.processAt <= :now')
			->andWhere('lsc.startProcessAt IS NULL')
			->setParameter('now', new \DateTime())
			->setMaxResults(1)
			->getQuery()
			->getOneOrNullResult();
	}
}
