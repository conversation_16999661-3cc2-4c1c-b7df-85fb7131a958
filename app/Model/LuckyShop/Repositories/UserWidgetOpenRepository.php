<?php

namespace tipli\Model\LuckyShop\Repositories;

use Doctrine\ORM\QueryBuilder;
use tipli\Model\Account\Entities\User;
use tipli\Model\Doctrine\BaseRepository;
use tipli\Model\LuckyShop\Entities\UserWidgetOpen;
use tipli\Model\LuckyShop\Entities\WidgetType;

class UserWidgetOpenRepository extends BaseRepository
{
	public function getUserWidgetOpens(): QueryBuilder
	{
		return $this->createQueryBuilder('uwo')
			->innerJoin('uwo.widgetType', 'wt')
			->addSelect('wt');
	}

	public function findUserWidgetOpensByUser(User $user): array
	{
		return $this->getUserWidgetOpens()
			->andWhere('uwo.user = :user')
			->setParameter('user', $user)
			->addOrderBy('uwo.createdAt', 'DESC')
			->getQuery()
			->getResult();
	}

	public function findUserWidgetOpensByUserAndType(User $user, WidgetType $widgetType): array
	{
		return $this->getUserWidgetOpens()
			->andWhere('uwo.user = :user')
			->setParameter('user', $user)
			->andWhere('uwo.widgetType = :widgetType')
			->setParameter('widgetType', $widgetType)
			->addOrderBy('uwo.createdAt', 'DESC')
			->getQuery()
			->getResult();
	}
}
