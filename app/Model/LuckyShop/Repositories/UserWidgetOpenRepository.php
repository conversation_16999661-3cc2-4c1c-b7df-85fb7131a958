<?php

namespace tipli\Model\LuckyShop\Repositories;

use tipli\Model\Doctrine\BaseRepository;
use tipli\Model\LuckyShop\Entities\UserWidgetOpen;

class UserWidgetOpenRepository extends BaseRepository
{
	public function saveUserWidgetOpen(UserWidgetOpen $userWidgetOpen): UserWidgetOpen
	{
		$this->getEntityManager()->persist($userWidgetOpen);
		$this->getEntityManager()->flush($userWidgetOpen);

		return $userWidgetOpen;
	}
}
