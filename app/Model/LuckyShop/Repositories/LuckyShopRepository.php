<?php

namespace tipli\Model\LuckyShop\Repositories;

use Doctrine\ORM\QueryBuilder;
use tipli\Model\Doctrine\BaseRepository;
use tipli\Model\LuckyShop\Entities\LuckyShop;
use tipli\Model\LuckyShop\Entities\LuckyShopCampaign;

class LuckyShopRepository extends BaseRepository
{
	public function getLuckyShops(?LuckyShopCampaign $luckyShopCampaign = null): QueryBuilder
	{
		$qb = $this->createQueryBuilder('ls');

		if ($luckyShopCampaign) {
			$qb->andWhere('ls.luckyShopCampaign = :luckyShopCampaign')
				->setParameter('luckyShopCampaign', $luckyShopCampaign);
		}

		return $qb;
	}

	public function findOrReturnNull(int $id): ?LuckyShop
	{
		return $this->getLuckyShops()
			->andWhere('ls.id = :id')
			->setParameter('id', $id)
			->getQuery()
			->getOneOrNullResult()
		;
	}

	public function findLuckyShopByCampaign(LuckyShopCampaign $luckyShopCampaign, ?LuckyShop $exceptLuckyShop): ?LuckyShop
	{
		$qb = $this->getLuckyShops()
			->andWhere('ls.luckyShopCampaign = :luckyShopCampaign')
			->setParameter('luckyShopCampaign', $luckyShopCampaign)
			->orderBy('ls.createdAt', 'DESC')
		;

		if ($exceptLuckyShop) {
			$qb->andWhere('ls.id != :exceptLuckyShop')
				->setParameter('exceptLuckyShop', $exceptLuckyShop);
		}

		return $qb->getQuery()
			->setMaxResults(1)
			->getOneOrNullResult()
		;
	}

	public function findLuckyShopToProcessRewards(): ?LuckyShop
	{
		return $this->getLuckyShops()
			->andWhere('ls.rewardsProcessedAt IS NULL')
			->andWhere('ls.userRewardRequestsCloseAt <= :now')
			->setParameter('now', new \DateTime())
			->setMaxResults(1)
			->getQuery()
			->getOneOrNullResult();
	}

	public function findCurrentLuckyShopByCampaign(LuckyShopCampaign $luckyShopCampaign): ?LuckyShop
	{
		return $this->getLuckyShops()
			->andWhere('ls.luckyShopCampaign = :luckyShopCampaign')
			->setParameter('luckyShopCampaign', $luckyShopCampaign)
			->orderBy('ls.createdAt', 'DESC')
			->andWhere('ls.validSince <= :now')
			->setParameter('now', new \DateTime())
			->setMaxResults(1)
			->getQuery()
			->getOneOrNullResult();
	}

	public function findCountOfAllLuckyShops(LuckyShopCampaign $luckyShopCampaign)
	{
		return $this->getLuckyShops()
			->select('COUNT(ls.id)')
			->andWhere('ls.luckyShopCampaign = :luckyShopCampaign')
			->setParameter('luckyShopCampaign', $luckyShopCampaign)
			->andWhere('ls.userRewardRequestsCloseAt <= :now')
			->setParameter('now', new \DateTime())
			->andWhere('ls.countOfUsersWithCheck > 0')
			->getQuery()
			->getSingleScalarResult();
	}

	public function findCountOfWinnerByCampaign(LuckyShopCampaign $luckyShopCampaign)
	{
		return $this->getLuckyShops()
			->select('SUM(ls.countOfUsersWithCheck)')
			->andWhere('ls.luckyShopCampaign = :luckyShopCampaign')
			->setParameter('luckyShopCampaign', $luckyShopCampaign)
			->andWhere('ls.userRewardRequestsCloseAt <= :now')
			->setParameter('now', new \DateTime())
			->getQuery()
			->getSingleScalarResult();
	}
}
