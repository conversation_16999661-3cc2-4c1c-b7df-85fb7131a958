<?php

namespace tipli\Model\LuckyShop\Repositories;

use Doctrine\ORM\QueryBuilder;
use tipli\Model\Doctrine\BaseRepository;
use tipli\Model\Localization\Entities\Localization;

class WidgetTypeRepository extends BaseRepository
{
	public function getWidgetTypes(): QueryBuilder
	{
		return $this->createQueryBuilder('wt');
	}

	public function findAllWidgetTypes(Localization $localization): array
	{
		return $this->getWidgetTypes()
			->andWhere('wt.localization = :localization')
			->setParameter('localization', $localization)
			->addOrderBy('wt.priority', 'DESC')
			->getQuery()
			->getResult()
			;
	}

	public function findActiveWidgetTypes(Localization $localization)
	{
		return $this->getWidgetTypes()
			->andWhere('wt.active = :active')
			->setParameter('active', true)
			->andWhere('wt.localization = :localization')
			->setParameter('localization', $localization)
			->addOrderBy('wt.priority', 'DESC')
			->getQuery()
			->getResult()
		;
	}
}
