<?php

namespace tipli\Model\LuckyShop\Repositories;

use Doctrine\ORM\QueryBuilder;
use tipli\Model\Account\Entities\User;
use tipli\Model\Doctrine\BaseRepository;
use tipli\Model\LuckyShop\Entities\UserWidget;
use tipli\Model\LuckyShop\Entities\WidgetType;

class UserWidgetRepository extends BaseRepository
{
	public function getUserWidgets(): QueryBuilder
	{
		return $this->createQueryBuilder('uw')
			->innerJoin('uw.widgetType', 'wt')
			->addSelect('wt')
		;
	}

	public function findUserWidgets(User $user, \DateTime $validSince, \DateTime $validTill)
	{
		return $this->getUserWidgets()
			->andWhere('uw.user = :user')
			->setParameter('user', $user)
			->andWhere('uw.validSince <= :validSince')
			->setParameter('validSince', $validSince)
			->andWhere('uw.validTill >= :validTill')
			->setParameter('validTill', $validTill)
			->getQuery()
			->getResult()
		;
	}

	public function findUserWidget(User $user, WidgetType $widgetType, \DateTime $validSince, \DateTime $validTill): ?UserWidget
	{
		return $this->getUserWidgets()
			->andWhere('uw.user = :user')
			->setParameter('user', $user)
			->andWhere('uw.widgetType = :widgetType')
			->setParameter('widgetType', $widgetType)
			->andWhere('uw.validSince <= :validTill')
			->setParameter('validTill', $validTill)
			->andWhere('uw.validTill >= :validSince')
			->setParameter('validSince', $validSince)
			->setMaxResults(1)
			->getQuery()
			->getOneOrNullResult()
		;
	}
}
