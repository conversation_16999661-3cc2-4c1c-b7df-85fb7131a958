<?php

namespace tipli\Model\LuckyShop;

use tipli\Model\Account\Entities\User;
use tipli\Model\LuckyShop\Entities\WidgetType;
use tipli\Model\LuckyShop\Repositories\UserWidgetRepository;

class WidgetCooldownChecker
{
	public function __construct(private UserWidgetRepository $userWidgetRepository)
	{
	}

	public function hasCooldown(User $user, WidgetType $widgetType, \DateTime $validSince): bool
	{
		$cooldownDays = $widgetType->getCooldown();
		if ($cooldownDays === null || $cooldownDays <= 1) {
			return false;
		}

		$daysToSubtract = $cooldownDays - 1;
		$cooldownWindowStart = (clone $validSince)->modify("-{$daysToSubtract} days");

		return $this->userWidgetRepository->findUserWidget($user, $widgetType, $cooldownWindowStart, $validSince) !== null;
	}
}
