<?php

namespace tipli\Model\LuckyShop;

use tipli\Model\Account\Entities\User;
use tipli\Model\Doctrine\EntityManager;
use tipli\Model\LuckyShop\Entities\UserLuckyShop;
use tipli\Model\LuckyShop\Events\DefaultUserLuckyShopCreatedEvent;
use tipli\Model\Refunds\Entities\Refund;
use tipli\Model\Shops\Entities\Shop;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface as EventDispatcher;
use tipli\Model\Transactions\Entities\Transaction;

class UserLuckyShopManager
{
	public function __construct(private EntityManager $em, private EventDispatcher $eventDispatcher)
	{
	}

	public function createUserLuckyShop(
		User $user,
		string $source,
		\DateTime $validSince,
		\DateTime $validTill,
		?Shop $shop = null,
		?\DateTime $originalValidSince = null,
		?User $createdBy = null,
		?string $note = null,
		?Transaction $transaction = null,
		?Refund $refund = null
	): UserLuckyShop {
		$userLuckyShop = new UserLuckyShop(
			$user,
			$source,
			$validSince,
			$validTill,
			$originalValidSince,
			$shop,
			$createdBy,
			$note,
			$transaction,
			$refund
		);

		if ($source === UserLuckyShop::SOURCE_DEFAULT) {
			$this->eventDispatcher->dispatch(
				new DefaultUserLuckyShopCreatedEvent($user)
			);
		}

		return $this->saveUserLuckyShop($userLuckyShop);
	}

	public function saveUserLuckyShop(UserLuckyShop $userLuckyShop): UserLuckyShop
	{
		$this->em->persist($userLuckyShop);
		$this->em->flush();

		return $userLuckyShop;
	}
}
