<?php

namespace tipli\Model\LuckyShop;

use tipli\Model\Account\Entities\User;
use tipli\Model\Account\UserFacade;
use tipli\Model\Doctrine\EntityManager;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\LuckyShop\Entities\LuckyShop;
use tipli\Model\LuckyShop\Entities\UserLuckyShop;
use tipli\Model\LuckyShop\Entities\UserLuckyShopCheck;
use tipli\Model\LuckyShop\Queries\LuckyShopsQuery;
use tipli\Model\LuckyShop\Repositories\LuckyShopCampaignRepository;
use tipli\Model\LuckyShop\Repositories\LuckyShopRepository;
use tipli\Model\LuckyShop\Entities\LuckyShopCampaign;
use tipli\Model\LuckyShop\Repositories\UserLuckyShopCheckRepository;
use tipli\Model\LuckyShop\Repositories\UserLuckyShopRepository;
use tipli\Model\Refunds\Entities\Refund;
use tipli\Model\Refunds\RefundFacade;
use tipli\Model\Shops\Entities\Shop;
use Nette\Database\Context;
use tipli\Model\Transactions\Entities\Transaction;
use tipli\Model\Transactions\TransactionFacade;

class LuckyShopFacade
{
	public function __construct(
		private LuckyShopCampaignRepository $luckyShopCampaignRepository,
		private LuckyShopCampaignManager $luckyShopCampaignManager,
		private LuckyShopRepository $luckyShopRepository,
		private LuckyShopManager $luckyShopManager,
		private UserLuckyShopRepository $userLuckyShopRepository,
		private UserLuckyShopManager $userLuckyShopManager,
		private UserLuckyShopCheckManager $userLuckyShopCheckManager,
		private UserLuckyShopCheckRepository $userLuckyShopCheckRepository,
		private UserFacade $userFacade,
		private TransactionFacade $transactionFacade,
		private RefundFacade $refundFacade,
		private Context $context,
		private EntityManager $entityManager
	) {
	}

	public function findLuckyShop(int $id): LuckyShop
	{
		return $this->luckyShopRepository->find($id);
	}

	public function findOrReturnNull(int $id): ?LuckyShop
	{
		return $this->luckyShopRepository->findOrReturnNull($id);
	}

	public function findLuckyShopCampaignToProcess()
	{
		return $this->luckyShopCampaignRepository->findLuckyShopCampaignToProcess();
	}

	public function saveLuckyShopCampaign(LuckyShopCampaign $luckyShopCampaign): LuckyShopCampaign
	{
		return $this->luckyShopCampaignManager->saveLuckyShopCampaign($luckyShopCampaign);
	}

	private function getValidShopsForCampaign(LuckyShopCampaign $luckyShopCampaign): array
	{
		$userLuckyShops = $this->userLuckyShopRepository->findUserLuckyShops(
			$luckyShopCampaign->getLocalization(),
			new \DateTime(),
			$luckyShopCampaign->getProcessedAt()
		);

		return array_values(array_map(
			static fn ($userLuckyShop) => $userLuckyShop->getShop(),
			array_filter($userLuckyShops, static fn ($userLuckyShop) => $userLuckyShop->getShop()->isCashbackAllowed() &&
				in_array($userLuckyShop->getShop()->getId(), Shop::LUCKY_SHOP_IDS[$luckyShopCampaign->getLocalization()->getId()], true))
		));
	}

	public function resolveLuckyShopForCampaign(LuckyShopCampaign $luckyShopCampaign): ?Shop
	{
		$validShops = $this->getValidShopsForCampaign($luckyShopCampaign);

		if (!empty($validShops)) {
			$shopIds = array_map(static fn ($shop) => $shop->getId(), $validShops);

			return $validShops[array_rand($shopIds)];
		}

		return null;
	}

	public function createLuckyShopForCampaign(LuckyShopCampaign $luckyShopCampaign, ?Shop $shop = null): LuckyShop
	{
		$luckyShopCampaign->startProcess();
		$this->luckyShopCampaignManager->saveLuckyShopCampaign($luckyShopCampaign);

		$validSince = (clone $luckyShopCampaign->getLastLuckyShopValidSince())->modify($luckyShopCampaign->getProcessTimeShift());

		$userRewardRequestsCloseAt = (clone $validSince)->modify($luckyShopCampaign->getUserRewardRequestsTimeShift());

		$luckyShopCampaign->process();
		$luckyShopCampaign->setLastLuckyShopValidSince($validSince);

		$validShops = $this->getValidShopsForCampaign($luckyShopCampaign);

		if (empty($validShops)) {
			throw new \RuntimeException('No valid shops found for the campaign.');
		}

		if ($shop) {
			$luckyShop = $shop;
		} else {
			$luckyShop = $this->resolveLuckyShopForCampaign($luckyShopCampaign);
		}

		$countOfUsers = count($this->findUsersWithLuckyShop($luckyShop, $luckyShopCampaign->getProcessedAt()));

		$estimatedCountOfUsers = $this->luckyShopManager->findEstimatedCountOfUsers(
			$luckyShop->getId(),
			$validSince
		);

		$newLuckyShop = $this->luckyShopManager->createLuckyShop(
			$luckyShopCampaign,
			$luckyShop,
			$countOfUsers,
			$userRewardRequestsCloseAt,
			$validSince,
			$estimatedCountOfUsers
		);

		$this->saveLuckyShopCampaign($luckyShopCampaign);

		return $newLuckyShop;
	}

	public function findLuckyShopCampaignByName(Localization $localization, string $name)
	{
		return $this->luckyShopCampaignRepository->findLuckyShopCampaignByName($localization, $name);
	}

	public function findCurrentLuckyShopByCampaign(LuckyShopCampaign $luckyShopCampaign): ?LuckyShop
	{
		return $this->luckyShopRepository->findCurrentLuckyShopByCampaign($luckyShopCampaign);
	}

	public function findLuckyShopByCampaign(LuckyShopCampaign $luckyShopCampaign, ?LuckyShop $exceptLuckyShop): ?LuckyShop
	{
		return $this->luckyShopRepository->findLuckyShopByCampaign($luckyShopCampaign, $exceptLuckyShop);
	}

	public function findCountOfAllWinChecksForLuckyShop(LuckyShop $luckyShop)
	{
		return $this->userLuckyShopCheckRepository->findCountOfAllWinChecksForLuckyShop($luckyShop);
	}

	public function findCountOfAllLuckyShops(LuckyShopCampaign $luckyShopCampaign)
	{
		return $this->luckyShopRepository->findCountOfAllLuckyShops($luckyShopCampaign);
	}

	public function findUserLuckyShopsForCampaign(User $user, LuckyShopCampaign $luckyShopCampaign)
	{
		return $this->userLuckyShopRepository->findUserLuckyShopsForCampaign($user, $luckyShopCampaign);
	}

	public function findUserLuckyShop(int $id): UserLuckyShop
	{
		return $this->userLuckyShopRepository->find($id);
	}

	public function findDefaultUserLuckyShopForUser(User $user): ?UserLuckyShop
	{
		return $this->userLuckyShopRepository->findDefaultUserLuckyShopForUser($user);
	}

	public function findValidUserLuckyShopBySource(User $user, string $source): ?UserLuckyShop
	{
		return $this->userLuckyShopRepository->findUserLuckyShopBySource($user, $source, true);
	}

	public function findValidUserLuckyShopByShop(User $user, Shop $shop, \DateTime $luckyShopProcessedAt): ?UserLuckyShop
	{
		return $this->userLuckyShopRepository->findValidUserLuckyShopByShop($user, $shop, $luckyShopProcessedAt);
	}

	public function findValidUserLuckyShops(User $user, bool $onlyWithoutShop = false, bool $onlyWithShop = false, ?string $source = null)
	{
		return $this->userLuckyShopRepository->findValidUserLuckyShops($user, $onlyWithoutShop, $onlyWithShop, $source);
	}

	public function findAllUserLuckyShops(User $user)
	{
		return $this->userLuckyShopRepository->findAllUserLuckyShops($user);
	}

	public function createUserLuckyShop(
		User $user,
		string $source,
		\DateTime $validSince,
		\DateTime $validTill,
		?Shop $shop = null,
		?\DateTime $originalValidSince = null,
		?Transaction $transaction = null,
		?Refund $refund = null
	): UserLuckyShop {
		return $this->userLuckyShopManager->createUserLuckyShop($user, $source, $validSince, $validTill, $shop, $originalValidSince, transaction: $transaction, refund: $refund);
	}

	public function saveUserLuckyShop(UserLuckyShop $userLuckyShop): UserLuckyShop
	{
		return $this->userLuckyShopManager->saveUserLuckyShop($userLuckyShop);
	}

	public function updateUserLuckyShop(UserLuckyShop $userLuckyShop, Shop $newShop): void
	{
		// $userLuckyShop = $this->findValidUserLuckyShopBySource($user, $source);

		if ($userLuckyShop->getShop() === $newShop) {
			return;
		}

		$userLuckyShopValidTill = clone $userLuckyShop->getValidTill();
		$userLuckyShop->expire();

		$this->createUserLuckyShop(
			$userLuckyShop->getUser(),
			$userLuckyShop->getSource(),
			new \DateTime(),
			$userLuckyShopValidTill,
			$newShop,
			$userLuckyShop->getOriginalValidSince()
		);

		$this->userLuckyShopManager->saveUserLuckyShop($userLuckyShop);
	}

	public function createOrUpdateUserLuckyShop(User $user, string $source, ?int $transactionId = null, ?int $refundId = null): ?UserLuckyShop
	{
		if ($source === UserLuckyShop::SOURCE_DEFAULT) {
			return null;
		}

		if ($user->isHost()) {
			return null;
		}

		if ($user->hasUserLuckyShopData() === false) {
			return null;
		}

		$originalValidSince = null;
		$transaction = null;
		$refund = null;

		$today = (new \DateTime())->setTime(0, 0);
		if ($source === UserLuckyShop::SOURCE_TRANSACTION && $user->getUserLuckyShopData()->getLastUserLuckyShopFromTransactionCreatedAt() >= $today) {
			return null;
		}

		if ($source === UserLuckyShop::SOURCE_REFUND) {
			$refund = $this->refundFacade->find($refundId);

			$originalValidSince = $refund->getPurchasedAt();
			$hasRefundUserLuckyShop = $this->findUserLuckyShopByOriginalValidSince($user, UserLuckyShop::SOURCE_REFUND, $refund->getPurchasedAt());

			if ($hasRefundUserLuckyShop) {
				return null;
			}

			if ($refund->getRefundTransaction() !== null) {
				$transaction = $refund->getRefundTransaction();
			}
		}

		$validUserLuckyShopBySource = $this->findValidUserLuckyShopBySource($user, $source);

		if ($source === UserLuckyShop::SOURCE_MISSED_REWARD) {
			$luckyShopCampaign = $this->findLuckyShopCampaignByName($user->getLocalization(), 'default');
			$currentLuckyShop = $this->findCurrentLuckyShopByCampaign($luckyShopCampaign);

			if ($previousLuckyShop = $this->findLuckyShopByCampaign($luckyShopCampaign, $currentLuckyShop)) {
				$userLuckyShopForPreviousLuckyShop = $this->findUserLuckyShopForLuckyShop($user, $previousLuckyShop);
				$previousUserLuckyShopCheck = $this->findUserLuckyShopCheck($user, $previousLuckyShop);

				$missedRewardUserLuckyShop = $this->findLastUserLuckyShopBySource($user, UserLuckyShop::SOURCE_MISSED_REWARD);

				if ($missedRewardUserLuckyShop && $missedRewardUserLuckyShop->getOriginalValidSince() >= $previousLuckyShop->getValidSince()) {
					return null;
				}

				if ($previousUserLuckyShopCheck !== null) {
					return null;
				}

				if ($userLuckyShopForPreviousLuckyShop === null) {
					return null;
				}

				return $this->createUserLuckyShop($user, $source, $previousLuckyShop->getValidSince(), new \DateTime('+ 7 days'));
			}

			return null;
		}

		if ($validUserLuckyShopBySource && $validUserLuckyShopBySource->isValidTillExtensionAllowed()) {
			$currentValidTill = clone $validUserLuckyShopBySource->getValidTill();
			$validUserLuckyShopBySource->setValidTill(new \DateTime(UserLuckyShop::getSourceDuration($source)));

			if ($validUserLuckyShopBySource->getValidTill() > $currentValidTill) {
				return $this->saveUserLuckyShop($validUserLuckyShopBySource);
			}

			return null;
		}

		$validTill = new \DateTime(UserLuckyShop::getSourceDuration($source));

		if ($transactionId !== null) {
			$transaction = $this->transactionFacade->find($transactionId);
		}

		$luckyShop = $this->createUserLuckyShop($user, $source, new \DateTime(), $validTill, originalValidSince: $originalValidSince, transaction: $transaction, refund: $refund);

		if ($source === UserLuckyShop::SOURCE_TRANSACTION) {
			$user->getUserLuckyShopData()->setLastUserLuckyShopFromTransactionCreatedAt(new \DateTime());
			$this->userFacade->saveUser($user);
		}

		return $luckyShop;
	}

	public function resolveUserLuckyShopCheckStreak(User $user): int
	{
		$streak = 1;
		$lastCheckDate = $user->getLuckyShopLastCheckAt();

		if ($lastCheckDate) {
			$lastCheckDate->setTime(0, 0);
			$yesterday = (new \DateTime('-1 day'))->setTime(0, 0);

			$streak = ($lastCheckDate < $yesterday) ? 1 : $user->getLuckyShopCheckStreak() + 1;
		}

		$user->setLuckyShopCheckStreak($streak);
		$this->userFacade->saveUser($user);

		return $streak;
	}

	public function createUserLuckyShopCheck(User $user, LuckyShop $luckyShop, LuckyShopCampaign $luckyShopCampaign, ?UserLuckyShop $userLuckyShop): UserLuckyShopCheck
	{
		return $this->userLuckyShopCheckManager->createUserLuckyShopCheck($user, $luckyShop, $luckyShopCampaign, $userLuckyShop);
	}

	public function findUserLuckyShopCheck(User $user, LuckyShop $luckyShop): ?UserLuckyShopCheck
	{
		return $this->userLuckyShopCheckRepository->findUserLuckyShopCheck($user, $luckyShop);
	}

	public function findUsersWithLuckyShop(Shop $shop, \DateTime $validAt)
	{
		return $this->userLuckyShopRepository->findUsersLuckyShopsForShop(
			$shop,
			$validAt
		);
	}

	public function findUsersWithActiveUserLuckyShop(Localization $localization)
	{
		return $this->userFacade->findUsersWithActiveUserLuckyShop($localization);
	}

	public function findUsersChecksWithLuckyShop(LuckyShop $luckyShop)
	{
		return $this->userLuckyShopCheckRepository->findUsersChecksWithLuckyShop($luckyShop);
	}

	public function resetTestData(User $user)
	{
		$this->context->query('
			DELETE FROM tipli_lucky_shop_user_lucky_shop_checks where user_id = ?
		', $user->getId());

		$this->context->query('
			DELETE FROM tipli_lucky_shop_user_lucky_shops where user_id = ?
		', $user->getId());

		$this->context->query('
			DELETE FROM tipli_account_user_lucky_shop_data where user_id = ?
		', $user->getId());
	}

	public function saveUserLuckyShopCheck(UserLuckyShopCheck $userLuckyShopCheck): void
	{
		$this->userLuckyShopCheckManager->saveUserLuckyShopCheck($userLuckyShopCheck);
	}

	public function findLuckyShopToProcessRewards(): ?LuckyShop
	{
		return $this->luckyShopRepository->findLuckyShopToProcessRewards();
	}

	public function saveLuckyShop(LuckyShop $luckyShop): void
	{
		$this->luckyShopManager->saveLuckyShop($luckyShop);
	}

	public function processLuckyShopRewards(LuckyShop $luckyShop): void
	{
		$userLuckyShopChecksToCreateBonusTransaction = $this->userLuckyShopCheckRepository->findChecksToCreateBonusTransaction($luckyShop);

		$luckyShop->setCountOfUsersWithCheck($this->findCountOfAllWinChecksForLuckyShop($luckyShop));

		/** @var UserLuckyShopCheck $userLuckyShopCheck */
		foreach ($userLuckyShopChecksToCreateBonusTransaction as $userLuckyShopCheck) {
			$this->userLuckyShopCheckManager->createTransaction($luckyShop, $userLuckyShopCheck);
		}

		$luckyShop->processRewards();
		$this->saveLuckyShop($luckyShop);
	}

	public function increaseLuckyShopCountOfUsersWithCheck(LuckyShop $luckyShop): void
	{
		$luckyShop->increaseCountOfUsersWithCheck();
		$this->saveLuckyShop($luckyShop);
	}

	public function findUserLuckyShopForLuckyShop(User $user, LuckyShop $luckyShop)
	{
		return $this->userLuckyShopRepository->findUserLuckyShopForLuckyShop($user, $luckyShop);
	}

	public function findUserLuckyShopsForLuckyShop(User $user, LuckyShop $luckyShop)
	{
		return $this->userLuckyShopRepository->findUserLuckyShopsForLuckyShop($user, $luckyShop);
	}

	public function createLuckyShopsQuery(): LuckyShopsQuery
	{
		return new LuckyShopsQuery();
	}

	public function fetchLuckyShops(LuckyShopsQuery $query)
	{
		return $this->luckyShopRepository->fetch($query);
	}

	public function findValidUserLuckyShopsWithoutShop(User $user)
	{
		return $this->userLuckyShopRepository->findValidUserLuckyShopsWithoutShop($user);
	}

	public function findValidUserLuckyShopWithoutShop(User $user, ?string $source = null): ?UserLuckyShop
	{
		return $this->userLuckyShopRepository->findValidUserLuckyShopWithoutShop($user, $source);
	}

	public function findUserLuckyShopCheckCountAndSumWithWin(User $user)
	{
		return $this->userLuckyShopCheckRepository->findUserLuckyShopCheckCountAndSumWithWin($user);
	}

	public function getLuckyShopsByCampaign(LuckyShopCampaign $luckyShopCampaign): \Doctrine\ORM\QueryBuilder
	{
		return $this->luckyShopRepository->getLuckyShops($luckyShopCampaign);
	}

	public function findLuckyShopCampaigns(): array
	{
		return $this->luckyShopCampaignRepository->findAll();
	}

	public function findUserLuckyShopBySourceAndDate(User $user, string $source, \DateTime $date): ?UserLuckyShop
	{
		return $this->userLuckyShopRepository->findUserLuckyShopBySourceAndDate($user, $source, $date);
	}

	public function findUserLuckyShopByOriginalValidSince(User $user, string $source, \DateTime $originalValidSince): ?UserLuckyShop
	{
		return $this->userLuckyShopRepository->findUserLuckyShopByOriginalValidSince($user, $source, $originalValidSince);
	}

	public function findLastUserLuckyShopBySource(User $user, string $source): ?UserLuckyShop
	{
		return $this->userLuckyShopRepository->findLastUserLuckyShopBySource($user, $source);
	}

	public function createUserLuckyShopByAdmin(User $user, string $source, int $validDays, User $createdBy, ?string $note = null): UserLuckyShop
	{
		$validSince = new \DateTime();
		$validTill = (clone $validSince)->modify('+' . $validDays . ' days');

		return $this->userLuckyShopManager->createUserLuckyShop(
			$user,
			$source,
			$validSince,
			$validTill,
			null,
			null,
			$createdBy,
			$note
		);
	}

	public function getUserLuckyShopRepository(): UserLuckyShopRepository
	{
		return $this->userLuckyShopRepository;
	}

	public function findUserLuckyShopChecks(User $user): array
	{
		return $this->userLuckyShopCheckRepository->findUserLuckyShopChecks($user);
	}

	public function findLatestUserLuckyShopBySource(User $user, string $source): ?UserLuckyShop
	{
		return $this->userLuckyShopRepository->findLatestUserLuckyShopBySource($user, $source);
	}

	public function findCountOfWinnerByCampaign(LuckyShopCampaign $luckyShopCampaign)
	{
		return $this->luckyShopRepository->findCountOfWinnerByCampaign($luckyShopCampaign);
	}
}
