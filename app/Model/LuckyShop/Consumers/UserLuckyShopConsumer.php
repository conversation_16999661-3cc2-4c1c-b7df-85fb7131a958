<?php

namespace tipli\Model\LuckyShop\Consumers;

use Bunny\Message;
use Contributte\RabbitMQ\Consumer\IConsumer;
use tipli\Model\Account\UserFacade;
use tipli\Model\LuckyShop\LuckyShopFacade;
use tipli\Model\LuckyShop\Messages\UserLuckyShopMessage;
use tipli\Model\RabbitMq\BaseConsumer;

class UserLuckyShopConsumer extends BaseConsumer
{
	public function __construct(private LuckyShopFacade $luckyShopFacade, private UserFacade $userFacade)
	{
	}

	public function consume(Message $data): int
	{
		$message = UserLuckyShopMessage::fromJson($data->content);

		$this->luckyShopFacade->createOrUpdateUserLuckyShop($this->userFacade->find($message->getUserId()), $message->getSource(), $message->getTransactionId(), $message->getRefundId());

		return IConsumer::MESSAGE_ACK;
	}
}
