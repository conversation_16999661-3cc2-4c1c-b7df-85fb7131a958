<?php

namespace tipli\Model\LuckyShop\Consumers;

use Bunny\Message;
use Contributte\RabbitMQ\Consumer\IConsumer;
use Nette\Utils\Json;
use tipli\Model\Account\UserFacade;
use tipli\Model\LuckyShop\LuckyShopEmailProvider;
use tipli\Model\LuckyShop\LuckyShopFacade;
use tipli\Model\LuckyShop\Messages\UserLuckyShopEmailMessage;
use tipli\Model\RabbitMq\BaseConsumer;

class LuckyShopEmailConsumer extends BaseConsumer
{
	public function __construct(private LuckyShopEmailProvider $luckyShopEmailProvider, private LuckyShopFacade $luckyShopFacade, private UserFacade $userFacade)
	{
	}

	public function consume(Message $data): int
	{
		$json = Json::decode($data->content);

		$userLuckyShopEmailMessage = UserLuckyShopEmailMessage::fromJson($json);

		$luckyShop = $this->luckyShopFacade->findOrReturnNull($userLuckyShopEmailMessage->getLuckyShopId());

		if ($luckyShop === null) {
			return IConsumer::MESSAGE_ACK;
		}

		$user = $this->userFacade->find($userLuckyShopEmailMessage->getUserId());

		if ($json->eventType === LuckyShopEmailProvider::EVENT_TYPE_LUCKY_SHOP_CREATED) {
			$this->luckyShopEmailProvider->scheduleLuckyShopCreatedEmail($luckyShop, $user);
		} elseif ($json->eventType === LuckyShopEmailProvider::EVENT_TYPE_LUCKY_SHOP_REWARD_CREATED) {
			$this->luckyShopEmailProvider->scheduleLuckyShopRewardCreatedEmail($luckyShop, $user);
		} elseif ($json->eventType === LuckyShopEmailProvider::EVENT_TYPE_LUCKY_SHOP_WIN_CONFIRMATION) {
			$this->luckyShopEmailProvider->scheduleLuckyShopWinConfirmationEmail($luckyShop, $user);
		}

		return IConsumer::MESSAGE_ACK;
	}
}
