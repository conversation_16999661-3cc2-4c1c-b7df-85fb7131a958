<?php

namespace tipli\Model\LuckyShop\Consumers;

use Bunny\Message;
use Contributte\RabbitMQ\Consumer\IConsumer;
use Nette\Utils\Json;
use tipli\Model\Account\UserFacade;
use tipli\Model\LuckyShop\LuckyShopFacade;
use tipli\Model\LuckyShop\LuckyShopNotificationProvider;
use tipli\Model\LuckyShop\Messages\UserLuckyShopNotificationMessage;
use tipli\Model\RabbitMq\BaseConsumer;

class LuckyShopNotificationConsumer extends BaseConsumer
{
	public function __construct(
		private UserFacade $userFacade,
		private LuckyShopFacade $luckyShopFacade,
		private LuckyShopNotificationProvider $luckyShopNotificationProvider
	) {
	}

	public function consume(Message $data): int
	{
		$json = Json::decode($data->content);

		$userLuckyShopEmailMessage = UserLuckyShopNotificationMessage::fromJson($json);

		$luckyShop = $this->luckyShopFacade->findOrReturnNull($userLuckyShopEmailMessage->getLuckyShopId());

		if ($luckyShop === null) {
			return IConsumer::MESSAGE_ACK;
		}

		$user = $this->userFacade->find($userLuckyShopEmailMessage->getUserId());

		if ($json->eventType === LuckyShopNotificationProvider::EVENT_TYPE_LUCKY_SHOP_CREATED) {
			$this->luckyShopNotificationProvider->scheduleLuckyShopCreatedNotification($luckyShop, $user);
		}

		return IConsumer::MESSAGE_ACK;
	}
}
