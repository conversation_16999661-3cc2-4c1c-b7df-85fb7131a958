<?php

namespace tipli\Model\LuckyShop;

use tipli\Model\Account\Entities\User;
use tipli\Model\LuckyShop\Entities\WidgetType;
use tipli\Model\LuckyShop\Repositories\WidgetTypeRepository;
use tipli\Model\LuckyShop\WidgetConditions\AddonWidgetCondition;
use tipli\Model\LuckyShop\WidgetConditions\MobileAppWidgetCondition;
use tipli\Model\LuckyShop\WidgetConditions\SazkaPromoWidgetCondition;
use tipli\Model\LuckyShop\WidgetConditions\StreakWidgetCondition;

class UserWidgetTestProvider
{
	public function __construct(
		private readonly WidgetTypeRepository $widgetTypeRepository,
		private readonly StreakWidgetCondition $streakWidgetCondition,
		private readonly AddonWidgetCondition $addonWidgetCondition,
		private readonly MobileAppWidgetCondition $mobileAppWidgetCondition,
		private readonly SazkaPromoWidgetCondition $sazkaPromoWidgetCondition,
		private readonly WidgetCooldownChecker $widgetCooldownChecker
	) {
	}

	/**
	 * @return array<int, UserWidgetTestResult>
	 */
	public function provideUserWidgets(User $user, \DateTime $validSince): array
	{
		$testResults = [];
		$allWidgetTypes = $this->widgetTypeRepository->findAllWidgetTypes($user->getLocalization());

		foreach ($allWidgetTypes as $widgetType) {
			$result = new UserWidgetTestResult($widgetType);

			if ($widgetType->isActive() === false) {
				$result->addReason('Widget je globálně deaktivován.');
			}

			if ($this->widgetCooldownChecker->hasCooldown($user, $widgetType, $validSince)) {
				$result->addReason(sprintf('Uživatel má aktivní cooldown (%d dní).', $widgetType->getCooldown()));
			}

			$this->checkWidgetSpecificConditions($user, $widgetType, $result);

			$testResults[] = $result;
		}

		usort($testResults, static fn ($a, $b) => $b->getWidgetType()->getPriority() <=> $a->getWidgetType()->getPriority());

		return $testResults;
	}

	private function checkWidgetSpecificConditions(User $user, WidgetType $widgetType, UserWidgetTestResult $result): void
	{
		$isAllowed = match ($widgetType->getType()) {
			WidgetType::TYPE_STREAK => $this->streakWidgetCondition->isAllowed($user, $widgetType),
			WidgetType::TYPE_ADDON => $this->addonWidgetCondition->isAllowed($user, $widgetType),
			WidgetType::TYPE_MOBILE_APP => $this->mobileAppWidgetCondition->isAllowed($user, $widgetType),
			WidgetType::TYPE_PROMO_SAZKA => $this->sazkaPromoWidgetCondition->isAllowed($user, $widgetType),
			default => true,
		};

		if ($isAllowed === false) {
			$result->addReason(sprintf('Nesplňuje podmínky zobrazení'));
		}
	}
}
