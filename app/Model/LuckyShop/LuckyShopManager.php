<?php

namespace tipli\Model\LuckyShop;

use Nette\Database\Context;
use tipli\Model\Doctrine\EntityManager;
use tipli\Model\LuckyShop\Entities\LuckyShop;
use tipli\Model\LuckyShop\Entities\LuckyShopCampaign;
use tipli\Model\LuckyShop\Events\LuckyShopCreatedEvent;
use tipli\Model\Shops\Entities\Shop;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface as EventDispatcher;

class LuckyShopManager
{
	public function __construct(private EntityManager $em, private EventDispatcher $eventDispatcher, private Context $context)
	{
	}

	public function createLuckyShop(LuckyShopCampaign $luckyShopCampaign, Shop $shop, int $countOfUsers, \DateTime $userRewardRequestsCloseAt, \DateTime $validSince, int $estimatedCountOfUsers = 0): LuckyShop
	{
		$luckyShop = new LuckyShop($luckyShopCampaign, $shop, $countOfUsers, $userRewardRequestsCloseAt, $validSince, $estimatedCountOfUsers);
		$luckyShop = $this->saveLuckyShop($luckyShop);

		$this->eventDispatcher->dispatch(
			new LuckyShopCreatedEvent($luckyShop)
		);

		return $luckyShop;
	}

	public function saveLuckyShop(LuckyShop $luckyShop): LuckyShop
	{
		$this->em->persist($luckyShop);
		$this->em->flush();

		return $luckyShop;
	}

	public function findEstimatedCountOfUsers(int $shopId, \DateTime $dateTime): int
	{
		$sql = '
        -- CTE #1: Nalezení všech uživatelů, kteří měli v den losování správně nastavený obchod
        WITH potential_winners AS (
            SELECT
                uls.user_id
            FROM
                tipli_lucky_shop_user_lucky_shops uls
            WHERE
                uls.shop_id = ? -- Parametr č. 1 (ID obchodu)
                AND ? BETWEEN DATE(uls.valid_since) AND DATE(uls.valid_till) -- Parametr č. 2 (Datum losování)
        ),

        -- CTE #2: Agregace všech typů aktivit uživatelů do jedné tabulky
        user_activity AS (
            SELECT user_id, created_at AS activity_ts FROM tipli_lucky_shop_user_lucky_shop_checks
            UNION ALL
            SELECT user_id, created_at AS activity_ts FROM tipli_lucky_shop_user_lucky_shops
            UNION ALL
            SELECT user_id, updated_at AS activity_ts FROM tipli_lucky_shop_user_lucky_shops WHERE updated_at IS NOT NULL
        )

        -- Finální SELECT: Spočítá unikátní uživatele splňující podmínku.
        SELECT
            COUNT(DISTINCT pw.user_id)
        FROM
            potential_winners pw
        WHERE
            (
                SELECT MAX(act.activity_ts)
                FROM user_activity act
                WHERE act.user_id = pw.user_id
                  AND act.activity_ts < ? -- Parametr č. 2 (Datum losování)
            ) >= DATE_SUB(?, INTERVAL 14 DAY); -- Parametr č. 2 (Datum losování)
    ';

		$count = $this->context->fetchField(
			$sql,
			$shopId,
			$dateTime,
			$dateTime,
			$dateTime
		);

		return (int) $count;
	}
}
