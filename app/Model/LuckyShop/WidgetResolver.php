<?php

namespace tipli\Model\LuckyShop;

use tipli\Model\Account\Entities\User;
use tipli\Model\LuckyShop\Entities\WidgetType;
use tipli\Model\LuckyShop\Repositories\WidgetTypeRepository;
use tipli\Model\LuckyShop\WidgetConditions\AddonWidgetCondition;
use tipli\Model\LuckyShop\WidgetConditions\MobileAppWidgetCondition;
use tipli\Model\LuckyShop\WidgetConditions\SazkaPromoWidgetCondition;
use tipli\Model\LuckyShop\WidgetConditions\StreakWidgetCondition;

class WidgetResolver
{
	public function __construct(
		private readonly WidgetTypeRepository $widgetTypeRepository,
		private readonly StreakWidgetCondition $streakWidgetCondition,
		private readonly AddonWidgetCondition $addonWidgetCondition,
		private readonly MobileAppWidgetCondition $mobileAppWidgetCondition,
		private readonly SazkaPromoWidgetCondition $sazkaPromoWidgetCondition,
		private readonly WidgetCooldownChecker $widgetCooldownChecker
	) {
	}

	/** @return WidgetType[] */
	public function resolveAllowedWidgetTypesForUser(User $user, \DateTime $validSince): array
	{
		$allowedWidgetTypes = [];
		$activeWidgetTypes = $this->widgetTypeRepository->findActiveWidgetTypes($user->getLocalization());

		foreach ($activeWidgetTypes as $widgetType) {
			if ($this->widgetCooldownChecker->hasCooldown($user, $widgetType, $validSince)) {
				continue;
			}

			if ($this->isWidgetAllowed($user, $widgetType) === false) {
				continue;
			}

			$allowedWidgetTypes[] = $widgetType;
		}

		usort($allowedWidgetTypes, static fn ($a, $b) => $b->getPriority() <=> $a->getPriority());

		return $allowedWidgetTypes;
	}

	private function isWidgetAllowed(User $user, WidgetType $widgetType): bool
	{
		if ($widgetType->getType() === WidgetType::TYPE_STREAK) {
			return $this->streakWidgetCondition->isAllowed($user, $widgetType);
		}

		if ($widgetType->getType() === WidgetType::TYPE_ADDON) {
			return $this->addonWidgetCondition->isAllowed($user, $widgetType);
		}

		if ($widgetType->getType() === WidgetType::TYPE_MOBILE_APP) {
			return $this->mobileAppWidgetCondition->isAllowed($user, $widgetType);
		}

		if ($widgetType->getType() === WidgetType::TYPE_PROMO_SAZKA) {
			return $this->sazkaPromoWidgetCondition->isAllowed($user, $widgetType);
		}

		return true;
	}
}
