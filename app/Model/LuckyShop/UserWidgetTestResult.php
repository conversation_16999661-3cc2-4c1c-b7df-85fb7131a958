<?php

namespace tipli\Model\LuckyShop;

use tipli\Model\LuckyShop\Entities\WidgetType;

class UserWidgetTestResult
{
	private array $reasons = [];

	public function __construct(
		private readonly WidgetType $widgetType
	) {
	}

	public function getWidgetType(): WidgetType
	{
		return $this->widgetType;
	}

	public function addReason(string $reason): void
	{
		$this->reasons[] = $reason;
	}

	public function getReasons(): array
	{
		return $this->reasons;
	}

	public function isAllowed(): bool
	{
		return empty($this->reasons);
	}
}
