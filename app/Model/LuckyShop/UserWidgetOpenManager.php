<?php

namespace tipli\Model\LuckyShop;

use tipli\Model\Account\Entities\User;
use tipli\Model\LuckyShop\Entities\UserWidgetOpen;
use tipli\Model\LuckyShop\Entities\WidgetType;
use tipli\Model\Doctrine\EntityManager;

class UserWidgetOpenManager
{
	public function __construct(private EntityManager $em)
	{
	}

	public function createUserWidgetOpen(User $user, WidgetType $widgetType): UserWidgetOpen
	{
		$userWidgetOpen = new UserWidgetOpen($user, $widgetType);

		$this->saveUserWidgetOpen($userWidgetOpen);

		return $userWidgetOpen;
	}

	public function saveUserWidgetOpen(UserWidgetOpen $userWidgetOpen): UserWidgetOpen
	{
		$this->em->persist($userWidgetOpen);
		$this->em->flush($userWidgetOpen);

		return $userWidgetOpen;
	}
}
