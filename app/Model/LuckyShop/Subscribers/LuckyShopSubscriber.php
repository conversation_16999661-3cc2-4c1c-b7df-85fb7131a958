<?php

namespace tipli\Model\LuckyShop\Subscribers;

use Nette\SmartObject;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use tipli\Model\Account\Entities\SendingPolicy;
use tipli\Model\Account\Entities\User;
use tipli\Model\Account\UserFacade;
use tipli\Model\LuckyShop\Entities\UserLuckyShop;
use tipli\Model\LuckyShop\Events\DefaultUserLuckyShopCreatedEvent;
use tipli\Model\LuckyShop\Events\LuckyShopCreatedEvent;
use tipli\Model\LuckyShop\Events\UserLuckyShopCheckTransactionCreated;
use tipli\Model\LuckyShop\Events\UserLuckyShopCheckEvent;
use tipli\Model\LuckyShop\LuckyShopFacade;
use tipli\Model\LuckyShop\Producers\LuckyShopEmailProducer;
use tipli\Model\LuckyShop\Producers\LuckyShopNotificationProducer;
use tipli\Model\LuckyShop\Producers\UserLuckyShopProducer;
use tipli\Model\Messages\EmailManager;
use tipli\Model\Messages\MessageFacade;
use tipli\Model\Refunds\Events\RefundApprovedEvent;
use tipli\Model\Transactions\Events\ITransactionEvent;
use tipli\Model\Transactions\Events\TransactionConfirmedEvent;
use tipli\Model\Transactions\Events\TransactionRegisteredEvent;

class LuckyShopSubscriber implements EventSubscriberInterface
{
	use SmartObject;

	public function __construct(
		private LuckyShopFacade $luckyShopFacade,
		private UserLuckyShopProducer $userLuckyShopProducer,
		private LuckyShopEmailProducer $luckyShopEmailProducer,
		private LuckyShopNotificationProducer $luckyShopNotificationProducer,
		private UserFacade $userFacade,
		private MessageFacade $messageFacade,
		private EmailManager $emailManager
	) {
	}

	public static function getSubscribedEvents(): array
	{
		return [
			DefaultUserLuckyShopCreatedEvent::class => 'afterCreateDefaultUserLuckyShop',
			UserLuckyShopCheckEvent::class => [
				['abortScheduledEmail', 40],
				['updateLuckyShopStats', 30],
				['checkStreak', 20],
				['scheduleSendLuckyShopWinConfirmationEmail', 10],
			],
			TransactionRegisteredEvent::class => 'createOrUpdateUserLuckyShopFromTransactionSource',
			TransactionConfirmedEvent::class => 'createOrUpdateUserLuckyShopFromUserRecommendationSource',
			LuckyShopCreatedEvent::class => [
				['scheduleSendLuckyShopCreatedNotification', 20],
				['scheduleSendLuckyShopCreatedEmail', 10],
			],
			UserLuckyShopCheckTransactionCreated::class => 'scheduleSendLuckyShopRewardCreatedEmail',
			RefundApprovedEvent::class => ['createOrUpdateUserLuckyShopFromRefundSource', 100],
		];
	}

	public function abortScheduledEmail(UserLuckyShopCheckEvent $userLuckyShopCheckEvent): void
	{
		$scheduledLuckyShopEmail = $this->messageFacade->findScheduledLuckyShopEmail(
			$userLuckyShopCheckEvent->getUser()
		);

		if ($scheduledLuckyShopEmail === null) {
			return;
		}

		$this->emailManager->abortEmail($scheduledLuckyShopEmail);
	}

	public function updateLuckyShopStats(UserLuckyShopCheckEvent $userLuckyShopCheckEvent): void
	{
		$userLuckyShopCheck = $userLuckyShopCheckEvent->getUserLuckyShopCheck();

		if ($userLuckyShopCheck->hasWin() === false) {
			return;
		}

		$luckyShop = $userLuckyShopCheck->getLuckyShop();

		$this->luckyShopFacade->increaseLuckyShopCountOfUsersWithCheck($luckyShop);
	}

	public function scheduleSendLuckyShopWinConfirmationEmail(UserLuckyShopCheckEvent $userLuckyShopCheckEvent): void
	{
		$userLuckyShopCheck = $userLuckyShopCheckEvent->getUserLuckyShopCheck();

		if ($userLuckyShopCheck->hasWin() === false) {
			return;
		}

		$this->luckyShopEmailProducer->scheduleSendLuckyWinConfirmationEmail($userLuckyShopCheckEvent->getUser(), $userLuckyShopCheck->getLuckyShop());
	}

	public function scheduleSendLuckyShopRewardCreatedEmail(UserLuckyShopCheckTransactionCreated $userLuckyShopCheckTransactionCreated): void
	{
		$luckyShop = $userLuckyShopCheckTransactionCreated->getLuckyShop();
		$user = $userLuckyShopCheckTransactionCreated->getUser();

		$this->luckyShopEmailProducer->scheduleSendLuckyShopRewardCreatedEmail($user, $luckyShop);
	}

	public function afterCreateDefaultUserLuckyShop(DefaultUserLuckyShopCreatedEvent $defaultUserLuckyShopCreatedEvent): void
	{
		$user = $defaultUserLuckyShopCreatedEvent->getUser();

		if ($user->hasUserLuckyShopData()) {
			return;
		}

		$user->createUserLuckyShopData();
		$this->userFacade->saveUser($user);

		if ($user->hasInstalledAddon()) {
			$this->userLuckyShopProducer->scheduleCreateOrUpdateUserLuckyShop($user, UserLuckyShop::SOURCE_ADDON);
		}
	}

	public function createOrUpdateUserLuckyShopFromUserRecommendationSource(ITransactionEvent $transactionEvent): void
	{
		$transaction = $transactionEvent->getTransaction();
		$user = $transaction->getUser();

		if ($transaction->isBonusRecommendation() === false) {
			return;
		}

		if ($user->hasUserLuckyShopData() === false) {
			return;
		}

		$this->userLuckyShopProducer->scheduleCreateOrUpdateUserLuckyShop(
			$user,
			UserLuckyShop::SOURCE_USER_RECOMMENDATION
		);
	}

	public function createOrUpdateUserLuckyShopFromRefundSource(RefundApprovedEvent $refundApprovedEvent)
	{
		$refund = $refundApprovedEvent->getRefund();

		$user = $refund->getUser();

		if ($refund->isTypeMissingCommission() === false) {
			return;
		}

		if ($user->hasUserLuckyShopData() === false) {
			return;
		}

		$this->userLuckyShopProducer->scheduleCreateOrUpdateUserLuckyShop(
			$user,
			UserLuckyShop::SOURCE_REFUND,
			null,
			$refund->getId()
		);
	}

	public function createOrUpdateUserLuckyShopFromTransactionSource(ITransactionEvent $transactionEvent): void
	{
		$transaction = $transactionEvent->getTransaction();
		$user = $transaction->getUser();

		if ($user === null) {
			return;
		}

		if ($user && $user->hasUserLuckyShopData() === false) {
			return;
		}

		if (!$transaction->isCommission()) {
			return;
		}

		$today = (new \DateTime())->setTime(0, 0);

		if ($user->getUserLuckyShopData()->getLastUserLuckyShopFromTransactionCreatedAt() >= $today) {
			return;
		}

		$this->userLuckyShopProducer->scheduleCreateOrUpdateUserLuckyShop(
			$user,
			UserLuckyShop::SOURCE_TRANSACTION,
			$transaction->getId()
		);
	}

	public function scheduleSendLuckyShopCreatedEmail(LuckyShopCreatedEvent $luckyShopCreatedEvent): void
	{
		$luckyShop = $luckyShopCreatedEvent->getLuckyShop();
		$luckyShopCampaign = $luckyShop->getLuckyShopCampaign();

		/** @var User $user */
		foreach ($this->luckyShopFacade->findUsersWithActiveUserLuckyShop($luckyShopCampaign->getLocalization()) as $user) {
			if ($user->hasAllowedSendingEmails(SendingPolicy::MESSAGE_TYPE_EMAIL, SendingPolicy::CONTENT_TYPE_LUCKY_SHOP) === false) {
				continue;
			}

			$this->luckyShopEmailProducer->scheduleSendLuckyShopCreatedEmail($user, $luckyShopCreatedEvent->getLuckyShop());
		}
	}

	public function scheduleSendLuckyShopCreatedNotification(LuckyShopCreatedEvent $luckyShopCreatedEvent): void
	{
		$luckyShop = $luckyShopCreatedEvent->getLuckyShop();
		$luckyShopCampaign = $luckyShop->getLuckyShopCampaign();

		/** @var User $user */
		foreach ($this->luckyShopFacade->findUsersWithActiveUserLuckyShop($luckyShopCampaign->getLocalization()) as $user) {
			if ($user->hasAllowedPushNotifications(SendingPolicy::MESSAGE_TYPE_PUSH_NOTIFICATIONS, SendingPolicy::CONTENT_TYPE_LUCKY_SHOP) === false) {
				continue;
			}

			$this->luckyShopNotificationProducer->scheduleSendLuckyShopCreatedNotification($user, $luckyShopCreatedEvent->getLuckyShop());
		}
	}

	public function checkStreak(UserLuckyShopCheckEvent $userLuckyShopCheckEvent): void
	{
		$user = $userLuckyShopCheckEvent->getUser();

		$streak = $this->luckyShopFacade->resolveUserLuckyShopCheckStreak($user);

		if ($streak >= 7) {
			$this->luckyShopFacade->createOrUpdateUserLuckyShop($user, UserLuckyShop::SOURCE_STREAK);
		}
	}
}
