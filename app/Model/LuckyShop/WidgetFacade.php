<?php

namespace tipli\Model\LuckyShop;

use tipli\Model\Account\Entities\User;
use tipli\Model\LuckyShop\Entities\UserWidget;
use tipli\Model\LuckyShop\Entities\WidgetType;
use tipli\Model\LuckyShop\Repositories\UserWidgetRepository;

class WidgetFacade
{
	public function __construct(
		private readonly UserWidgetManager $userWidgetManager,
		private readonly UserWidgetRepository $userWidgetRepository
	) {
	}

	public function createUserWidget(User $user, WidgetType $widget, \DateTime $validSince, \DateTime $validTill, int $priority): UserWidget
	{
		return $this->userWidgetManager->createUserWidget($user, $widget, $validSince, $validTill, $priority);
	}

	public function findUserWidget(User $user, WidgetType $widget, \DateTime $validSince, \DateTime $validTill): ?UserWidget
	{
		return $this->userWidgetRepository->findUserWidget($user, $widget, $validSince, $validTill);
	}
}
