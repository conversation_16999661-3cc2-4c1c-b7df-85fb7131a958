<?php

namespace tipli\Model\LuckyShop;

use tipli\Model\Account\Entities\User;
use tipli\Model\LuckyShop\Entities\UserWidget;
use tipli\Model\LuckyShop\Repositories\UserWidgetRepository;

class UserWidgetProvider
{
	public function __construct(
		private readonly WidgetResolver $widgetTypesForUserResolver,
		private readonly WidgetFacade $widgetFacade,
		private readonly UserWidgetRepository $userWidgetRepository,
	) {
	}

	/** @return array<int, UserWidget> */
	public function provideUserWidgets(User $user, \DateTime $validSince, \DateTime $validTill, int $limit = 5): array
	{
		$userWidgets = $this->userWidgetRepository->findUserWidgets($user, $validSince, $validTill);

		if (!empty($userWidgets) && count($userWidgets) >= $limit) {
			return $userWidgets;
		}

		$allowedWidgetTypes = $this->widgetTypesForUserResolver->resolveAllowedWidgetTypesForUser($user, $validSince);

		$widgetsTypesToCreate = array_slice($allowedWidgetTypes, 0, $limit);

		$newUserWidgets = [];

		foreach ($widgetsTypesToCreate as $widgetType) {
			$userWidget = $this->widgetFacade->createUserWidget(
				$user,
				$widgetType,
				$validSince,
				$validTill,
				$widgetType->getPriority()
			);

			$newUserWidgets[] = $userWidget;
		}

		return $newUserWidgets;
	}
}
