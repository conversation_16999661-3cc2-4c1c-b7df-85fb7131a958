<?php

namespace tipli\Model\LuckyShop\Entities;

use Doctrine\ORM\Mapping as ORM;
use tipli\Model\Account\Entities\User;

/**
 * @ORM\Entity(repositoryClass="tipli\Model\LuckyShop\Repositories\UserWidgetOpenRepository")
 * @ORM\Table(name="tipli_lucky_shop_user_widget_open")
 */
class UserWidgetOpen
{
	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 */
	private int $id;

	/**
	 * @ORM\ManyToOne(targetEntity="tipli\Model\Account\Entities\User")
	 * @ORM\JoinColumn(name="user_id", referencedColumnName="id", nullable=false)
	 */
	private User $user;

	/**
	 * @ORM\ManyToOne(targetEntity="WidgetType")
	 * @ORM\JoinColumn(name="widget_type_id", referencedColumnName="id", nullable=false)
	 */
	private WidgetType $widgetType;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private \DateTime $createdAt;

	public function __construct(User $user, WidgetType $widgetType)
	{
		$this->user = $user;
		$this->widgetType = $widgetType;
		$this->createdAt = new \DateTime();
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function getUser(): User
	{
		return $this->user;
	}

	public function getWidgetType(): WidgetType
	{
		return $this->widgetType;
	}

	public function getCreatedAt(): \DateTime
	{
		return $this->createdAt;
	}
}
