<?php

namespace tipli\Model\LuckyShop\Entities;

use tipli\Model\Shops\Entities\Shop;
use Doctrine\ORM\Mapping as ORM;

/**
 * @ORM\Entity(repositoryClass="tipli\Model\LuckyShop\Repositories\LuckyShopRepository")
 * @ORM\Table(name="tipli_lucky_shop_lucky_shops")
 */
class LuckyShop
{
	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 */
	private int $id;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Shops\Entities\Shop")
	 * @ORM\JoinColumn(name="shop_id", referencedColumnName="id", nullable=true)
	 */
	private ?Shop $shop = null;

	/**
	 * @ORM\ManyToOne(targetEntity="LuckyShopCampaign")
	 * @ORM\JoinColumn(name="lucky_shop_campaign_id", referencedColumnName="id", nullable=true)
	 */
	private LuckyShopCampaign $luckyShopCampaign;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private ?\DateTime $userRewardRequestsCloseAt;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private ?\DateTime $rewardsProcessedAt;

	/**
	 * @ORM\Column(type="integer")
	 */
	private int $countOfUsers = 0;

	/**
	 * @ORM\Column(type="integer", nullable=true)
	 */
	private ?int $estimatedCountOfUsers;

	/**
	 * @ORM\Column(type="integer")
	 */
	private int $countOfUsersWithCheck = 0;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private \DateTime $validSince;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private \DateTime $createdAt;

	public function __construct(LuckyShopCampaign $luckyShopCampaign, Shop $shop, int $countOfUsers, \DateTime $userRewardRequestsCloseAt, \DateTime $validSince, int $estimatedCountOfUsers = 0)
	{
		$this->luckyShopCampaign = $luckyShopCampaign;
		$this->shop = $shop;
		$this->validSince = $validSince;
		$this->userRewardRequestsCloseAt = $userRewardRequestsCloseAt;
		$this->countOfUsers = $countOfUsers;
		$this->estimatedCountOfUsers = $estimatedCountOfUsers;
		$this->createdAt = new \DateTime();
	}

	public function getShop(): ?Shop
	{
		return $this->shop;
	}

	public function getLuckyShopCampaign(): LuckyShopCampaign
	{
		return $this->luckyShopCampaign;
	}

	public function getUserRewardRequestsCloseAt(): ?\DateTime
	{
		return $this->userRewardRequestsCloseAt;
	}

	public function isUserRewardRequestAllowed(): bool
	{
		return $this->userRewardRequestsCloseAt > new \DateTime();
	}

	public function setCountOfUsersWithCheck(int $countOfUsersWithCheck): void
	{
		$this->countOfUsersWithCheck = $countOfUsersWithCheck;
	}

	public function setCountOfUsers(int $countOfUsers): void
	{
		$this->countOfUsers = $countOfUsers;
	}

	public function getCountOfUsers(): int
	{
		return $this->countOfUsers;
	}

	public function getCountOfUsersWithCheck(): int
	{
		return $this->countOfUsersWithCheck;
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function processRewards(): void
	{
		$this->rewardsProcessedAt = new \DateTime();
	}

	public function increaseCountOfUsersWithCheck(): void
	{
		$this->countOfUsersWithCheck++;
	}

	public function getRewardAmountPerUser(): float
	{
		return $this->luckyShopCampaign->getRewardAmountPerUser($this->countOfUsersWithCheck);
	}

	public function getCurrentRewardAmountPerUser(): float
	{
		$rewardAmount = $this->luckyShopCampaign->getTotalRewardAmount();

		if ($this->countOfUsersWithCheck) {
			return $rewardAmount / $this->countOfUsersWithCheck;
		}

		return $rewardAmount;
	}

	public function getValidSince(): \DateTime
	{
		return $this->validSince;
	}

	public function isCheckAllowed(): bool
	{
		return new \DateTime() <= $this->userRewardRequestsCloseAt;
	}
}
