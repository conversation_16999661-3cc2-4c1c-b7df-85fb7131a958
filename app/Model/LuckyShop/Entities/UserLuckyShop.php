<?php

namespace tipli\Model\LuckyShop\Entities;

use Doctrine\ORM\Mapping as ORM;
use tipli\Model\Account\Entities\User;
use tipli\Model\Localization\Entities\Localization;
use tipli\Model\Refunds\Entities\Refund;
use tipli\Model\Shops\Entities\Shop;
use tipli\Model\Transactions\Entities\Transaction;

/**
 * @ORM\Entity(repositoryClass="tipli\Model\LuckyShop\Repositories\UserLuckyShopRepository")
 * @ORM\Table(name="tipli_lucky_shop_user_lucky_shops")
 */
class UserLuckyShop
{
	public const SOURCE_DEFAULT = 'default';
	public const SOURCE_ADMIN = 'admin';
	public const SOURCE_STREAK = 'streak';
	public const SOURCE_TRANSACTION = 'transaction';
	public const SOURCE_USER_RECOMMENDATION = 'user_recommendation';
	public const SOURCE_ADDON = 'addon';
	public const SOURCE_MISSED_REWARD = 'missed_reward';
	public const SOURCE_SUPPORT = 'support';
	public const SOURCE_REFUND = 'refund';

	public const STATUS_EXPIRED = 'expired';
	public const STATUS_ACTIVE = 'active';
	public const STATUS_ACTIVE_WITHOUT_SHOP = 'active_without_shop';
	public const STATUS_DELISTED = 'delisted';

	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 */
	private int $id;

	/**
	 * @ORM\ManyToOne(targetEntity="tipli\Model\Account\Entities\User", inversedBy="userLuckyShops")
	 * @ORM\JoinColumn(name="user_id", referencedColumnName="id", nullable=false)
	 */
	private User $user;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Localization\Entities\Localization")
	 * @ORM\JoinColumn(name="localization_id", referencedColumnName="id")
	 */
	private Localization $localization;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Shops\Entities\Shop")
	 * @ORM\JoinColumn(name="shop_id", referencedColumnName="id", nullable=true)
	 */
	private ?Shop $shop;

	/**
	 * @ORM\ManyToOne(targetEntity="tipli\Model\Transactions\Entities\Transaction")
	 * @ORM\JoinColumn(name="transaction_id", referencedColumnName="id")
	 */
	private ?Transaction $transaction;

	/**
	 * @ORM\ManyToOne(targetEntity="tipli\Model\Refunds\Entities\Refund")
	 * @ORM\JoinColumn(name="refund_id", referencedColumnName="id")
	 */
	private ?Refund $refund;

	/**
	 * @ORM\Column(type="string")
	 */
	private string $source;

	/**
	 * @ORM\Column(type="string", nullable=true)
	 */
	private ?string $note = null;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private \DateTime $validSince;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private \DateTime $validTill;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private \DateTime $originalValidSince;

	/**
	 * @ORM\Column(type="datetime", nullable=true)
	 */
	private ?\DateTime $updatedAt;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private \DateTime $createdAt;

	/**
	 * @ORM\ManyToOne(targetEntity="tipli\Model\Account\Entities\User")
	 * @ORM\JoinColumn(name="created_by_user_id", referencedColumnName="id", nullable=true)
	 */
	private ?User $createdBy = null;

	public function __construct(
		User $user,
		string $source,
		\DateTime $validSince,
		\DateTime $validTill,
		?\DateTime $originalValidSince = null,
		?Shop $shop = null,
		?User $createdBy = null,
		?string $note = null,
		?Transaction $transaction = null,
		?Refund $refund = null
	) {
		$this->user = $user;
		$this->source = $source;
		$this->localization = $user->getLocalization();
		$this->validSince = $validSince;
		$this->validTill = $validTill->setTime(23, 59, 59);
		$this->originalValidSince = $originalValidSince ?: $validSince;
		$this->shop = $shop;
		$this->note = $note;
		$this->createdBy = $createdBy;
		$this->createdAt = new \DateTime();
		$this->transaction = $transaction;
		$this->refund = $refund;
	}

	public function getShop(): ?Shop
	{
		return $this->shop;
	}

	public function getValidTill(): \DateTime
	{
		return $this->validTill;
	}

	public function getValidSince(): \DateTime
	{
		return $this->validSince;
	}

	public function getSource(): string
	{
		return $this->source;
	}

	public function getUser(): User
	{
		return $this->user;
	}

	public function isValid(): bool
	{
		$now = new \DateTime();
		return $this->validSince <= $now && $this->validTill >= $now;
	}

	public function expire(): void
	{
		$this->validTill = new \DateTime('- 5 seconds');
	}

	public function isDefault(): bool
	{
		return $this->source === self::SOURCE_DEFAULT;
	}

	public function getValidTillDays()
	{
		$now = new \DateTime();
		$diff = $this->validTill->diff($now);

		return $diff->days + 1;
	}

	public function isDefaultSource(): bool
	{
		return $this->source === self::SOURCE_DEFAULT;
	}

	public function isStreakSource(): bool
	{
		return $this->source === self::SOURCE_STREAK;
	}

	public function setValidTill(\DateTime $validTill): void
	{
		$this->updatedAt = new \DateTime();
		$this->validTill = $validTill->setTime(23, 59, 59);
	}

	public static function getSources(): array
	{
		return [
			self::SOURCE_DEFAULT,
			self::SOURCE_ADMIN,
			self::SOURCE_STREAK,
			self::SOURCE_TRANSACTION,
			self::SOURCE_REFUND,
			self::SOURCE_USER_RECOMMENDATION,
			self::SOURCE_ADDON,
			self::SOURCE_MISSED_REWARD,
			self::SOURCE_SUPPORT,
		];
	}

	public static function getSourceDuration($source): string
	{
		$sourceDurations = [
			UserLuckyShop::SOURCE_STREAK => '+1 day',
			UserLuckyShop::SOURCE_ADDON => '+30 days',
			UserLuckyShop::SOURCE_TRANSACTION => '+30 days',
			UserLuckyShop::SOURCE_REFUND => '+30 days',
			UserLuckyShop::SOURCE_USER_RECOMMENDATION => '+60 days',
			UserLuckyShop::SOURCE_MISSED_REWARD => '+7 days',
		];

		if (!isset($sourceDurations[$source])) {
			throw new \InvalidArgumentException("Invalid source: $source");
		}

		return $sourceDurations[$source];
	}

	public function isValidTillExtensionAllowed(): bool
	{
		return $this->source === self::SOURCE_STREAK || $this->source === self::SOURCE_ADDON;
	}

	public function getId(): int
	{
		return $this->id;
	}

	public function getOriginalValidSince(): \DateTime
	{
		return $this->originalValidSince;
	}

	public function getStatus(): string
	{
		if ($this->isValid() === false) {
			return self::STATUS_EXPIRED;
		}

		if ($this->shop === null) {
			return self::STATUS_ACTIVE_WITHOUT_SHOP;
		}

		if ($this->shop->isCashbackAllowed() === false) {
			return self::STATUS_DELISTED;
		}

		if (in_array($this->shop->getId(), Shop::LUCKY_SHOP_IDS[$this->localization->getId()]) === false) {
			return self::STATUS_DELISTED;
		}

		return self::STATUS_ACTIVE;
	}

	public function isDelisted(): bool
	{
		return $this->getStatus() === self::STATUS_DELISTED;
	}

	public function getCreatedBy(): ?User
	{
		return $this->createdBy;
	}

	public function getCreatedAt()
	{
		return $this->createdAt;
	}

	public function getNote(): ?string
	{
		return $this->note;
	}

	public function setTransaction(Transaction $transaction): void
	{
		$this->transaction = $transaction;
	}

	public function getTransaction(): ?Transaction
	{
		return $this->transaction;
	}
}
