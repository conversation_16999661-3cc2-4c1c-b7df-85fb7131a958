<?php

namespace tipli\Model\LuckyShop\Entities;

use Doctrine\ORM\Mapping as ORM;
use tipli\Model\Account\Entities\User;

/**
 * @ORM\Entity(repositoryClass="tipli\Model\LuckyShop\Repositories\UserWidgetClickRepository")
 * @ORM\Table(name="tipli_lucky_shop_user_widget_click")
 */
class UserWidgetClick
{
	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 */
	private int $id;

	/**
	 * @ORM\ManyToOne(targetEntity="UserWidget")
	 * @ORM\JoinColumn(name="user_widget_id", referencedColumnName="id", nullable=false)
	 */
	private UserWidget $userWidget;

	/**
	 * @ORM\ManyToOne(targetEntity="WidgetType")
	 * @ORM\JoinColumn(name="widget_type_id", referencedColumnName="id", nullable=false)
	 */
	private WidgetType $widgetType;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private \DateTime $validSince;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private \DateTime $validTill;

	/**
	 * @ORM\Column(type="integer")
	 */
	private int $priority;

	/**
	 * @ORM\Column(type="integer")
	 */
	private int $countOfImpressions = 1;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private $createdAt;

	public function __construct(User $user, WidgetType $widgetType, \DateTime $validSince, \DateTime $validTill, int $priority)
	{
		$this->user = $user;
		$this->widgetType = $widgetType;
		$this->validSince = $validSince->setTime(0, 0);
		$this->validTill = $validTill;
		$this->priority = $priority;
		$this->createdAt = new \DateTime();
	}
}
