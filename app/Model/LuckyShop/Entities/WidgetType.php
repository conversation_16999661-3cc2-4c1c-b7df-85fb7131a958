<?php

namespace tipli\Model\LuckyShop\Entities;

use Doctrine\ORM\Mapping as ORM;
use tipli\Model\Localization\Entities\Localization;

/**
 * @ORM\Entity(repositoryClass="tipli\Model\LuckyShop\Repositories\WidgetTypeRepository")
 * @ORM\Table(name="tipli_lucky_shop_widget_type")
 */
class WidgetType
{
	public const TYPE_DEFAULT = 'default';
	public const TYPE_PROMO_SAZKA = 'promo_sazka';
	public const TYPE_MOBILE_APP = 'mobile_app';
	public const TYPE_ADDON = 'addon';
	public const TYPE_ADVERTISEMENT = 'advertisement';
	public const TYPE_STREAK = 'streak';
	public const TYPE_TIP = 'tip';

	/**
	 * @ORM\Id
	 * @ORM\Column(type="integer")
	 * @ORM\GeneratedValue
	 */
	private int $id;

	/**
	 * @ORM\ManyToOne(targetEntity="\tipli\Model\Localization\Entities\Localization")
	 * @ORM\JoinColumn(name="localization_id", referencedColumnName="id")
	 */
	private Localization $localization;

	/**
	 * @ORM\Column(type="string", unique=true)
	 */
	private string $type;

	/**
	 * @ORM\Column(type="string")
	 */
	private string $name;

	/**
	 * @ORM\Column(type="boolean")
	 */
	private bool $active = true;

	/**
	 * @ORM\Column(type="integer")
	 */
	private int $priority = 10;

	/**
	 * @ORM\Column(type="integer", nullable=true)
	 */
	private ?int $cooldown = null;

	/**
	 * @ORM\Column(type="datetime")
	 */
	private \DateTime $createdAt;

	public function getType(): string
	{
		return $this->type;
	}

	public function getPriority(): int
	{
		return $this->priority;
	}

	public function getCooldown(): ?int
	{
		return $this->cooldown;
	}

	public static function getCooldownByType(string $type): ?int
	{
		return match ($type) {
			self::TYPE_PROMO_SAZKA,
			self::TYPE_MOBILE_APP => 2,
			self::TYPE_ADDON,
			self::TYPE_ADVERTISEMENT => 1,
			default => null,
		};
	}

	public function getPriorityByType(string $type): int
	{
		return match ($type) {
			self::TYPE_DEFAULT, self::TYPE_ADVERTISEMENT, self::TYPE_TIP => 10,
			self::TYPE_PROMO_SAZKA, self::TYPE_ADDON => 100,
			self::TYPE_MOBILE_APP => 50,
			self::TYPE_STREAK => 10000,
			default => 30,
		};
	}

	public function isActive(): bool
	{
		return $this->active;
	}

	public function getCamelCaseType(): string
	{
		return lcfirst(str_replace(' ', '', ucwords(str_replace('_', ' ', $this->type))));
	}
}
