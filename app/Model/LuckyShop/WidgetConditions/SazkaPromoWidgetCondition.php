<?php

namespace tipli\Model\LuckyShop\WidgetConditions;

use tipli\Model\Account\Entities\User;
use tipli\Model\LuckyShop\Entities\WidgetType;
use tipli\Model\Transactions\TransactionFacade;

class SazkaPromoWidgetCondition implements WidgetConditionInterface
{
	public function __construct(
		private readonly TransactionFacade $transactionFacade
	) {
	}

	public function isAllowed(User $user, WidgetType $widgetType): bool
	{
		if ($user->isCzech() === false) {
			return false;
		}

		return $this->transactionFacade->findSazkaTransaction($user) === null;
	}
}
