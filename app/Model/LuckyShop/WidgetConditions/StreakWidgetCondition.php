<?php

namespace tipli\Model\LuckyShop\WidgetConditions;

use tipli\Model\Account\Entities\User;
use tipli\Model\LuckyShop\Entities\UserLuckyShop;
use tipli\Model\LuckyShop\Entities\WidgetType;
use tipli\Model\LuckyShop\LuckyShopFacade;

class StreakWidgetCondition implements WidgetConditionInterface
{
	public function __construct(private readonly LuckyShopFacade $luckyShopFacade)
	{
	}

	public function isAllowed(User $user, WidgetType $widgetType): bool
	{
		return count($this->luckyShopFacade->findValidUserLuckyShops($user, true, false, UserLuckyShop::SOURCE_STREAK)) !== 0;
	}
}
