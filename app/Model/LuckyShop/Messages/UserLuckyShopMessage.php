<?php

namespace tipli\Model\LuckyShop\Messages;

use Nette\Utils\Json;

class UserLuckyShopMessage
{
	public function __construct(private int $userId, private string $source, private ?int $transactionId = null, private ?int $refundId = null)
	{
	}

	public function getUserId(): int
	{
		return $this->userId;
	}

	public function getSource(): string
	{
		return $this->source;
	}

	public function getTransactionId(): ?int
	{
		return $this->transactionId;
	}

	public function getRefundId(): ?int
	{
		return $this->refundId;
	}

	public function __toString()
	{
		return Json::encode([
			'userId' => $this->userId,
			'source' => $this->source,
			'transactionId' => $this->transactionId,
			'refundId' => $this->refundId,
		]);
	}

	public static function fromJson(string $data): self
	{
		$data = Json::decode($data);
		return new self($data->userId, $data->source, $data->transactionId, $data->refundId);
	}
}
