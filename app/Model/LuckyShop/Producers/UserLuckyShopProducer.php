<?php

namespace tipli\Model\LuckyShop\Producers;

use tipli\Model\Account\Entities\User;
use tipli\Model\LuckyShop\Messages\UserLuckyShopMessage;
use tipli\Model\RabbitMq\BaseProducer;

class UserLuckyShopProducer extends BaseProducer
{
	public function scheduleCreateOrUpdateUserLuckyShop(User $user, string $source, ?int $transactionId = null, ?int $refundId = null): void
	{
		$this->producer->publish(
			new UserLuckyShopMessage($user->getId(), $source, $transactionId, $refundId)
		);
	}
}
