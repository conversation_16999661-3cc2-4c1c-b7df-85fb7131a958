<?php

namespace tipli\Commands;

use Nette\Database\Context;
use tipli\Model\Account\UserFacade;
use tipli\Model\Account\UserSegmentDataFacade;
use tipli\Model\Queues\Entities\SqlQuery;

class SynchronizeSegmentData extends Job
{
	public const USER_LIMIT = 150;

	/** @var UserFacade */
	private $userFacade;

	/** @var UserSegmentDataFacade */
	private $userSegmentDataFacade;

	/** @var Context */
	private $context;

	public function __construct(UserFacade $userFacade, UserSegmentDataFacade $userSegmentDataFacade, Context $context)
	{
		parent::__construct();

		$this->userFacade = $userFacade;
		$this->userSegmentDataFacade = $userSegmentDataFacade;
		$this->context = $context;
	}

	protected function configure()
	{
		$this->setName('tipli:synchronize-user-data:run');
	}

	public function start()
	{
		ini_set('memory_limit', '4096M');
		ini_set('max_execution_time', 600);

		$log = $this->onStart();

		$currentHour = date('G');

		$this->startUpdateLastInteraction();

		switch ($currentHour) {
			case 2:
				$this->startUpdateNumberOfPurchasesScore();
				$this->startUpdateUsersUtm();
				break;
			case 3:
				$this->startUpdateServiceUsedAt();
				$this->startUpdateNumberOfPurchases();
				break;
			case 4:
				$this->updateUsersUtmByRecommendation();
				break;
			case 6:
				$this->startUpdateLastInteractionScore();
				break;
		}

		$this->onFinish($log);
	}

	private function startUpdateNumberOfPurchases()
	{
		$usersToUpdate = $this->context->query('
			SELECT DISTINCT user_id FROM tipli_transactions_transaction
			WHERE created_at >= NOW() - INTERVAL 1 DAY
		')->fetchAll();

		$userIds = array_filter(array_map(static function ($user) {
			return $user->user_id ?? null;
		}, $usersToUpdate));

		$chunks = array_chunk($userIds, self::USER_LIMIT);

		foreach ($chunks as $chunk) {
			$this->scheduleSqlQuery(
				'
				UPDATE tipli_account_segment_data s
				INNER JOIN (
					SELECT t.user_id, COUNT(DISTINCT DATE(t.created_at), t.shop_id) AS cnt
					FROM tipli_transactions_transaction t
					WHERE t.shop_id IS NOT NULL
					  AND t.type IN (\'commission\', \'commission_erabat\', \'bonus_refund\')
					  AND t.user_id IN (?)
					GROUP BY t.user_id
				) number_of_purchases ON number_of_purchases.user_id = s.user_id
				SET s.number_of_purchases = number_of_purchases.cnt
				',
				[$chunk]
			);

			$this->scheduleSqlQuery(
				'
				  UPDATE tipli_account_segment_data d
				  INNER JOIN (
					SELECT t.user_id AS user_id, COUNT(t.id) AS cnt
					FROM tipli_transactions_transaction t
					WHERE t.type = \'commission\'
					  AND t.user_id IN (?)
					GROUP BY t.user_id
				  ) transactions ON transactions.user_id = d.user_id
				  SET d.count_of_commission_transactions = transactions.cnt
				',
				[$chunk]
			);

			$this->scheduleSqlQuery(
				'
				  UPDATE tipli_account_segment_data d
				  INNER JOIN (
					SELECT t.user_id AS user_id, COUNT(t.id) AS cnt
					FROM tipli_transactions_transaction t
					WHERE t.type = \'commission\'
					  AND t.confirmed_at IS NOT NULL
					  AND t.user_commission_amount > 0
					  AND t.user_id IN (?)
					GROUP BY t.user_id
				  ) transactions ON transactions.user_id = d.user_id
				  SET d.count_of_confirmed_commission_transactions = transactions.cnt
				',
				[$chunk]
			);

			$this->scheduleSqlQuery(
				'
				UPDATE tipli_account_segment_data d
				INNER JOIN (
					SELECT t.user_id, COUNT(t.id) AS cnt
					FROM tipli_transactions_transaction t
					WHERE t.type = \'commission\'
					  AND t.confirmed_at IS NOT NULL
					  AND t.user_commission_amount = 0
					  AND t.user_id IN (?)
					GROUP BY t.user_id
				) transactions ON transactions.user_id = d.user_id
				SET d.count_of_canceled_commission_transactions = transactions.cnt
				',
				[$chunk]
			);
		}
	}

	private function startUpdateNumberOfPurchasesScore()
	{
		$numberOfIntervals = 5;

		$result = $this->context->fetch('SELECT COUNT(id) AS usersCount FROM tipli_account_segment_data;');
		$usersCount = $result['usersCount'];

		$usersInInterval = round($usersCount / $numberOfIntervals);

		for ($i = $numberOfIntervals - 1; $i >= 0; --$i) {
			$score = $i;
			$limit = $usersInInterval;
			$offset = ($numberOfIntervals - $i - 1) * $usersInInterval;

			$this->context->query('
                UPDATE tipli_account_segment_data
                SET number_of_purchases_score = ' . $score . '
                WHERE id IN (
                  SELECT * FROM (
                    SELECT id
                    FROM tipli_account_segment_data
                    ORDER BY number_of_purchases DESC, user_id DESC
                    LIMIT ' . $offset . ', ' . $limit . '
                  ) a
                );
            ');
		}
	}

	private function startUpdateLastInteraction()
	{
		$data = $this->context->query('
			SELECT DISTINCT sd.user_id
			FROM tipli_account_segment_data sd
			INNER JOIN tipli_account_user u ON u.id = sd.user_id
			WHERE (
			    sd.last_logged_in_at > last_interaction_at
			    OR sd.last_shop_redirection_at > last_interaction_at
			    OR sd.last_web_visit_at > last_interaction_at
			    OR sd.last_mobile_app_visit_at > last_interaction_at
			    OR sd.addon_feed_downloaded_at > last_interaction_at
			    OR sd.opened_at > last_interaction_at
			    OR sd.clicked_at > last_interaction_at
			    OR u.created_at > last_interaction_at
			);
		')->fetchAll();

		$ids = [];
		foreach ($data as $row) {
			$ids[] = $row->user_id;
		}

		foreach (array_chunk($ids, 100) as $ids) {
			$this->scheduleSqlQuery('
				UPDATE tipli_account_segment_data sd
				INNER JOIN tipli_account_user u ON (u.id=sd.user_id)
				SET last_interaction_at = GREATEST(
				   COALESCE(last_logged_in_at, 0),
				   COALESCE(last_shop_redirection_at, 0),
				   COALESCE(last_web_visit_at, 0),
				   COALESCE(last_mobile_app_visit_at, 0),
				   COALESCE(addon_feed_downloaded_at, 0),
				   COALESCE(opened_at, 0),
				   COALESCE(clicked_at, 0),
				   COALESCE(u.created_at,0)
				)
				WHERE sd.user_id IN (' . implode(',', $ids) . ')');
		}
	}

	private function startUpdateLastInteractionScore()
	{
		$numberOfIntervals = 5;

		$result = $this->context->fetch('SELECT COUNT(id) AS usersCount FROM tipli_account_segment_data;');
		$usersCount = $result['usersCount'];

		$usersInInterval = round($usersCount / $numberOfIntervals);

		for ($i = $numberOfIntervals; $i >= 0; --$i) {
			$score = $i;
			$limit = $usersInInterval;
			$offset = ($numberOfIntervals - $i) * $usersInInterval;

			$this->context->query('
                UPDATE tipli_account_segment_data
                SET last_interaction_score = ' . $score . '
                WHERE id IN (
                  SELECT * FROM (
                    SELECT id
                    FROM tipli_account_segment_data
                    ORDER BY last_interaction_at DESC, user_id DESC
                    LIMIT ' . $offset . ', ' . $limit . '
                  ) a
                );
            ');
		}
	}

	private function startUpdateServiceUsedAt()
	{
		// leaflets
		$this->scheduleSqlQuery('
                UPDATE tipli_account_segment_data sd
				LEFT JOIN tipli_account_user u ON u.id = sd.user_id
				LEFT JOIN tipli_account_user_data ud ON u.id = ud.user_id
				SET sd.leaflets_used_at = u.created_at
				WHERE sd.leaflets_used_at IS NULL AND (ud.first_visit_page LIKE \'%letak%\' OR ud.first_visit_page LIKE \'%gazetk%\');
            ');

		//deals
		$this->scheduleSqlQuery('
                UPDATE tipli_account_segment_data sd
				LEFT JOIN tipli_account_user u ON u.id = sd.user_id
				LEFT JOIN tipli_account_user_data ud ON u.id = ud.user_id
				LEFT JOIN tipli_shops_redirection r ON u.id = r.user_id
				SET sd.deals_used_at = u.created_at
				WHERE sd.deals_used_at IS NULL AND (ud.first_visit_page LIKE \'%slev%\' OR ud.first_visit_page LIKE \'%zlav%\' OR ud.first_visit_page LIKE \'%znizk%\' OR r.sale_id IS NOT NULL OR r.deal_id IS NOT NULL);
            ');

		//cashback
		$this->scheduleSqlQuery('
                UPDATE tipli_account_segment_data sd
				LEFT JOIN tipli_account_user u ON u.id = sd.user_id
				LEFT JOIN tipli_account_user_data ud ON u.id = ud.user_id
				LEFT JOIN tipli_shops_redirection r ON u.id = r.user_id
				SET sd.cashback_used_at = r.created_at
				WHERE sd.cashback_used_at IS NULL AND (r.shop_id IS NOT NULL OR r.sale_id IS NULL OR r.deal_id IS NULL)
            ');

		//cashback
		$this->scheduleSqlQuery('
                UPDATE tipli_account_segment_data sd
				LEFT JOIN tipli_account_user u ON u.id = sd.user_id
				LEFT JOIN tipli_account_user_data ud ON u.id = ud.user_id
				SET sd.cashback_used_at = u.created_at
				WHERE sd.cashback_used_at IS NULL AND (ud.first_visit_page LIKE \'%/obchod/%\' OR ud.first_visit_page LIKE \'%/sklep/%\')
            ');
	}

	private function startUpdateUsersUtm()
	{
		$referers = [
			'facebook',
			'google',
			'instagram',
			'seznam',
		];

		foreach ($referers as $referer) {
			$this->updateUsersUtmByReferer($referer);
		}

		$this->updateUsersUtmByRecommendation();
	}

	private function updateUsersUtmByReferer(string $referer): void
	{
		// Localization id => Empty UTM id
		$emptyUtmIds = [
			1 => 3,     // CZ
			2 => 125,   // SK
			3 => 2699,   // PL
			4 => 130875,   // RO
			6 => 181147,   // HU
			7 => 207667,   // SI
			8 => 207673,   // HR
			9 => 207853,   // BG
		];

		foreach ($emptyUtmIds as $localizationId => $emptyUtmId) {
			$usersToUpdate = $this->context->query('
				SELECT u.id, (SELECT id FROM tipli_utm_utm WHERE utm_source = \'referer-' . $referer . '\' AND localization_id = ' . $localizationId . ') as utmId
				FROM tipli_account_user u
				INNER JOIN tipli_account_user_data d ON u.id = d.user_id
				WHERE u.utm_id = ' . $emptyUtmId . ' AND u.parent_id IS NULL AND u.partner_organization_id IS NULL AND d.referer LIKE \'%' . $referer . '%\';
			')->fetchAll();

			foreach ($usersToUpdate as $user) {
				$this->scheduleSqlQuery('
					UPDATE tipli_account_user u
					SET u.utm_id = ' . $user->utmId . '
					WHERE u.id = ' . $user->id . '
				');
			}
		}
	}

	private function updateUsersUtmByRecommendation(): void
	{
		// Localization id => Empty UTM id
		$emptyUtmIds = [
			1 => 3,     // CZ
			2 => 125,   // SK
			3 => 2699,   // PL
			4 => 130875,   // RO
			6 => 181147,   // HU
			7 => 207667,   // SI
			8 => 207673,   // HR
			9 => 207853,   // BG
		];

		foreach ($emptyUtmIds as $localizationId => $emptyUtmId) {
			$this->scheduleSqlQuery('
              UPDATE tipli_account_user u
              SET u.utm_id = (SELECT id FROM tipli_utm_utm WHERE utm_source = \'doporuceni\' AND localization_id = ' . $localizationId . ')
              WHERE u.utm_id = ' . $emptyUtmId . ' AND u.parent_id IS NOT NULL AND u.partner_organization_id IS NULL
              AND u.created_at >= "' . (new \DateTime('- 1 day'))->format('Y-m-d') . '"
            ');
		}
	}

	private function scheduleSqlQuery($sqlQuery, array $parameters = [], int $priority = 200)
	{
		$this->queueFacade->scheduleCreateSqlQuery($sqlQuery, $parameters, SqlQuery::ROUTING_SEGMENT_DATA, $priority);
	}
}
