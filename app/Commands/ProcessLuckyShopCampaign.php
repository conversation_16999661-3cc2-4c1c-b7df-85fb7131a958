<?php

namespace tipli\Commands;

use tipli\Model\LuckyShop\LuckyShopFacade;

class ProcessLuckyShopCampaign extends Job
{
	public function __construct(private LuckyShopFacade $luckyShopFacade)
	{
		parent::__construct();
	}

	protected function configure()
	{
		$this->setName('tipli:process-lucky-shop-campaign:run');
	}

	public function start()
	{
		$log = $this->onStart();

		$this->processLuckyShopCampaign();

		$this->onFinish($log);
	}

	private function processLuckyShopCampaign(): void
	{
		$luckyShopCampaignToProcess = $this->luckyShopFacade->findLuckyShopCampaignToProcess();

		if (!$luckyShopCampaignToProcess) {
			return;
		}

		$this->luckyShopFacade->createLuckyShopForCampaign(
			$luckyShopCampaignToProcess
		);
	}
}
