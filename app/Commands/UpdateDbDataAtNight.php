<?php

namespace tipli\Commands;

use Nette\Database\Context;
use tipli\Model\Queues\Entities\SqlQuery;
use tipli\Model\Transactions\Entities\Transaction;
use Tracy\Debugger;

class UpdateDbDataAtNight extends Job
{
	/** @var Context */
	private $context;

	public function __construct(Context $context)
	{
		parent::__construct();

		$this->context = $context;
	}

	protected function configure()
	{
		$this->setName('tipli:update-db-data-at-night:run');
	}

	public function start()
	{
		ini_set('memory_limit', '1024M');
		ini_set('max_execution_time', 300);

		$log = $this->onStart();

		Debugger::log(1, 'update-db-data-at-night-run');

		$this->updateBalances();
		$this->updateShopStats();
		$this->calculateShopTransactionRegistrationTime();
		$this->updateUtmData();
		$this->updateVocalFirstNames();
		$this->updatePartnerSystemsConfirmedTurnover();
		$this->updateAccountSegmentDataIsAddonInstalled();
		$this->updateShopAverageCashback();
		$this->removePartnerSystemProcess();
		$this->removeQueueSqlQueries();
		$this->removeAccessErrors();
		$this->updateSegments();
		$this->updateCountOfRedirections();
		//$this->updatePartnerSystemsLastTransactionAt();
		# $this->removeOldNotifications();

		$this->onFinish($log);
	}

	private function updateCountOfRedirections(): void
	{
		$deals = $this->context->query('
			SELECT d.id
			FROM tipli_deals_deal d
			JOIN tipli_shops_shop s ON s.id = d.shop_id
			WHERE
				d.valid_till > NOW() AND
				d.removed_at IS NULL AND
				d.visible_on_web = 1 AND
				s.cashback_allowed = 1
		')->fetchAll();

		foreach ($deals as $deal) {
			$this->scheduleSqlQuery('
				UPDATE tipli_deals_deal d
				SET count_of_redirections = (
					SELECT count(r.id)
					FROM tipli_shops_redirection r
					WHERE
						r.deal_id = d.id AND
						r.created_at > "' . ((new \DateTime())->modify('-24 hours')->format('Y-m-d H:i:s')) . '"
					)
				WHERE d.id = ' . $deal['id'] . '
			');
		}
	}

	private function updateSegments()
	{
		$bonusRefundTransactionsRows = $this->context->query('SELECT user_id, count(id) AS c FROM tipli_transactions_transaction WHERE type="bonus_refund" GROUP BY user_id');
		foreach ($bonusRefundTransactionsRows as $row) {
			$this->scheduleSqlQuery('
				UPDATE tipli_account_segment_data d
				SET d.count_of_bonus_refund_transactions = ' . $row->c . '
				WHERE d.user_id = ' . $row->user_id . '
			');
		}
	}

	private function updateTagsStats()
	{
		$activeTags = $this->context->query('
			SELECT t.id
			FROM tipli_tags_tag t
			WHERE t.removed_at IS NULL AND t.type = "sale"
		')->fetchAll();

		foreach ($activeTags as $tag) {
			$this->scheduleSqlQuery('UPDATE tipli_tags_tag t
            SET t.count_of_non_cashback_shops = (
				SELECT COUNT(s.id)
				FROM tipli_shops_shop_tag st
				INNER JOIN tipli_shops_shop s on s.id = st.shop_id
				WHERE st.tag_id = t.id AND s.published_at <= NOW()
				AND s.active = 1 and s.visible = 1
				AND s.cashback_allowed = 0
				AND t.id = ' . $tag->id . ')
            ');
		}
	}

	private function updateShopStats()
	{
		$shopsDataToUpdateTurnover = $this->context->query('
			SELECT t.shop_id, IFNULL(SUM(t.commission_amount), 0) as turnover
            FROM tipli_transactions_transaction t
            WHERE t.created_at >= DATE_SUB(NOW(), INTERVAL 14 DAY)
			AND t.shop_id IS NOT NULL
            GROUP BY t.shop_id
        ')->fetchAll();

		foreach ($shopsDataToUpdateTurnover as $shopData) {
			$this->scheduleSqlQuery('
				UPDATE tipli_shops_shop_data
				SET total_commission_amount_in_last30days = ' . $shopData->turnover . '
				WHERE shop_id = ' . $shopData->shop_id . '
			');
		}

		// average confirmation period
		/*$this->scheduleSqlQuery('
			UPDATE tipli_shops_shop
			SET average_confirmation_period = (
			  SELECT ROUND(AVG(DATEDIFF(tipli_transactions_transaction.confirmed_at, tipli_transactions_transaction.registered_at)) * 1.5) AS average_confirmation_period
			  FROM tipli_transactions_transaction
			  WHERE shop_id = tipli_shops_shop.id AND confirmed_at IS NOT NULL AND type = "commission"
			)
			WHERE tipli_shops_shop.count_of_transactions > 10 AND id NOT IN (2, 299);
		'); // id 2, 299 => booking*/

		// aliexpress
		$this->scheduleSqlQuery('UPDATE tipli_shops_shop SET average_confirmation_period = 65 WHERE partner_system_id = 2', SqlQuery::ROUTING_STATS);

		$this->scheduleSqlQuery('UPDATE tipli_shops_shop SET average_confirmation_period = 70 WHERE average_confirmation_period > 70', SqlQuery::ROUTING_STATS);

		// average registration period
		/*$this->scheduleSqlQuery('UPDATE tipli_shops_shop SET average_registration_period =
			(SELECT
			ROUND(AVG(DATEDIFF(tipli_transactions_transaction.created_at, tipli_transactions_transaction.registered_at)) * 1.5)
			AS average_registration_period FROM tipli_transactions_transaction WHERE shop_id = tipli_shops_shop.id AND type = "commission"
		) WHERE tipli_shops_shop.count_of_transactions > 10');*/

		// count of transactions
		//$this->scheduleSqlQuery('UPDATE tipli_shops_shop SET count_of_transactions = (SELECT COUNT(tipli_transactions_transaction.id) AS c FROM tipli_transactions_transaction WHERE shop_id = tipli_shops_shop.id)');

		// count of redirections
		// $this->scheduleSqlQuery('UPDATE tipli_shops_shop SET count_of_redirections = (SELECT COUNT(tipli_shops_redirection.id) AS c FROM tipli_shops_redirection WHERE shop_id = tipli_shops_shop.id)');

		// last leaflet created at
		$rows = $this->context->query('SELECT shop_id, MAX(created_at) AS max_created_at FROM tipli_leaflets_leaflet WHERE shop_id IS NOT NULL GROUP BY shop_id')->fetchAll();
		foreach ($rows as $row) {
			$this->scheduleSqlQuery('UPDATE tipli_shops_shop_data SET last_leaflet_created_at = "' . $row->max_created_at->format('Y-m-d H:i:s') . '" WHERE shop_id = ' . $row->shop_id . '', SqlQuery::ROUTING_STATS);
		}

		// last sale created
//        $this->scheduleSqlQuery('UPDATE tipli_shops_shop_data SET last_sale_created_at = (SELECT tipli_sales_sale.created_at AS s FROM tipli_sales_sale WHERE shop_id = tipli_shops_shop_data.shop_id ORDER BY tipli_sales_sale.created_at DESC LIMIT 1)');

		// update confirmation rates
		$this->scheduleSqlQuery('UPDATE tipli_shops_shop_data SET confirmation_rate = NULL');

		$shops = $this->context->fetchAll('SELECT DISTINCT shop_id FROM tipli_transactions_transaction WHERE type = "commission" AND confirmed_at IS NOT NULL AND confirmed_at > DATE_SUB(CURRENT_DATE(), INTERVAL 3 MONTH)');
		foreach ($shops as $shop) {
			$shopId = $shop->shop_id;

			$this->scheduleSqlQuery('UPDATE tipli_shops_shop_data
			SET confirmation_rate =
			(SELECT SUM(turnover)/SUM(original_turnover)
			FROM
				(SELECT d.turnover AS turnover, d.original_turnover AS original_turnover
				FROM tipli_transactions_transaction t
				INNER JOIN tipli_transactions_transaction_data d ON (d.transaction_id = t.id)
				WHERE t.type = "commission" AND t.shop_id =  ' . $shopId . '
				 AND t.confirmed_at IS NOT NULL
				 AND t.confirmed_at > DATE_SUB(CURRENT_DATE(), INTERVAL 3 MONTH)
				ORDER BY t.confirmed_at DESC
				LIMIT 1000
			) d)
			WHERE shop_id = ' . $shopId . '
			');
		}

		$rows = $this->context->query('SELECT shop_id, MAX(created_at) AS max_created_at FROM tipli_transactions_transaction WHERE created_at >= (NOW() - INTERVAL 48 HOUR) AND type = ? GROUP BY shop_id', Transaction::TYPE_COMMISSION);

		foreach ($rows as $row) {
			$this->scheduleSqlQuery('UPDATE tipli_shops_shop_data sd SET sd.last_transaction_at = "' . $row->max_created_at->format('Y-m-d H:i:s') . '" WHERE sd.shop_id = ' . $row->shop_id . '', SqlQuery::ROUTING_STATS);
		}
	}

	private function updateUtmData()
	{
		$this->scheduleSqlQuery('UPDATE tipli_utm_utm_data SET active = 0', SqlQuery::ROUTING_STATS);

		$this->scheduleSqlQuery('UPDATE tipli_utm_utm_data, (SELECT utm_id FROM tipli_utm_utm_cost WHERE paid_from > DATE_SUB(CURRENT_DATE(), INTERVAL 24 HOUR) GROUP BY utm_id) AS activeUtm
        SET tipli_utm_utm_data.active = 1 WHERE tipli_utm_utm_data.utm_id = activeUtm.utm_id', SqlQuery::ROUTING_STATS);
	}

	private function updateVocalFirstNames()
	{
		$usersToUpdate = $this->context->query('SELECT DISTINCT user_id FROM tipli_account_user_change WHERE first_name IS NOT NULL AND created_at >= NOW() - INTERVAL 5 DAY')->fetchAll();
		foreach ($usersToUpdate as $user) {
			$this->scheduleSqlQuery('
		        UPDATE tipli_account_user_data AS ud
		        INNER JOIN tipli_account_user AS u ON u.id = ud.user_id
		        LEFT JOIN tipli_vocal_first_names AS f ON ud.first_name = f.first_name AND f.localization_id = u.localization_id
		        SET ud.vocal_first_name = CASE
		            WHEN u.localization_id = 2 THEN ud.first_name
		            WHEN f.vocal_first_name IS NOT NULL THEN f.vocal_first_name
		            ELSE ud.vocal_first_name
		        END
		        WHERE ud.first_name IS NOT NULL AND ud.user_id = ' . $user->user_id);
		}
	}

	private function updatePartnerSystemsConfirmedTurnover()
	{
		$partnerSystemsToUpdate = $this->context->query('SELECT id FROM tipli_partner_systems_partner_system WHERE last_transaction_at > DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH)')->fetchAll();

		foreach ($partnerSystemsToUpdate as $partnerSystem) {
			$this->scheduleSqlQuery('UPDATE tipli_partner_systems_partner_system p SET p.confirmed_turnover = (
	          SELECT IFNULL(SUM(d.turnover),0)
	          FROM tipli_transactions_transaction t
	          INNER JOIN tipli_transactions_transaction_data d ON (d.transaction_id=t.id)
	          WHERE t.partner_system_id=p.id AND t.type="commission" AND t.confirmed_at IS NOT NULL
          	) WHERE p.id = ' . $partnerSystem->id);
		}
	}

	private function scheduleSqlQuery(string $sqlQuery, string $routing = SqlQuery::ROUTING_STATS)
	{
		$this->queueFacade->scheduleCreateSqlQuery($sqlQuery, [], $routing);
	}

	public function updateAccountSegmentDataIsAddonInstalled()
	{
		$usersToUpdate = $this->context->query('SELECT user_id FROM tipli_account_segment_data WHERE addon_feed_downloaded_at > DATE_SUB(CURRENT_DATE(), INTERVAL 10 DAY) AND addon_installed = 0')->fetchAll();

		foreach ($usersToUpdate as $user) {
			$this->scheduleSqlQuery('UPDATE tipli_account_segment_data SET addon_installed = 1 WHERE user_id = ' . $user->user_id);
		}
	}

	private function calculateShopTransactionRegistrationTime()
	{
		$shops = $this->context->fetchAll('SELECT DISTINCT s.id FROM tipli_shops_shop s INNER JOIN tipli_shops_shop_data sd on sd.shop_id = s.id WHERE s.cashback_allowed = 1 AND s.active = 1 and s.visible = 1 AND (sd.paused_at IS NULL OR sd.paused_at > "' . (new \DateTime())->format('Y-m-d H:i:s') . '")  ');

		foreach ($shops as $shop) {
			$shopId = $shop->id;

			$this->scheduleSqlQuery('UPDATE tipli_shops_shop_data sd
            SET average_transaction_registration_time = (
                SELECT AVG(tt.timediff) as median_val
                FROM (
                SELECT timestampdiff(SECOND, t.registered_at, t.created_at) as timediff, t.shop_id, @rownum:=@rownum+1 as row_number, @total_rows:=@rownum
                  FROM tipli_transactions_transaction t, (SELECT @rownum:=0) r
                  WHERE t.type = \'commission\' AND t.user_id != 153000 AND t.shop_id = ' . $shopId . '
                  ORDER BY t.created_at DESC
                  LIMIT 100
                ) as tt
                WHERE tt.row_number IN ( FLOOR((@total_rows+1)/2), FLOOR((@total_rows+2)/2) )
                and sd.shop_id = ' . $shopId . '
            )
        ', SqlQuery::ROUTING_STATS);
		}
	}

	public function updatePartnerSystemsLastTransactionAt()
	{
		$this->scheduleSqlQuery('
			UPDATE tipli_partner_systems_partner_system ps
			SET ps.last_transaction_at = (
			    SELECT Max(created_at)
				FROM tipli_transactions_transaction t
				WHERE t.partner_system_id = ps.id
			    AND t.type != "payout"
			)
        ');
	}

	private function updateShopAverageCashback()
	{
		$this->scheduleSqlQuery('
			UPDATE tipli_shops_shop_data sd SET average_cashback = (
				    SELECT ROUND(AVG(t.user_commission_amount / NULLIF(td.order_amount, 0)), 3)
				    FROM tipli_transactions_transaction t
				        INNER JOIN tipli_transactions_transaction_data td on td.transaction_id = t.id
				    WHERE t.shop_id = sd.shop_id
				      AND t.created_at >=  "' . ((new \DateTime('- 30 days'))->format('Y-m-d H:i:s')) . '"
				      AND t.type != \'payout\'
			    )
		');

		$this->scheduleSqlQuery('
			UPDATE tipli_shops_shop_data sd SET average_commission_amount = (
				    SELECT ROUND(AVG(t.commission_amount), 3)
				    FROM tipli_transactions_transaction t
				    WHERE t.shop_id = sd.shop_id
				      AND t.created_at >=  "' . ((new \DateTime('- 30 days'))->format('Y-m-d H:i:s')) . '"
				      AND t.type = \'commission\'
			    )
		');
	}

	private function removePartnerSystemProcess()
	{
		$date = ((new \DateTime('- 21 days'))
			->setTime(0, 0)
			->format('Y-m-d H:i:s'))
		;

		$this->scheduleSqlQuery('
			DELETE FROM tipli_partner_systems_partner_system_process  WHERE created_at < "' . $date . '"
		');
	}

	private function removeQueueSqlQueries()
	{
		$date = ((new \DateTime('- 36 hours'))
			->format('Y-m-d H:i:s'))
		;

		$this->scheduleSqlQuery('DELETE FROM tipli_queues_sql_query  WHERE created_at < "' . $date . '" AND (completed_at IS NOT NULL OR failed_at IS NOT NULL) AND routing != "' . SqlQuery::ROUTING_NOTIFICATIONS . '"');
	}

	public function removeAccessErrors()
	{
		$date = ((new \DateTime('-3 months'))
			->setTime(0, 0)
			->format('Y-m-d H:i:s'))
		;

		$this->scheduleSqlQuery('
			DELETE FROM access_errors WHERE created_at < "' . $date . '"
		', SqlQuery::ROUTING_STATS);
	}

	private function updateBalances(): void
	{
//		$this->scheduleSqlQuery(
//			'UPDATE tipli_account_user_data ud
//				SET ud.balance = (SELECT SUM(t.user_commission_amount)+SUM(t.bonus_amount) FROM tipli_transactions_transaction t WHERE t.user_id=ud.user_id AND t.billable=1)'
//		);
	}
}
