<?php

namespace tipli\Commands;

use Nette\Database\Context;
use Nette\Utils\Strings;
use tipli\Model\Deals\Entities\Deal;
use tipli\Model\Localization\LocalizationFacade;
use tipli\Model\Queues\Entities\SqlQuery;
use Tracy\Debugger;

class UpdateDbDataEveryHour extends Job
{
	/**
	 * @var LocalizationFacade
	 */
	private $localizationFacade;

	/**
	 * @var Context
	 */
	private $context;

	public function __construct(LocalizationFacade $localizationFacade, Context $context)
	{
		parent::__construct();

		$this->localizationFacade = $localizationFacade;
		$this->context = $context;
	}

	protected function configure()
	{
		$this->setName('tipli:update-db-data-every-hour:run');
	}

	public function start()
	{
		$log = $this->onStart();
		Debugger::log(1, 'update-db-data-every-hour-run');

//		$this->updateAverageTransactionRegistrationPeriod();
		$this->updateBalances();
		$this->updateSegments();
//		$this->updateDealsOrders();
		$this->updateUsersUnsubscribedNewsletters();
		$this->updateCountOfShops();
		$this->updateShopsStats();
		$this->updateTagsStats();
		$this->updateNotificationCampaignsStats();
		$this->updateBannersStats();
		$this->updatePopupsStats();
		$this->updatePartnerSystemStats();
		$this->updateLeaflets();
		$this->updateRewardCampaigns();
//		$this->updateTransactionData();
		$this->updateCountOfRedirections();
		$this->scheduleMetricsRefresh();

		$this->onFinish($log);
	}

	private function updateSegments()
	{
		// ALIEXPRESS ADDON ENABLED
		$aliexpressAddonEnabledRows = $this->context->query('
			SELECT distinct user_id
			FROM tipli_transactions_transaction t
			WHERE t.shop_id IN (1,298,617,5732,6493) AND t.type IN ("commission","bonus_refund")
			AND t.user_id IN (SELECT p.user_id FROM tipli_payouts_payout p WHERE p.confirmed_at IS NOT NULL)
		')->fetchAll();

		$ids = [];
		foreach ($aliexpressAddonEnabledRows as $row) {
			$ids[] = $row->user_id;
		}

		foreach (array_chunk($ids, 1000) as $idsChunk) {
			$this->scheduleSqlQuery('UPDATE tipli_account_segment_data d SET d.aliexpress_addon_enabled = 1 WHERE d.user_id IN (' . implode(',', $idsChunk) . ')');
		}

		// HAS SPECIAL SHARE COEFFICIENT
		$hasSpecialShareCoefficientRows = $this->context->query('
			SELECT user_id
			FROM tipli_rewards_share_reward r
			INNER JOIN tipli_rewards_share_reward_shops s ON (s.share_reward_id=r.id)
			WHERE r.user_id IS NOT NULL
			AND r.valid_since <= NOW()
			AND r.valid_till >= NOW()
		')->fetchAll();

		$ids = [];
		foreach ($hasSpecialShareCoefficientRows as $row) {
			$ids[] = $row->user_id;
		}

		foreach (array_chunk($ids, 1000) as $idsChunk) {
			$this->scheduleSqlQuery('UPDATE tipli_account_segment_data d SET d.has_special_share_coefficient = 1 WHERE d.user_id IN (' . implode(',', $idsChunk) . ')');
		}

		// SHARE COEFFICIENT
		$this->scheduleSqlQuery('
			UPDATE tipli_account_segment_data sd
			LEFT JOIN (
				SELECT r.user_id, MAX(r.share_coefficient) AS max_share
				FROM tipli_rewards_share_reward r
				LEFT JOIN tipli_rewards_share_reward_shops s ON r.id = s.share_reward_id
				WHERE r.user_id IS NOT NULL
				  AND r.valid_since <= NOW()
				  AND r.valid_till >= NOW()
				  AND r.shop_id IS NULL
				  AND s.share_reward_id IS NULL
				GROUP BY r.user_id
			) reward ON reward.user_id = sd.user_id
			SET sd.share_coefficient = IFNULL(reward.max_share, 0.5)
		');

		$bonusRefundTransactionsRows = $this->context->query('SELECT user_id FROM tipli_transactions_transaction WHERE type="bonus_refund" AND created_at >= NOW() - INTERVAL 1 DAY GROUP BY user_id');
		foreach ($bonusRefundTransactionsRows as $row) {
			$this->scheduleSqlQuery('
				UPDATE tipli_account_segment_data d
				SET d.count_of_bonus_refund_transactions = (SELECT count(id) FROM tipli_transactions_transaction WHERE type="bonus_refund" AND user_id = d.user_id)
				WHERE d.user_id = ' . $row->user_id . '
			');
		}
		//$this->scheduleSqlQuery('UPDATE tipli_account_user AS user SET segment = "active" WHERE user.id IN (SELECT DISTINCT user_id FROM tipli_transactions_transaction AS transaction WHERE transaction.type = "commission")');

		/*
		$this->scheduleSqlQuery('
			UPDATE tipli_account_segment_data AS segment
			INNER JOIN tipli_transactions_transaction AS transaction
			ON transaction.type = "bonus_addon" AND transaction.user_id = segment.user_id
			SET addon_installed = 1
		');*/

		//$this->scheduleSqlQuery('UPDATE tipli_account_segment_data SET first_transaction_at = (SELECT MIN(t.registered_at) FROM tipli_transactions_transaction t WHERE (t.type = "commission" OR t.type = "bonus_refund") AND t.user_id = tipli_account_segment_data.user_id)');
		//$this->scheduleSqlQuery('UPDATE tipli_account_segment_data SET count_of_recommended_users = (SELECT count(u.id) FROM tipli_account_user u WHERE u.parent_id = tipli_account_segment_data.user_id)');
		//$this->scheduleSqlQuery('UPDATE tipli_account_segment_data SET last_transaction_at = (SELECT MAX(t.registered_at) FROM tipli_transactions_transaction t WHERE (t.type = "commission" OR t.type = "bonus_refund") AND t.user_id = tipli_account_segment_data.user_id)');
		//$this->scheduleSqlQuery('UPDATE tipli_account_segment_data SET last_recommended_user_at = (SELECT MAX(u.created_at) FROM tipli_account_user u WHERE u.parent_id = tipli_account_segment_data.user_id)');
		//$this->scheduleSqlQuery('UPDATE tipli_account_segment_data SET has_money_reward_bonus = 1 WHERE user_id IN (SELECT DISTINCT t.user_id FROM tipli_transactions_transaction t WHERE t.type = "bonus_money_reward")');

		//Debugger::log('Update segments');
	}

	private function updateCountOfRedirections(): void
	{
		$redirectionsWithDeal = $this->context->query('
			SELECT deal_id
			FROM `tipli_shops_redirection`
			WHERE `deal_id` IS NOT NULL
			  AND `created_at` >= NOW() - INTERVAL 2 HOUR
		')->fetchAll();

		foreach ($redirectionsWithDeal as $redirection) {
			$this->scheduleSqlQuery('
				UPDATE tipli_deals_deal d
				SET count_of_redirections = (
					SELECT count(r.id)
					FROM tipli_shops_redirection r
					WHERE
						r.deal_id = d.id AND
						r.created_at > "' . ((new \DateTime())->modify('-24 hours')->format('Y-m-d H:i:s')) . '"
					)
				WHERE d.id = ' . $redirection->deal_id . '
			', routingKey: SqlQuery::ROUTING_STATS);
		}
	}

	private function updateCountOfShops()
	{
		$this->scheduleSqlQuery('UPDATE tipli_partner_systems_partner_system p SET count_of_shops = (SELECT count(s.id) FROM tipli_shops_shop_partner_system s WHERE s.partner_system_id = p.id)');
	}

	private function updateNotificationCampaignsStats(): void
	{
		$notificationsToProcess = $this->context->query('SELECT id  FROM tipli_inbox_notification_campaign nc  WHERE nc.valid_till IS NOT NULL AND nc.valid_till > NOW()')->fetchAll();

		foreach ($notificationsToProcess as $notification) {
			$this->scheduleSqlQuery('
				UPDATE tipli_inbox_notification_campaign nc
				SET recipients_count = (
					SELECT COUNT(n.id)
					FROM tipli_inbox_notification n
					WHERE n.notification_campaign_id = nc.id
					AND DATEDIFF(now(), n.created_at) < 7
				)
				WHERE nc.id = ' . $notification->id . '
			', []); // pouzivam tuhle frontu protoze bude zpracovana rychleji nez default, stava se totiz to, ze pokud se ten dotaz nespusti vcas, tak pak ty metriky vynuluje

			$this->scheduleSqlQuery('
				UPDATE tipli_inbox_notification_campaign nc
				SET recipients_mobile_app_count = (
					SELECT COUNT(n.id)
					FROM tipli_inbox_notification n
					WHERE n.notification_campaign_id = nc.id AND n.pushed_at IS NOT NULL
					AND DATEDIFF(now(), n.created_at) < 7
				)
				WHERE nc.id = ' . $notification->id . '
			', []);

			$this->scheduleSqlQuery('
				UPDATE tipli_inbox_notification_campaign nc
				SET web_clicks_count = (
					SELECT COUNT(n.id)
					FROM tipli_inbox_notification n
					WHERE n.notification_campaign_id = nc.id AND n.clicked_at IS NOT NULL
					AND DATEDIFF(now(), n.created_at) < 7
				)
				WHERE nc.id = ' . $notification->id . '
			', []);

			$this->scheduleSqlQuery('
				UPDATE tipli_inbox_notification_campaign nc
				SET mobile_app_clicks_count = (
					SELECT COUNT(n.id)
					FROM tipli_inbox_notification n
					WHERE n.notification_campaign_id = nc.id AND (n.mobile_clicked_at IS NOT NULL OR n.mobile_opened_at IS NOT NULL)
					AND DATEDIFF(now(), n.created_at) < 7
				)
				WHERE nc.id = ' . $notification->id . '
			', []);
		}
	}

	private function updateBannersStats()
	{
		$banners = $this->context->query('
        SELECT id
        FROM tipli_marketing_banner b
        WHERE b.valid_till IS NOT NULL
          AND b.valid_till <= NOW()
          AND b.valid_till >= NOW() - INTERVAL 7 DAY
    ')->fetchAll();

		foreach ($banners as $banner) {
			$this->scheduleSqlQuery('
	            UPDATE tipli_marketing_banner
	            SET count_of_clicks = (
	                SELECT COUNT(id)
	                FROM tipli_marketing_banner_click
	                WHERE banner_id = ' . $banner->id . '
	            )
	            WHERE id = ' . $banner->id . '
            ');
		}
	}

	private function updatePopupsStats()
	{
		// @TODO Docasne vypnuto, musi se zoptimalizovat
		// count of opens
//		$this->scheduleSqlQuery('UPDATE tipli_popups_popup_campaign p SET count_of_opens = (SELECT count(i.id) FROM tipli_popups_popup_interaction i WHERE i.popup_campaign_id=p.id)', routingKey: SqlQuery::ROUTING_STATS);

		// count of closures
//		$this->scheduleSqlQuery('UPDATE tipli_popups_popup_campaign p SET count_of_closures = (SELECT count(i.id) FROM tipli_popups_popup_interaction i WHERE i.popup_campaign_id=p.id AND i.closed_at IS NOT NULL)', routingKey: SqlQuery::ROUTING_STATS);// count of closures

		// count of clicks
//		$this->scheduleSqlQuery('UPDATE tipli_popups_popup_campaign p SET count_of_clicks = (SELECT count(i.id) FROM tipli_popups_popup_interaction i WHERE i.popup_campaign_id=p.id AND i.clicked_at IS NOT NULL)', routingKey: SqlQuery::ROUTING_STATS);
	}

	private function updateShopsStats()
	{
		$shopsDataToUpdateTurnover = $this->context->query('
			SELECT t.shop_id, IFNULL(SUM(t.commission_amount), 0) as turnover
            FROM tipli_transactions_transaction t
            WHERE t.created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY)
			AND t.shop_id IS NOT NULL
            GROUP BY t.shop_id
        ')->fetchAll();

		foreach ($shopsDataToUpdateTurnover as $shopData) {
			$this->scheduleSqlQuery('
				UPDATE tipli_shops_shop_data
				SET total_commission_amount_in_last30days = ' . $shopData->turnover . '
				WHERE shop_id = ' . $shopData->shop_id . '
			', routingKey: SqlQuery::ROUTING_STATS);
		}

		// leaflets
		$this->scheduleSqlQuery('UPDATE tipli_shops_shop_data sd
            SET sd.count_of_leaflets = (
               SELECT IFNULL(COUNT(l.id), 0)
               FROM tipli_leaflets_leaflet l
               WHERE l.shop_id = sd.shop_id AND l.valid_since <= NOW() AND l.valid_till >= NOW()
            )
        ', routingKey: SqlQuery::ROUTING_STATS);

		$this->scheduleSqlQuery('UPDATE tipli_shops_shop_data sd
            SET sd.count_of_leaflets_total = (
               SELECT IFNULL(COUNT(l.id), 0)
               FROM tipli_leaflets_leaflet l
               WHERE l.shop_id = sd.shop_id
            )
        ', routingKey: SqlQuery::ROUTING_STATS);

		$shopsWithDeals = $this->context->query('
			SELECT DISTINCT d.shop_id as id
			FROM tipli_deals_deal d
			WHERE d.valid_since <= NOW()
				AND d.valid_till >= NOW()
			  	AND d.valid_till < "2050-01-01"
				AND d.removed_at IS NULL
				AND d.type != "cashback"
		')->fetchAll();

		foreach ($shopsWithDeals as $shop) {
			$this->scheduleSqlQuery('UPDATE tipli_shops_shop_data sd
            SET sd.count_of_deals = (
               SELECT IFNULL(COUNT(d.id), 0)
               FROM tipli_deals_deal d
               WHERE d.shop_id = sd.shop_id
                AND d.valid_since <= NOW()
                AND d.valid_till >= NOW()
                AND d.removed_at IS NULL
				AND visible_on_web = 1
                AND d.type != \'cashback\'
            )
			WHERE sd.only_leaflet_shop = 0 AND sd.shop_id = ' . $shop->id . '
        ', routingKey: SqlQuery::ROUTING_STATS);

			$this->scheduleSqlQuery('UPDATE tipli_shops_shop_data sd
            SET sd.count_of_coupon_deals = (
               SELECT IFNULL(COUNT(d.id), 0)
               FROM tipli_deals_deal d
               WHERE d.shop_id = sd.shop_id
                AND d.valid_since <= NOW()
                AND d.valid_till >= NOW()
                AND d.type = \'coupon\'
                AND d.code IS NOT NULL
                AND d.removed_at IS NULL
                AND visible_on_web = 1
            )
			WHERE sd.only_leaflet_shop = 0 AND sd.shop_id = ' . $shop->id . '
        ', routingKey: SqlQuery::ROUTING_STATS);

			$this->scheduleSqlQuery('UPDATE tipli_shops_shop_data sd
            SET sd.recent_deal_expire_at = (
               SELECT MAX(valid_till)
               FROM tipli_deals_deal d
               WHERE d.shop_id = sd.shop_id
                AND d.valid_till > NOW()
                AND d.removed_at IS NULL
				AND visible_on_web = 1
                AND d.type != \'cashback\'
                AND d.type != \'coupon\'
            )
            WHERE sd.shop_id = ' . $shop->id . '
        ', routingKey: SqlQuery::ROUTING_STATS);

			$this->scheduleSqlQuery('UPDATE tipli_shops_shop_data sd
            SET sd.recent_coupon_expire_at = (
               SELECT MAX(valid_till)
               FROM tipli_deals_deal d
               WHERE d.shop_id = sd.shop_id
                AND d.valid_till > NOW()
                AND d.removed_at IS NULL
				AND visible_on_web = 1
                AND d.code IS NOT NULL
            )
            WHERE sd.shop_id = ' . $shop->id . '
        ', routingKey: SqlQuery::ROUTING_STATS);
		}
	}

	private function updateTagsStats()
	{
		$activeTags = $this->context->query('
			SELECT t.id, t.type
			FROM tipli_tags_tag t
			WHERE t.removed_at IS NULL
		')->fetchAll();

		foreach ($activeTags as $tag) {
			if ($tag->type === 'sale') {
				$this->scheduleSqlQuery('UPDATE tipli_tags_tag t
		            SET t.count_of_coupon_deals = (
						SELECT COUNT(DISTINCT d.shop_id)
						FROM tipli_deals_deal_tag dt
						LEFT JOIN tipli_deals_deal d on d.id = dt.deal_id
						WHERE dt.tag_id = t.id AND d.valid_since <= NOW()
						AND d.valid_till >= NOW()
						AND d.removed_at IS NULL
						AND d.type = \'coupon\'
						AND t.localization_id = d.localization_id
						AND t.id = ' . $tag->id . '
                )
            ', routingKey: SqlQuery::ROUTING_STATS);
			}

			if ($tag->type === 'shop') {
				$countOfShops = $this->context->query('
				SELECT COUNT(s.id) as cnt
				FROM tipli_shops_shop_tag st
				INNER JOIN tipli_shops_shop s on s.id = st.shop_id
				WHERE st.tag_id = ' . $tag->id . '
				AND s.published_at <= NOW()
				AND s.active = 1 and s.visible = 1
			')->fetchField('cnt');

				$this->scheduleSqlQuery('UPDATE tipli_tags_tag t
            SET t.count_of_shops = ' . $countOfShops . '
            WHERE t.id = ' . $tag->id . '
        ', routingKey: SqlQuery::ROUTING_STATS);
			}
		}
	}

	private function updateUsersUnsubscribedNewsletters()
	{
		$this->scheduleSqlQuery('UPDATE tipli_account_segment_data SET has_unsubscribed_newsletters=0');

		$this->scheduleSqlQuery('
            UPDATE tipli_account_segment_data sd
            INNER JOIN tipli_account_sending_policy sp ON (sd.user_id = sp.user_id AND sp.message_type = "email" AND sp.content_type = "newsletter")
            SET sd.has_unsubscribed_newsletters=1
        ');
	}

	private function updateDealsOrders()
	{
		$this->scheduleSqlQuery('UPDATE tipli_deals_deal SET trend_order = NULL');

		foreach ($this->localizationFacade->findLocalizations(false) as $localization) {
			$this->scheduleSqlQuery('
				SET @c = 0;
				UPDATE tipli_deals_deal d SET d.trend_order = @c:=@c+1
				WHERE d.type = "' . Deal::TYPE_PRODUCT . '"
					AND d.localization_id = ' . $localization->getId() . '
					AND d.valid_since <= "' . ((new \DateTime())->format('Y-m-d H:i:s')) . '"
					AND d.valid_till >= "' . ((new \DateTime())->format('Y-m-d H:i:s')) . '"
				ORDER BY d.priority DESC, d.ga_ctr DESC
			', routingKey: SqlQuery::ROUTING_STATS);

			$this->scheduleSqlQuery('
				SET @c = 0;
				UPDATE tipli_deals_deal d SET d.trend_order = @c:=@c+1
				WHERE d.type IN ("' . Deal::TYPE_SALE . '","' . Deal::TYPE_TIP . '","' . Deal::TYPE_COUPON . '","' . Deal::TYPE_FREE_SHIPPING . '")
					AND d.localization_id = ' . $localization->getId() . '
					AND d.valid_since <= "' . ((new \DateTime())->format('Y-m-d H:i:s')) . '"
					AND d.valid_till >= "' . ((new \DateTime())->format('Y-m-d H:i:s')) . '"
				ORDER BY d.priority DESC, d.ga_ctr DESC
			', routingKey: SqlQuery::ROUTING_STATS);
		}
	}

	private function updateAverageTransactionRegistrationPeriod()
	{
		$ids = array_map(static function ($row) {
			return $row->id;
		}, $this->context->query('
				SELECT s.id
				FROM tipli_shops_shop s
				INNER JOIN tipli_shops_shop_data d ON (d.shop_id=s.id)
				WHERE s.cashback_allowed = 1 AND d.paused_at IS NULL
				AND (SELECT count(t.id) FROM tipli_transactions_transaction t WHERE t.shop_id=s.id AND t.type="commission" AND t.created_at >= ?) > 10
			', (new \DateTime())->modify('-1 year'))->fetchAll());

		$this->scheduleSqlQuery('UPDATE tipli_shops_shop_data SET average_transaction_registration_period = NULL');

		foreach ($ids as $shopId) {
			$this->scheduleSqlQuery('
			UPDATE tipli_shops_shop_data sd SET sd.average_transaction_registration_period =
			(
				SELECT ROUND(TIMESTAMPDIFF(hour, MIN(d.created_at),MAX(d.created_at)) / COUNT(d.id)) as h
				FROM (
				   SELECT t.*
				   FROM tipli_transactions_transaction t
				   INNER JOIN tipli_shops_shop s ON (s.id = t.shop_id)
				   INNER JOIN tipli_shops_shop_data d ON (d.shop_id = s.id)
				   WHERE t.shop_id = ' . $shopId . '
				   AND s.cashback_allowed = 1
				   AND d.paused_at IS NULL
				   AND t.created_at >= "' . (new \DateTime())->modify('- 6 months')->format('Y-m-d H:i:s') . '"
				   LIMIT 1000
				) d
			) WHERE sd.shop_id= ' . $shopId . '
			', routingKey: SqlQuery::ROUTING_STATS);
		}

		$this->scheduleSqlQuery('UPDATE tipli_shops_shop_data SET average_transaction_registration_period = 2 WHERE average_transaction_registration_period IS NOT NULL AND average_transaction_registration_period < 2', routingKey: SqlQuery::ROUTING_STATS);
		$this->scheduleSqlQuery('UPDATE tipli_shops_shop_data SET average_transaction_registration_period = 4 WHERE average_transaction_registration_period IS NOT NULL AND average_transaction_registration_period < 4 AND average_transaction_registration_period > 2', routingKey: SqlQuery::ROUTING_STATS);
		$this->scheduleSqlQuery('UPDATE tipli_shops_shop_data SET average_transaction_registration_period = 6 WHERE average_transaction_registration_period IS NOT NULL AND average_transaction_registration_period < 6 AND average_transaction_registration_period > 4', routingKey: SqlQuery::ROUTING_STATS);
	}

	private function scheduleSqlQuery($sqlQuery, array $params = [], string $routingKey = SqlQuery::ROUTING_DEFAULT)
	{
		$routing = SqlQuery::ROUTING_DEFAULT;
		$priority = 100;

		if ($sqlQuery && Strings::contains($sqlQuery, 'tipli_account_segment_data')) {
			$routing = SqlQuery::ROUTING_SEGMENT_DATA;
			$priority = 200;
		}

		if ($sqlQuery && Strings::contains($sqlQuery, 'tipli_reports_metric')) {
			$routing = SqlQuery::ROUTING_METRICS;
			$priority = 1000;
		}

		if ($routingKey === SqlQuery::ROUTING_STATS) {
			$routing = SqlQuery::ROUTING_STATS;
			$priority = 800;
		}

		$this->queueFacade->scheduleCreateSqlQuery($sqlQuery, $params, $routing, $priority);
	}

	private function updatePartnerSystemStats()
	{
		$partnerSystemsWithCommissions = $this->context->query('
			SELECT MIN(t.created_at) as oldestTransactionAt, t.partner_system_id
			FROM tipli_transactions_transaction t
			WHERE t.confirmed_at IS NULL AND t.type="commission"
			AND t.partner_system_id IS NOT NULL
			GROUP BY t.partner_system_id
		')->fetchAll();

		foreach ($partnerSystemsWithCommissions as $partnerSystem) {
			$this->scheduleSqlQuery('
				UPDATE tipli_partner_systems_partner_system p
				SET p.oldest_transaction_registered_at = "' . $partnerSystem->oldestTransactionAt . '"
				WHERE p.id = ' . $partnerSystem->partner_system_id . '
			', routingKey: SqlQuery::ROUTING_STATS);
		}
	}

	private function scheduleMetricsRefresh()
	{
		if ((int) date('H') >= 1) {
			return;
		}

		$metricsToRefresh = $this->context->query('
			SELECT v.id, m.name, v.started_at AS started_at FROM tipli_reports_metric_value v
			INNER JOIN tipli_reports_metric m ON (m.id = v.metric_id)
			WHERE v.refresh_at IS NULL
			AND v.localization_id IS NOT NULL
			AND m.is_snapshot = 0
		')->fetchAll();

		$ids = [];

		foreach ($metricsToRefresh as $metric) {
			if (
				Strings::contains($metric->name, 'mailkit') || Strings::contains($metric->name, 'lucky')
				&& $metric->started_at <= new \DateTime('2025-04-01 00:00:00')
			) {
				continue;
			}

			$ids[] = $metric->id;
		}

		// global metrics
		$globalMetricsToRefresh = $this->context->query('
			SELECT v.id FROM tipli_reports_metric_value v
			INNER JOIN tipli_reports_metric m ON (m.id = v.metric_id)
			WHERE v.refresh_at IS NULL
			AND v.localization_id IS NULL
			AND v.shop_id IS NULL
			AND v.partner_system_id IS NULL
			AND v.partner_system_type IS NULL
			AND m.is_snapshot = 0
		')->fetchAll();

		foreach ($globalMetricsToRefresh as $globalMetric) {
			$ids[] = $globalMetric->id;
		}

		foreach (array_chunk($ids, 100) as $idsChunk) {
			$this->scheduleSqlQuery('
				UPDATE tipli_reports_metric_value
				SET refresh_at = NOW()
				WHERE id IN (' . implode(',', $idsChunk) . ')
			', [], SqlQuery::ROUTING_METRICS);
		}

		$this->scheduleSqlQuery('
			DELETE FROM tipli_reports_metric_value
			WHERE shop_id IS NOT NULL
			AND started_at < "' . (new \DateTime(SynchronizeMetrics::SHOP_RETENTION_TIME_SHIFT))->format('Y-m-d') . '"
		');
		$this->scheduleSqlQuery('
			DELETE FROM tipli_reports_metric_value
			WHERE partner_system_id IS NOT NULL
			AND started_at < "' . (new \DateTime(SynchronizeMetrics::PARTNER_SYSTEM_RETENTION_TIME_SHIFT))->format('Y-m-d') . '"
		');
		$this->scheduleSqlQuery('
			DELETE FROM tipli_reports_metric_value
			WHERE partner_system_type IS NOT NULL
			AND started_at < "' . (new \DateTime(SynchronizeMetrics::PARTNER_SYSTEM_TYPE_RETENTION_TIME_SHIFT))->format('Y-m-d') . '"
		');
	}

	private function updateLeaflets()
	{
		$this->scheduleSqlQuery('
			UPDATE tipli_leaflets_leaflet l
			SET l.count_of_pages = (SELECT count(p.id) FROM tipli_leaflets_leaflet_page p WHERE p.leaflet_id=l.id)
			WHERE l.completed_at IS NOT NULL
		', routingKey: SqlQuery::ROUTING_STATS);
	}

	private function updateRewardCampaigns()
	{
		$this->scheduleSqlQuery('
		UPDATE tipli_rewards_share_reward_campaign c
		SET c.individual_shops = 1
		WHERE c.id IN (SELECT DISTINCT s.share_reward_campaign_id FROM tipli_rewards_share_reward_campaign_shops s)
		', routingKey: SqlQuery::ROUTING_STATS);
	}

	private function updateTransactionData(): void
	{
		$this->scheduleSqlQuery('
            UPDATE tipli_transactions_transaction_data td
            INNER JOIN tipli_transactions_transaction t ON (t.id=td.transaction_id)
            SET td.is_paid = 1
            WHERE t.type != "payout"
            AND t.confirmed_at <= now()
            AND td.is_paid = 0
            AND t.billable = 1
            AND t.user_id IN (
                  SELECT DISTINCT p.user_id
                  FROM tipli_payouts_payout p
                  WHERE p.confirmed_at IS NOT NULL
                  AND p.confirmed_at <= now()
                  AND p.confirmed_at >= "' . (new \DateTime('- 12 hours'))->format('Y-m-d H:i:s') . '"
               )
        ');
	}

	private function updateBalances(): void
	{
		$usersToUpdate = $this->context->query('
			SELECT DISTINCT user_id FROM tipli_transactions_transaction
			where created_at >= ?
			', new \DateTime('- 2 hours'))->fetchAll();

		foreach ($usersToUpdate as $row) {
			$amount = $this->context->query('SELECT SUM(t.user_commission_amount)+SUM(t.bonus_amount) AS amount FROM tipli_transactions_transaction t WHERE t.user_id = ' . $row->user_id . ' AND t.billable=1')->fetch()->amount;
			$this->scheduleSqlQuery('UPDATE tipli_account_user_data ud SET ud.balance = ? where ud.user_id = ?', [$amount, $row->user_id], routingKey: SqlQuery::ROUTING_STATS);
		}
	}
}
