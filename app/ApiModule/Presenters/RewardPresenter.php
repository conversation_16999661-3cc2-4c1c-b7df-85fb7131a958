<?php

namespace tipli\ApiModule\Presenters;

use Nette\Localization\Translator;
use Nette\Application\Responses\JsonResponse;
use Nette\Caching\Cache;
use Nette\Caching\Storage;
use tipli\Model\Layers\UtmLayer;
use tipli\Model\Localization\LocalizationFacade;
use tipli\Model\Shops\Entities\Shop;

class RewardPresenter extends BasePresenter
{
	/** @var UtmLayer @inject */
	public $utmLayer;

	/** @var LocalizationFacade @inject */
	public $localizationFacade;

	/** @var Translator @inject */
	public $translator;

	/** @var Storage @inject */
	public $storage;

	public function renderReward(Shop $shop)
	{
		$cache = new Cache($this->storage, self::class);
		$cacheKey = $this->getCashbackCacheKey();

		if ($cacheKey) {
			$cacheKey .= $shop->getId();

			if ($data = $cache->load($cacheKey)) {
				$this->sendResponse(new JsonResponse([
					'reward' => $data,
				]));
			}
		}

		if (!$shop->isPaused() && $shop->isCashbackActive()) {
			$data = $this->rewardFilter->__invoke($shop, true, 'complete');
		} else {
			$data = '';
			$countOfLeaflets = $shop->getCountOfLeaflets();
			$countOfCouponDeals = $shop->getCountOfCouponDeals();
			$countOfDeals = $shop->getCountOfDeals() - $countOfCouponDeals;

			if ($countOfCouponDeals) {
				$data .= '<span class="_text">' . $this->translator->translate('model.shops.rewardFilter.countOfCoupons', $countOfCouponDeals) . '</span>';
			}

			if ($countOfLeaflets) {
				if ($countOfCouponDeals) {
					$data .= ' ' . $this->translator->translate('model.shops.rewardFilter.and') . ' ';
				}

				$data .= '<span class="_text">' . $this->translator->translate('model.shops.rewardFilter.countOfLeaflets', $countOfLeaflets) . '</span>';
			} elseif ($countOfDeals) {
				if ($countOfCouponDeals) {
					$data .= ' ' . $this->translator->translate('model.shops.rewardFilter.and') . ' ';
				}

				$data .= '<span class="_text">' . $this->translator->translate('model.shops.rewardFilter.countOfDeals', $countOfDeals) . '</span>';
			}
		}

		if ($cacheKey) {
			$cache->save($cacheKey, $data, [Cache::EXPIRE => '4 hours']);
		}

		$this->sendResponse(new JsonResponse([
			'reward' => $data,
		]));
	}
}
