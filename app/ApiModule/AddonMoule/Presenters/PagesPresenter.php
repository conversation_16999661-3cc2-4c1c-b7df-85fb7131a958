<?php

namespace tipli\ApiModule\AddonModule\Presenters;

use Nette\Caching\Cache;
use tipli\Model\Addon\Events\AddonFeedRenderedEvent;
use tipli\Model\Addon\FeedBuilders\PagesResponseBuilder;
use tipli\Model\Addon\ShopsProvider;

class PagesPresenter extends BasePresenter
{
	/** @var ShopsProvider @inject */
	public $shopsProvider;

	/** @var PagesResponseBuilder @inject */
	public $pagesResponseBuilder;

	public function actionDefault()
	{
		$this->requireMethod(self::METHOD_GET);

		$user = $this->getUserFromRequest();

		if ($user !== null) {
			$this->eventDispatcher->dispatch(
				new AddonFeedRenderedEvent($user)
			);
		}

		$this->datadogProducer->scheduleSendEvent('addon2.get.pages');

		$cache = new Cache($this->storage, self::class);

		$cacheKey = 'pages.' . $this->getLocalization()->getId();
		if ($this->getUserFromRequest() && $this->getUserFromRequest()->isAdmin()) {
			$cacheKey .= '.a';
		}

		if ($data = $cache->load($cacheKey)) {
			$this->sendData($data);
		}

		$shops = $this->shopsProvider->getShopsFeed($this->getLocalization(), $this->getUserIdentity());
		$data = $this->pagesResponseBuilder->buildPagesFeed($shops);

		$cache->save($cacheKey, $data, [Cache::EXPIRE => '4 hours']);

		$this->sendData($data);
	}
}
