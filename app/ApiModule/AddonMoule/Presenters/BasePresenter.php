<?php

namespace tipli\ApiModule\AddonModule\Presenters;

use Nette\Caching\Storage;
use Nette\Security\IIdentity;
use tipli\Model\Account\Entities\User;
use tipli\Model\HtmlBuilders\ContentFilter;
use tipli\Model\Layers\SafariSessionTokenLayer;
use tipli\Model\Layers\UtmLayer;
use tipli\Model\Localization\LocalizationFacade;

class BasePresenter extends \tipli\ApiModule\Presenters\BasePresenter
{
	protected const METHOD_GET = 'GET';
	protected const METHOD_POST = 'POST';
	protected const METHOD_PUT = 'PUT';
	protected const METHOD_DELETE = 'DELETE';

	/** @var Storage @inject */
	public $storage;

	/** @var LocalizationFacade @inject */
	public $localizationFacade;

	/** @var SafariSessionTokenLayer @inject */
	public $safariSessionTokenLayer;

	/** @var UtmLayer @inject */
	public $utmLayer;

	public function startup()
	{
		parent::startup();

		if ($user = $this->getUserIdentity()) {
//			Debugger::log($this->getHttpRequest()->getMethod() . ' # ' . $user->getId() . ' # ' . $this->getHttpRequest()->getHeader('x-addon-version'), 'addon-ep-log');
		}

//		Debugger::log(json_encode($this->getHttpRequest()->getHeaders()), 'addon-ep-log-parameters');
	}

	protected function isPostMethod()
	{
		return $this->getHttpRequest()->isMethod(self::METHOD_POST);
	}

	protected function isGetMethod()
	{
		return $this->getHttpRequest()->isMethod(self::METHOD_GET);
	}

	protected function isPutMethod()
	{
		return $this->getHttpRequest()->isMethod(self::METHOD_PUT);
	}

	protected function isDeleteMethod()
	{
		return $this->getHttpRequest()->isMethod(self::METHOD_DELETE);
	}

	protected function sendData($data = null, $success = true, $code = 200)
	{
		$this->getHttpResponse()->setCode($code);

		$response = [];

		if ($data !== null) {
			$response = $data;
		}

		$this->sendJson($response);
	}

	protected function sendSuccess($code = 204)
	{
		$this->sendData(null, $code);
	}

	protected function sendError(string $message, $code = 400)
	{
		$this->sendData(['errorMessage' => $message], false, 400);
	}

	protected function getRequestData($key = null)
	{
		if ($key !== 'password') {
			$data = json_decode(ContentFilter::removeEmoji($this->getHttpRequest()->getRawBody())) ? : [];
		} else {
			$data = json_decode($this->getHttpRequest()->getRawBody()) ? : [];
		}

		if (!$key) {
			return $data;
		}

		return $data->{$key} ?? null;
	}

	public function getRawRequestData()
	{
		return $this->getHttpRequest()->getRawBody();
	}

	protected function getLocalization()
	{
		return $this->localizationFacade->getCurrentLocalization();
	}

	protected function getPagingOffset()
	{
		return $this->getParameter('offset') ? : 0;
	}

	protected function getPagingLimit($defaultLimit = 100)
	{
		return $this->getParameter('limit') ?: $defaultLimit;
	}

	protected function requireMethod($method)
	{
		if ($method !== $this->getHttpRequest()->getMethod()) {
			$this->sendError('Unsupported method', 405);
		}
	}

	protected function requireDataParameters($parameters)
	{
		$data = $this->getRequestData();

		foreach ($parameters as $parameter) {
			if (!property_exists($data, $parameter)) {
				$this->sendError('Required data not provided (' . $parameter . ')');
			}
		}
	}

	protected function getUserFromRequest(): IIdentity|User|null
	{
		$user = $this->safariSessionTokenLayer->getUserBySafariSessionToken();

		return $user !== null ? $user : $this->getUserIdentity();
	}
}
