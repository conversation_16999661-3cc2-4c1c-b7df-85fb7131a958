<?php

namespace tipli\AdminModule\Presenters;

use Nette\Application\LinkGenerator;
use tipli\Commands\ImportVisits;
use tipli\Commands\PayoutConfirmation;
use tipli\Commands\ProcessEmailCampaignStats;
use tipli\Commands\ProcessLuckyShopCampaign;
use tipli\Commands\ScheduleAutoresponders;
use tipli\Commands\SendOpsGeniePingEmails;
use tipli\Commands\UnsubscribeMailchimpUsers;
use tipli\Commands\ProcessProducts;
use tipli\Commands\SynchronizeFioPayments;
use tipli\Commands\SynchronizeFreshdeskTickets;
use tipli\Model\Account\Entities\SendingPolicy;
use tipli\Model\Account\Subscribers\UserSubscriber;
use tipli\Model\Account\UserFacade;
use tipli\Model\Campaign\CampaignFacade;
use tipli\Model\Currencies\CurrencyFacade;
use tipli\Model\Datadog\DatadogProducer;
use tipli\Model\Deals\DealFacade;
use tipli\Model\GoogleTagManager\GoogleTagManagerClient;
use tipli\Model\Inbox\Entities\Notification;
use tipli\Model\Inbox\NotificationFactory;
use tipli\Model\LuckyShop\LuckyShopEmailProvider;
use tipli\Model\LuckyShop\LuckyShopFacade;
use tipli\Model\LuckyShop\LuckyShopNotificationProvider;
use tipli\Model\LuckyShop\Producers\LuckyShopEmailProducer;
use tipli\Model\LuckyShop\Producers\LuckyShopNotificationProducer;
use tipli\Model\LuckyShop\Producers\UserLuckyShopProducer;
use tipli\Model\Marketing\NewsletterFacade;
use tipli\Model\Messages\EmailManager;
use tipli\Model\Messages\EmailTemplateFactory;
use tipli\Model\Messages\Repositories\EmailRepository;
use tipli\Model\Messages\UserAutoResponderSender;
use tipli\Model\PartnerSystems\Networks\EHub;
use tipli\Model\Refunds\RefundResponseProvider;
use tipli\Model\Freshdesk\FreshdeskFacade;
use tipli\Model\Inbox\NotificationFacade;
use tipli\Model\Inbox\NotificationManager;
use tipli\Model\Inbox\Producers\NotificationsProducer;
use tipli\Model\Messages\EmailFactory;
use tipli\Model\Messages\Entities\UserAutoResponder;
use tipli\Model\Messages\IMandrillEmailSender;
use tipli\Model\Messages\MandrillClient;
use tipli\Model\Messages\MessageFacade;
use tipli\Model\Messages\MjmlConverter;
use tipli\Model\PartnerSystems\PartnerSystemFacade;
use tipli\Model\Payouts\PayoutFacade;
use tipli\Model\Products\CategoryFacade;
use tipli\Model\Products\ProductFacade;
use tipli\Model\Refunds\RefundFacade;
use tipli\Model\Reviews\ReviewFacade;
use tipli\Model\Rewards\Entities\MoneyReward;
use tipli\Model\Rewards\RewardFacade;
use tipli\Model\Risk\RiskFacade;
use tipli\Model\Shops\RewardFilter;
use tipli\Model\Shops\ShopChecker;
use tipli\Model\Shops\ShopFacade;
use tipli\Model\Shops\ShopProductFeedFacade;
use tipli\Model\Tags\TagFacade;
use tipli\Model\Templates\NotificationTemplateFacade;
use tipli\Model\Transactions\Entities\Transaction;
use tipli\Model\Transactions\TransactionFacade;
use App\Model\Refunds\RefundsExportFileGenerator;
use tipli\Model\Files\FileStorage;
use Nette\Database\Context;
use tipli\Model\Transactions\TransactionTriggerProcessor;

class SandboxPresenter extends BasePresenter
{
	/** @var FreshdeskFacade @inject */
	public $freshdeskFacade;

//	/** @var TransactionTriggersProducer @inject */
//	public $transactionTriggersProducer;

	/** @var UserFacade @inject */
	public UserFacade $userFacade;

	/** @var MessageFacade @inject */
	public $messageFacade;

	/** @var CampaignFacade @inject */
	public $campaignFacade;

	/** @var TransactionFacade @inject */
	public $transactionFacade;

	/** @var EmailFactory @inject */
	public $emailFactory;

	/** @var MjmlConverter @inject */
	public $mjmlConverter;

	/** @var ShopFacade @inject */
	public $shopFacade;

	/** @var PartnerSystemFacade @inject */
	public $partnerSystemFacade;

	/** @var ProductFacade @inject */
	public $productFacade;

	/** @var IMandrillEmailSender @inject */
	public $mandrillEmailSender;

	/** @var MandrillClient @inject */
	public $mandrillClient;

	/** @var CategoryFacade @inject */
	public $categoryFacade;

	/** @var TagFacade @inject */
	public $tagFacade;

	/** @var ShopProductFeedFacade @inject */
	public $shopProductFeedFacade;

	/** @var PayoutFacade @inject */
	public $payoutFacade;

	/** @var DatadogProducer @inject */
	public $datadogProducer;

	/** @var NotificationManager @inject */
	public $notificationManager;

	/** @var NotificationFacade @inject */
	public $notificationFacade;

	/** @var RewardFilter @inject */
	public $rewardFilter;

	/** @var ShopChecker @inject */
	public $shopChecker;

	/** @var RefundFacade @inject */
	public $refundFacade;

	/** @var CurrencyFacade @inject */
	public $currencyFacade;

	/** @var NotificationsProducer @inject */
	public $notificationsProducer;

	/** @var DealFacade @inject */
	public $dealFacade;

	/** @var LinkGenerator @inject */
	public $linkGenerator;

	/** @var GoogleTagManagerClient @inject */
	public $googleTagManagerClient;

	/** @var PayoutConfirmation @inject */
	public $payoutConfirmation;

	/** @var RewardFacade @inject */
	public $rewardFacade;

	/** @var \tipli\Model\Rewards2\RewardFacade @inject */
	public $rewardFacade2;

	/** @var RefundResponseProvider @inject */
	public $refundResponseProvider;

	/** @var SynchronizeFreshdeskTickets @inject */
	public $synchronizeFreshdeskTickets;

	/** @var SynchronizeFioPayments @inject */
	public $synchronizeFioPayments;

	/** @var ImportVisits @inject */
	public $importVisits;

	/** @var ProcessProducts @inject */
	public $processProducts;

	/** @var ProcessEmailCampaignStats @inject */
	public $processEmailCampaignStats;

	/** @var RefundsExportFileGenerator @inject */
	public $refundsExportFileGenerator;

	/** @var FileStorage @inject */
	public $fileStorage;

	/** @var RiskFacade @inject */
	public $riskFacade;

	/** @var UnsubscribeMailchimpUsers @inject */
	public $unsubscribeMailchimpUsers;

	/** @var ProcessLuckyShopCampaign @inject */
	public $processLuckyShopCampaign;

	/** @var LuckyShopFacade @inject */
	public $luckyShopFacade;

	/** @var UserLuckyShopProducer @inject */
	public $userLuckyShopProducer;

	/** @var LuckyShopEmailProvider @inject */
	public $luckyShopEmailProvider;

	/** @var Context @inject */
	public Context $context;

	/** @var UserAutoResponderSender @inject */
	public UserAutoResponderSender $userAutoResponderSender;

	/** @var SendOpsGeniePingEmails @inject */
	public SendOpsGeniePingEmails $sendOpsGeniePingEmails;

	/** @var UserSubscriber @inject */
	public UserSubscriber $userSubscriber;

	/** @var TransactionTriggerProcessor @inject */
	public TransactionTriggerProcessor $transactionTriggerProcessor;

	/** @var NewsletterFacade @inject */
	public NewsletterFacade $newsletterFacade;

	/** @var EmailManager @inject */
	public EmailManager $emailManager;

	/** @var ScheduleAutoresponders @inject */
	public ScheduleAutoresponders $scheduleAutoresponders;

	/** @var EmailTemplateFactory @inject */
	public EmailTemplateFactory $emailTemplateFactory;

	/** @var ReviewFacade @inject */
	public ReviewFacade $reviewFacade;

	/** @var LuckyShopEmailProducer @inject */
	public LuckyShopEmailProducer $luckyShopEmailProducer;

	/** @var EmailRepository @inject */
	public EmailRepository $emailRepository;

	/** @var LuckyShopNotificationProvider @inject */
	public LuckyShopNotificationProducer $luckyShopNotificationProducer;

	/** @var NotificationTemplateFacade @inject */
	public NotificationTemplateFacade $notificationTemplateFacade;

	/** @var NotificationFactory @inject */
	public NotificationFactory $notificationFactory;

	public function actionTestPayoutEmail(): void
	{
		$emailDataObject = $this->emailTemplateFactory->buildPayoutReviewRequestEmail($this->reviewFacade->findReviewRequest(961775));
		echo $emailDataObject->getHtml();
		exit();
	}

	public function renderCdn(string $imageUrl, ?int $width = null, ?int $height = null, ?string $flag = 'fit', bool $absolute = false, ?int $quality = null)
	{
		bdump($imageUrl);
		bdump($width);
		bdump($height);
		bdump($flag);
		bdump($absolute);
		bdump($quality);
		$cdnUrl = $this->imageFilter->__invoke($imageUrl, $width, $height, $flag, false, null, false, false, $quality);
		bdump($cdnUrl);
		$this->template->width = $width;
		$this->template->height = $height;
		$this->template->flag = $flag;
		$this->template->absolute = $absolute;
		$this->template->quality = $quality;
		$this->template->imageUrl = $imageUrl;
		$this->template->cdnUrl = $cdnUrl;
//		exit();
	}

	public function renderDefault()
	{
		$shop = $this->shopFacade->find(1);
		$logoUrl = $this->linkGenerator->link('Api:ShopImage:default', ['shopId' => $shop->getId()]);
		dump($logoUrl);
		$cdnLogo = $this->imageFilter->__invoke($logoUrl, 200, 100, 'fitMiddle', false, null, false, false, 100);
		dump($cdnLogo);
		exit();
		//throw new InvalidArgumentException('Test Tipli sandbox');
	}

	public function actionTriggers()
	{
		$this->scheduleAutoresponders->startAutoresponder('reactivationCampaign');
		$this->terminate();
	}

	public function actionSendChangeEmail()
	{
		$this->messageFacade->sendNewPasswordEmail($this->userFacade->find(2747780));
		$this->messageFacade->sendAccountNumberVerificationEmail($this->userFacade->find(2747780));
	}

	public function actionSendWelcomeEmail()
	{
		$this->userAutoResponderSender->sendReactivationCampaignAutoresponder($this->userFacade->find(2747780));
		$this->userAutoResponderSender->sendReactivationCampaignAutoresponder($this->userFacade->find(23030));
//		$this->userAutoResponderSender->sendMobileAppAutoresponder($this->userFacade->find(2747780));
//
//		$user = $this->userFacade->find(3915018);
//		$userCreatedEvent = new UserCreatedEvent($user);
//		$this->userSubscriber->sendRecommenderNotification($userCreatedEvent);
//
//		$user = $this->userFacade->find(2747780);
//		$recommendedUser = $this->userFacade->find(3915018);
//
//		if ($user && $recommendedUser) {
//			$emailDataObject = $this->newsletterFacade->getEmail(
//				$user->getLocalization(),
//				'transaction-rb-registration',
//				[
//					'currency' => $user->getLocalization()->getCurrency(),
//					'bonusAmount' => $bonusAmount = $this->rewardFacade->findConstantRewardByType($user->getLocalization(), ConstantReward::TYPE_BONUS_RECOMMENDATION, $recommendedUser->getCreatedAt())->getAmount(),
//					'referralEmail' => $recommendedUser->getEmail(),
//					'vocalName' => $user->getVocalFirstName(),
//					'accessToken' => $user->getAccessToken(),
//				]
//			);
//
//			$duplicityHash = substr(sha1('bonus_registered' . $user->getId()) . $recommendedUser->getId(), -32);
//
//			$this->messageFacade->sendEmailByDTO($user, $emailDataObject, $duplicityHash);
//		}
//		$this->userAutoResponderSender->sendWelcomeAutoresponder($this->userFacade->find(4289527));
//		$this->userAutoResponderSender->sendAddonAutoresponder($this->userFacade->find(4289527));
//		$this->userAutoResponderSender->sendLuckyShopAutoresponder($this->userFacade->find(4289527));
//		$this->userAutoResponderSender->sendDoubleExtraRewardAutoresponder($this->userFacade->find(4289527));
//		$this->userAutoResponderSender->sendBonusAfterRegistrationAutoresponder($this->userFacade->find(4289527));
//		$this->userAutoResponderSender->sendBonusAfterRegistrationLastDayAutoresponder($this->userFacade->find(4289527));
//
//		$this->userAutoResponderSender->sendWelcomeAutoresponder($this->userFacade->find(4278935));
//		$this->userAutoResponderSender->sendAddonAutoresponder($this->userFacade->find(4278935));
//		$this->userAutoResponderSender->sendLuckyShopAutoresponder($this->userFacade->find(4278935));
//		$this->userAutoResponderSender->sendDoubleExtraRewardAutoresponder($this->userFacade->find(4278935));
//		$this->userAutoResponderSender->sendBonusAfterRegistrationAutoresponder($this->userFacade->find(4278935));
//		$this->userAutoResponderSender->sendBonusAfterRegistrationLastDayAutoresponder($this->userFacade->find(4278935));
//		$this->userAutoResponderSender->sendWelcomeAutoresponder($this->userFacade->find(2747780));
//		$this->userAutoResponderSender->sendWelcomeAutoresponder($this->userFacade->find(4278935));
	}

	public function actionSendAutoResponderDealsEmail(array $dealIds)
	{
		//$deals = $this->dealFacade->findByIds($dealIds);

		foreach ([2747780, 4289527, 3931168, 4395928, 3931174, 4203933, 4203019, 4890924] as $userId) {
			$user = $this->userFacade->find($userId);

			$topCoupons = $this->dealFacade->findTopCoupons($user->getLocalization());

			$count = rand(1, 3);
			$selectedKeys = array_rand($topCoupons, $count);

			$deals = is_array($selectedKeys) ? array_intersect_key($topCoupons, array_flip($selectedKeys)) : [$topCoupons[$selectedKeys]];

			$this->userAutoResponderSender->sendDealsAutoresponder($user, $deals);
		}
	}

	public function actionEhub($from = null, $to = null)
	{
		$partnerSystem = $this->partnerSystemFacade->find(7);

		if ($from === null) {
			$from = (new \DateTime())->modify('-1 month');
		} else {
			$from = new \DateTime($from);
		}

		if ($to === null) {
			$to = new \DateTime();
		} else {
			$to = new \DateTime($to);
		}

		dump("FROM: " . $from->format('Y-m-d H:i:s'));
		dump("TO: " . $to->format('Y-m-d H:i:s'));

		/** @var EHub $network */
		$network = $this->partnerSystemFacade->getNetwork($partnerSystem);

		$records = [];
		if ($from && $to) {
			for ($i = 1; $i < 9999; $i++) {
				$foundRecords = $network->getRecords($from, $to, $i);

				if (empty($foundRecords)) {
					dump("PAGE: $i");
					break;
				}
				$records = array_merge($records, $foundRecords);
			}
		}

		dump("---------------------------------");

		$shopIds = $this->context->query('
			SELECT id
			FROM tipli_shops_shop
			WHERE partner_system_id = 7
		')->fetchPairs('id', 'id');

		$refundOrderIds = $this->context->query('
			SELECT id, order_id
			FROM tipli_refunds_refund
			WHERE state = "approved" AND
			      type = "missing_commission" AND
			      refund_transaction_id IS NOT NULL AND
			      shop_id IN (?) AND
			      created_at > ?
		', $shopIds, $from)->fetchPairs('id', 'order_id');

		$transactionsIds = $this->context->query('
			SELECT id, transaction_id
			FROM tipli_transactions_transaction
			WHERE partner_system_id = 7 AND
			      created_at > ? AND created_at < ?
		', $from, $to)->fetchPairs('id', 'transaction_id');

		dump("FOUND EHUB RECORDS - " . count($records));
		dump("FOUND TRANSACTIONS IDS - " . count($transactionsIds));
		dump("FOUND REFUND ORDER IDS - " . count($refundOrderIds));
		dump("---------------------------------");

		$total = 0;
		$refundsPaired = 0;
		$amountFromRefunds = 0;
		$transactionsPaired = 0;
		$amountFromTransactions = 0;

		foreach ($records as $record) {
			if (in_array($record->id, $transactionsIds)) {
				$transactionsPaired++;
				$amountFromTransactions += (float)$record->commission;
				continue;
			}
			if (in_array($record->orderId, $refundOrderIds)) {
				$refundsPaired++;
				$amountFromRefunds += (float)$record->commission;
				continue;
			}
			$total += (float)$record->commission;
		}
		dump("TRANSACTIONS PAIRED: $transactionsPaired");
		dump("REFUNDS PAIRED: $refundsPaired");
		dump("---------------------------------");

		dump("AMOUNT FROM TRANSACTIONS: $amountFromTransactions");
		dump("AMOUNT FROM REFUNDS: $amountFromRefunds");
		dump("REST IMPORT TOTAL: $total");
		exit();
	}

	public function actionResendPayouts()
	{
//		$userIds = [
//			26118,44949,48391,62527,64017,69565,72267,74913,75556,85096,86578,86694,99303,112682,118130,118961,121484,129877,131817,133382,133807,136630,152118,166696,167033,173447,175745,183008,195875,212050,224783,226827,227010,231399,234762,235921,266218,282242,330951,331532,366956,370115,392499,430939,457587,479919,482917,486899,504817,537648,538431,538594,568541,594512,606319,609588,619101,629129,660993,665849,680188,693259,694088,697285,700653,702243,712819,726962,729547,732469,739436,762093,802603,807812,843331,916481,943286,1001472,1014994,1032211,1063845,1155744,1156249,1179230,1191480,1196145,1210369,1216871,1241710,1245713,1269845,1279410,1285724,1318357,1323122,1339821,1385439,1391819,1457640,1509198,1509781,1523315,1531824,1532502,1534630,1564788,1567203,1597455,1612615,1639729,1639786,1644413,1688371,1713899,1722456,1752235,1786341,1804073,1860801,1881601,1964678,1970794,1971565,1993386,2025356,2037400,2053848,2088213,
//		];
//
//		foreach ($userIds as $userId) {
//			$user = $this->userFacade->find($userId);
//
//			try {
//				$this->payoutFacade->createPayoutForUser($user);
//			} catch (InvalidArgumentException | \Exception $e) {
//			}
//		}

		$this->terminate();
	}

//	public function actionSendWelcomeEmail()
//	{
////        $this->messageFacade->sendRegistrationEmail($this->getUserIdentity());
////
////        $this->terminate();
//
//		$user = $this->userFacade->getUsers()
//			->andWhere('u.localization = :localizationId')->setParameter('localizationId', $this->getParameter('localizationId') ?? 1)
//			->andWhere('u.password IS NOT NULL')
//			->setMaxResults(1)
//			->getQuery()
//			->getOneOrNullResult();
//
//		$email = $this->emailFactory->createRegistrationEmail($user, true);
//
//		echo $this->mjmlConverter->mjmlToHtml($email->getBody());
//
//		$this->terminate();
//	}

	public function actionTestHowToVideoAutoresponder()
	{
		$user = $this->userFacade->getUsers()
			->andWhere('u.localization = :localizationId')
			->setParameter('localizationId', $this->getParameter('localizationId') ?? 1)
			->setMaxResults(1)
			->getQuery()
			->getOneOrNullResult();

		$email = $this->emailFactory->createHowToVideoAutoResponderEmail($user);

		echo $this->mjmlConverter->mjmlToHtml($email->getBody());

		$this->terminate();
	}

	public function actionTestEmail()
	{
		if (!$this->getParameter('localizationId') || $this->getUserIdentity()->getLocalization()->getId() === $this->getParameter('localizationId')) {
			$user = $this->getUserIdentity();
		} else {
			$user = $this->userFacade->getUsers()
				->andWhere('u.localization = :localizationId')->setParameter('localizationId', $this->getParameter('localizationId') ?? 1)
				->setMaxResults(1)
				->getQuery()
				->getOneOrNullResult();
		}

		$shops = $this->shopFacade->getShops($user->getLocalization())
			->getQuery()
			->getResult();

		$transaction = $this->transactionFacade->find(7521191);

		$payout = $this->payoutFacade->find(558491);

		$shareReward = $this->rewardFacade->findShareReward(2);

		$userReward = $this->rewardFacade2->findUserReward(1);

		$email = $this->emailFactory->createVerificationEmail($user);

		echo $this->mjmlConverter->mjmlToHtml($email->getBody());
		echo "<hr />";
		echo $this->mjmlConverter->mjmlToHtml($this->emailFactory->createPayoutCreationEmail($payout));
		echo "<hr />";
		echo $this->mjmlConverter->mjmlToHtml($this->emailFactory->createPayoutConfirmationEmail($payout));

		$this->terminate();
	}

	public function actionTestAddonAutoresponder()
	{
		if (!$this->getParameter('localizationId') || $this->getUserIdentity()->getLocalization()->getId() === $this->getParameter('localizationId')) {
			$user = $this->getUserIdentity();
		} else {
			$user = $this->userFacade->getUsers()
				->andWhere('u.localization = :localizationId')->setParameter('localizationId', $this->getParameter('localizationId') ?? 1)
				->setMaxResults(1)
				->getQuery()
				->getOneOrNullResult();
		}

		$email = $this->emailFactory->createAddonAutoResponderEmail($user);

		$this->emailManager->scheduleSendEmail($email);

		echo $this->mjmlConverter->mjmlToHtml($email->getBody());
		$this->terminate();
	}

	public function actionTestElectronicsAutoresponder()
	{
		if (!$this->getParameter('localizationId') || $this->getUserIdentity()->getLocalization()->getId() === $this->getParameter('localizationId')) {
			$user = $this->getUserIdentity();
		} else {
			$user = $this->userFacade->getUsers()
				->andWhere('u.localization = :localizationId')->setParameter('localizationId', $this->getParameter('localizationId') ?? 1)
				->setMaxResults(1)
				->getQuery()
				->getOneOrNullResult();
		}

		$email = $this->emailFactory->createElectronicsResponderEmail($user);

		echo $this->mjmlConverter->mjmlToHtml($email->getBody());
		$this->terminate();
	}

	public function actionTestTravelAutoresponder()
	{
		if (!$this->getParameter('localizationId') || $this->getUserIdentity()->getLocalization()->getId() === $this->getParameter('localizationId')) {
			$user = $this->getUserIdentity();
		} else {
			$user = $this->userFacade->getUsers()
				->andWhere('u.localization = :localizationId')->setParameter('localizationId', $this->getParameter('localizationId') ?? 1)
				->setMaxResults(1)
				->getQuery()
				->getOneOrNullResult();
		}

		$email = $this->emailFactory->createTravelResponderEmail($user);

		echo $this->mjmlConverter->mjmlToHtml($email->getBody());
		$this->terminate();
	}

	public function actionTestAliexpressAutoresponder()
	{
		if (!$this->getParameter('localizationId') || $this->getUserIdentity()->getLocalization()->getId() === $this->getParameter('localizationId')) {
			$user = $this->getUserIdentity();
		} else {
			$user = $this->userFacade->getUsers()
				->andWhere('u.localization = :localizationId')->setParameter('localizationId', $this->getParameter('localizationId') ?? 1)
				->setMaxResults(1)
				->getQuery()
				->getOneOrNullResult();
		}

		$email = $this->emailFactory->createAliExpressResponderEmail($user);

		echo $this->mjmlConverter->mjmlToHtml($email->getBody());
		$this->terminate();
	}

	public function actionTestFashionAutoresponder()
	{
		if (!$this->getParameter('localizationId') || $this->getUserIdentity()->getLocalization()->getId() === $this->getParameter('localizationId')) {
			$user = $this->getUserIdentity();
		} else {
			$user = $this->userFacade->getUsers()
				->andWhere('u.localization = :localizationId')->setParameter('localizationId', $this->getParameter('localizationId') ?? 1)
				->setMaxResults(1)
				->getQuery()
				->getOneOrNullResult();
		}

		$email = $this->emailFactory->createFashionAutoResponderEmail($user);

		echo $this->mjmlConverter->mjmlToHtml($email->getBody());
		$this->terminate();
	}

	public function actionTestHealthAutoresponder()
	{
		if (!$this->getParameter('localizationId') || $this->getUserIdentity()->getLocalization()->getId() === $this->getParameter('localizationId')) {
			$user = $this->getUserIdentity();
		} else {
			$user = $this->userFacade->getUsers()
				->andWhere('u.localization = :localizationId')->setParameter('localizationId', $this->getParameter('localizationId') ?? 1)
				->setMaxResults(1)
				->getQuery()
				->getOneOrNullResult();
		}

		$email = $this->emailFactory->createHealthAutoResponderEmail($user);

		echo $this->mjmlConverter->mjmlToHtml($email->getBody());
		$this->terminate();
	}

	public function actionSendWelcomeEmailWithoutPassword()
	{
		$user = $this->userFacade->find(1149);

		$email = $this->emailFactory->createRegistrationEmail($user, true);

		$this->emailManager->scheduleSendEmail($email);

		echo $this->mjmlConverter->mjmlToHtml($email->getBody());

		$this->terminate();
	}

	public function actionSendWelcomeEmailWithMoneyReward()
	{
		$user = $this->userFacade->getUsers()
			->innerJoin(MoneyReward::class, 'mr', 'WITH', 'mr.user = u')
			->andWhere('u.localization = :localizationId')->setParameter('localizationId', $this->getParameter('localizationId') ?? 1)
			->andWhere('mr.amount > 300')
			->setMaxResults(1)
			->getQuery()
			->getOneOrNullResult();

		$email = $this->emailFactory->createRegistrationEmail($user, true);

		echo $this->mjmlConverter->mjmlToHtml($email->getBody());

		$this->terminate();
	}

	public function actionTestNewPasswordEmail()
	{
		$user = $this->userFacade->getUsers()
			->innerJoin(MoneyReward::class, 'mr', 'WITH', 'mr.user = u')
			->andWhere('u.localization = :localizationId')->setParameter('localizationId', $this->getParameter('localizationId') ?? 1)
			->andWhere('mr.amount > 300')
			->setMaxResults(1)
			->getQuery()
			->getOneOrNullResult();

		$email = $this->emailFactory->createNewPasswordEmail($user);

		echo $this->mjmlConverter->mjmlToHtml($email->getBody());

		$this->terminate();
	}

	public function actionTestHowToGetRewardsAutoresponder()
	{
		if (!$this->getParameter('localizationId') || $this->getUserIdentity()->getLocalization()->getId() === $this->getParameter('localizationId')) {
			$user = $this->getUserIdentity();
		} else {
			$user = $this->userFacade->getUsers()
				->andWhere('u.localization = :localizationId')->setParameter('localizationId', $this->getParameter('localizationId') ?? 1)
				->setMaxResults(1)
				->getQuery()
				->getOneOrNullResult();
		}

		$email = $this->emailFactory->createHowToGetRewardsResponderEmail($user);

		echo $this->mjmlConverter->mjmlToHtml($email->getBody());

		$this->terminate();
	}

	public function actionTestShopsAutoresponder()
	{
		if (!$this->getParameter('localizationId') || $this->getUserIdentity()->getLocalization()->getId() === $this->getParameter('localizationId')) {
			$user = $this->getUserIdentity();
		} else {
			$user = $this->userFacade->getUsers()
				->andWhere('u.localization = :localizationId')->setParameter('localizationId', $this->getParameter('localizationId') ?? 1)
				->setMaxResults(1)
				->getQuery()
				->getOneOrNullResult();
		}

		$email = $this->emailFactory->createShopsAutoResponderEmail($user);

		echo $email->getSubject();

		echo $this->mjmlConverter->mjmlToHtml($email->getBody());
		$this->terminate();
	}

	public function actionTestTransactionRegistrationEmail()
	{
		$email = $this->emailFactory->createTransactionRegistrationEmail($this->transactionFacade->find(18036057));

		echo $this->mjmlConverter->mjmlToHtml($email->getBody());
		$this->terminate();

//        $this->messageFacade->sendTransactionRegistrationEmail($this->transactionFacade->find(1035));
	}

	public function actionTestAfterRegistrationThreeDays()
	{
		if (!$this->getParameter('localizationId') || $this->getUserIdentity()->getLocalization()->getId() === $this->getParameter('localizationId')) {
			$user = $this->getUserIdentity();
		} else {
			$user = $this->userFacade->getUsers()
				->andWhere('u.localization = :localizationId')->setParameter('localizationId', $this->getParameter('localizationId') ?? 1)
				->setMaxResults(1)
				->getQuery()
				->getOneOrNullResult();
		}

		$countOfShops = $this->shopFacade->findCountOfShops($user->getLocalization(), true);
		$topShops = $this->shopFacade->findTopShops(20, $user->getLocalization(), false, null, true);
		$email = $this->emailFactory->createAfterRegistration3DaysAutoResponder($user, $topShops->toArray(), $countOfShops);

		echo $this->mjmlConverter->mjmlToHtml($email->getBody());
		$this->terminate();
	}

	public function actionProcessTransactions(?string $from, ?string $to)
	{
		$network = $this->partnerSystemFacade->getNetwork($this->partnerSystemFacade->find($this->getParameter('partnerSystemId')));

		$from = $from ? (new \DateTime($from))->setTime(0, 0) : null;
		$to = $to ? (new \DateTime($to))->setTime(23, 59, 59) : null;

		$network->processTransactions($from, $to);

		$this->terminate();
	}

	public function actionTestCampaignEmails()
	{
		$user = $this->userFacade->find(4143837);
		$campaignSubscription = $this->campaignFacade->findCampaignSubscriptionByUser($user);
		$transaction = $this->transactionFacade->find(18036057);
		$bonusTransaction = $this->transactionFacade->find(18036058);

		$email = $this->emailFactory->createCampaignAutoResponderEmail($campaignSubscription, UserAutoResponder::TYPE_CAMPAIGN_BONUS_CREATED_NEW_USER, $transaction, $bonusTransaction);

		echo $this->mjmlConverter->mjmlToHtml($email->getBody());

		$this->terminate();
	}

	public function actionTestNotification()
	{
		$notification = $this->notificationFacade->findNotificationCampaign(1);

		$this->notificationManager->createNotificationsFromCampaign($notification, [1, 2, 3]);

		$this->terminate();
	}

	public function actionCancelTransactions()
	{
		$transactionIds = [14131031, 14132160, 14118479, 14118426, 14132096, 14143987, 14143981, 14132132, 14130973, 14108981, 14131044, 14131020, 14136883, 14124054, 14294905, 14143954, 14132114, 14105294, 14123108, 14105579, 14105284, 14110166, 14111856, 14112527, 14113701, 14114419, 14117830, 14117911, 14121784, 14128871, 14136699, 14136714, 14139543, 14142038, 14144626, 14150784, 14106159, 14107001, 14107067, 14110161, 14113496, 14114143, 14114142, 14114315, 14119333, 14119399, 14122523, 14128473, 14130797, 14137109, 14142696, 14144582, 14144623, 14144790, 14294906, 14111971, 14117816, 14150785, 14118481, 14123650, 14142036, 14145563, 14141861, 14118509, 14132124, 14131037, 14132103, 14131019, 14035038, 14105376, 14107466, 14111005, 14111690, 14112984, 14117776, 14122372, 14129162, 14142128, 14118429, 14118468, 14129495, 14118530, 14143968, 14118645, 14118453, 14118639, 14118594, 14110187, 14111843, 14143966, 14118505, 14111256, 14143959, 14118501, 14130978, 14144013, 14132090, 14143983, 14132147, 14131030, 14108982, 14133341, 14131016, 14132154, 14136790, 14144009, 14118451, 14133345, 14131042, 14118428, 14132146, 14132122, 14143967, 14132113, 14108985, 14132150, 14118450, 14108984, 14143957, 14130044, 14118477, 14118427, 14128446, 14118583, 14144010, 14143952, 14132107, 14132159, 14132140, 14118532, 14118483, 14132108, 14118596, 14130974, 14118581, 14130987, 14118511, 14118595, 14118577, 14121795, 14141176, 14128091, 14118448, 14118647, 14108987, 14118531, 14118644, 14118449, 14118618, 14118486, 14132092, 14118549, 14109950, 14132118, 14130992, 14118580, 14131142, 14132091, 14143976, 14132137, 14143964, 14133344, 14143975, 14118546, 14131048, 14136417, 14141529, 14144583, 14118579, 14144012, 14132102, 14132093, 14143978, 14118597, 14131058, 14118506, 14132089, 14108998, 14118470, 14118484, 14118551, 14132151, 14132136, 14131057, 14132133, 14118574, 14123857, 14131010, 14143956, 14118504, 14143979, 14130991, 14108988, 14118643, 14143955, 14143965, 14143958, 14118648, 14131055, 14132119, 14132149, 14118550, 14144014, 14132143, 14118533, 14118446, 14132120, 14132152, 14131050, 14133536, 14131148, 14132129, 14130990, 14118528, 14132134, 14132111, 14130989, 14131041, 14131039, 14118503, 14132104, 14118431, 14144017, 14118621, 14132148, 14118425, 14132135, 14118626, 14118485, 14118514, 14118625, 14108991, 14143963, 14143961, 14132099, 14144016, 14132153, 14111371, 14118593, 14118439, 14108997, 14118469, 14118534, 14132109, 14144011, 14132117, 14132112, 14108983, 14143953, 14144015, 14118649, 14118488, 14143962, 14132131, 14118510, 14118561, 14130972, 14132106, 14130977, 14132157, 14144008, 14108986, 14118545, 14118543, 14132141, 14133340, 14118582, 14143977, 14131026, 14130986, 14118575, 14132139, 14132121, 14118430, 14130984, 14118513, 14131053, 14106253, 14143960, 14143982, 14131051, 14118573, 14144007, 14132142, 14118535, 14118452, 14118472, 14118558, 14132127, 14131046, 14131038, 14132095, 14132094, 14132125, 14118557, 14118544, 14130975, 14118620, 14132144, 14143969, 14118489, 14130988, 14118547, 14132115, 14118473, 14132156, 14131013, 14118540, 14118512, 14118475, 14143980, 14131145, 14145743, 14128320, 14127472, 14135332, 14126906, 14129163, 14137480, 14119430, 14140561];
		$partnerSystem = $this->partnerSystemFacade->find(72);

		foreach ($transactionIds as $transactionId) {
			/** @var Transaction|null $transaction */
			$transaction = $this->transactionFacade->findById($transactionId, $partnerSystem);

			if ($transaction === null || $transaction->isCancelled() || $transaction->isConfirmed()) {
				continue;
			}

			$this->transactionFacade->cancel($transaction);
		}

		$this->terminate();
	}

	public function actionConfirmTransactions()
	{
		$transactionIds = [21912077, 21915428, 21916123, 21957072, 21964771, 21964870, 21964871, 21973349, 21985502, 21985503, 21985504, 21985505, 21985506, 21985507, 21985508, 21985509, 21985510, 21985513, 22080166, 22080496, 22080740, 22096409, 22183216, 22183217, 22183218, 22190495, 22190496, 22190497, 22190498, 22190499, 22196391, 22196393, 22196394, 22196395, 22196396, 22196397, 22196398, 22196399, 22196400, 22196401, 22196402, 22196403, 22196404, 22200794, 22201265, 22201266, 22201267, 22201268, 22201269, 22201270, 22211750, 22211878, 22226820, 22226821, 22226822, 22226824, 22226825, 22247044, 22247045, 22247046, 22247047, 22247049, 22247050, 22247051, 22247052, 22247053, 22247054, 22247055, 22247056, 22247057, 22247058, 22247059, 22247060, 22247062, 22252798, 22252799, 22252800, 22252801, 22260573, 22260574, 22260575, 22260576, 22260577, 22260578, 22260579, 22260580, 22260581, 22260582, 22260583, 22260585, 22260586, 22260587, 22260588, 22260589, 22260590, 22268205, 22268206, 22268207, 22268209, 22268210, 22268211, 22268212, 22268213, 22268214, 22268215, 22268216, 22268217, 22274726, 22274728, 22274729, 22274731, 22274732, 22274733, 22274734, 22282547, 22282548, 22282549, 22282550, 22282552, 22287082, 22287166, 22287167, 22287195, 22287479, 22287883, 22287884, 22287885, 22293170, 22293171, 22297414, 22297491, 22297576, 22298164, 22298246, 22298247, 22298269, 22298374, 22299254, 22299255, 22299256, 22304169, 22305489, 22307303, 22307304, 22308497, 22314298, 22314775, 22315417, 22322644, 22330920, 22330921, 22330922, 22330923, 22330924, 22330925, 22330926, 22338948, 22338949, 22338950, 22338951, 22338952, 22338953, 22338954, 22338955, 22338956, 22338957, 22338958, 22359184, 22359375, 22360483, 22360484, 22360486, 22362573, 22370694, 22370695, 22370696, 22370697, 22378338, 22384732, 22384883, 22386158, 22386684, 22386685, 22394848, 22395369, 22395370, 22395373, 22395374, 22395375, 22395376, 22395378, 22395379, 22395380, 22395381, 22395382, 22395383, 22395384, 22395385, 22395386, 22395387, 22401736, 22401737, 22401739, 22401740, 22401741, 22401743, 22403189, 22406895, 22407151, 22409124, 22409125, 22409126, 22409127, 22409128, 22417408, 22417409, 22417410, 22425544, 22425545, 22426154, 22426155, 22426156, 22426157, 22426158, 22426159, 22426160, 22432340, 22432866, 22432867, 22440468, 22440469, 22447806, 22447807, 22447808, 22447809, 22447810, 22447811, 22447812, 22447813, 22447815, 22447816, 22447817, 22447819, 22447820, 22447822, 22447823, 22447824, 22447825, 22447826, 22447827, 22447829, 22447830, 22447832, 22447833, 22447834, 22447835, 22447836, 22447837, 22447838, 22447839, 22447840, 22447841, 22447842, 22447843, 22447844, 22453317, 22453318, 22453320, 22453321, 22453322, 22453324, 22458306, 22461126, 22465921, 22466532, 22466533, 22473116, 22473654, 22481532, 22486164, 22486166, 22486167, 22489412, 22489413, 22489414, 22495357, 22500463, 22500464, 22500465, 22500466];

		foreach ($transactionIds as $transactionId) {
			/** @var Transaction|null $transaction */
			$transaction = $this->transactionFacade->find($transactionId);

			if ($transaction === null || $transaction->isCancelled() || $transaction->isConfirmed()) {
				continue;
			}

			if ($transaction->isBillable() === false) {
				continue;
			}

			$this->transactionFacade->confirm($transaction);
		}
	}

	public function actionConvert()
	{
		dumpe($this->currencyFacade->convert(1, 'EUR', 'USD'));
	}

	public function actionTestRisk()
	{
		$user = $this->userFacade->find(1);
		$transaction = $this->transactionFacade->find(2);

		$this->riskFacade->checkRiskFromRegisteredTransaction(
			$user,
			$transaction
		);

		$this->terminate();
	}

	public function actionTest()
	{
		$localization = $this->localizationFacade->find(1);

		$luckyShopCampaign = $this->luckyShopFacade->findLuckyShopCampaignByName($localization, 'default');
		$lastLuckyShop = $this->luckyShopFacade->findLuckyShopByCampaign($luckyShopCampaign, null);

		$user = $this->userFacade->findOneByEmail('<EMAIL>', $localization);

		$this->luckyShopEmailProvider->scheduleLuckyShopCreatedEmail($lastLuckyShop, $user);

		$this->terminate();
	}

	public function actionScheduleLuckyShopNotifications()
	{
		$localizations = $this->localizationFacade->findIn([1, 2, 3, 4, 6, 8]);

		foreach ($localizations as $localization) {
			$luckyShopCampaign = $this->luckyShopFacade->findLuckyShopCampaignByName($localization, 'default');
			$lastLuckyShop = $this->luckyShopFacade->findLuckyShopByCampaign($luckyShopCampaign, null);

			foreach ($this->luckyShopFacade->findUsersWithActiveUserLuckyShop($localization) as $user) {
				if ($user->hasUserLuckyShopData() === false) {
					continue;
				}

				if ($user->hasAllowedSendingEmails(SendingPolicy::MESSAGE_TYPE_EMAIL, SendingPolicy::CONTENT_TYPE_LUCKY_SHOP)) {
					if ($this->messageFacade->findTodayLuckyShopEmailForUser($user)) {
						continue;
					}

					$this->luckyShopEmailProducer->scheduleSendLuckyShopCreatedEmail($user, $lastLuckyShop);
				}

				if ($user->hasAllowedPushNotifications(SendingPolicy::MESSAGE_TYPE_PUSH_NOTIFICATIONS, SendingPolicy::CONTENT_TYPE_LUCKY_SHOP)) {
					if ($this->notificationFacade->findTodayLuckyShopNotificationForUser($user)) {
						continue;
					}

					$this->luckyShopNotificationProducer->scheduleSendLuckyShopCreatedNotification($user, $lastLuckyShop);
				}
			}
		}

		$this->terminate();
	}

	public function actionTestNotifications()
	{
		$notificationTemplateDataObject = $this->notificationTemplateFacade->getNotificationTemplate(
			$this->localizationFacade->find(8),
			'lucky_shop',
			[
				'vocalName' => 'Josefe',
				'link' => 'https://www.tipli.cz/tipli-daruje',
			]
		);

		$notificationTemplateDataObject->setLink('https://www.tipli.cz/tipli-daruje');

		$notification = $this->notificationFactory->createNotificationFromNotificationTemplateDataObject(
			$this->userFacade->find(4192389),
			$notificationTemplateDataObject,
			Notification::TYPE_LUCKY_SHOP,
			new \DateTime(),
			null,
			new \DateTime()
		);

		$this->notificationManager->saveNotification($notification);

		dumpe($notification);
	}

	public function actionTestTransaction()
	{
		$transactionQuery = $this->transactionFacade->createTransactionsQuery()
			->onlyCommission();

		$transactions = $this->transactionFacade->fetch($transactionQuery)
			->applyPaging(0, 100);

		dumpe($transactions->toArray());
	}
}
