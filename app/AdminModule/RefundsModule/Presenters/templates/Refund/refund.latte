{var $freeContent = true}
{block headerContent}
	<a n:href="Refunds:" class="btn btn-light btn-sm back-button"> < back</a>
{/block}
{var $title = 'Refund detail'}

{block title}Refund detail'{/block}
{block content}
{varType tipli\Model\Refunds\Entities\Refund $refund}
{varType tipli\Model\Refunds\RefundResolver $refundResolver}
{varType tipli\Model\Refunds\RefundResponseProvider $refundResponseProvider}
{varType tipli\Model\Refunds\Entities\RefundProcess $refundProcess}
{varType tipli\Model\Account\Entities\User $userEntit}

{capture $userCurrency}{$userEntity->getLocalization()->getCurrency() |currency}{/capture}
{capture $cashback}
	{if $refund->getShop()}
		{($refund->getShop() |reward: true, 'interval', $refund->getUser()) |noescape}
	{/if}
{/capture}

{block head}
	<!-- redactor.css -->
	<link rel="stylesheet" href="{$basePath}/libs/redactor/redactor.min.css" />
{/block}

{block scripts}
	<!-- redactor.js -->
	<script src="{$basePath}/libs/redactor/redactor.min.js"></script>
	<!-- plugin js -->
	<script src="{$basePath}/js/redactor/tipliPlugin.js?v=2"></script>
	<script src="{$basePath}/libs/redactor/plugins/widget/widget.js"></script>

	<script>
		$R('.redactor', {
			lang: 'cs',
			focus: true,
			breakline: true,
			toolbarFixedTopOffset: 60,
			autoparseLinks: false,
			minHeight: '280px',
			buttons: ['undo', 'redo', 'format', 'bold', 'italic', 'ul', 'ol', 'link', 'html', 'image'],
			plugins: [],
			imageUpload: '/api/v1/image/admin-redactor-upload',
			spellcheck: parseInt($('meta[name=current-localization-id]').attr('content')) === 1
		});
	</script>
{/block}

<div class="clearfix"></div>
<div class="row">
	<div class="col-md-12">
		{*Zakladni informace*}
		<div class="card card-accent-primary">
			<div class="card-body">
				<h1 style="display: inline">
					Refund #{$refund->getUniqueId()},
					{$refund->getTypeLabel()},
					user: {$refund->getUser()->getEmail()}
				</h1>
				{if $refund->getShop()}
					<img src="{$refund->getShop()->getLogo() |image:150,60,fitMiddleWithoutBorders}" style="float:right;max-width:100px;max-height:80px;line-height:80px;" />
				{/if}
			</div>
			<dv class="card-footer">
				<a n:href="refund $previousRefund->getId()" n:if="$previousRefund" class="btn btn-sm btn-primary">Previous refund</a>
				<a n:href="refund $nextRefund->getId()" n:if="$nextRefund" class="btn btn-sm btn-success">Next refund</a>
			</dv>
		</div>
	</div>
</div>

<div class="row">
	<div class="col-md-4">

		{*Zakladni informace*}
		<div class="card card-accent-primary">
			<div class="card-header">
				<strong>User</strong>
			</div>
			<div class="card-body">
				<div>
					<span class="badge badge-warning text-white mb-2" n:if="$userEntity->isVip()">VIP</span>
					<span class="badge badge-success" n:if="$userEntity->isAffiliate()">Affiliate</span>
					<span class="badge badge-dark" n:if="$userEntity->isSuspected()" data-toggle="tooltip" data-placement="top" title="Reason for suspicion: {$userEntity->getSegmentData()->getSuspectedReason()}">suspicious</span>
				</div>

				ID: <strong>{$userEntity->getId()}</strong>
				<br />
				Full name: <strong>{$userEntity->getFullName()}</strong>
				<br />
				Email: <strong>{$userEntity->getEmail()}</strong>
				<br />
				Phone: {if $userEntity->getPhoneNumber()}<strong>{$userEntity->getPhoneNumber()}</strong>{else}<em>empty</em>{/if}
				<br />
				Account number: {if $userEntity->hasFilledBankDetails()}<strong>{$userEntity->getAccountNumber()}</strong>{else}<em>empty</em>{/if}
				<br />
				Date of registration: <strong>{$userEntity->getCreatedAt()->format('d.m.Y H:i')}</strong>
				{if $userEntity->getParentUser()}
					<br />
					Recommended by the user: {include userLink, $userEntity->getParentUser()}
				{/if}
				<br />
				Addon installed: {if $userEntity->hasInstalledAddon()}<span class="badge badge-success">yes</span>{else}<span class="badge badge-danger">no</span>{/if}
				<br />
				Email verified:
				{if $userEntity->hasVerifiedEmail()}
					<span class="badge badge-success">yes</span>
				{else}
					<span class="badge badge-danger">no</span>
				{/if}
				<br />
				Account number verified:
				{if !empty($userEntity->getAccountNumber())}
					{if $userEntity->hasVerifiedAccountNumber()}
						<span class="badge badge-success">yes</span>
					{else}
						<span class="badge badge-danger">no</span>
					{/if}
				{else}
					<em>empty</em>
				{/if}
				<br />
				Phone verified:
				{if !empty($userEntity->getPhoneNumber())}
					{if $userEntity->hasVerifiedPhoneNumber()}
						<span class="badge badge-success">yes</span>
					{else}
						<span class="badge badge-danger">no</span>
					{/if}
				{else}
					<em>empty</em>
				{/if}
				<br />
				Mobile app:
				{if $userEntity->getMobileDevices()->isEmpty()}
					<span class="badge badge-danger">not installed</span>
				{else}
					{foreach $userEntity->getMobileDevices() as $mobileDevice}
						<span class="badge badge-primary">{$mobileDevice->getPlatform()}</span>
					{/foreach}
				{/if}
				{if $utm = $userEntity->getUtm()}
					<br />
					UTM: {$utm->getUtmSource()} / {$utm->getUtmMedium()} / {$utm->getUtmCampaign()}
				{/if}
				<br />
				Number of approved refunds: <strong>{$countOfApprovedRefunds}</strong>
				<br />
				approved by affil: <strong>{$countOfApprovedRefundsByAffil}</strong>, declined by affil: <strong>{$countOfDeclinedRefundsByAffil}</strong>
				<br />
				Note: <strong class="text-danger">{$userEntity->getNote()}</strong>
				<br />
				<br />
				<a n:href=":Admin:Account:User:userCard $userEntity->getId()" target="_blank">
					<span class="fa fa-credit-card"></span> Go to user card >
				</a>
				<br />
				<a n:href=":Admin:Account:User:user $userEntity->getId()" target="_blank">
					<span class="fa fa-pen"></span> Account settings >
				</a>
				<br />
				<a n:href=":Admin:Account:User:login $userEntity->getId()" target="_blank">
					<span class="fa fa-key"></span> Log-in as user >
				</a>

			</div>
		</div>

		{*Přesměrování*}
		<div class="card card-accent-primary" n:if="$refund->getShop()">
			<div class="card-header">
				<div class="row align-items-center">
					<!-- Levý sloupec -->
					<div class="col-auto">
						<strong>Redirection to shop {$refund->getShop()->getName()}<br> 96 hours before order</strong>
					</div>

					<!-- Pravý sloupec -->
					<div class="col text-end">
						<a target="_blank" class="btn btn-sm btn-primary" n:href=":Admin:Shops:Redirection:default, redirectionsGrid-filter => ['name' => $userEntity->getEmail()]">Redirections</a>
					</div>
				</div>
			</div>
			<div class="card-body" style="padding: 0;overflow-x:auto">
				{control shopRedirectsGrid}
			</div>
		</div>

		{*Transakce*}
		<div class="card card-accent-primary" n:if="$refund->getShop()">
			<div class="card-header">
				<div class="row align-items-center">
					<!-- Levý sloupec -->
					<div class="col-auto">
						<strong>Transactions in shop {$refund->getShop()->getName()}</strong>
					</div>

					<!-- Pravý sloupec -->
					<div class="col text-end">
						<a target="_blank" class="btn btn-sm btn-primary btn-smf" n:href=":Admin:Transactions:Transaction:default, transactionsFilter => ['userId' => $userEntity->getId()]">Transactions</a>
					</div>
				</div>
			</div>
			<div class="card-body" style="padding: 0;overflow-x:auto">
				<div style="padding: 10px 0 0 12px">
					Transaction active days: <strong>{$transactionActiveDays['previous']}/{$transactionActiveDays['current']}</strong>
                </div>

				{control transactionsGrid}
			</div>
		</div>


		{*Poznámky k obchodu/partn. síti*}
		<div class="card card-accent-primary" n:if="$refund->getShop()">
			<div class="card-header">
				<strong>Notes relevant to shop {$refund->getShop()->getName()}</strong>
			</div>
			<div class="card-body" style="padding: 0;overflow-x:auto">
				{control notesGrid, gridType: 'simple'}
			</div>
		</div>


		{*Reklamace*}
		<div class="card card-accent-primary">
			<div class="card-header">
				<strong>Other user's refunds</strong>
			</div>
			<div class="card-body" style="padding: 0;overflow-x:auto">
				{control otherRefundsGrid}
			</div>
            <div class="card-body" style="padding: 0;overflow-x:auto">
                <p style="font-size: 13px;padding: 5px 15px;margin: 0; background: #f5f5f5">Other rewards of the type "refunds" which has not been created from new system</p>
                {control otherRefundTransactionsGrid}
            </div>
		</div>

		{*Duplicity*}
		<div class="card card-accent-primary">
			<div class="card-header">
				<strong>User duplicates</strong>
			</div>
			<div class="card-body" style="padding: 0;overflow-x:auto">
				{control userDuplicityGrid}
			</div>
		</div>

		{*Účet*}
		<div class="card card-accent-primary">
			<div class="card-header"><strong>Account</strong></div>
			<div class="card-body">
				<h4 style="margin:0">Current account balance: <span class="badge badge-primary">{$balance |amount} {$userCurrency}</span></h4>
				<h5>Confirmed balance: {$confirmedBalance |amount} {$userCurrency}</h5>
				<h5>Registered balance (waiting to confirm): {$registeredBalance |amount} {$userCurrency}</h5>

				{if $userEntity->getCanceledCommissionTransactionsRatio() >= 0.3}
					<span class="badge badge-sm badge-warning" data-toggle="tooltip" data-placement="top" title="cancelled {$userEntity->getCountOfCanceledCommissionTransactions()} from {$userEntity->getCountOfCommissionTransactions()} transactions" style="font-weight: normal; font-size: 11px; color: #000">
                     cancelled {$userEntity->getCanceledCommissionTransactionsRatio() * 100 |number} % transactions
                    </span>
				{/if}

				<hr />
				<h4>Commissions from purchases</h4>
				<table style="width:100%;">
					<col width="60%">
					<col width="20%">
					<col width="20%" align="right">
					<tr style="font-weight:bold">
						<td>Total:</td>
						<td>{($registeredCommissionTransactions->countOfTransactions + $confirmedCommissionTransactions->countOfTransactions)}</td>
						<td>{($registeredCommissionTransactions->amount + $confirmedCommissionTransactions->amount) |amount} {$userCurrency}</td>
					</tr>
					<tr>
						<td>Registered:</td>
						<td>{$registeredCommissionTransactions->countOfTransactions}</td>
						<td>{$registeredCommissionTransactions->amount |amount} {$userCurrency}</td>
					</tr>
					<tr>
						<td>Confirmed:</td>
						<td>{$confirmedCommissionTransactions->countOfTransactions}</td>
						<td>{$confirmedCommissionTransactions->amount |amount} {$userCurrency}</td>
					</tr>
				</table>
				<hr />
				<h4>Bonuses</h4>
				<table style="width:100%;">
					<col width="60%">
					<col width="20%">
					<col width="20%" align="right">
					<tr style="font-weight:bold">
						<td>Total:</td>
						<td>{($registeredBonusTransactions->countOfTransactions + $confirmedBonusTransactions->countOfTransactions)}</td>
						<td>{($registeredBonusTransactions->amount + $confirmedBonusTransactions->amount) |amount} {$userCurrency}</td>
					</tr>
					<tr>
						<td>Confirmed:</td>
						<td>{$confirmedBonusTransactions->countOfTransactions}</td>
						<td>{$confirmedBonusTransactions->amount |amount} {$userCurrency}</td>
					</tr>
					<tr>
						<td>Registered:</td>
						<td>{$registeredBonusTransactions->countOfTransactions}</td>
						<td>{$registeredBonusTransactions->amount |amount} {$userCurrency}</td>
					</tr>
				</table>
				<hr />
				<h4>Refunds</h4>
				<table style="width:100%;">
					<col width="60%">
					<col width="20%">
					<col width="20%" align="right">
					<tr style="font-weight:bold">
						<td>Total:</td>
						<td>{($registeredRefundTransactions->countOfTransactions + $confirmedRefundTransactions->countOfTransactions)}</td>
						<td>{($registeredRefundTransactions->amount + $confirmedRefundTransactions->amount) |amount} {$userCurrency}</td>
					</tr>
					<tr>
						<td>Confirmed:</td>
						<td>{$confirmedRefundTransactions->countOfTransactions}</td>
						<td>{$confirmedRefundTransactions->amount |amount} {$userCurrency}</td>
					</tr>
					<tr>
						<td>Registered:</td>
						<td>{$registeredRefundTransactions->countOfTransactions}</td>
						<td>{$registeredRefundTransactions->amount |amount} {$userCurrency}</td>
					</tr>
				</table>
			</div>
		</div>

		{*Profity*}
		<div class="card card-accent-primary">
			<div class="card-header">
				<strong>Profit</strong>
			</div>
			<div class="card-body">
				<h4 data-toggle="tooltip" data-placement="left" title="sum(commissionAmount)-sum(userCommissionAmount), type = commission">Transaction profit income: <span class="badge badge-primary">{if $income}{$income[amount] |amount} {$userCurrency} ({round($income[percentage], 2)} %){else}0 {$userCurrency}{/if}</span></h4>
				<h4 n:if="$accountantIncome" data-toggle="tooltip" data-placement="left" title="sum(commissionAmount)-(sum(userCommissionAmount)+sum(bonusAmount)), type != payout">Accounting profit from the user: <span class="badge badge-primary">{$accountantIncome[amount] |amount} {$userCurrency}</span></h4>
			</div>
		</div>

		{*Zvýhodnění*}
		<div class="card card-accent-primary">
			<div class="card-header">
				<strong>Benefits</strong>
			</div>
			<div class="card-body">
				<strong>Active benefits</strong>
				{if !$shareRewards->isEmpty()}
					<ul>
						{foreach $shareRewards as $shareReward}
							<li>
								{$shareReward->getName()} ({$shareReward->getShareCoefficient()}) -
								{if $shareReward->getShops()->isEmpty()}
									all shops
								{else}
									{foreach $shareReward->getShops() as $shop}
										{$shop->getName()}
										{sep}, {/sep}
									{/foreach}
								{/if}
								-
								{$shareReward->getValidTill()|date:'d.m.Y'}
							</li>
						{/foreach}
					</ul>
				{else}
					<em>no active benefits yet</em>
				{/if}
				{if !$expiredShareRewards->isEmpty()}
					<hr />
					<strong>Expired benefits</strong>
					<ul>
						{foreach $expiredShareRewards as $expiredShareReward}
							<li>
								{$expiredShareReward->getName()} ({$expiredShareReward->getShareCoefficient()}) -
								{if $expiredShareReward->getShops()->isEmpty()}
									all shops
								{else}
									{foreach $expiredShareReward->getShops() as $shop}
										{$shop->getName()}
										{sep}, {/sep}
									{/foreach}
								{/if}
							</li>
						{/foreach}
					</ul>
				{/if}
			</div>
		</div>

		{*Výplaty*}
		<div class="card card-accent-primary">
			<div class="card-header">
				<strong>Payouts</strong>
			</div>
			<div class="card-body">
				{if $payouts}
					<h4 style="margin:0">
						Total confirmed payouts: <span class="badge badge-primary">{$sumOfConfirmedPayouts->amount |amount} {$userCurrency}</span>
						<br />
						<small>including bonuses: <span class="badge badge-primary">{$sumOfConfirmedPayouts->bonusAmount |amount} {$userCurrency}</span></small>
					</h4>
					<br />
					<ol>
						{foreach $payouts as $payout}
							<li>
								{$payout->getCreatedAt()->format('d.m.Y')}:
								<strong>{$payout->getAmount() |amount} {$payout->getCurrency() |currency}</strong>
								{if $payout->getBonusAmount()} <small>including bonuses: <strong>{$payout->getBonusAmount() |amount} {$userCurrency}</strong></small>{/if}
								{if $payout->isConfirmed()}<small class="fa fa-check text-success"></small>{else}<small class="text-warning">not confirmed</small>{/if}
								{if $payout->isCancelled()}<small class="badge badge-danger">cancelled</small>{/if}
								{var $comments = $payout->getComments()}
{*								{if !$comments->isEmpty()}*}
{*									<small>| <a href="javascript:void(0);" data-toggle="modal" data-target="#payoutComments{$payout->getId()}">comments ({$comments->count()}) »</a></small>*}
{*									{include payoutCommentsModal, $payout}*}
{*								{/if}*}
							</li>
						{/foreach}
					</ol>
				{else}
					<em>user has not any payout yet</em>
				{/if}
			</div>
		</div>

		{*Doporučení uživatelé*}
		<div class="card card-accent-primary">
			<div class="card-header">
				<strong>Recommended users</strong>
			</div>
			<div class="card-body">
				{if count($userEntity->getReferencedUsers()->toArray()) > 0}
					<ol>
						{foreach $userEntity->getReferencedUsers() as $referencedUser}
							<li>
								{include userLink, $referencedUser}
							</li>
						{/foreach}
					</ol>
				{else}
					<em>user has not referred anyone yet.</em>
				{/if}
			</div>
		</div>

	</div>


	<div class="col-md-4">
		{*Typ: missing_commission*}
		<div class="card card-accent-primary" n:if="$refund->getType() === $refund::TYPE_MISSING_COMMISSION">
			<div class="card-header">
				<strong>Refund detail</strong>
			</div>
			<div class="card-body">
				<span class="badge badge-warning" n:if="$refund->isAddonUsed()">redirected through addon</span>
				<span class="badge badge-warning" n:if="$refund->isMobileAppUsed()">purchased through mobile app Tipli</span>

				<table width="100%">
					<tr>
						<td>Type:</td>
						<td align="right">missing commission</td>
					</tr>
					<tr>
						<td>Shop:</td>
						<td align="right"><a n:href=":Admin:Shops:Shop:shop, $refund->getShop()->getId()">{$refund->getShop()->getName()}</a></td>
					</tr>
					<tr>
						<td>Afill. site:</td>
						<td align="right">{$refund->getShop()->getPartnerSystem() ? $refund->getShop()->getPartnerSystem()->getName() : '-'}</td>
					</tr>
					<tr>
						<td>Cashback:</td>
						<td align="right">{$cashback}</td>
					</tr>
					<tr>
						<td>Order amount with Tax:</td>
						<td align="right">{$refund->getOrderAmount() |amount} {$refund->getOrderCurrency() |currency}</td>
					</tr>
					<tr>
						<td>Order amount without Tax:</td>
						<td align="right">{$refundResolver->resolveOrderAmountWithoutTax($refund) |amount} {$userCurrency}</td>
					</tr>
					<tr>
						<td>Value of highest cashback reward:</td>
						<td align="right">{$refundResolver->resolveBonusAmount($refund) |amount} {$userCurrency}</td>
					</tr>
					<tr>
						<td>Coupon:</td>
						<td align="right">
							{($refund->isCouponUsed() ? ($refund->getCouponCode() ? : 'used, but did not specify the any code'): 'did not use any coupon') |noescape}

							{if $refund->getCouponCode()}
								{var $coupon = $getCouponByCode($refund->getCouponCode())}
								<span class="badge badge-success" n:if="$coupon">coupon from tipli</span>
								<span class="badge badge-warning" n:if="!$coupon">coupon does not exist at Tipli</span>
							{/if}
						</td>
					</tr>
					<tr>
						<td>Date of purchase:</td>
						<td align="right">
							{if $refund->getLocalization()->isPolish() && $refund->getCreatedAt() >= new DateTime('2021-08-25')}
								{$refund->getPurchasedAt()->format('d.m.Y H:i')}
							{else}
								{$refund->getPurchasedAt()->format('d.m.Y')}
							{/if}

							<span class="badge badge-warning" n:if="$refund->isPurchaseDateLessThanTwoDays()">It hasn't been 48 hours</span>
						</td>
					</tr>
					<tr>
						<td>Attached document:</td>
						{if $refund->isResolved() && $refund->getResolvedAt() <= (new \DateTime)->modify('- 6 months') && $refund->getInvoice() === null}
						<td align="right">documents have been archived</td>
						{else}
						<td align="right">{($refund->getInvoice() ? ('<a target="_blank" href="' . $baseUrl . '/upload/files/' . ($refund->getInvoice()::NAMESPACE_REFUNDS) . '/' . $refund->getInvoice()->getIdentifier() . '">' . $refund->getInvoice()->getIdentifier() . '</a>') : 'did not attach any document') |noescape}</td>
						{/if}
					</tr>
					{if $refund->getSecondInvoice()}
						<tr>
							<td>Another attached document:</td>
							<td align="right">{('<a target="_blank" href="' . $baseUrl . '/upload/files/' . ($refund->getSecondInvoice()::NAMESPACE_REFUNDS) . '/' . $refund->getSecondInvoice()->getIdentifier() . '">' . $refund->getSecondInvoice()->getIdentifier() . '</a>') |noescape}</td>
						</tr>
					{/if}
                    <tr>
                        <td>Order ID:</td>
                        <td align="right">
                            <input type="text" value="{$refund->getOrderId()}" class="form-control" onfocus="this.select();" readonly style="padding: 0; height: 22px; background: #fff; border: none; text-align: right">

                            {if $refund->getOrderId()}
                                {var $duplicityRefundsByOrderId = $getDuplicityRefundsByOrderId($refund)}

                                {if $duplicityRefundsByOrderId}
                                    <br /><strong>refund with same order number:</strong><br />
                                    {foreach $duplicityRefundsByOrderId as $duplicityRefund}
                                        <a n:href="refund, $duplicityRefund->getId()">{$duplicityRefund->getUniqueId()}</a>{sep}, {/sep}
                                    {/foreach}
                                {/if}
                            {/if}
                        </td>
					</tr>

					{if $refund->getMessage() !== null}
						<tr>
							<td>Message from user:</td>
							<td></td>
						</tr>
						<tr>
							<td colspan="2">
								<small>
									<em>{$refund->getMessage() |breakLines}</em>
								</small>
							</td>
						</tr>
                    {/if}
					{if isset($activeVouchers)}
						<table>
						<tr>
							<td>Tipli EXTRA:</td>
							{if count($activeVouchers) === 0}
							<td align="right">no active vouchers</td>
							{/if}
						</tr>
						{foreach $activeVouchers as $activeVoucher}
							<tr>
								{var $voucherCampaign = $activeVoucher->getVoucherCampaign()}
								{var $rewardType = $voucherCampaign->getRewardType()}
								<td>
									<span class="badge badge-success">active</span>
									<a target="_blank" href="{plink :Admin:Vouchers:UserVouchers:userVouchers, $activeVoucher->getVoucherCampaign()->getId()}&userVouchersGrid-filter[userId]={$activeVoucher->getUser()->getId()}">
									{if $voucherCampaign->getBonusMultiplier() > 0}
										{$voucherCampaign->getBonusMultiplier()+1}x
									{else}
										{$voucherCampaign->getBonusAmount() |amount}
									{/if}

									{if $voucherCampaign->getBonusMultiplier() === null}
										<span class="text-[26px] leading-[39px]">
											{$refund->getOrderCurrency() |currency |noescape}
										</span>
									{/if}
									{_newFront.vouchers.voucher.rewardType.$rewardType}
									</a>
									{$activeVoucher->getVoucherCampaign()->getDescription()}
								</td>
							</tr>
						</table>
						{/foreach}
					{/if}
				</table>
				<div n:if="$refund->getProductUrls()" style="margin-top: 10px">
				    <strong>Products:</strong><br />

					{snippet productLinks}
						{ifset $productLinks}
							{foreach $productLinks as $productLink}
								<div>
									{if $productLink['isAffiliate']}
										<span class="badge badge-success">cashback</span>
									{else}
										<span class="badge badge-danger">without cashback</span>
									{/if}

									<a href="{$productLink['url']}" target="_blank">
										{$productLink['url'] |truncate: 50}
									</a>
								</div>
							{/foreach}
						{else}
							<i class="fa fa-spinner fa-spin"></i>
						{/ifset}
					{/snippet}

					<script>
						$(document).ready(function() {
							setTimeout(() => {
								$.nette.ajax({
									url: {link getProductsData!},
									method: "GET"
								})
							}, 500)
						})
					</script>
				</div>
			</div>
		</div>

		{*Typ: missing_bonus*}
		<div class="card card-accent-primary" n:if="$refund->getType() === $refund::TYPE_MISSING_BONUS">
			<div class="card-header">
				<strong>Refund detail</strong>
			</div>
			<div class="card-body">
				<table width="100%">
					<tr>
						<td>Type:</td>
						<td align="right">missing bonus</td>
					</tr>
					<tr>
						<td>Message from user:</td>
						<td></td>
					</tr>
					<tr>
						<td colspan="2">
							<small>
								<em>{$refund->getMessage() |breakLines}</em>
							</small>
						</td>
					</tr>
				</table>
			</div>
		</div>

		{*Typ: canceled_commission*}
		<div class="card card-accent-primary" n:if="$refund->getType() === $refund::TYPE_CANCELED_COMMISSION">
			{var $relatedTransaction = $refund->getRelatedTransaction()}
			{varType tipli\Model\Transactions\Entities\Transaction $relatedTransaction}
			<div class="card-header">
				<strong>Refund detail</strong>
			</div>
			<div class="card-body">
				<table width="100%">
					<tr>
						<td>Type:</td>
						<td align="right">cancelled commission</td>
					</tr>
					<tr n:if="$relatedTransaction">
						<td>Type transaction:</td>
						<td align="right">{$relatedTransaction->getTypeLabel()}</td>
					</tr>
					<tr n:if="$relatedTransaction && $refund->getRelatedTransaction()->isCommission()">
						<td>Transaction ID:</td>
						<td align="right">
							<input type="text" value="{$relatedTransaction->getTransactionId()}" class="form-control" onfocus="this.select();" readonly style="padding: 0; height: 22px; background: #fff; border: none; text-align: right">
						</td>
					</tr>
					<tr n:if="$relatedTransaction && $relatedTransaction->getShop()">
						<td>Shop:</td>
						<td align="right">{$relatedTransaction->getShop()->getName()}</td>
					</tr>
					<tr n:if="$relatedTransaction && $relatedTransaction->getShop()">
						<td>Afill. site:</td>
						<td align="right">{$relatedTransaction->getShop()->getPartnerSystem() ? $relatedTransaction->getShop()->getPartnerSystem()->getName() : '-'}</td>
					</tr>
					<tr n:if="$relatedTransaction">
						<td>User reward:</td>
						<td align="right">{$relatedTransaction->getAmount()} {$userCurrency}</td>
					</tr>
					<tr n:if="$relatedTransaction">
						<td>Transaction state:</td>
						<td align="right">{$relatedTransaction->isConfirmed() ? ($relatedTransaction->isCancelled() ? "cancelled" : "confirmed") : "registered"}</td>
					</tr>
					<tr n:if="$relatedTransaction">
						<td>Transaction created at:</td>
						<td align="right">{$relatedTransaction->getCreatedAt()->format('d.m.Y H:i:s')}</td>
					</tr>
					<tr n:if="$relatedTransaction && $relatedTransaction->isConfirmed()">
						<td>Transaction {$relatedTransaction->isCancelled() ?
							"cancellation" : "confirmation"} date:</td>
						<td align="right">{$relatedTransaction->getConfirmedAt()->format('d.m.Y H:i:s')}</td>
					</tr>
					<tr>
						<td>Attached document:</td>
						{if $refund->isResolved() && $refund->getResolvedAt() <= (new \DateTime)->modify('- 6 months') && $refund->getInvoice() === null}
							<td align="right">documents have been archived</td>
						{else}
							<td align="right">{($refund->getInvoice() ? ('<a target="_blank" href="' . $baseUrl . '/upload/files/' . ($refund->getInvoice()::NAMESPACE_REFUNDS) . '/' . $refund->getInvoice()->getIdentifier() . '">' . $refund->getInvoice()->getIdentifier() . '</a>') : 'did not attach a document') |noescape}</td>
						{/if}
					</tr>
					<tr>
						<td>Message from user:</td>
						<td></td>
					</tr>
					<tr>
						<td colspan="2">
							<small>
								<em>{$refund->getMessage() |breakLines}</em>
							</small>
						</td>
					</tr>
				</table>
			</div>
		</div>

		{*Typ: unconfirmed_transaction*}
		<div class="card card-accent-primary" n:if="$refund->getType() === $refund::TYPE_UNCONFIRMED_TRANSACTION">
			{var $relatedTransaction = $refund->getRelatedTransaction()}
			{varType tipli\Model\Transactions\Entities\Transaction $relatedTransaction}
			<div class="card-header">
				<strong>Refund detail</strong>
			</div>
			<div class="card-body">
				<table width="100%">
					<tr>
						<td>Type:</td>
						<td align="right">unconfirmed transaction</td>
					</tr>
					<tr n:if="$relatedTransaction">
						<td>Transaction type:</td>
						<td align="right">{$relatedTransaction->getTypeLabel()}</td>
					</tr>
					<tr n:if="$relatedTransaction && $refund->getRelatedTransaction()->isCommission()">
						<td>Transaction ID:</td>
						<td align="right">
							<input type="text" value="{$relatedTransaction->getTransactionId()}" class="form-control" onfocus="this.select();" readonly style="padding: 0; height: 22px; background: #fff; border: none; text-align: right">
						</td>
					</tr>
					<tr n:if="$relatedTransaction && $relatedTransaction->getShop()">
						<td>Shop:</td>
						<td align="right">{$relatedTransaction->getShop()->getName()}</td>
					</tr>
					<tr n:if="$relatedTransaction && $relatedTransaction->getShop()">
						<td>Afill. site:</td>
						<td align="right">{$relatedTransaction->getShop()->getPartnerSystem() ? $relatedTransaction->getShop()->getPartnerSystem()->getName() : '-'}</td>
					</tr>
					<tr n:if="$relatedTransaction">
						<td>User reward:</td>
						<td align="right">{$relatedTransaction->getAmount()} {$userCurrency}</td>
					</tr>
					<tr n:if="$relatedTransaction">
						<td>Transaction state:</td>
						<td align="right">{$relatedTransaction->isConfirmed() ? ($relatedTransaction->isCancelled() ? "cancelled" : "confirmed") : "registered"}</td>
					</tr>
					<tr n:if="$relatedTransaction">
						<td>Transaction created at:</td>
						<td align="right">{$relatedTransaction->getCreatedAt()->format('d.m.Y H:i:s')}</td>
					</tr>
					<tr n:if="$relatedTransaction && $relatedTransaction->isConfirmed()">
						<td>Transaction {$relatedTransaction->isCancelled() ?
							"cancellation" : "confirmation"} date:</td>
						<td align="right">{$relatedTransaction->getConfirmedAt()->format('d.m.Y H:i:s')}</td>
					</tr>
					<tr>
						<td>Message from user:</td>
						<td></td>
					</tr>
					<tr>
						<td colspan="2">
							<small>
								<em>{$refund->getMessage() |breakLines}</em>
							</small>
						</td>
					</tr>
				</table>
			</div>
		</div>

		{*Typ: incorrect_amount*}
		<div class="card card-accent-primary" n:if="$refund->getType() === $refund::TYPE_INCORRECT_AMOUNT">
			{var $relatedTransaction = $refund->getRelatedTransaction()}
			{varType tipli\Model\Transactions\Entities\Transaction $relatedTransaction}
			<div class="card-header">
				<strong>Refund detail</strong>
			</div>
			<div class="card-body">
				<table width="100%">
					<tr>
						<td>Type:</td>
						<td align="right">incorrect amount</td>
					</tr>
					<tr n:if="$relatedTransaction">
						<td>Transaction type:</td>
						<td align="right">{$relatedTransaction->getTypeLabel()}</td>
					</tr>
					<tr n:if="$relatedTransaction && $refund->getRelatedTransaction()->isCommission()">
						<td>Transaction ID:</td>
						<td align="right">
							<input type="text" value="{$relatedTransaction->getTransactionId()}" class="form-control" onfocus="this.select();" readonly style="padding: 0; height: 22px; background: #fff; border: none; text-align: right">
						</td>
					</tr>
					<tr n:if="$relatedTransaction && $relatedTransaction->getShop()">
						<td>Shop:</td>
						<td align="right">{$relatedTransaction->getShop()->getName()}</td>
					</tr>
					<tr n:if="$relatedTransaction && $relatedTransaction->getShop()">
						<td>Afill. site:</td>
						<td align="right">{$relatedTransaction->getShop()->getPartnerSystem() ? $relatedTransaction->getShop()->getPartnerSystem()->getName() : '-'}</td>
					</tr>
					<tr n:if="$relatedTransaction">
						<td>User reward:</td>
						<td align="right">{$relatedTransaction->getAmount() |amount} {$userCurrency}</td>
					</tr>
					<tr>
						<td>User-specified expected reward amount:</td>
						<td align="right">{if $refund->getExpectedAmount()}{$refund->getExpectedAmount() |amount} {$userCurrency}{else}did not specified{/if}</td>
					</tr>
					<tr n:if="$refund->getExpectedAmount() && $relatedTransaction">
						<td>Amount difference:</td>
						<td align="right">{$refundResolver->resolveBonusAmount($refund) |amount} {$userCurrency}</td>
					</tr>
					<tr n:if="$relatedTransaction">
						<td>Trasanction state:</td>
						<td align="right">{$relatedTransaction->isConfirmed() ? ($relatedTransaction->isCancelled() ? "cancelled" : "confirmed") : "registered"}</td>
					</tr>
					<tr n:if="$relatedTransaction">
						<td>Transaction created at:</td>
						<td align="right">{$relatedTransaction->getCreatedAt()->format('d.m.Y H:i:s')}</td>
					</tr>
					<tr n:if="$relatedTransaction && $relatedTransaction->isConfirmed()">
						<td>Transaction {$relatedTransaction->isCancelled() ?
							"cancellation" : "confirmation"} date:</td>
						<td align="right">{$relatedTransaction->getConfirmedAt()->format('d.m.Y H:i:s')}</td>
					</tr>
					<tr>
						<td>Order number:</td>
						<td align="right">
							{$refund->getOrderId()}

							{if $refund->getOrderId()}
								{var $duplicityRefundsByOrderId = $getDuplicityRefundsByOrderId($refund)}

								{if $duplicityRefundsByOrderId}
									<br /><strong>refund with same order number:</strong><br />
									{foreach $duplicityRefundsByOrderId as $duplicityRefund}
										<a n:href="refund, $duplicityRefund->getId()">{$duplicityRefund->getUniqueId()}</a>{sep}, {/sep}
									{/foreach}
								{/if}
							{/if}
						</td>
					</tr>
					<tr>
						<td>Attached document:</td>
						{if $refund->isResolved() && $refund->getResolvedAt() <= (new \DateTime)->modify('- 6 months') && $refund->getInvoice() === null}
						<td align="right">documents have been archived</td>
						{else}
						<td align="right">{($refund->getInvoice() ? ('<a target="_blank" href="' . $baseUrl . '/upload/files/' . ($refund->getInvoice()::NAMESPACE_REFUNDS) . '/' . $refund->getInvoice()->getIdentifier() . '">' . $refund->getInvoice()->getIdentifier() . '</a>') : 'did not attach a document') |noescape}</td>
						{/if}
					</tr>
					<tr>
						<td>Message from user:</td>
						<td></td>
					</tr>
					<tr>
						<td colspan="2">
							<small>
								<em>{$refund->getMessage() |breakLines}</em>
							</small>
						</td>
					</tr>
				</table>
			</div>
		</div>

		{*Řešení reklamace missing commission*}
		<div class="card card-accent-{($refund->isResolved() ? ($refund->isApproved() ? "success" : "danger") : "primary")}" n:if="$refund->getType() === $refund::TYPE_MISSING_COMMISSION">
			<div class="card-header">
				<strong>State</strong>
			</div>
			<div class="card-body">
				<div class="text-center">
				{if $refund->getState() === $refund::STATE_WAITING_FOR_PROCESS}
					<i class="resolve-icon fa fa-spinner fa-spin"></i><br />
					Currently being processed by the robot.
				{elseif $refund->getState() === $refund::STATE_READY_FOR_APPROVE}
					<i class="resolve-icon fa fa-spinner fa-spin"></i><br />
					Pre-approved by the robot.<br />
					Refund will be approved within a few minutes.
				{elseif $refund->getState() === $refund::STATE_WAITING_FOR_OPERATOR}
					<i class="resolve-icon fa fa-warning"></i><br />
					<strong>Waiting to be resolved by the operator</strong><br />
					Robot couldn't resolve refund due to:<br />
					{foreach $refundProcess->getReasonMessages() as $message}
						<em>{$message}</em>{sep}<br />{/sep}
					{/foreach}
				{elseif $refund->getState() === $refund::STATE_APPROVED}
					<i class="resolve-icon fa fa-check-circle"></i><br />
					{if $refund->getResolvedByUser()}
						{$refund->getResolvedByUser()->getFullName()} <strong>approved</strong> refund from {$refund->getResolvedAt()->format('d.m.Y H:i')}<br />
						{if $refundProcess && $refund->getRefundTransaction()}bonus amount <strong>{$refund->getRefundTransaction()->getBonusAmount() |amount} {$userCurrency}</strong>{/if}<br />
					{else}
						<em>robot</em> <strong>approved</strong> refund from {$refund->getResolvedAt()->format('d.m.Y H:i')}<br />
						{if !$refund->getRefundTransaction() && $refundProcess}<em>Bonus will be added to user's account within 30 minutes.</em><br />{/if}
						{if $refundProcess}bonus amount <strong>{$refundProcess->getBonusAmount() |amount} {$userCurrency}</strong>{/if}<br />
					{/if}
				{elseif $refund->getState() === $refund::STATE_DECLINED}
					<i class="resolve-icon fa fa-times-circle"></i><br />
					{$refund->getResolvedByUser()->getFullName()} <strong>declined</strong> refund from {$refund->getResolvedAt()->format('d.m.Y H:i')}<br />
					<p>
						<a n:href="reopenRefund!">Reopen refund</a>
					</p>
				{/if}
				</div>
			</div>
		</div>

		{*Řešení reklamace cokoliv než missing commission*}
		<div class="card card-accent-{($refund->isResolved() ? ($refund->isApproved() ? "success" : "danger") : "primary")}" n:if="$refund->getType() !== $refund::TYPE_MISSING_COMMISSION">
			<div class="card-header">
				<strong>State</strong>
			</div>
			<div class="card-body">
				<div class="text-center">
					{if $refund->getState() === $refund::STATE_WAITING_FOR_OPERATOR}
						<i class="resolve-icon fa fa-warning"></i><br />
						<strong>Waiting to be resolved by the operator</strong><br />
					{elseif $refund->getState() === $refund::STATE_APPROVED}
						<i class="resolve-icon fa fa-check-circle"></i><br />
						<strong>Approved by operator: {$refund->getResolvedByUser()->getFullName()} <br />at {$refund->getResolvedAt()->format('d.m.Y H:i')}</strong><br />
					{elseif $refund->getState() === $refund::STATE_DECLINED}
						<i class="resolve-icon fa fa-times-circle"></i><br />
						<strong>Cancelled by operator: {$refund->getResolvedByUser()->getFullName()} <br />at {$refund->getResolvedAt()->format('d.m.Y H:i')}</strong><br />
						<p>
							<a n:href="reopenRefund!">Reopen refund</a>
						</p>
					{/if}
				</div>
			</div>
		</div>

		{* Komentáře *}
		<div class="card card-accent-primary">
			<div class="card-header">
				<strong>Comments</strong>
			</div>
			<div class="card-body">
				{snippet comments}
					<textarea class="form-control" style="width: 100%;min-height:100px;margin-bottom:15px;" data-type="comment"></textarea>
					<div class="text-center"><a href="javascript:void(0);" class="btn btn-primary" data-action="add-comment">Add comment ></a></div>
					<script>
						$(document).ready(function() {
							$("[data-action=add-comment]").off("click").on("click", function() {
								$.nette.ajax({
									url: {link addComment!},
									method: "POST",
									data: { comment: $("[data-type=comment]").val() }
								});
							});
						});
					</script>
					{varType tipli\Model\Refunds\Entities\RefundComment $comment}
					{if !$refund->getComments()->isEmpty()}<br />{/if}
					{foreach $refund->getComments() as $comment}
						<strong>{$comment->getUser()->getFullName()}</strong> at {$comment->getCreatedAt()->format('d.m.Y H:i:s')} added:
						<br />
						{$comment->getMessage() |breaklines}
						{sep}<hr />{/sep}
					{/foreach}
				{/snippet}
			</div>
		</div>

		{* Offers/nabídky *}
		<div class="card card-accent-primary" n:if="$refund->getShop()">
			<div class="card-header">
				<strong>Shop offers {$refund->getShop()->getName()}</strong>
			</div>
			<div class="card-body">
				<table class="table">
					<tr class="strong">
						<th width="55%">Offer</th>
						<th>Cashback</th>
						<th>Amount</th>
					</tr>
					<tr n:foreach="$getOffersList() as $offer">
						<td>{$offer->getName()}</td>
						<td>{$offer |reward: false, 'common', $refund->getUser() |noescape}</td>
						<td>{$refundResolver->resolveBonusAmount($refund, $offer)} {$userCurrency}</td>
					</tr>
				</table>
			</div>
		</div>

		{* Podmínky obchodu *}
		<div class="card card-accent-primary" n:if="$refund->getShop()">
			<div class="card-header">
				<strong>Shop conditions {$refund->getShop()->getName()}</strong>
			</div>
			<div class="card-body" n:if="$refund->getShop()->getShopData()->isOnlyFirstPurchaseCondition()" style="background: yellow">
				Cashback is valid only for the first order
			</div>

			<div class="card-body">
				{$refund->getShop()->getConditions()}
			</div>
		</div>

		<div class="card card-accent-primary" n:if="$refund->getShop() && ($refund->getShop()->getMobileMessageBeforeRedirect() || $refund->getShop()->getMobileWarningMessage() || $refund->getShop()->getWarningMessage())">
			<div class="card-header">
				<strong>Pre-redirect message's</strong>
			</div>
			<div class="card-body pb-0">
				{if $refund->getShop()->getMobileMessageBeforeRedirect()}
					<strong>Pre-redirect message's for mobile application:</strong>
					<p>{$refund->getShop()->getMobileMessageBeforeRedirect()}</p>
				{/if}

				{if $refund->getShop()->getMobileWarningMessage()}
					<strong>Pre-redirect message for web (mobile):</strong>
					<p>{$refund->getShop()->getMobileWarningMessage()}</p>
				{/if}

				{if $refund->getShop()->getWarningMessage()}
					<strong>Pre-redirect message for web (desktop):</strong>
					<p>{$refund->getShop()->getWarningMessage()}</p>
				{/if}
			</div>
		</div>

	</div>

	<div class="col-md-4">
		<div class="card card-accent-primary" n:if="$refund->getState() === $refund::STATE_WAITING_FOR_OPERATOR">
			<div class="card-header">
				<strong>Resolve</strong>
			</div>
			<div class="card-body">
				<div class="row">
					<div class="col-md-6 text-right">
						<a href="javascript:void(0);" data-toggle="modal" data-target="#modal-decline" data-backdrop="static" data-keyboard="false" class="resolve-button btn btn-outline-danger"><i class="fa fa-times"></i> Cancel refund</a>
					</div>
					<div class="col-md-6">
						<a href="javascript:void(0);" data-toggle="modal" data-target="#modal-approve" data-backdrop="static" data-keyboard="false" class="resolve-button btn btn-outline-success"><i class="fa fa-check"></i> Approve refund</a>
					</div>
				</div>
			</div>
		</div>

		<div class="card card-accent-primary">
			<div class="card-header">
				<strong>Agent</strong>
			</div>
			<div class="card-body">
				<div class="text-center">
                    <select name="operatorId" class="form-control" data-type="operatorId">
                        <option value="0">- no agent - </option>
                        <option n:attr="selected => $refund->getAssignedOperator() && $operatorId === $refund->getAssignedOperator()->getId()" n:foreach="$operators as $operatorId => $operatorName" value="{$operatorId}">{$operatorName}</option>
                    </select>

					<script>
						$(document).ready(function() {
							$("select[name=operatorId]").on("change", function () {
								$.nette.ajax({
									url: {link assignOperator!},
									method: "POST",
									data: { operatorId: $("[data-type=operatorId]").val() }
								});
							})
						})
					</script>
				</div>
			</div>
		</div>

		{* hodnocení řešení robotem *}
		{snippet resolveCheck}
		<div class="card card-accent-primary" n:if="$refund->getState() === $refund::STATE_APPROVED && $refund->getResolvedByUser() === null">
			<div class="card-header">
				<strong>The evaluation of the robot</strong>
				<a n:if="$refund->isResolveChecked()" href="{plink RenewRefundResolveCheck!, $refund->getId()}" class="btn btn-sm btn-warning pull-right ajax" onclick="return confirm('Do you really want to renew?');">
					<i class="fa fa-history"></i> Renew
				</a>
			</div>
			<div class="card-body">

				{if $refund->isResolveChecked()}
					<div class="text-center">
						{if $refund->getResolveCheckState() === tipli\Model\Refunds\Entities\Refund::RESOLVE_CHECK_STATE_APPROVED}
							<i class="resolve-icon fa fa-thumbs-up text-success"></i><br />
							The evaluation of the robot was <strong>positively</strong> rated by {$refund->getResolveCheckedByUser()->getFullName()}<br />
						{else}
							<i class="resolve-icon fa fa-thumbs-down text-danger"></i><br />
							The evaluation of the robot was <strong>negatively</strong> rated by {$refund->getResolveCheckedByUser()->getFullName()}<br />
						{/if}
						at {$refund->getResolveCheckedAt()->format('d.m.Y H:i')}
						<br/>

						{if $refund->getResolveCheckReasonType() !== null}
							<br/>
							<b>Reason:</b> {tipli\Model\Refunds\Entities\Refund::getResolveCheckReasons()[$refund->getResolveCheckReasonType()]}
						{/if}

						{if $refund->getResolveCheckMessage() !== null}
							<br/>
							<b>Note:</b> {$refund->getResolveCheckMessage()}
						{/if}
					</div>
				{else}
					{control refundResolveCheckControl}
				{/if}
			</div>
		</div>
		{/snippet}

		<div class="card card-accent-primary">
			<div class="card-header">
				<strong>Freshdesk conversations</strong>
				<a n:if="$refund->hasFreshdeskTicket()" href="{$getFreshdeskTicketLink()}" target="_blank" style="float:right">open ticket ></a>
			</div>
			<div class="card-body">
				{if $refund->hasFreshdeskTicket()}
					<div class="text-center" style="margin-bottom: 20px">
						<a href="javascript:void(0);" data-toggle="modal" data-target="#modal-message" data-backdrop="static" data-keyboard="false" class="resolve-button btn btn-outline-primary">Send message</a>
					</div>

					{snippet ticketConversation}
					    {if isset($messages)}
					        {foreach $messages as $message}
                                <div class="row">
                                    <div class="col-md-12">
                                        {if !$message->isIncoming()}
                                            {var $agent = $message->getResponder()}
                                            <strong>{($agent ? $agent->getFullName() : "<em>operator</em>") |noescape}</strong>
                                        {else}
                                            <strong>user</strong>
                                        {/if}
                                        v {$message->getMessageCreatedAt()->format('d.m.Y H:i:s')} added:<br />
										{if $message->getMessageId() === null}<span class='badge badge-danger'>Synchronizing with freshdesk</span><br />{/if}
                                        <small>{$message->getBody() |breakLines}</small>
                                        {sep}<hr />{/sep}
                                    </div>
                                </div>
					        {/foreach}
						{else}
							<div class="text-center">Loading…</div>
							<script>
								$(document).ready(function() {
									$.nette.ajax({
										off: ['unique'],
										url: {link loadTicketConversation!}
									});
								});
							</script>
						{/if}
					{/snippet}
				{else}
					<em>Ticket has not been created yet</em>
				{/if}
			</div>
		</div>

	</div>
</div>

<div class="modal fade" id="modal-approve" tabindex="-1" role="dialog" >
	<div class="modal-dialog" role="document" style="min-width:1140px">
		<div class="modal-content" style="min-width:1180px">
			<div class="modal-header">
				<h3 class="modal-title">Approve refund</h3>
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div class="modal-body">
				<div class="row">
					<div class="offset-2 col-8">
						{form resolveRefundControl-form, class => "form-horizontal approve-refund-form"}
							<div class="row form-group" n:ifset="$form[offer]">
								<div class="col-md-3 form-text">Shop offer:</div>
								<div class="col-md-9">
									{input offer, class => "form-control", data-type => "approve-offer"}
								</div>
							</div>

							<div class="row form-group" n:ifset="$form[orderAmount]">
                                <div class="col-md-3 form-text">Cashback(%):</div>
                                <div class="col-md-9">
                                    <input type="text" name="cashback" data-type="cashback" class="form-control">
                                </div>
                           </div>

							<div class="row form-group" n:ifset="$form[orderAmount]">
							    <div class="col-md-12">
							        <div class="row">
                                       <div class="col-6">
                                           <div class="row">
                                              <div class="col-md-6">
                                                <label>Order amount without Tax:</label>
                                              </div>

                                               <div class="col-md-6">
                                                {input orderAmount, class => "form-control text-center", data-type => "order-amount"}
                                               </div>
                                           </div>
                                       </div>

                                       <div class="col-6">
                                           <div class="row">
                                                <div class="col-md-6">
                                                    <label>With Tax:</label>
                                                </div>

                                               <div class="col-md-6">
                                                    {input orderAmountWithTax, class => "form-control text-center", data-type => "order-amount-with-tax"}
                                               </div>
                                           </div>
                                       </div>
                                    </div>
                                </div>

								<script>
									$(document).ready(function() {
										$("[data-type=approve-offer]").off("change").on("change", function() {
											let bonus = $(this).val();
											let bonusAmount = bonus.split("_")[1];

											$("[data-type=approve-bonusAmount]").val(bonusAmount);
											$("[data-type=cashback]").val(bonus.split("_")[3]);
											$(" [data-type=order-amount]").trigger('keyup');
										}).trigger("change");

										$("[data-type=order-amount-with-tax], [data-type=order-amount]").on("keyup", function() {
										    let bonus = $("[data-type=approve-offer]").val();
                                            let tax = parseInt(bonus.split("_")[4]);

										    if ($(this).data("type") === "order-amount-with-tax") {
                                             let orderAmount = parseFloat($(this).val());
                                                let coefficient = parseInt("1" + tax);
                                                let taxValue = orderAmount * tax / coefficient;
                                                $("[data-type=order-amount]").val((orderAmount - taxValue).toFixed(2));
										    } else {
                                                let orderAmountWithoutTax = parseFloat($(this).val());
                                                $("[data-type=order-amount-with-tax]").val((orderAmountWithoutTax + orderAmountWithoutTax / 100 * tax).toFixed(2));
										    }
										});

										$("[data-type=order-amount-with-tax], [data-type=order-amount], [data-type=cashback]").on("keyup", function() {
											let bonusData = $("[data-type=approve-offer]").val().split("_");
											let type = bonusData[2];

											let cashback = $("[data-type=cashback]").val()

											if (type === "absolute") {
												return;
											}

											let orderAmount = ($("[data-type=order-amount]").val().replace(/ /g,'').replace(',', '.') / 100) * cashback;
											orderAmount = parseFloat(orderAmount.toFixed(2));

											if (isNaN(orderAmount)) {
												$("[data-type=approve-bonusAmount]").val(0);
												return
											}

											$("[data-type=approve-bonusAmount]").val(orderAmount);
										});
									});
								</script>
							</div>

							<div class="row form-group" n:if="$refund->getType() === $refund::TYPE_MISSING_COMMISSION || $refund->getType() === $refund::TYPE_INCORRECT_AMOUNT">
								<div class="col-md-3 form-text">Amount in {$userCurrency}:</div>
								<div class="col-md-3">
									{input bonusAmount, class => "form-control text-center", data-type => "approve-bonusAmount"}
								</div>
{*							</div>*}

{*							<div class="row form-group" n:if="$refund->getType() === $refund::TYPE_MISSING_COMMISSION || $refund->getType() === $refund::TYPE_INCORRECT_AMOUNT">*}
								<div class="col-md-3 form-text">Confirmation treshold:</div>
								<div class="col-md-3">
									{input confirmationTreshold, class => "form-control text-center"}
								</div>
							</div>

							<div class="row form-group" data-type="approved-message-container"">
								<div class="col-md-3 form-text">Message for user:</div>
								<div class="col-md-9">
									{var $responseList = $refundResponseProvider->getRefundApprovedResponses($refund)}
									{if $responseList}
										<select class="form-control" data-type="approved-response">
											<option n:foreach="$responseList as $responseName => $responseContent" value="{str_replace("<br />", "\n", $responseContent)}">{$responseName}</option>
										</select>
										<script>
											$(document).ready(function() {
												$("[data-type=approved-response]").off("change").on("change", function() {
													// $("[data-type=approved-message]").val($(this).val());
													$R('[data-type=approved-message]', 'source.setCode', $(this).val())
												}).trigger("change");
											});
										</script>
									{/if}
									<br>
									{input message, class => "form-control redactor", style => "width:100%;min-height:340px", data-type => "approved-message"}
								</div>
							</div>

							<div class="row form-group">
								<div class="col-md-3"></div>
								<div class="col-md-9"><label><input n:name="noMessage" data-type="approved-noMessage" /> Do not send message to user</label></div>
								<script>
									$(document).ready(function() {
										$("[data-type=approved-noMessage]").off("change").on("change", function() {
											if ($(this).is(":checked")) {
												$("[data-type=approved-message-container]").slideUp();
											} else {
												$("[data-type=approved-message-container]").slideDown();
											}
										});
									});
								</script>
							</div>

							<div class="row form-group" n:if="$refund->getType() === $refund::TYPE_UNCONFIRMED_TRANSACTION">
								<div class="col-md-12" n:if="$refund->getRelatedTransaction()">
									Transaction will be confirmed to user.
								</div>
							</div>

							<div class="row form-group" n:if="$refund->getType() === $refund::TYPE_INCORRECT_AMOUNT || $refund->getType() === $refund::TYPE_MISSING_COMMISSION">
								<div class="col-md-12">
									The specified amount will be added to the user as a bonus.
								</div>
							</div>

							<div class="row form-group">
								<div class="col-md-4"></div>
								<div class="col-md-8">
									{input approve, class => "btn btn-success", value => "Approve refund", id => "approve-refund"}
								</div>
							</div>
						{/form}
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<div class="modal fade" id="modal-decline" tabindex="-1" role="dialog" >
	<div class="modal-dialog" role="document" style="min-width:940px">
		<div class="modal-content" style="min-width:980px">
			<div class="modal-header">
				<h3 class="modal-title">Decline refund</h3>
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div class="modal-body">
				<div class="row">
					<div class="offset-2 col-8">
						{form resolveRefundControl-form, class => "form-horizontal"}
							<div class="hide" n:ifset="$form[offer]">{input offer}</div>
							<div class="row form-group">
								<div class="col-md-3">Reason</div>
								<div class="col-md-9">
									{input declineReasonType}
								</div>
								<script>
									$(document).ready(function() {
										$("select[name=declineReasonType]").on("change", function() {
											if ($(this).val() === "other") {
												$(".decline-reason-message").show();
											} else {
												$(".decline-reason-message").hide();
											}
										});

										$("#decline-refund").on("click", function(e) {
											if (!$("select[name=declineReasonType]").val()) {
												e.preventDefault();
												alert("Select reason");
												return false;
											}
										})
									});
								</script>
							</div>

							<div class="row form-group decline-reason-message" style="display: none">
								<div class="col-md-3">Specify reason</div>
								<div class="col-md-9">
									{input declineReasonMessage, class => "form-control"}
								</div>
							</div>

							<div class="row form-group" data-type="declined-message-container">
								<div class="col-md-3 form-text">Message to user:</div>
								<div class="col-md-9">
									{var $responseList = $refundResponseProvider->getRefundDeclinedResponses($refund)}
									{if $responseList}
										<select class="form-control" data-type="declined-response">
											<option n:foreach="$responseList as $responseName => $responseContent" value="{str_replace("<br />", "\n", $responseContent)}">{$responseName}</option>
										</select>
										<script>
											$(document).ready(function() {
												$("[data-type=declined-response]").off("change").on("change", function() {
													$R('[data-type=declined-message]', 'source.setCode', $(this).val())
												}).trigger("change");
											});
										</script>
									{/if}
									<br>
									{input message, class => "form-control redactor", style => "width:100%;min-height:340px", data-type => "declined-message"}
								</div>
							</div>

							<div class="row form-group">
								<div class="col-md-3"></div>
								<div class="col-md-9"><label><input n:name="noMessage" data-type="declined-noMessage" /> Do not send message to user</label></div>
								<script>
									$(document).ready(function() {
										$("[data-type=declined-noMessage]").off("change").on("change", function() {
											if ($(this).is(":checked")) {
												$("[data-type=declined-message-container]").slideUp();
											} else {
												$("[data-type=declined-message-container]").slideDown();
											}
										});
									});
								</script>
							</div>

							<div class="row form-group">
								<div class="col-md-3"></div>
								<div class="col-md-9">
									{input decline, class => "btn btn-danger", value => "Decline refund", id => "decline-refund"}
								</div>
							</div>
						{/form}
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<div class="modal fade" id="modal-message" tabindex="-1" role="dialog" >
	<div class="modal-dialog" role="document" style="min-width:940px">
		<div class="modal-content" style="min-width:980px">
			<div class="modal-header">
				<h3 class="modal-title">Send message to user</h3>
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
			</div>
			<div class="modal-body">
				<div class="row">
					<div class="offset-2 col-8">
						{form refundSendMessageControl-form, class => "form-horizontal"}
							<div class="row form-group" data-type="declined-message-container">
								<div class="col-md-3 form-text">Message for user:</div>
								<div class="col-md-9">
									{var $responseList = $refundResponseProvider->getRefundMessageResponses($refund)}
									{if $responseList}
										<select class="form-control" data-type="message-response">
											<option n:foreach="$responseList as $responseName => $responseContent" value="{str_replace("<br />", "\n", $responseContent)}">{$responseName}</option>
										</select>
										<script>
											$(document).ready(function() {
												$("[data-type=message-response]").off("change").on("change", function() {
													$R('[data-type=message-message]', 'source.setCode', $(this).val())
												}).trigger("change");
											});
										</script>
									{/if}
									<br>
									{input message, class => "form-control redactor", style => "width:100%;min-height:340px", data-type => "message-message"}
								</div>
							</div>

							<div class="row form-group">
								<div class="col-md-3"></div>
								<div class="col-md-9">
									{input submit, class => "btn btn-success", value => "Send message", id => "send-message"}
								</div>
							</div>
						{/form}
					</div>
				</div>
			</div>
		</div>
	</div>
</div>


{define userLink, $linkedUser}
	<a n:href=":Admin:Account:User:userCard, id => $linkedUser->getId()">{$linkedUser->getUserName()}</a>
{/define}

<style>
	ul, ol { padding-left: 20px; }
	.resolve-icon { font-size: 24px }
</style>




{define subUsers $users, $reason}
	{if !empty($users)}
		{foreach $users as $conflictUser}
			<li>
				{$conflictUser->getLocalization()->getLocale()|flag|noescape}
				{include userEmail, $conflictUser->getEmail(), $conflictUser->getId()}
				({include userId, $conflictUser->getId()})
				{include suspectedLabel, $conflictUser}
				- {$reason}
			</li>
		{/foreach}
	{/if}
{/define}

{define userId $userId}<a href="{plink :Admin:Account:User:userCard, 'id' => $userId}" target="_blank">{$userId}</a>{/define}
{define userEmail $userEmail, $userId}<a href="{plink :Admin:Transactions:Transaction:default, 'transactionsFilter' => ['userId' => $userId]}" target="_blank">{$userEmail}</a>{/define}
