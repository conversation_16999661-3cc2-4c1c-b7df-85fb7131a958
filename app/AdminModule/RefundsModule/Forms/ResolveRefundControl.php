<?php

namespace tipli\AdminModule\RefundsModule\Forms;

use Nette;
use Nette\Application\UI\Form;
use tipli\InvalidArgumentException;
use tipli\Model\Account\Entities\User;
use tipli\Model\Configuration;
use tipli\Model\Currencies\CurrencyFilter;
use tipli\Model\Refunds\Entities\Refund;
use tipli\Model\Refunds\RefundFacade;
use tipli\Model\Refunds\RefundResolver;
use tipli\Model\Shops\Entities\Offer;
use tipli\Model\Shops\OfferFacade;
use tipli\Model\Shops\RewardFilter;

class ResolveRefundControl extends Nette\Application\UI\Control
{
	public $onSuccess = [];

	/** @var Refund */
	private $refund;

	/** @var RefundResolver */
	private $refundResolver;

	/** @var RefundFacade */
	private $refundFacade;

	/** @var User */
	private $admin;

	/** @var CurrencyFilter */
	private $currencyFilter;

	/** @var OfferFacade */
	private $offerFacade;

	/** @var Configuration */
	private $configuration;

	/** @var RewardFilter */
	private $rewardFilter;

	public function __construct(Refund $refund, User $admin, RefundResolver $refundResolver, RefundFacade $refundFacade, OfferFacade $offerFacade, CurrencyFilter $currencyFilter, Configuration $configuration, RewardFilter $rewardFilter)
	{
		$this->refund = $refund;
		$this->refundResolver = $refundResolver;
		$this->refundFacade = $refundFacade;
		$this->admin = $admin;
		$this->currencyFilter = $currencyFilter;
		$this->offerFacade = $offerFacade;
		$this->configuration = $configuration;
		$this->rewardFilter = $rewardFilter;
	}

	/**
	 * @return Form
	 */
	public function createComponentForm()
	{
		$form = new Form();
		$refund = $this->refund;

		$form->addSubmit('approve');
		$form->addSubmit('decline');

		$form->addCheckbox('noMessage');

		$form->addTextArea('message')
			->addConditionOn($form['noMessage'], Form::NOT_EQUAL, true)
			->setRequired('Zadejte zprávu pro uživatele.')
		;

		if ($refund->getType() === Refund::TYPE_MISSING_COMMISSION) {
			$form->addSelect('offer')
				->setItems($this->getOffersList());

			$form->addText('orderAmountWithTax')
				->setDefaultValue(number_format($refund->getOrderAmount(), 2, '.', ''));

			$form->addText('orderAmount')
				->setDefaultValue(number_format($this->refundResolver->resolveOrderAmountWithoutTax($refund), 2, '.', ''));
		}

		if (in_array($refund->getType(), [Refund::TYPE_MISSING_COMMISSION, Refund::TYPE_INCORRECT_AMOUNT], true)) {
			$form->addText('bonusAmount')
				->addConditionOn($form['approve'], Form::SUBMITTED)
				->setRequired('Zadejte částku')
				->addRule(Form::FLOAT, 'Neplatný formát částky')
			;

			$form->addText('confirmationTreshold')
				->setDefaultValue($this->configuration->getDefaultConfirmationTreshold($refund->getLocalization()))
				->addConditionOn($form['approve'], Form::SUBMITTED)
				->setRequired('Zadejte potvrzovací práh')
				->addRule(Form::FLOAT, 'Neplatný formát potvrzovacího práhu')
			;

			if ($refund->getType() === Refund::TYPE_MISSING_COMMISSION || $refund->getType() === Refund::TYPE_INCORRECT_AMOUNT) {
				$amount = $this->refundResolver->resolveBonusAmount($refund);

				if ($amount !== null) {
					$form['bonusAmount']->setValue($amount);
				}
			}
		}

		$form->addSelect('declineReasonType', null, Refund::getDeclineReasons())
			->setPrompt('--- vyberte jednu z možností ---')
			->setRequired(false);

		$form->addText('declineReasonMessage')
			->setNullable()
			->addConditionOn($form['declineReasonType'], $form::EQUAL, Refund::DECLINE_REASON_OTHER)
			->setRequired('Specifikujte důvod');

		$form->addSubmit('submit');

		$form->onSuccess[] = [$this, 'formSucceeded'];

		return $form;
	}

	public function formSucceeded(Form $form, $values)
	{
		try {
			$refund = $this->refund;
			$message = $values->noMessage ? null : $values->message;
			$confirmationTreshold = isset($values->confirmationTreshold) ? (float) $values->confirmationTreshold : null;

			if ($form['approve']->isSubmittedBy() || $this->configuration->getMode() === 'test') {
				$bonusAmount = $values->bonusAmount ?? null;

				$this->refundFacade->approveRefund(
					$refund,
					$message,
					$bonusAmount,
					$confirmationTreshold,
					$this->admin
				);
			}

			if ($form['decline']->isSubmittedBy()) {
				$this->refundFacade->declineRefund(
					$refund,
					$message,
					$this->admin,
					$values->declineReasonType,
					$values->declineReasonMessage
				);
			}

			$this->onSuccess();
		} catch (InvalidArgumentException $e) {
			$form->addError($e->getMessage());
		}
	}

	private function getOffersList(): array
	{
		$list = [];

		/** @var Offer $offer */
		foreach ($this->offerFacade->resolveOffersForShop($this->refund->getShop()) as $offer) {
			$bonusAmount = $this->refundResolver->resolveBonusAmount($this->refund, $offer);
			$orderAmountWithoutTax = number_format($this->refundResolver->resolveOrderAmountWithoutTax($this->refund), 2, '.', '');
			$tax = RefundResolver::TAXES[$this->refund->getUser()->getLocalization()->getId()];

			$coefficient = 0;
			if (!$offer->isAbsolute() && $orderAmountWithoutTax > 0) {
				$coefficient = round(($bonusAmount / $orderAmountWithoutTax) * 100, 2);
			}

			$list[$offer->getId() . '_' . $bonusAmount . '_' . $offer->getType() . '_' . $coefficient . '_' . $tax] = $offer->getName() . ' (' . $this->rewardFilter->__invoke($offer, false, 'pure', $this->refund->getUser()) . ')';
		}

		return $list;
	}
}

interface IResolveRefundControlFactory
{
	/** @return ResolveRefundControl */
	public function create(Refund $refund, User $admin);
}
