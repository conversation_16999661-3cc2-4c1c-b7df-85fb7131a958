{var $title = 'User card'}
{var $freeContent = true}
{block content}

{capture $userCurrency}{$userEntity->getLocalization()->getCurrency() |currency}{/capture}

<div class="row">
	<div class="col-md-12">
		<div class="card card-accent-primary">
			<div class="card-body">
				<h1 n:block="title" class="pull-left">
					<img src="{$basePath}/images/flags/{$userEntity->getLocalization()->getLocale()}.png" width="20px" style="display:inline;margin-right:10px" />
					User: {$userEntity->getUserName()}
					{if $userEntity->isInactiveUser()}
						<span class="badge badge-danger" style="font-size:18px;">inactive</span>
					{/if}
					{if $userEntity->isBanned()}
						<span class="badge badge-danger" style="font-size:18px;"><i class="fa fa-warning"></i> banned</span>
					{/if}
					{if $userEntity->isRemoved()}
						<span class="badge badge-danger" style="font-size:18px;">removed</span>
					{/if}
				</h1>
				<div class="pull-right">
					{form goToAnotherUserForm, class => "form-inline"}
						{input email, placeholder => "Email", class => "form-control mb-2 mr-sm-2"}
						{input submit, class => "btn btn-primary mb-2"}
					{/form}
					{ifset $anotherUsers}
						{foreach $anotherUsers as $anotherUser}
							<a n:href="this, $anotherUser->getId()">
							<div style="display:inline-block;margin-right:5px;float:left;">{$anotherUser->getLocalization() |flag |noescape}</div> {$anotherUser->getEmail()}<br />
							</a>
						{/foreach}
					{/ifset}
				</div>
			</div>
			<div class="card-footer">
				<a target="_blank" class="btn btn-sm btn-primary btn-smf" n:href=":Admin:Transactions:Transaction:default, transactionsFilter => ['userId' => $userEntity->getId()]">Transactions</a>
				<div class="dropdown" style="display:inline">
					<button class="btn btn-primary btn-sm dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
						Bonuses
					</button>
					<div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
						<a class="dropdown-item" target="_blank" n:href=":Admin:Rewards:MoneyReward:default, rewardsGrid-filter => ['email' => $userEntity->getEmail()]">User`s bonuses</a>
						<a class="dropdown-item" target="_blank" n:href=":Admin:Transactions:Transaction:dealBonus, 'userId' => $userEntity->getId()">Create bonus for reporting an invalid deal</a>
					</div>
				</div>
				<div class="dropdown" style="display:inline">
					<button class="btn btn-primary btn-sm  dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
						Share-based discount
					</button>
					<div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
						<a class="dropdown-item" target="_blank" n:href=":Admin:Rewards:ShareReward:default, rewardsGrid-filter => ['email' => $userEntity->getEmail()]">Share-based discounts</a>
						<a class="dropdown-item" target="_blank" n:href=":Admin:Rewards:ShareReward:reward, 'userId' => $userEntity->getId(), 'localizationId' => $userEntity->getLocalization()->getId()">New share-based discount</a>
					</div>
				</div>
				<a target="_blank" class="btn btn-sm btn-primary" n:href=":Admin:Payouts2:Payouts:default, localizationId: $userEntity->getLocalization()->getId(),userEmailOrId: $userEntity->getId(), state: search">Payouts</a>
				<a target="_blank" class="btn btn-sm btn-primary" n:href=":Admin:Shops:Redirection:default, redirectionsGrid-filter => ['name' => $userEntity->getEmail()]">Redirections</a></li>
				<a target="_blank" class="btn btn-sm btn-primary" n:href=":Admin:Messages:Message:default, messagesGrid-filter => ['toEmail' => $userEntity->getEmail()]">E-mails</a></li>
				<a target="_blank" class="btn btn-sm btn-primary" n:href=":Admin:Messages:Message:sendMessage 'userId' => $userEntity->getId(), 'section' => 'common'">Send email</a></li>
				<a target="_blank" class="btn btn-sm btn-primary" n:href=":Admin:Transactions:Transaction:refundBonus, 'userId' => $userEntity->getId()">New refund</a></li>
				<a n:if="!$userEntity->isBanned()" n:href=":Admin:Account:User:eventBanUser $userEntity->getId()" class="btn btn-sm btn-danger" onclick="javascript: return confirm('Do you really want to ban this user?');">BAN</a>
				<a n:if="$userEntity->isBanned()" n:href=":Admin:Account:User:eventUnbanUser $userEntity->getId()" class="btn btn-sm btn-danger" onclick="javascript: return confirm('Do you really want to unban this user?');">UNBAN</a>
			</div>
    	</div>
	</div>
</div>
<div class="clearfix"></div>

{var $someUserInCountries = $findSameUserInCountries()}
<div class="row" n:if="empty($someUserInCountries) === false">
	<div class="col-md-12">
		<div class="alert alert-info">User`s account`s with the same email in multiple countries.:&nbsp;
			{foreach $someUserInCountries as $someUserInCountry}
				<a target="_blank" n:href="this, $someUserInCountry->getId()"><img src="{$basePath}/images/flags/{$someUserInCountry->getLocalization()->getLocale()}.png" style="display:inline;" /> {$someUserInCountry->getEmail()}</a>
				{sep},&nbsp;{/sep}
			{/foreach}
		</div>
	</div>
</div>


<div class="row">
    <div class="col-md-4">

        {*Zakladni informace*}
        <div class="card card-accent-primary">
            <div class="card-header">
                <strong>General informations</strong>
            </div>
            <div class="card-body">
				<div>
					<span class="badge badge-warning text-white mb-2" n:if="$userEntity->isVip()">VIP</span>
					<span class="badge badge-success" n:if="$userEntity->isAffiliate()">Affiliate</span>
					<span class="badge badge-dark" n:if="$userEntity->isSuspected()" data-toggle="tooltip" data-placement="top" title="SUSPECT REASON: {$userEntity->getSegmentData()->getSuspectedReason()}">Suspected</span>
				</div>

                ID: <strong>{$userEntity->getId()}</strong>
                <br />
                Fullname: <strong>{$userEntity->getFullName()}</strong>
                <br />
                Email: <strong>{$userEntity->getEmail()}</strong>
                <br />
                Phone: {if $userEntity->getPhoneNumber()}<strong>{$userEntity->getPhoneNumber()}</strong>{else}<em>empty</em>{/if}
                <br />
                Account number: {if $userEntity->hasFilledBankDetails()}<strong>{$userEntity->getAccountNumber()}</strong>{else}<em>empty</em>{/if}
                <br />
                Created at: <strong>{$userEntity->getCreatedAt()->format('d.m.Y H:i')}</strong>
                {if $userEntity->getParentUser()}
                    <br />
                    Recommende by user {include userLink, $userEntity->getParentUser()}
                {/if}
                <br />
                Addon installed: {if $userEntity->hasInstalledAddon()}<span class="badge badge-success">yes</span>{else}<span class="badge badge-danger">no</span>{/if}
                <br />
                Email verified:
                {if $userEntity->hasVerifiedEmail()}
                    <span class="badge badge-success">yes</span>
                {else}
                    <span class="badge badge-danger">no</span>
                    <a n:href="verifyUserEmail! $userEntity->getId()" class="text-danger" onclick="javascript: return confirm('Do you really want to verify this user?');">verify</a>
                {/if}
                <br />
                Account number verified:
                {if !empty($userEntity->getAccountNumber())}
                    {if $userEntity->hasVerifiedAccountNumber()}
                        <span class="badge badge-success">yes</span>
                    {else}
                        <span class="badge badge-danger">no</span>
                        <a n:href="verifyUserAccountNumber! $userEntity->getId()" class="text-danger" onclick="javascript: return confirm('Do you really want to verify this account?');">verify</a>,
                        <a n:href="sendAccountNumberVerificationEmail! $userEntity->getId()" class="text-primary" onclick="javascript: return confirm('Do you really want to send verification email?');">send verification e-mail</a>
                    {/if}
                {else}
                    <em>empty</em>
                {/if}
                <br />
                Phone verified:
                {if !empty($userEntity->getPhoneNumber())}
                    {if $userEntity->hasVerifiedPhoneNumber()}
                        <span class="badge badge-success">yes</span>
                    {else}
                        <span class="badge badge-danger">no</span>
                        <a n:href="verifyUserPhoneNumber! $userEntity->getId()" class="text-danger" onclick="javascript: return confirm('Do you really want to verify phone?');">verify</a>
                    {/if}
				{else}
                    <em>empty</em>
                {/if}
				<br />
				Mobile app:
				{if $userEntity->getMobileDevices()->isEmpty()}
					<span class="badge badge-danger">not installed</span>
				{else}
					{foreach $userEntity->getMobileDevices() as $mobileDevice}
						<span class="badge badge-primary">{$mobileDevice->getPlatform()}</span>
					{/foreach}
				{/if}
                {if $utm = $userEntity->getUtm()}
                    <br />
                    UTM: {$utm->getUtmSource()} / {$utm->getUtmMedium()} / {$utm->getUtmCampaign()}
                {/if}
                <br />
                <br />
                <a n:href=":Admin:Account:User:user $userEntity->getId()" class="btn btn-primary btn-sm">
                    <span class="fa fa-pen"></span> User settings
                </a>
                <a n:href=":Admin:Account:User:login $userEntity->getId()" class="btn btn-light btn-sm">
                    <span class="fa fa-key"></span> Log-in as user
                </a>

            </div>
        </div>

        <div class="card card-accent-primary">
            <div class="card-header">
                <strong>Email subscribe</strong>
            </div>
            <div class="card-body">
                {snippet sendingPolicies}
					Send emails:
                    {if $userEntity->hasUnsubscribedEmails()}
                        <span class="badge badge-danger">not activated</span>
                        <a href="javascript: void(0)" data-event="subscribe" data-action="update-email-subscription">set active</a>
                    {else}
                        <span class="badge badge-success">activated</span>
                        <a href="javascript: void(0)" data-event="unsubscribe" data-action="update-email-subscription">set inactive</a>
                    {/if}

                    {if !$userEntity->hasUnsubscribedEmails()}
						<hr />
						<ul>
							{var $sendingPolicy = $userEntity->getSendingPolicyValues()}
							{foreach \tipli\Model\Account\Entities\SendingPolicy::getEmailContentTypes() as $emailContentType => $contentType}
								<li>
									{_'model.account.sendingPolicy.contentType.' . $contentType}

									{if $sendingPolicy[$emailContentType]}
										<span class="badge badge-success">activated</span>
										<a href="javascript: void(0)" data-type="{$emailContentType}" data-event="unsubscribe" data-action="update-sending-policy">set active</a>
									{else}
										<span class="badge badge-danger">not activated</span>
										<a href="javascript: void(0)" data-type="{$emailContentType}" data-event="subscribe" data-action="update-sending-policy">set inactive</a>
									{/if}
								</li>
							{/foreach}
						</ul>
                    {/if}

                    <script>
                        $(document).ready(function() {
                            $("[data-action=update-sending-policy]").off("click").on("click", function() {
                                $.nette.ajax({
                                    url: {link updateSendingPolicy!},
                                    method: "POST",
                                    data: {
                                        type: $(this).data('type'),
                                        event: $(this).data('event')
                                    }
                                });
                            });

                            $("[data-action=update-email-subscription]").off("click").on("click", function() {
                                $.nette.ajax({
                                    url: {link updateEmailSubscription!},
                                    method: "POST",
                                    data: {
                                        event: $(this).data('event')
                                    }
                                });
                            });
                        });
                    </script>
                {/snippet}
            </div>
        </div>

        <div class="card card-accent-primary">
            <div class="card-header">
                <strong>Last user changes</strong>
            </div>
            <div class="card-body">
                <em n:if="$userEntity->getChanges()->isEmpty()">- none -</em>
                {foreach $userEntity->getChanges() as $change}
                    {continueIf $change->isVerificationRequired() && !$change->isVerified()}

                    <div><strong data-toggle="tooltip" data-placement="top" title="{$change->getIp()}">{$change->getCreatedAt()->format('d.m.Y H:i:s')}</strong></div>
                    {if $change->getFirstName()}firstname: {$change->getFirstName()}<br />{/if}
                    {if $change->getLastName()}lastname: {$change->getLastName()}<br />{/if}
                    {if $change->getGender()}gender: {$change->getGender()}<br />{/if}
                    {if $change->getBirthdate()}birthdate: {$change->getBirthdate()->format('d.m.Y')}<br />{/if}
                    {if $change->getPhoneNumber()}phone: {$change->getPhoneNumber()}<br />{/if}
                    {if $change->isPasswordChanged()}password changed<br />{/if}
                    {if $change->getAccountNumber()}account number: {$change->getAccountNumber()}<br />{/if}
                    <hr />
                {/foreach}
            </div>
        </div>

		{*Campaign 300*}
		<div class="card card-accent-warning" n:if="!$userEntity->isWhiteLabelUser()">
			<div class="card-header">
				<strong>Campaign</strong>
			</div>
			<div class="card-body">
				{if $isCampaignAllowed}
					<table style="width:100%;">
						<col width="30%">
						<col width="70%" align="right">
						<tr>
							<td>Campaign activated at:</td>
							<td>{$campaignSubscribedAt ? '<strong>' . $campaignSubscribedAt->format('d.m.Y H:i')  . '</strong>': '<em>not yet</em>' |noescape}</td>
						</tr>
                        <tr>
                            <td>Campaign valid till:</td>
                            <td>
                                {$campaignValidTill ? '<strong>' . $campaignValidTill->format('d.m.Y H:i')  . '</strong>': '<em>not yet</em>' |noescape}
                                <span class="badge badge-danger" n:if="!$isCampaignValid && $campaignValidTill">Campaign already expired</span>
                            </td>
                        </tr>
						<tr n:if="$campaignSubscribedAt">
							<td>Campaign mode</td>
							<td>{if $campaignNewUserMode}new user <small>(double reward)<em>{else}active user<small>(needs to earn 200 kč / 5 €)</small>{/if}</td>
						</tr>
						<tr n:if="$campaignSubscribedAt">
							<td>Available bonus:</td>
							<td>{$campaignAvailableBonus |amount} {$userEntity->getLocalization() |currency}</td>
						</tr>
						<tr n:if="$campaignSubscribedAt">
							<td>Used bonus:</td>
							<td>{$campaignUsedBonus |amount} {$userEntity->getLocalization() |currency}</td>
						</tr>
						{if !$campaignNewUserMode && $campaignActiveTransaction}
							<tr>
								<td>State:</td>
								<td>{$campaignActiveTransaction->isConfirmed() ? "confirmed" : "not confirmed"}</td>
							</tr>
							<td n:if="!$campaignActiveTransaction->isConfirmed()" colspan="2">
								<em style="font-size:12px;">
									The bonus will be confirmed once the rewards and refunds are confirmed as well from the campaign activation date in amount <strong>{$campaignActiveTransaction->getConfirmationTreshold()} {$campaignActiveTransaction->getCurrency() |currency}</strong>.
								Already confirmed transactions amount: <strong>{$campaignAlreadyConfirmedTransactionsAmount |amount:2} {$campaignActiveTransaction->getCurrency() |currency}</strong>.
								</em>
							</td>
						{/if}
					</table>

{*					<hr />*}
{*					<strong n:if="!$campaignSubscribedAt">Aktivovat kampaň ručně:</strong>*}
{*					<strong n:if="$campaignSubscribedAt">Reaktivovat kampaň ručně:</strong>*}
{*					<div class="row">*}
{*						<div class="col-md-8">Datum počátku kampaně: <br /><small>(bonusy budou od tohoto data zpětně přidány)</small></div>*}
{*						<div class="col-md-4"><input type="text" class="form-control datetimepicker" id="create-campaign-date" value="{(new \DateTime)->format('Y-m-d H:i:s')}" /></div>*}
{*					</div>*}
{*					<div class="row">*}
{*						<div class="col-md-8"></div>*}
{*						<div class="col-md-4 text-right">*}
{*							<a href="javascript:activateCampaign();" class="btn btn-primary" style="width:100%;">Aktivovat kampaň</a>*}
{*						</div>*}
{*					</div>*}
{*					<script>*}
{*						function activateCampaign() {*}
{*							let date = $("#create-campaign-date").val();*}
{*							if (date === "") {*}
{*								alert("Zadejte datum.");*}
{*								return;*}
{*							}*}
{**}
{*							window.location = "{$presenter->link("activateCampaign!") |noescape}&date=" + date;*}
{*						}*}
{*					</script>*}
				{else}
					<div class="text-center"><em>Campaign is only for CZ/SK users.</em></div>
				{/if}
			</div>
		</div>

        <div class="card card-accent-primary">
            <div class="card-header">
                <strong>Links</strong>
            </div>
            <div class="card-body">
                <ul>
					{if $userEntity->isActive()}
						<li><a n:href=":Admin:Account:User:eventDeactivateUser $userEntity->getId()" onclick="javascript: return confirm('Do you really want to deactivate user?');">Deactivate user</a></li>
					{/if}
					{if !$userEntity->isRemoved() && $user->getIdentity()->isSuperadmin()}
						<li><a n:href=":Admin:Account:User:eventRemoveUser $userEntity->getId()" class="text-danger" onclick="javascript: return confirm('Do you really want to remove user? Every information about him (including email) will be removed.');">Remove user</a></li>
					{/if}
                </ul>
				<div>
					{if $userEntity->isVip()}
						<a n:href=":Admin:Account:User:eventUnsetVipSegment $userEntity->getId()" class="btn btn-outline-info btn-sm">Odebrat VIP</a>
					{else}
						<a n:href=":Admin:Account:User:eventSetVipSegment $userEntity->getId()" class="btn btn-info text-white btn-sm">Nastavit VIP</a>
					{/if}
					{if $userEntity->isAffiliate()}
						<a n:href=":Admin:Account:User:eventUnsetAffiliateSegment $userEntity->getId()" class="btn btn-outline-success btn-sm">Odebrat affiliate</a>
					{else}
						<a n:href=":Admin:Account:User:eventSetAffiliateSegment $userEntity->getId()" class="btn btn-success text-white btn-sm">Nastavit affiliate</a>
					{/if}
					{if $userEntity->isSuspected()}
						<a n:href=":Admin:Account:User:eventUnsuspectUser $userEntity->getId()" class="btn btn-outline-danger btn-sm" data-toggle="tooltip" data-placement="top" title="DŮVOD PODEZŘENÍ: {$userEntity->getSegmentData()->getSuspectedReason()}">Odebrat uživateli status 'podezřelý'</a>
					{else}
						<a n:href=":Admin:Account:User:eventSuspectUser $userEntity->getId()" class="btn btn-danger text-white btn-sm">Označit uživatele jako podezřelého</a>
					{/if}
				</div>
            </div>
        </div>

        {*Poznamka*}
        <div class="card card-accent-primary">
            <div class="card-header">
                <strong>Poznámka</strong>
            </div>
            <div class="card-body">
                <form class="form-horizontal {empty($userEntity->getNote()) ? hide} settings-page" id="note-form" n:name="userNoteControl-form">
                    {input note, style => "width:100%;height: " . ($userEntity->getNote() ? 120 : 70) ."px;", class => "form-control"}
                    <div style="margin:10px 0"></div>
                    {input $form[submit], class => "btn"}
                </form>
                <a n:if="empty($userEntity->getNote())" href="javascript:void(0);" onclick="javascript:$(this).remove();$('#note-form').removeClass('hide');">
                    + přidat poznámku
                </a>
            </div>
        </div>

        <div class="card card-accent-primary">
            <div class="card-header">
                <strong>Duplicity</strong>
            </div>
            <div class="card-body" style="padding: 0;">
                {control userDuplicityGrid}
            </div>
        </div>

        {*Doporučení uživatelé*}
        <div class="card card-accent-primary">
            <div class="card-header">
                <strong>Doporučení uživatelé</strong>
            </div>
            <div class="card-body">
                {if count($userEntity->getReferencedUsers()->toArray()) > 0}
                    <ol>
                        {foreach $userEntity->getReferencedUsers() as $referencedUser}
                            <li>
                                {include userLink, $referencedUser}
                            </li>
                        {/foreach}
                    </ol>
                {else}
                    <em>uživatel zatím nikoho nedoporučil</em>
                {/if}
            </div>
        </div>

    </div>


    <div class="col-md-4">

        {*Účet*}
        <div class="card card-accent-primary">
            <div class="card-header"><strong>Účet</strong></div>
            <div class="card-body">
                <h4 style="margin:0 0 5px 0">Aktuální stav účtu: <span class="badge badge-primary">{$balance |amount} {$userCurrency}</span></h4>
                <h5>K vyplacení: {$confirmedBalance |amount} {$userCurrency}</h5>
                <h7>K vyplacení v odměnách: {$confirmedComissionBalance |amount} {$userCurrency}</h7>
				<br>
                <h7">K vyplacení v bonusech: {$confirmedBonusBalance |amount} {$userCurrency}</h7>
                <h5 style="margin-top:10px;">Čeká na potvrzení: {$registeredBalance |amount} {$userCurrency}</h5>

                {if $userEntity->getCanceledCommissionTransactionsRatio() >= 0.3}
                    <span class="badge badge-sm badge-warning" data-toggle="tooltip" data-placement="top" title="zrušeno {$userEntity->getCountOfCanceledCommissionTransactions()} z {$userEntity->getCountOfCommissionTransactions()} transakcí" style="font-weight: normal; font-size: 11px; color: #000">
                     zrušeno {$userEntity->getCanceledCommissionTransactionsRatio() * 100 |number} % transakcí
                    </span>
                {/if}

                <hr />
                <h4>Provize z nákupů</h4>
                <table style="width:100%;">
                    <col width="60%">
                    <col width="20%">
                    <col width="20%" align="right">
                    <tr style="font-weight:bold">
                        <td>Celkem:</td>
                        <td>{($registeredCommissionTransactions->countOfTransactions + $confirmedCommissionTransactions->countOfTransactions)}</td>
                        <td>{($registeredCommissionTransactions->amount + $confirmedCommissionTransactions->amount) |amount} {$userCurrency}</td>
                    </tr>
                    <tr>
                        <td>Registrováno:</td>
                        <td>{$registeredCommissionTransactions->countOfTransactions}</td>
                        <td>{$registeredCommissionTransactions->amount |amount} {$userCurrency}</td>
                    </tr>
                    <tr>
                        <td>Potvrzeno:</td>
                        <td>{$confirmedCommissionTransactions->countOfTransactions}</td>
                        <td>{$confirmedCommissionTransactions->amount |amount} {$userCurrency}</td>
                    </tr>
                    <tr>
                        <td>Zrušeno:</td>
                        <td>{$cancelledCommissionTransactions->countOfTransactions}</td>
                        <td>{$cancelledCommissionTransactions->amount |amount} {$userCurrency}</td>
                    </tr>
					<tr>
						<td class="{$expiredCommissionTransactions->countOfTransactions > 0 ? "text-danger"}">Expirováno:</td>
						<td class="{$expiredCommissionTransactions->countOfTransactions > 0 ? "text-danger"}">{$expiredCommissionTransactions->countOfTransactions}</td>
						<td class="{$expiredCommissionTransactions->countOfTransactions > 0 ? "text-danger"}">{$expiredCommissionTransactions->amount |amount} {$userCurrency}</td>
					</tr>
					<tr>
						<td class="{$nonBillableCommissionTransactions->countOfTransactions > 0 ? "text-danger"}">Skryto:</td>
						<td class="{$nonBillableCommissionTransactions->countOfTransactions > 0 ? "text-danger"}">{$nonBillableCommissionTransactions->countOfTransactions}</td>
						<td class="{$nonBillableCommissionTransactions->countOfTransactions > 0 ? "text-danger"}">{$nonBillableCommissionTransactions->amount |amount} {$userCurrency}</td>
					</tr>
                </table>
                <hr />
                <h4>Bonusy</h4>
                <table style="width:100%;">
                    <col width="60%">
                    <col width="20%">
                    <col width="20%" align="right">
                    <tr style="font-weight:bold">
                        <td>Celkem:</td>
                        <td>{($registeredBonusTransactions->countOfTransactions + $confirmedBonusTransactions->countOfTransactions)}</td>
                        <td>{($registeredBonusTransactions->amount + $confirmedBonusTransactions->amount) |amount} {$userCurrency}</td>
                    </tr>
                    <tr>
                        <td>Potvrzeno:</td>
                        <td>{$confirmedBonusTransactions->countOfTransactions}</td>
                        <td>{$confirmedBonusTransactions->amount |amount} {$userCurrency}</td>
                    </tr>
                    <tr>
                        <td>Registrováno:</td>
                        <td>{$registeredBonusTransactions->countOfTransactions}</td>
                        <td>{$registeredBonusTransactions->amount |amount} {$userCurrency}</td>
                    </tr>
					<tr>
                        <td>Expirováno:</td>
                        <td>{$expiredBonusTransactions->countOfTransactions}</td>
                        <td>{$expiredBonusTransactions->amount |amount} {$userCurrency}</td>
                    </tr>
                </table>
                <hr />
                <h4>Reklamace</h4>
                <table style="width:100%;">
                    <col width="60%">
                    <col width="20%">
                    <col width="20%" align="right">
                    <tr style="font-weight:bold">
                        <td>Celkem:</td>
                        <td>{($registeredRefundTransactions->countOfTransactions + $confirmedRefundTransactions->countOfTransactions)}</td>
                        <td>{($registeredRefundTransactions->amount + $confirmedRefundTransactions->amount) |amount} {$userCurrency}</td>
                    </tr>
                    <tr>
                        <td>Potvrzeno:</td>
                        <td>{$confirmedRefundTransactions->countOfTransactions}</td>
                        <td>{$confirmedRefundTransactions->amount |amount} {$userCurrency}</td>
                    </tr>
                    <tr>
                        <td>Registrováno:</td>
                        <td>{$registeredRefundTransactions->countOfTransactions}</td>
                        <td>{$registeredRefundTransactions->amount |amount} {$userCurrency}</td>
                    </tr>
					<tr>
                        <td>Expirováno:</td>
                        <td>{$expiredRefundTransactions->countOfTransactions}</td>
                        <td>{$expiredRefundTransactions->amount |amount} {$userCurrency}</td>
                    </tr>
                </table>
            </div>
        </div>

        {* Profity *}
        <div class="card card-accent-primary">
            <div class="card-header">
                <strong>Profit</strong>
            </div>
            <div class="card-body">
                <h4 data-toggle="tooltip" data-placement="left" title="sum(commissionAmount)-sum(userCommissionAmount), type = commission">Marže z transakcí: <span class="badge badge-primary">{if $income}{$income[amount] |amount} {$userCurrency} ({round($income[percentage], 2)} %){else}0 {$userCurrency}{/if}</span></h4>
                <h4 n:if="$accountantIncome" data-toggle="tooltip" data-placement="left" title="sum(commissionAmount)-(sum(userCommissionAmount)+sum(bonusAmount)), type != payout">Učetní profit z uživatele: <span class="badge badge-primary">{$accountantIncome[amount] |amount} {$userCurrency}</span></h4>
            </div>
        </div>

        {* Výplaty *}
        <div class="card card-accent-primary">
            <div class="card-header">
                <strong>Výplaty</strong>
            </div>
            <div class="card-body">
                {if $payouts}
                <h4 style="margin:0">
                    Celkem vyplaceno: <span class="badge badge-primary">{$sumOfConfirmedPayouts->amount |amount} {$userCurrency}</span>
                    <br />
                    <small>z toho bonusy: <span class="badge badge-primary">{$sumOfConfirmedPayouts->bonusAmount |amount} {$userCurrency}</span></small>
                </h4>
                <br />
                <ol>
                    {foreach $payouts as $payout}
                        <li>
                            {$payout->getCreatedAt()->format('d.m.Y')}:
                            <strong>{$payout->getAmount() |amount} {$payout->getCurrency() |currency}</strong>
                            {if $payout->getBonusAmount()} <small>z toho bonusy: <strong>{$payout->getBonusAmount() |amount} {$userCurrency}</strong></small>{/if}
                            {if $payout->isConfirmed()}
								<small class="fa fa-check text-success"></small>
							{else}
								{if $payout->isScheduled()}
									<small class="text-warning">na cestě do banky</small>
								{else}
									<small class="text-warning">čeká na potvrzení</small>
								{/if}
							{/if}
                            {if $payout->isCancelled()}<small class="badge badge-danger">zrušeno</small>{/if}
                            {var $comments = $payout->getComments()}
                            {if !$comments->isEmpty()}
                                <small>| <a href="javascript:void(0);" data-toggle="modal" data-target="#payoutComments{$payout->getId()}">komentáře ({$comments->count()}) »</a></small>
                                {include payoutCommentsModal, $payout}
                            {/if}
                        </li>
                    {/foreach}
                </ol>
                {else}
                    <em>uživatel nemá zatím žádnou výplatu</em>
                {/if}

				<hr>
				<button class="btn btn-link px-0" onclick="this.remove();$('#invoice-payout-form').removeClass('hide');">
					Vytvořit výplatu s vlastní částkou
				</button>

				{form invoicePayoutControl-form, class => "form-inline"}
					<div id="invoice-payout-form" n:class="!$form->hasErrors() ? hide">
						<div class="form-group">
							<ul class="errors mt-1" n:if="$form->hasErrors()">
								<script>$("html, body").animate({ scrollTop: $("#invoice-payout-form").offset().top - 250}, 0);</script>
								<li n:foreach="$form->errors as $error">{$error |noescape}</li>
							</ul>
						</div>

						<div class="form-inline">
							<div class="form-group">
								<label class="mr-2">Částka</label>
								{input amount, class => "form-control mr-sm-2"}
							</div>
							<br />
							<div class="form-group">
								<label>
									<input type="checkbox" n:name="isInvoice" />&nbsp;Výplata na fakturu
								</label>
							</div>
							<br />
							{input submit, class => "btn btn-primary mt-2 mt-xl-0", value => "Vytvořit výplatu na fakturu", style => "margin-top:15px;"}
						</div>
					</div>
				{/form}
            </div>
        </div>

    </div>

    <div class="col-md-4">

        {* Tickety *}
        <div class="card card-accent-primary">
            <div class="card-header">
                <strong>Tickety</strong>
            </div>
            <div class="card-body">
                {if !empty($tickets)}
                <ul>
                    {foreach $tickets as $ticket}
                        <li>
                            <a n:href=":Admin:Tickets:Ticket:ticket $ticket->getId()" title="Zobrazit detail ticketu">
                                {$ticket->getCreatedAt()|date:'d.m.Y'}
                                -
                                {$ticket->getTypeLabel()}</a>
                            -
                            {if $ticket->isResolved()}
                                <span n:if="$ticket->isAccepted()" class="badge badge-success">potvrzeno</span>
                                <span n:if="$ticket->isDeclined()" class="badge badge-danger">zamítnuto</span>
                            {else}
                                <span class="badge badge-dark">čekající</span>
                            {/if}
                        </li>
                    {/foreach}
                </ul>
                <a n:href=":Admin:Tickets:Ticket:default, ticketsGrid-filter => ['userId' => $userEntity->getId()]" class="btn btn-sm btn-primary">Všechny tickety ></a>
                {else}
                    <em>žádné tickety</em>
                {/if}
            </div>
        </div>

        {* Zvýhodnění *}
        <div class="card card-accent-primary">
            <div class="card-header">
                <strong>Zvýhodnění</strong>
            </div>
            <div class="card-body">
                <strong>Aktivní zvýhodnění</strong>
                {if !$shareRewards->isEmpty()}
                <ul>
                    {foreach $shareRewards as $shareReward}
                        <li>
                            {$shareReward->getName()} ({$shareReward->getShareCoefficient()}) -
                            {if $shareReward->getShops()->isEmpty()}
                                všechny obchody
                            {else}
                                {foreach $shareReward->getShops() as $shop}
                                    {$shop->getName()}
                                    {sep}, {/sep}
                                {/foreach}
                            {/if}
                            -
                            {$shareReward->getValidTill()|date:'d.m.Y'}
                        </li>
                    {/foreach}
                </ul>
                {else}
                    <em>žádné aktivní zvýhodnění</em>
                {/if}
                {if !$expiredShareRewards->isEmpty()}
                <hr />
                <strong>Již expirované zvýhodnění</strong>
                <ul>
                    {foreach $expiredShareRewards as $expiredShareReward}
                        <li>
                            {$expiredShareReward->getName()} ({$expiredShareReward->getShareCoefficient()}) -
                            {if $expiredShareReward->getShops()->isEmpty()}
                                všechny obchody
                            {else}
                                {foreach $expiredShareReward->getShops() as $shop}
                                    {$shop->getName()}
                                    {sep}, {/sep}
                                {/foreach}
                            {/if}
                        </li>
                    {/foreach}
                </ul>
                {/if}
            </div>
        </div>

        {*Freshdesk tickety*}
        {if $freshdeskTickets}
        <div class="card card-accent-primary">
            <div class="card-header">
                <strong>Tickety na freshdesku</strong>
            </div>
            <div class="card-body">
                <ul>
                    {foreach $freshdeskTickets as $freshdeskTicket}
                        <li>
                            {$freshdeskTicket->getOpenedAt()->format('d.m.Y')}
                            <a href="https://{$userEntity->getLocalization()->isSlovak() ? 'tiplisk' : 'tipli'}.freshdesk.com/helpdesk/tickets/{$freshdeskTicket->getTicketId()}" target="_blank">
                                {if $freshdeskTicket->getSubject()}
                                - {$freshdeskTicket->getSubject()}
                                {else}
                                    - <em>bez předmětu</em>
                                {/if}
                            </a>
                        </li>
                    {/foreach}
                </ul>
            </div>
        </div>
        {/if}

        <div class="card card-accent-primary">
            <div class="card-header">
                <strong>Reklamace</strong>
            </div>
            <div class="card-body">
                {if $countOfRefunds > 0}
                    {control refundsGrid}
                {else}
                    <em>zatím žádná reklamace</em>
                {/if}
            </div>
        </div>

        {* Obchody *}
        <div class="card card-accent-primary">
            <div class="card-header">
                <strong>Obchody</strong>
            </div>
            <div class="card-body">
                {if $shopsStatistics}
                    {foreach $shopsStatistics as $shopStatistic}
                        <strong>{$shopStatistic["name"]}</strong>:<br />
                        {$shopStatistic["countOfTransactions"]} {$shopStatistic["countOfTransactions"] < 5 ? "transakce" : "transakcí"}<br />
                        celkem v obchodě vydělal: {round($shopStatistic["sumOfUserCommissions"]) |amount} {$userCurrency} <br />
                        z toho potvrzeno: {round($shopStatistic["sumOfConfirmedUserCommissions"]) |amount} {$userCurrency} <br />
                        poslední transakce: {(new \DateTime($shopStatistic["lastTransactionAt"]))->format('d.m.Y h:i')}<br />
                        {sep}<br />{/sep}
                    {/foreach}
                {else}
                    <em>žádná transakce</em>
                {/if}
            </div>
        </div>

        <div class="card card-accent-primary">
            <div class="card-header">
                <strong>Tipli giveaway</strong>
            </div>
            <div class="card-body">
                {if $userEntity->hasUserLuckyShopData()}
                    Joined at: {$userEntity->getUserLuckyShopData()->getJoinedAt()->format('d.m.Y H:i')}<br>

                    Count of wins: {$userLuckyShopChecksCountAndSumWithWin['count']}<br>

                    Sum of wins: {$userLuckyShopChecksCountAndSumWithWin['sum'] ?: 0 |amount} {$userCurrency} <br><br>

                    <a n:href="luckyShopsHistory, id: $userEntity->getId()" class="block">Show daily history</a>

                    <hr>

                    <strong>User lucky shops: {$userLuckyShopsFilledCount} / {$userLuckyShopsCount} filled</strong><br><br>

                 {if $lastAddedLuckyShop}
                        Last added: {$lastAddedLuckyShop->getCreatedAt()->format('d.m.Y H:i')} ({$lastAddedLuckyShop->getSource()}
                        {if $lastAddedLuckyShop->getShop()}, {$lastAddedLuckyShop->getShop()->getName()}{/if})<br>
                 {/if}

                    {if $lastFilledLuckyShopCheck}
                        Last filled: {$lastFilledLuckyShopCheck->getCreatedAt()->format('d.m.Y H:i')}
                        {if $lastFilledLuckyShopCheck->getLuckyShop()->getShop()}
                            ({$lastFilledLuckyShopCheck->getLuckyShop()->getShop()->getName()})
                        {/if}
                    {/if}

                    <br><br>

                    <div class='d-flex justify-content-between'>
                    <a n:href="userLuckyShopsDetail, id: $userEntity->getId()" class="block">User lucky shops history</a>
                    <a n:href="newUserLuckyShop, userId: $userEntity->getId()" class="d-flex align-items-center">
                        Add window
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" style='width: 1rem;' class="size-4">
                            <path d="M8.75 3.75a.75.75 0 0 0-1.5 0v3.5h-3.5a.75.75 0 0 0 0 1.5h3.5v3.5a.75.75 0 0 0 1.5 0v-3.5h3.5a.75.75 0 0 0 0-1.5h-3.5v-3.5Z" />
                        </svg>

                    </a>
                    </div>
                {else}
                    <em>User has not yet joined the game.</em>
                {/if}
            </div>
        </div>
    </div>
</div>


{define userLink, $linkedUser}
    <a n:href="this, $linkedUser->getId()">{$linkedUser->getUserName()}</a>
{/define}

<style>
    ul, ol { padding-left: 20px; }
</style>


{define payoutCommentsModal, $payout}
    <div class="modal fade" id="payoutComments{$payout->getId()}" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document" style="width:800px;">
            <div class="modal-content" style="width:800px;">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">Komentáře výplaty #{$payout->getId()}</h4>
                </div>
                <div class="modal-body">
                    {foreach $payout->getComments() as $comment}
                        {if $comment->getAuthor()}<strong>{$comment->getAuthor()->getFullName()}</strong>,{/if} {$comment->getCreatedAt()->format('d.m.Y H:i')}
                        <p>{$comment->getText()}</p>
                        {if $comment->getEmailBody()}
                            <strong><em>Odeslaný email uživateli:</em></strong>
                            <p>{$comment->getEmailBody()}</p>
                        {/if}
                        {sep}<hr />{/sep}
                    {/foreach}
                </div>
            </div>
        </div>
    </div>
{/define}

{define subUsers $users, $reason}
    {if !empty($users)}
        {foreach $users as $conflictUser}
            <li>
                {$conflictUser->getLocalization()->getLocale()|flag|noescape}
                {include userEmail, $conflictUser->getEmail(), $conflictUser->getId()}
                ({include userId, $conflictUser->getId()})
                {include suspectedLabel, $conflictUser}
                - {$reason}
            </li>
        {/foreach}
    {/if}
{/define}

{define userId $userId}<a href="{plink :Admin:Account:User:userCard, 'id' => $userId}" target="_blank">{$userId}</a>{/define}
{define userEmail $userEmail, $userId}<a href="{plink :Admin:Transactions:Transaction:default, 'transactionsFilter' => ['userId' => $userId]}" target="_blank">{$userEmail}</a>{/define}
