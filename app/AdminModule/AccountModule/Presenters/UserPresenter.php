<?php

namespace tipli\AdminModule\AccountModule\Presenters;

use Nette\Application\Attributes\Persistent;
use Nette\Utils\Html;
use tipli\AdminModule\AccountModule\Components\UserFilterControl\UserFilterControl;
use tipli\AdminModule\AccountModule\Components\UserFilterControl\UserFilterControlFactory;
use tipli\Model\Doctrine\EntityManager;
use Doctrine\ORM\QueryBuilder;
use Nette\Application\UI\Form;
use Nette\Utils\ArrayHash;
use Nette\Utils\Strings;
use tipli\AdminModule\AccountModule\Components\IUserListNewFactory;
use tipli\AdminModule\AccountModule\Forms\IGroupRegistrationFactory;
use tipli\AdminModule\AccountModule\Forms\IInvoicePayoutControlFactory;
use tipli\AdminModule\AccountModule\Forms\IRecommenderControlFactory;
use tipli\AdminModule\AccountModule\Forms\IRemoveUserControlFactory;
use tipli\AdminModule\AccountModule\Forms\IUserControlFactory;
use tipli\AdminModule\AccountModule\Forms\IUserEventControlFactory;
use tipli\AdminModule\AccountModule\Forms\IUserNoteControlFactory;
use tipli\AdminModule\AccountModule\Forms\RecommenderControl;
use tipli\AdminModule\AccountModule\Forms\UserControl;
use tipli\AdminModule\Presenters\BasePresenter;
use tipli\InvalidArgumentException;
use tipli\Model\Account\AdminReLoginManager;
use tipli\Model\Account\EmailSubscriptionManager;
use tipli\Model\Account\Entities\Event;
use tipli\Model\Account\Entities\User;
use tipli\Model\Account\Entities\UserDuplicity;
use tipli\Model\Account\EventManager;
use tipli\Model\Account\UserDuplicityCheckerFacade;
use tipli\Model\Account\UserFacade;
use tipli\Model\BrowserActivities\BrowserActivityFacade;
use tipli\Model\Campaign\CampaignFacade;
use tipli\Model\Campaign\Entities\CampaignSubscription;
use tipli\Model\Datagrid\QueryBuilderDataSource;
use tipli\Model\Freshdesk\FreshdeskFacade;
use tipli\Model\LuckyShop\Entities\LuckyShop;
use tipli\Model\LuckyShop\Entities\LuckyShopCampaign;
use tipli\Model\LuckyShop\Entities\UserLuckyShop;
use tipli\Model\LuckyShop\LuckyShopFacade;
use tipli\Model\Messages\MessageFacade;
use tipli\Model\Payouts\PayoutFacade;
use tipli\Model\Reports\StatisticDataProvider;
use tipli\Model\Rewards\ShareRewardFacade;
use tipli\Model\Tickets\TicketFacade;
use tipli\Model\Transactions\Entities\Transaction;
use tipli\Model\Transactions\TransactionFacade;

class UserPresenter extends BasePresenter
{
	/** @var array @persistent */
	public array $filters = [];
	/** @var EntityManager @inject */
	public $em;

	/** @var UserFacade @inject */
	public UserFacade $userFacade;

	/** @var AdminReLoginManager @inject */
	public $adminReLoginManager;

	/** @var IUserControlFactory @inject */
	public $userControlFactory;

	/** @var IRecommenderControlFactory @inject */
	public $recommenderControlFactory;

	/** @var TransactionFacade @inject */
	public $transactionFacade;

	/** @var StatisticDataProvider @inject */
	public $statisticDataProvider;

	/** @var PayoutFacade @inject */
	public $payoutFacade;

	/** @var ShareRewardFacade @inject */
	public $shareRewardFacade;

	/** @var BrowserActivityFacade @inject */
	public $browserActivityFacade;

	/** @var FreshdeskFacade @inject */
	public $freshdeskFacade;

	/** @var TicketFacade @inject */
	public $ticketFacade;

	/** @var IUserNoteControlFactory @inject */
	public $userNoteControlFactory;

	/** @var IUserListNewFactory @inject */
	public $userListNewFactory;

	/** @var IGroupRegistrationFactory @inject */
	public $groupRegistrationFactory;

	/** @var EmailSubscriptionManager @inject */
	public $emailSubscriptionManager;

	/** @var IRemoveUserControlFactory @inject */
	public $removeUserControlFactory;

	/** @var IUserEventControlFactory @inject */
	public $userEventControlFactory;

	/** @var IInvoicePayoutControlFactory @inject */
	public $invoicePayoutControlFactory;

	/** @var UserDuplicityCheckerFacade @inject */
	public $userDuplicityCheckerFacade;

	/** @var EventManager @inject */
	public $eventManager;

	/** @var CampaignFacade @inject */
	public $campaignFacade;

	/** @var MessageFacade @inject */
	public $messageFacade;

	/** @var UserFilterControlFactory @inject */
	public $userFilterControlFactory;

	/** @var LuckyShopFacade @inject */
	public LuckyShopFacade $luckyShopFacade;

	/** @var \tipli\AdminModule\AccountModule\Forms\CreateUserLuckyShopControlFactory @inject */
	public $userLuckyShopSupportControlFactory;

	#[Persistent]
	public ?int $userId;

	#[Persistent]
	public ?bool $onlyActive = false;

	public function beforeRender()
	{
		parent::beforeRender();

		$this->template->filters = $this->filters;

		ini_set('memory_limit', '1024M');
		ini_set('max_execution_time', 607);
	}

	public function createComponentUsersGrid($name)
	{
		$users = $this->userFacade->getUsers()
			->setMaxResults(50)
			->innerJoin('u.segmentData', 'sd')
			->addOrderBy('u.id', 'desc')
		;

		if (isset($this->filters['localizationId'])) {
			$users->andWhere('u.localization = :localization')
				->setParameter('localization', $this->filters['localizationId'])
			;
		}

		if (isset($this->filters['userId'])) {
			$users->andWhere('u.id = :id')
				->setParameter('id', $this->filters['userId'])
			;
		}

		if (isset($this->filters['email'])) {
			$users->andWhere('u.email LIKE :email')
				->setParameter('email', $this->filters['email'] . '%')
			;
		}

		if (isset($this->filters['firstName'])) {
			$users->andWhere('d.firstName LIKE :firstName')
				->setParameter('firstName', $this->filters['firstName'] . '%')
			;
		}

		if (isset($this->filters['lastName'])) {
			$users->andWhere('d.lastName LIKE :lastName')
				->setParameter('lastName', $this->filters['lastName'] . '%')
			;
		}

		if (isset($this->filters['suspected'])) {
			$users->andWhere('sd.suspectedAt IS NOT NULL');
		}

		$grid = $this->dataGridFactory->create()
			->getGrid($this, $name, $users->getQuery()->getResult());

		$grid->addColumnText('localization', 'Localization')
			->setRenderer(static function (User $user) {
				return $user->getLocalization()->getName();
			});

		$grid->addColumnText('suspected', 'Suspected')
			->setRenderer(static function (User $user) {
				return Html::el('span')->setText($user->isSuspected() ? 'suspected' : '-')->setAttribute('class', $user->isSuspected() ? 'badge badge-warning' : '');
			});

		$grid->addColumnText('email', 'E-mail');
		$grid->addColumnText('firstName', 'Firstname');
		$grid->addColumnText('lastName', 'Lastname');
		$grid->addColumnText('createdAt', 'Created at')->setRenderer(static function (User $user) {
			return $user->getCreatedAt()->format('d.m.Y H:i:s');
		});
		$grid->addColumnText('id', 'User ID');

		$grid->addAction('userCard', '', 'userCard')->setClass('btn btn-xs btn-primary')->setIcon('credit-card-alt');
		$grid->addAction('user', '', 'user')->setClass('btn btn-xs btn-primary')->setIcon('pen');
		$grid->addAction('login', '', 'login')->setClass('btn btn-xs btn-primary')->setIcon('lock');
		$grid->addAction('refreshBonusTransactions', '', 'refreshBonusTransactions')->setClass('btn btn-xs btn-primary')->setIcon('refresh');

		$grid->setPagination(false);
	}

	public function createComponentUsersGridNew($name)
	{
		$qb = $this->em->getRepository(User::class)->createQueryBuilder('u');

		$qb
			->leftJoin('u.userData', 'd')->addSelect('d')
			->leftJoin('u.userSecurity', 's')->addSelect('s')
			->leftJoin('u.segmentData', 'sd')->addSelect('sd')
			->addOrderBy('u.id', 'desc');

		$grid = $this->dataGridFactory->create()
			->getGrid($this, $name, new QueryBuilderDataSource($qb));

		$grid->addColumnText('localization', 'Localization')
			->setRenderer(static function (User $user) {
				return $user->getLocalization()->getName();
			})->setFilterSelect($this->localizationFacade->findPairs());

		$grid->addColumnText('email', 'E-mail');
		$grid->addColumnText('firstName', 'Firstname');
		$grid->addColumnText('lastName', 'Lastname');
		$grid->addColumnText('createdAt', 'Created at')->setRenderer(static function (User $user) {
			return $user->getCreatedAt()->format('d.m.Y H:i<:s');
		});

		$grid->addFilterText('email', 'E-mail')
			->setCondition(static function (QueryBuilder $qb, $value) {
				$qb->andWhere('u.email LIKE :email')->setParameter('email', '%' . $value . '%');
			});

		$grid->addFilterText('firstName', 'Firstname')
			->setCondition(static function (QueryBuilder $qb, $value) {
				$qb->andWhere('d.firstName LIKE :firstName')->setParameter('firstName', '%' . $value . '%');
			});

		$grid->addFilterText('lastName', 'Lastname')
			->setCondition(static function (QueryBuilder $qb, $value) {
				$qb->andWhere('d.lastName LIKE :lastName')->setParameter('lastName', '%' . $value . '%');
			});

		$grid->addAction('userCard', '', 'userCard')->setClass('btn btn-xs btn-primary')->setIcon('credit-card-alt');
		$grid->addAction('user', '', 'user')->setClass('btn btn-xs btn-primary')->setIcon('pen');
		$grid->addAction('login', '', 'login')->setClass('btn btn-xs btn-primary')->setIcon('lock');
		$grid->addAction('refreshBonusTransactions', '', 'refreshBonusTransactions')->setClass('btn btn-xs btn-primary')->setIcon('refresh');
	}

	public function renderLuckyShopsHistory($id = null)
	{
		/** @var User $user */
		$user = $this->userFacade->find($id);

		if (!$user) {
			$this->redirect('default');
		}

		$this->template->userEntity = $user;

		$userLuckyShops = $this->luckyShopFacade->findValidUserLuckyShops($user);

		$this->template->userLuckyShopsCount = count($userLuckyShops);
		$this->template->userLuckyShopsFilledCount = count(array_filter($userLuckyShops, static function ($uls) {
			return $uls->getShop() !== null;
		}));

		$this->template->userLuckyShopChecksCountAndSumWithWin = $this->luckyShopFacade->findUserLuckyShopCheckCountAndSumWithWin($user);
	}

	public function createComponentUserLuckyShopsHistoryGrid($name)
	{
		$user = $this->userFacade->find($this->getParameter('id'));

		$defaultLuckyShopCampaign = $this->luckyShopFacade->findLuckyShopCampaignByName($user->getLocalization(), LuckyShopCampaign::NAME_DEFAULT);

		$grid = $this->dataGridFactory->create()
			->getGrid($this, $name, $this->luckyShopFacade->getLuckyShopsByCampaign($defaultLuckyShopCampaign)->addOrderBy('ls.id', 'desc'));

		$grid->addColumnDateTime('validSince', 'Date');

		$grid->addColumnText('userLuckyShops', 'Set Shops')->setRenderer(function (LuckyShop $luckyShop) use ($user) {
			$userLuckyShops = $this->luckyShopFacade->findUserLuckyShopsForLuckyShop($user, $luckyShop);

			if ($userLuckyShops) {
				$shopNames = [];
				foreach ($userLuckyShops as $index => $userLuckyShop) {
					$shopNames[] = $userLuckyShop->getShop()->getName() . ($index + 1 < count($userLuckyShops) ? ', ' : '');
				}

				return implode(', ', $shopNames);
			} else {
				return '-';
			}
		});

		$grid->addColumnText('shop', 'Winning Shop', 'shop.name');

		$grid->addColumnDateTime('userLuckyShopCheck', 'Revealed Shop')->setRenderer(function (LuckyShop $luckyShop) use ($user) {
			$userLuckyShopCheck = $this->luckyShopFacade->findUserLuckyShopCheck($user, $luckyShop);

			if ($userLuckyShopCheck) {
				return $userLuckyShopCheck->getCreatedAt()->format('H:i:s');
			} else {
				return '-';
			}
		});

		$grid->addColumnDateTime('hasWin', 'Win')->setRenderer(function (LuckyShop $luckyShop) use ($user) {
			$userLuckyShopCheck = $this->luckyShopFacade->findUserLuckyShopCheck($user, $luckyShop);

			if ($userLuckyShopCheck === null) {
				return '-';
			} elseif ($userLuckyShopCheck->hasWin()) {
				return 'YES';
			} else {
				return 'NO';
			}
		});

		$grid->addColumnText('countOfUsers', 'Total Winners Count');

		$grid->addColumnText('userReward', 'User Reward')->setRenderer(function (LuckyShop $luckyShop) use ($user) {
			$userLuckyShopCheck = $this->luckyShopFacade->findUserLuckyShopCheck($user, $luckyShop);

			if ($userLuckyShopCheck && $userLuckyShopCheck->hasWin()) {
				if (new \DateTime() < $luckyShop->getUserRewardRequestsCloseAt()) {
					return '?';
				}

				return $this->amountFilter->__invoke($luckyShop->getRewardAmountPerUser()) . ' ' . $this->currencyFilter->__invoke($user->getCurrency());
			} else {
				return '-';
			}
		});

		$grid->addColumnText('rewardStatus', 'Reward Status')->setRenderer(function (LuckyShop $luckyShop) use ($user) {
			$userLuckyShopCheck = $this->luckyShopFacade->findUserLuckyShopCheck($user, $luckyShop);

			if ($userLuckyShopCheck === null) {
				return '-';
			}

			if ($userLuckyShopCheck->hasWin() === false) {
				return '-';
			}

			if ($userLuckyShopCheck->hasWin() && new \DateTime() < $luckyShop->getUserRewardRequestsCloseAt()) {
				return 'Waiting for final amount';
			}

			if ($userLuckyShopCheck->getTransaction()) {
				return 'Confirmed';
			} else {
				return 'Waiting for credit';
			}
		});
	}

	public function actionUser($id = null)
	{
		if ($id && !$this->userFacade->find($id)) {
			$this->error('User not found');
		}
	}

	public function actionLogin($id)
	{
		/** @var User $user */
		$user = $this->userFacade->find($id);
		if (!$user) {
			$this->error('User not found.');
		} else {
			try {
				$urlDomain = $user->getLocalization()->getDomain();

				if ($this->configuration->getMode() != 'normal') {
					$urlDomain .= 'local';
				}

				if ($urlDomain === 'hu') {
					$url = 'https://www.tiplino.hu/';
				} else {
					$url = 'https://www.tipli.' . $urlDomain . '/';
				}

				if ($user->getLocalization() === $this->getUserIdentity()->getLocalization()) {
					$this->adminReLoginManager->loginAsMember($user, $this->link('//userCard', $user->getId()));

					$this->flashMessage('You are logged as user ' . $user->getUserName() . '.');
				} else {
					$url .= '?at=' . $this->userFacade->getUserAccessToken($user);
				}

				$this->redirectUrl($url);
			} catch (InvalidArgumentException $e) {
				$this->flashMessage($e->getMessage());

				$this->redirect('default');
			}
		}
	}

	/**
	 * @param int $id
	 */
	public function actionRefreshBonusTransactions($id)
	{
		$user = $this->userFacade->find($id);
		if (!$user) {
			$this->error('User not found.');
		} else {
			$this->transactionFacade->confirmPreparedForConfirmTransactions($user);
			$this->flashMessage('Bonus confirmation has been checked.');
			$this->redirect('default');
		}
	}

	/**
	 * @param int $id
	 */
	public function actionEventBanUser($id)
	{
		$user = $this->userFacade->find($id);
		if (!$user) {
			$this->error('User not found.');
		}
	}

	/**
	 * @param int $id
	 */
	public function actionEventUnbanUser($id)
	{
		$user = $this->userFacade->find($id);
		if (!$user) {
			$this->error('User not found.');
		}
	}

	/**
	 * @param int $id
	 */
	public function actionEventDeactivateUser($id)
	{
		$user = $this->userFacade->find($id);
		if (!$user) {
			$this->error('User not found.');
		}
	}

	/**
	 * @param int $id
	 */
	public function actionEventRemoveUser($id)
	{
		if (!$this->getUserIdentity()->isSuperadmin()) {
			$this->flashMessage('You are not allowed to remove users.');
			$this->redirect('default');
		}

		$user = $this->userFacade->find($id);
		if (!$user) {
			$this->error('User not found.');
		}
	}

	/**
	 * @param int $id
	 */
	public function actionEventSuspectUser($id)
	{
		$user = $this->userFacade->find($id);
		if (!$user) {
			$this->error('User not found.');
		}
	}

	/**
	 * @param int $id
	 */
	public function actionEventUnSuspectUser($id)
	{
		$user = $this->userFacade->find($id);
		if (!$user) {
			$this->error('User not found.');
		}
	}

	/**
	 * @param int $id
	 */
	public function actionEventSetAffiliateSegment($id)
	{
		$user = $this->userFacade->find($id);
		if (!$user) {
			$this->error('User not found.');
		}
	}

	/**
	 * @param int $id
	 */
	public function actionEventUnsetAffiliateSegment($id)
	{
		$user = $this->userFacade->find($id);
		if (!$user) {
			$this->error('User not found.');
		}
	}

	/**
	 * @param int $id
	 */
	public function actionEventSetVipSegment($id)
	{
		$user = $this->userFacade->find($id);
		if (!$user) {
			$this->error('Uživatel nebyl nalezen.');
		}
	}

	/**
	 * @param int $id
	 */
	public function actionEventUnsetVipSegment($id)
	{
		$user = $this->userFacade->find($id);
		if (!$user) {
			$this->error('User not found.');
		}
	}

	/**
	 * @return UserControl
	 */
	protected function createComponentUserControl()
	{
		$user = null;
		if ($this->getParameter('id')) {
			$user = $this->userFacade->find($this->getParameter('id'));
		}

		$control = $this->userControlFactory->create();
		$control->setUser($user);
		$control->setLoggedUser($this->getUser()->getIdentity());
		$control->onSuccess[] = function (User $user) {
			$this->flashMessage('Saved.');
			$this->redirect('User:user', $user->getId());
		};

		return $control;
	}

	/**
	 * @return RecommenderControl
	 */
	protected function createComponentRecommenderControl()
	{
		$control = $this->recommenderControlFactory->create();
		$control->onSuccess[] = function () {
			$this->flashMessage('Saved.');
			$this->redirect('User:');
		};

		return $control;
	}

	public function renderUserCard($id)
	{
		/** @var User $user */
		$user = $this->userFacade->find($id);

		if (!$user) {
			$this->redirect('default');
		}

		$this->template->userEntity = $user;

		// Shops
		$this->template->shopsStatistics = $this->statisticDataProvider->getUserShopsStatistics($user);

		// Payouts
		$this->template->payouts = $this->payoutFacade->getPayoutsByUser($user)->getQuery()->getResult();
		$this->template->sumOfConfirmedPayouts = $this->statisticDataProvider->getSumOfPayouts(null, null, null, true, $user);

		// Share rewards
		$shareRewardsQuery = $this->shareRewardFacade->createShareRewardsQuery($user)
			->sortByCreatedAt()
			->onlyValid();

		$this->template->shareRewards = $this->shareRewardFacade->fetch($shareRewardsQuery);

		$expiredShareRewardsQuery = $this->shareRewardFacade->createShareRewardsQuery($user)
			->sortByCreatedAt()
			->onlyExpired();

		$this->template->expiredShareRewards = $this->shareRewardFacade->fetch($expiredShareRewardsQuery);

		// Transactions
		$this->template->balance = $this->transactionFacade->getBalance($user);
		$this->template->confirmedBalance = $this->transactionFacade->getConfirmedBalance($user);
		$this->template->registeredBalance = $this->transactionFacade->getRegisteredBalance($user);

		$this->template->confirmedComissionBalance = $this->transactionFacade->getConfirmedCommissionBalance($user);
		$this->template->confirmedBonusBalance = $this->transactionFacade->getConfirmedBonusBalance($user);

		$this->template->registeredCommissionTransactions = $this->statisticDataProvider->getSumOfUserTransactions($user, Transaction::TYPE_COMMISSION, false);
		$this->template->confirmedCommissionTransactions = $this->statisticDataProvider->getSumOfUserTransactions($user, Transaction::TYPE_COMMISSION, true);
		$this->template->expiredCommissionTransactions = $this->statisticDataProvider->getSumOfUserTransactions($user, Transaction::TYPE_COMMISSION, null, true);
		$this->template->cancelledCommissionTransactions = $this->statisticDataProvider->getSumOfUserTransactions($user, Transaction::TYPE_COMMISSION, null, false, true);
		$this->template->nonBillableCommissionTransactions = $this->statisticDataProvider->getSumOfUserTransactions($user, Transaction::TYPE_COMMISSION, null, false, false, false);

		$this->template->registeredBonusTransactions = $this->statisticDataProvider->getSumOfUserTransactions($user, Transaction::TYPE_BONUS, false);
		$this->template->confirmedBonusTransactions = $this->statisticDataProvider->getSumOfUserTransactions($user, Transaction::TYPE_BONUS, true);
		$this->template->expiredBonusTransactions = $this->statisticDataProvider->getSumOfUserTransactions($user, Transaction::TYPE_BONUS, null, true);

		$this->template->registeredRefundTransactions = $this->statisticDataProvider->getSumOfUserTransactions($user, Transaction::TYPE_BONUS_REFUND, false);
		$this->template->confirmedRefundTransactions = $this->statisticDataProvider->getSumOfUserTransactions($user, Transaction::TYPE_BONUS_REFUND, true);
		$this->template->expiredRefundTransactions = $this->statisticDataProvider->getSumOfUserTransactions($user, Transaction::TYPE_BONUS_REFUND, null, true);
		$this->template->countOfRefunds = $this->statisticDataProvider->getCountOfRefundTransactions(null, null, null, null, $user);

		// Income
		$this->template->income = $this->statisticDataProvider->getUserIncome($user);
		$this->template->accountantIncome = $this->statisticDataProvider->getUserIncome($user, true);

		// Freshdesk tickets
		$this->template->freshdeskTickets = $this->freshdeskFacade->findTicketsByUser($user);

		// Tickets
		$this->template->tickets = $this->ticketFacade->findTicketsByUser($user);

		$this->template->userFacade = $this->userFacade;

		// Campaign 300
		$isCampaignAllowed = true;
		$this->template->isCampaignAllowed = $isCampaignAllowed;

		if ($isCampaignAllowed) {
			/** @var CampaignSubscription|null $campaignSubscription */
			$campaignSubscription = $this->campaignFacade->findCampaignSubscriptionByUser($user);
			$availableBonus = $this->campaignFacade->getAvailableBonus($user);

			$this->template->campaignSubscribedAt = $user->isActiveUser() ? ($campaignSubscription ? $campaignSubscription->getCreatedAt() : null) : $user->getCreatedAt();
			$this->template->campaignAvailableBonus = $availableBonus;
			$this->template->campaignUsedBonus = CampaignSubscription::getBonusForUser($user) - $availableBonus;

			$this->template->campaignNewUserMode = !$user->isActiveUser() || ($campaignSubscription && $campaignSubscription->isNewUser());
			$this->template->campaignValidTill = $campaignSubscription ? $campaignSubscription->getValidTill() : null;
			$this->template->isCampaignValid = $campaignSubscription && $campaignSubscription->isValid();

			$campaignActiveTransaction = $this->campaignFacade->findCampaignTransactionForActiveUser($user);
			$campaignActiveTransaction = $campaignActiveTransaction ? $campaignActiveTransaction->getBonusTransaction() : null;

			$this->template->campaignActiveTransaction = $campaignActiveTransaction;
			$this->template->campaignAlreadyConfirmedTransactionsAmount = $campaignActiveTransaction
				? $this->transactionFacade->getConfirmedBalance(
					$user,
					$campaignSubscription->getValidSince(),
					[Transaction::TYPE_COMMISSION, Transaction::TYPE_BONUS_REFUND]
				)
				: 0
			;
		}

		$this->template->findSameUserInCountries = (function () use ($user) {
			$users = [];

			/** @var User $anotherUser */
			foreach ($this->userFacade->findUsersByEmail($user->getEmail()) as $anotherUser) {
				if ($anotherUser !== $user) {
					$users[] = $anotherUser;
				}
			}

			return $users;
		});

		$this->template->userLuckyShopChecksCountAndSumWithWin = $this->luckyShopFacade->findUserLuckyShopCheckCountAndSumWithWin($user);

		if ($user->hasUserLuckyShopData()) {
			$userLuckyShops = $this->luckyShopFacade->findValidUserLuckyShops($user);
			$this->template->userLuckyShopsCount = count($userLuckyShops);
			$this->template->userLuckyShopsFilledCount = count(array_filter($userLuckyShops, static function ($uls) {
				return $uls->getShop() !== null;
			}));

			$this->template->lastAddedLuckyShop = !empty($userLuckyShops) ? $userLuckyShops[0] : null;

			$userLuckyShopChecks = $this->luckyShopFacade->findUserLuckyShopChecks($user);
			$this->template->lastFilledLuckyShopCheck = !empty($userLuckyShopChecks) ? $userLuckyShopChecks[0] : null;
		}
	}

	public function handleActivateCampaign($date)
	{
		/** @var User $user */
		$user = $this->getParameter('id') ? $this->userFacade->find($this->getParameter('id')) : null;

		if (!$user) {
			$this->redirect('default');
		}

		$this->campaignFacade->activateCampaignBackwards($user, new \DateTime($date));

		$this->flashMessage('Campaign has been activated. Bonuses will be activated within few minutes.');

		$this->redirect('this');
	}

	public function createComponentUserNoteControl()
	{
		$user = $this->userFacade->find($this->getParameter('id'));

		$control = $this->userNoteControlFactory->create($user);
		$control->onSuccess[] = function ($user) {
			$this->redirect('userCard', $user->getId());
		};

		return $control;
	}

	public function handleVerifyUserEmail($id)
	{
		/** @var User $user */
		$user = $this->userFacade->find($id);

		if ($user) {
			$this->userFacade->verifyUserEmail($user);
			$this->eventManager->createEvent($user, $this->getUserIdentity(), Event::ACTION_VERIFY_EMAIL);

			$this->flashMessage('E-mail verified.');
		} else {
			$this->flashMessage('User not found.', 'danger');
		}

		$this->redirect('this');
	}

	public function handleVerifyUserPhoneNumber($id)
	{
		/** @var User $user */
		$user = $this->userFacade->find($id);

		if ($user) {
			$this->userFacade->verifyUserPhoneNumber($user);
			$this->eventManager->createEvent($user, $this->getUserIdentity(), Event::ACTION_VERIFY_PHONE_NUMBER);

			$this->flashMessage('Phone number verified.');
		} else {
			$this->flashMessage('User not found.', 'danger');
		}

		$this->redirect('this');
	}

	public function handleVerifyUserAccountNumber($id)
	{
		/** @var User $user */
		$user = $this->userFacade->find($id);

		if ($user) {
			$user->verifyAccountNumber();
			$this->userFacade->saveUser($user, false);
			$this->eventManager->createEvent($user, $this->getUserIdentity(), Event::ACTION_VERIFY_BANK_ACCOUNT);
			$this->payoutFacade->updateAccountNumberInPayoutRequests($user);

			$this->flashMessage('Account number verified.');
		} else {
			$this->flashMessage('User not found.', 'danger');
		}

		$this->redirect('this');
	}

	public function handleSendAccountNumberVerificationEmail($id)
	{
		/** @var User $user */
		$user = $this->userFacade->find($id);

		if ($user) {
			$this->messageFacade->sendAccountNumberVerificationEmail($user);

			$this->flashMessage('Verification email has been send to user.');
		} else {
			$this->flashMessage('User not found.', 'danger');
		}

		$this->redirect('this');
	}

	public function handleUnsubscribeEmails($id)
	{
		/** @var User $user */
		$user = $this->userFacade->find($id);

		if ($user) {
			// $user->unsubscribeEmails('Odhlášeno administrátorem.');
			$this->emailSubscriptionManager->unsubscribe($user, null, EmailSubscriptionManager::SITE_TIPLI, EmailSubscriptionManager::SOURCE_ADMIN, 'Odhlaseno administratorem ' . $this->getUserIdentity()->getId());
			$this->userFacade->saveUser($user);
			$this->eventManager->createEvent($user, $this->getUserIdentity(), Event::ACTION_UNSUBSCRIBE_USER);

			$this->flashMessage('E-mail unsubscribed.');
		} else {
			$this->flashMessage('User not found.', 'danger');
		}

		$this->redirect('this');
	}

	public function createComponentRefundsGrid($name)
	{
		$qb = $this->transactionFacade->getTransactions();
		$qb->addOrderBy('t.id', 'desc')
			->andWhere('t.user = :user')
			->setParameter('user', $this->userFacade->find($this->getParameter('id')))
			->andWhere('t.type = :bonusRefund')
			->setParameter('bonusRefund', Transaction::TYPE_BONUS_REFUND);

		$grid = $this->dataGridFactory->create()
			->getGrid($this, $name, $qb, 20);

		$grid->setTemplateFile(__DIR__ . '/templates/User/grid/refunds.latte');

		$grid->addColumnText('shop', 'Shop')->setRenderer(static function ($item) {
			return $item->getShop() ? $item->getShop()->getName() : null;
		});

		$grid->addColumnText('commission', 'Commission');

		$grid->addColumnText('date', 'Created at / Confirmed at');

		$grid->addFilterText('shop', 'Shop')
			->setCondition(static function (QueryBuilder $qb, $value) {
				$qb->andWhere('s.name LIKE :shop')->setParameter('shop', '%' . $value . '%');
			});
	}

	protected function createComponentGroupRegistrationControl()
	{
		$control = $this->groupRegistrationFactory->create();
		$control->onSuccess[] = function ($realRegisteredEmails) {
			$this->flashMessage('Saved.');
			$this->flashMessage('New registered users:' . implode(',', $realRegisteredEmails));

			$this->redirect('this');
		};

		return $control;
	}

	protected function createComponentGoToAnotherUserForm()
	{
		$form = new Form();

		$form->addText('email');

		$form->addSubmit('submit', 'Go to user');

		$form->onSuccess[] = function (Form $form, ArrayHash $values) {
			$email = $values->email;

			$users = $this->userFacade->findUsersByEmail($email);

			if (count($users) === 1) {
				$this->redirect('userCard', $users[0]->getId());
			} elseif (count($users) === 0) {
				$this->flashMessage('User with this email does not exists.');

				$this->redirect('this');
			} else {
				$this->template->anotherUsers = $users;
				$this->flashMessage('Multiple users with this email exists. Please choose one of them.', 'warning');
			}
		};

		return $form;
	}

	protected function createComponentUserListNew()
	{
		return $this->userListNewFactory->create();
	}

	public function handleVerifyChange($changeId)
	{
		$change = $this->userFacade->findChange($changeId);

		if ($change && $change->isVerificationRequired() && !$change->isVerified()) {
			$this->userFacade->verifyChange($change, $this->clientLayer->getIp(), $this->clientLayer->getUserAgent());
			$this->userFacade->applyChange($change);

			$this->flashMessage('Verified.');
		}

		$this->redirect('this');
	}

	public function createComponentBanUserControl()
	{
		$user = $this->userFacade->find($this->getParameter('id'));

		$control = $this->userEventControlFactory->create(
			$user,
			$this->getUser()->getIdentity(),
			Event::ACTION_BAN_USER
		);
		$control->onSuccess[] = (function () use ($user) {
			$this->flashMessage('User has been banned.');
			$this->redirect('userCard', $user->getId());
		});

		return $control;
	}

	public function createComponentUnbanUserControl()
	{
		$user = $this->userFacade->find($this->getParameter('id'));

		$control = $this->userEventControlFactory->create(
			$user,
			$this->getUser()->getIdentity(),
			Event::ACTION_UNBAN_USER
		);
		$control->onSuccess[] = (function () use ($user) {
			$this->flashMessage('User has been unbanned');
			$this->redirect('userCard', $user->getId());
		});

		return $control;
	}

	public function createComponentDeactivateUserControl()
	{
		$user = $this->userFacade->find($this->getParameter('id'));

		$control = $this->userEventControlFactory->create(
			$user,
			$this->getUser()->getIdentity(),
			Event::ACTION_DEACTIVATE_USER
		);
		$control->onSuccess[] = (function () use ($user) {
			$this->flashMessage('User has been deactivated.');
			$this->redirect('userCard', $user->getId());
		});

		return $control;
	}

	public function createComponentRemoveUserControl()
	{
		$user = $this->userFacade->find($this->getParameter('id'));

		$control = $this->removeUserControlFactory->create($user, $this->getUser()->getIdentity());
		$control->onSuccess[] = (function () {
			$this->flashMessage('User has been removed.');
			$this->redirect('default');
		});

		return $control;
	}

	public function createComponentSuspectUserControl()
	{
		$user = $this->userFacade->find($this->getParameter('id'));

		$control = $this->userEventControlFactory->create(
			$user,
			$this->getUser()->getIdentity(),
			Event::ACTION_SUSPECT_USER
		);
		$control->onSuccess[] = (function () use ($user) {
			$this->flashMessage('User has been marked as suspected.');
			$this->redirect('userCard', $user->getId());
		});

		return $control;
	}

	public function createComponentUnsuspectUserControl()
	{
		$user = $this->userFacade->find($this->getParameter('id'));

		$control = $this->userEventControlFactory->create(
			$user,
			$this->getUser()->getIdentity(),
			Event::ACTION_UNSUSPECT_USER
		);
		$control->onSuccess[] = (function () use ($user) {
			$this->flashMessage('User has been marked as not suspected.');
			$this->redirect('userCard', $user->getId());
		});

		return $control;
	}

	public function createComponentSetAffiliateSegmentControl()
	{
		$user = $this->userFacade->find($this->getParameter('id'));

		$control = $this->userEventControlFactory->create(
			$user,
			$this->getUser()->getIdentity(),
			Event::ACTION_SET_AFFILIATE_SEGMENT
		);
		$control->onSuccess[] = (function () use ($user) {
			$this->flashMessage('Affiliate segment has been set.');
			$this->redirect('userCard', $user->getId());
		});

		return $control;
	}

	public function createComponentUnsetAffiliateSegmentControl()
	{
		$user = $this->userFacade->find($this->getParameter('id'));

		$control = $this->userEventControlFactory->create(
			$user,
			$this->getUser()->getIdentity(),
			Event::ACTION_UNSET_AFFILIATE_SEGMENT
		);
		$control->onSuccess[] = (function () use ($user) {
			$this->flashMessage('Affiliate segment has been unset.');
			$this->redirect('userCard', $user->getId());
		});

		return $control;
	}

	public function createComponentSetVipSegmentControl()
	{
		$user = $this->userFacade->find($this->getParameter('id'));

		$control = $this->userEventControlFactory->create(
			$user,
			$this->getUser()->getIdentity(),
			Event::ACTION_SET_VIP_SEGMENT
		);
		$control->onSuccess[] = (function () use ($user) {
			$this->flashMessage('VIP segment has been set.');
			$this->redirect('userCard', $user->getId());
		});

		return $control;
	}

	public function createComponentUnsetVipSegmentControl()
	{
		$user = $this->userFacade->find($this->getParameter('id'));

		$control = $this->userEventControlFactory->create(
			$user,
			$this->getUser()->getIdentity(),
			Event::ACTION_UNSET_VIP_SEGMENT
		);
		$control->onSuccess[] = (function () use ($user) {
			$this->flashMessage('VIP segment has been unset.');
			$this->redirect('userCard', $user->getId());
		});

		return $control;
	}

	public function createComponentInvoicePayoutControl()
	{
		$user = $this->userFacade->find($this->getParameter('id'));

		$control = $this->invoicePayoutControlFactory->create($user);

		$control->onSuccess[] = (function () use ($user) {
			$this->flashMessage('Payout has been created.');
			$this->redirect('userCard', $user->getId());
		});

		return $control;
	}

	public function createComponentUserDuplicityGrid($name)
	{
		$user = $this->userFacade->find($this->getParameter('id'));

		$grid = $this->dataGridFactory->create()
			->getGrid($this, $name, $this->userDuplicityCheckerFacade->getUserDuplicity($user));

		$grid->addColumnText('relatedUser', 'Duplicate user', 'relatedUser.id')
			->setRenderer(function (UserDuplicity $userDuplicity) {
				return '<a href="' . $this->link('userCard', $userDuplicity->getRelatedUser()->getId()) . '" target="_blank">' . $userDuplicity->getRelatedUser()->getEmail() . '</a>';
			})->setTemplateEscaping(false);

		$grid->addColumnText('type', 'Duplicate')->setRenderer(static function (UserDuplicity $userDuplicity) {
			return $userDuplicity->getDuplicityTypeName();
		});

		$grid->addColumnText('note', 'Note')->setRenderer(static function (UserDuplicity $userDuplicity) {
			return '<strong>' . $userDuplicity->getValue() . '</strong><br>' . $userDuplicity->getNote();
		})->setTemplateEscaping(false);

		$grid->setDefaultPerPage(10);

		return $grid;
	}

	public function handleUpdateSendingPolicy(int $id, $type, $event)
	{
		/** @var User $user */
		$user = $this->userFacade->find($id);

		$contentType = Strings::lower(str_replace('email', '', $type));

		if ($event === 'subscribe') {
			$this->emailSubscriptionManager->subscribeContentType($user, null, $contentType, EmailSubscriptionManager::SITE_TIPLI, EmailSubscriptionManager::SOURCE_ADMIN, 'prihlaseno adminem (userCard)');
		} elseif ($event === 'unsubscribe') {
			$this->emailSubscriptionManager->unsubscribeContentType($user, null, $contentType, EmailSubscriptionManager::SITE_TIPLI, EmailSubscriptionManager::SOURCE_ADMIN, 'odhlaseno adminem (userCard)');
		}

		$this->redrawControl('sendingPolicies');
	}

	public function handleUpdateEmailSubscription(int $id, $event)
	{
		/** @var User $user */
		$user = $this->userFacade->find($id);

		if ($event === 'subscribe') {
			$this->emailSubscriptionManager->subscribe($user, null, EmailSubscriptionManager::SITE_TIPLI, EmailSubscriptionManager::SOURCE_SETTINGS, 'prihlaseno adminem (userCard)');
		} elseif ($event === 'unsubscribe') {
			$this->emailSubscriptionManager->unsubscribe($user, null, EmailSubscriptionManager::SITE_TIPLI, EmailSubscriptionManager::SOURCE_SETTINGS, 'odhlaseno adminem (userCard)');
		}

		$this->redrawControl('sendingPolicies');
	}

	public function handleClearFilters()
	{
		$this->redirect('this', ['filters' => []]);
	}

	protected function createComponentUserFilterControl(): UserFilterControl
	{
		$control = $this->userFilterControlFactory->create($this->filters);

		$control->onSuccess[] = function ($values) {
			$this->redirect('this', ['filters' => $values]);
		};

		return $control;
	}

	public function actionUserLuckyShopsDetail($id)
	{
		$user = $this->userFacade->find($id);
		if (!$user) {
			$this->error('User not found');
		}

		$this->template->userEntity = $user;
	}

	public function renderUserLuckyShopsDetail($id)
	{
		$user = $this->userFacade->find($id);

		$userLuckyShops = $this->luckyShopFacade->findAllUserLuckyShops($user);
		$this->template->userLuckyShops = $userLuckyShops;

		$userLuckyShopChecks = $this->luckyShopFacade->findUserLuckyShopChecks($user);
		$this->template->userLuckyShopChecks = $userLuckyShopChecks;
	}

	public function createComponentUserLuckyShopsGrid($name)
	{
		$user = $this->userFacade->find($this->getParameter('id'));
		if (!$user) {
			throw new \InvalidArgumentException('User not found');
		}

		$qb = $this->luckyShopFacade->getUserLuckyShopRepository()->getUserLuckyShops()
			->leftJoin('uls.shop', 's')
			->leftJoin('uls.createdBy', 'cb')
			->andWhere('uls.user = :user')
			->setParameter('user', $user)
			->addOrderBy('uls.createdAt', 'DESC');

		if ($this->onlyActive) {
			$qb->andWhere('uls.validTill >= :now')
				->setParameter('now', new \DateTime());
		}

		$grid = $this->dataGridFactory->create()
			->getGrid($this, $name, $qb);

		$grid->addColumnText('id', 'ID')
			->setSortable();

		$grid->addColumnText('source', 'Type')
			->setRenderer(static function (UserLuckyShop $userLuckyShop) {
				$badgeClass = $userLuckyShop->getSource() === 'support' ? 'warning' : 'info';
				return '<span class="badge badge-' . $badgeClass . '">' . $userLuckyShop->getSource() . '</span>';
			})
			->setTemplateEscaping(false)
			->setFilterSelect([
				'' => 'All',
			] + array_combine(UserLuckyShop::getSources(), UserLuckyShop::getSources()));

		$grid->addColumnText('shop', 'Selected shop')
			->setRenderer(static function (UserLuckyShop $userLuckyShop) {
				if ($userLuckyShop->getShop()) {
					return $userLuckyShop->getShop()->getName();
				}
				return '<em>Not set</em>';
			})
			->setTemplateEscaping(false);

		$grid->addColumnText('validSince', 'Valid since')
			->setRenderer(static function (UserLuckyShop $userLuckyShop) {
				return $userLuckyShop->getValidSince()->format('d.m.Y H:i');
			})
			->setSortable();

		$grid->addColumnText('validTill', 'Valid till')
			->setRenderer(static function (UserLuckyShop $userLuckyShop) {
				return $userLuckyShop->getValidTill()->format('d.m.Y H:i');
			})
			->setSortable();

		$grid->addColumnText('status', 'Status')
			->setRenderer(static function (UserLuckyShop $userLuckyShop) {
				$status = $userLuckyShop->getStatus();
				$badgeClass = $status === 'active' ? 'success' : 'secondary';
				return '<span class="badge badge-' . $badgeClass . '">' . $status . '</span>';
			})
			->setTemplateEscaping(false)
			->setFilterSelect([
				'' => 'All',
				'active' => 'Active',
				'active_without_shop' => 'Active without shop',
				'expired' => 'Expired',
			])
			->setCondition(static function (\Doctrine\ORM\QueryBuilder $qb, $value) {
				if ($value === 'active') {
					$qb->andWhere('uls.validTill >= :now')
						->andWhere('uls.shop IS NOT NULL')
						->setParameter('now', new \DateTime());
				} elseif ($value === 'active_without_shop') {
					$qb->andWhere('uls.validTill >= :now')
						->andWhere('uls.shop IS NULL')
						->setParameter('now', new \DateTime());
				} elseif ($value === 'expired') {
					$qb->andWhere('uls.validTill < :now')
						->setParameter('now', new \DateTime());
				}
			});

		$grid->addColumnText('note', 'Note');

		$grid->addColumnText('createdAt', 'Created at')
			->setRenderer(static function (UserLuckyShop $userLuckyShop) {
				return $userLuckyShop->getCreatedAt()->format('d.m.Y H:i');
			})
			->setSortable();

		$grid->addColumnText('createdBy', 'Created by')
			->setRenderer(function (UserLuckyShop $userLuckyShop) {
				if ($userLuckyShop->getCreatedBy()) {
					$createdBy = $userLuckyShop->getCreatedBy();
					return '<a href="' . $this->link('userCard', $createdBy->getId()) . '" target="_blank">' .
						   $createdBy->getEmail() . '</a>';
				}

				if ($userLuckyShop->getTransaction()) {
					$user = $userLuckyShop->getUser()->getId();
					$transactionId = $userLuckyShop->getTransaction();

					return '<a title="Transaction: ' . $transactionId->getTransactionId() . '" href="' . $this->link(':Admin:Transactions:Transaction:default') . '?transactionsFilter[transactionId]=' . $transactionId->getTransactionId() . '&transactionsFilter[userId]=' . $user . '" target="_blank">' .
						   'Transaction: ' . $transactionId->getId() . '</a>';
				}

				return '<em>System</em>';
			})
			->setTemplateEscaping(false);

		$grid->addToolbarButton('this', 'Only active')
			->setRenderer(function () {
				return Html::el('a', 'Only active')->href($this->link('this', ['onlyActive' => !$this->onlyActive]))
					->setAttribute('class', 'btn btn-xs ' .
						(isset($this->onlyActive) && $this->onlyActive === true ? 'btn-primary' : 'btn-secondary'));
			});

		return $grid;
	}

	public function createComponentCreateUserLuckyShopControl()
	{
		$user = $this->userFacade->find($this->getParameter('userId'));

		$control = $this->userLuckyShopSupportControlFactory->create($user, $this->getUserIdentity());
		$control->onSuccess[] = function (User $user) {
			$this->redirect('userLuckyShopsDetail', ['id' => $user->getId()]);
		};

		return $control;
	}
}
