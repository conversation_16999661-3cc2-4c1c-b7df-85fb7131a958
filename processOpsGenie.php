<?php

$incidents = json_decode(file_get_contents('https://api.eu.opsgenie.com/v1/incidents?limit=100&query=' . rawurlencode('status: open'), false, stream_context_create(["http" => ["method" => "GET", "header" => "Authorization: GenieKey c0a7e491-2f13-4547-ac61-b7a75ef6ac4f"]])));

foreach ($incidents->data as $incident) {
	$incidentId = $incident->id;
	$areAlertsClosed = true;

	$alertsIds = json_decode(file_get_contents('https://api.eu.opsgenie.com/v1/incidents/' . $incidentId . '/associated-alert-ids', false, stream_context_create(["http" => ["method" => "GET", "header" => "Authorization: GenieKey c0a7e491-2f13-4547-ac61-b7a75ef6ac4f"]])));
	$alertsIds = $alertsIds->data;

	foreach ($alertsIds as $alertId) {
		$alert = json_decode(file_get_contents('https://api.eu.opsgenie.com/v2/alerts/' . $alertId, false, stream_context_create(["http" => ["method" => "GET", "header" => "Authorization: GenieKey c0a7e491-2f13-4547-ac61-b7a75ef6ac4f"]])));

		if ($areAlertsClosed === true && $alert->data->status !== 'closed') {
			$areAlertsClosed = false;
			break;
		}
	}

	if (count($alertsIds) > 0 && $areAlertsClosed === true) {
		$ch = curl_init('https://api.eu.opsgenie.com/v1/incidents/' . $incident->id . '/close');
		curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['note' => 'Incident closed because all associated alerts are resolved.']));
		curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type:application/json', 'Authorization: GenieKey c0a7e491-2f13-4547-ac61-b7a75ef6ac4f']);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		$result = curl_exec($ch);
		curl_close($ch);
	}
}

file_get_contents('https://api.eu.opsgenie.com/v2/heartbeats/tipli-bot/ping', false, stream_context_create(["http" => ["method" => "GET", "header" => "Authorization: GenieKey c0a7e491-2f13-4547-ac61-b7a75ef6ac4f"]]));
