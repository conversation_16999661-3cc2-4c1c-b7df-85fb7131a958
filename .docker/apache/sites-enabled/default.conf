<VirtualHost *:80>
	ServerName tipli.czlocal
	ServerAlias tipli.sklocal
	ServerAlias tipli.pllocal
	ServerAlias tipli.rolocal
	ServerAlias tiplino.hulocal
	ServerAlias tipli.silocal
	ServerAlias tipli.hrlocal
	ServerAlias tipli.rslocal
	ServerAlias tipli.bglocal

	## Vhost docroot
	DocumentRoot "/project/www"
	<Directory "/project/www">
		Options Indexes FollowSymLinks MultiViews
		AllowOverride all
		Require all granted
	</Directory>
</VirtualHost>


<VirtualHost *:443>
	ServerName tipli.czlocal
	ServerAlias tipli.sklocal
	ServerAlias tipli.pllocal
	ServerAlias tipli.rolocal
	ServerAlias tiplino.hulocal
	ServerAlias tipli.silocal
	ServerAlias tipli.hrlocal
	ServerAlias tipli.rslocal
	ServerAlias tipli.bglocal
	DocumentRoot "/project/www"

	<Directory "/project/www">
		Options Indexes FollowSymLinks MultiViews
		AllowOverride all
		Require all granted
	</Directory>

	SSLEngine on
	SSLCertificateFile /etc/apache2/ssl/cert.pem
	SSLCertificateKeyFile /etc/apache2/ssl/cert-key.pem
</VirtualHost>
