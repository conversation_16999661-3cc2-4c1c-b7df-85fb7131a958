<?php

declare(strict_types=1);

use Rector\CodeQuality\Rector\BooleanNot\ReplaceMultipleBooleanNotRector;
use <PERSON>\CodeQuality\Rector\Class_\InlineConstructorDefaultToPropertyRector;
use Rector\CodeQuality\Rector\ClassMethod\ReturnTypeFromStrictScalarReturnExprRector;
use <PERSON>\CodeQuality\Rector\Equal\UseIdenticalOverEqualWithSameTypeRector;
use Rector\CodeQuality\Rector\Identical\GetClassToInstanceOfRector;
use Rector\CodeQuality\Rector\If_\ExplicitBoolCompareRector;
use Rector\CodeQuality\Rector\LogicalAnd\LogicalToBooleanRector;
use Rector\Config\RectorConfig;

return static function (RectorConfig $rectorConfig): void {
	$rectorConfig->paths([
	__DIR__ . '/app',
	]);

//	$rectorConfig->disableParallel();
	$rectorConfig->parallel(600, 4, 20);

// register a single rule
	$rectorConfig->rule(InlineConstructorDefaultToPropertyRector::class);
//	$rectorConfig->rule(ExplicitBoolCompareRector::class);
	$rectorConfig->rule(GetClassToInstanceOfRector::class);
	$rectorConfig->rule(LogicalToBooleanRector::class);
	$rectorConfig->rule(ReplaceMultipleBooleanNotRector::class);
	$rectorConfig->rule(UseIdenticalOverEqualWithSameTypeRector::class);
	$rectorConfig->rule(ReturnTypeFromStrictScalarReturnExprRector::class);

// define sets of rules
//	$rectorConfig->sets([
//		LevelSetList::UP_TO_PHP_80,
//	]);
};
