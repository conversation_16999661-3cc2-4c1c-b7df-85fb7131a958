# FixRedirectChains Command - Refactoring Documentation

## Přehled změn

Command `FixRedirectChains` byl kompletně refaktorován pro robustnější odstranění odkazů z HTML obsahu v tabulkách `tipli_articles_article` a `tipli_shops_shop_description_block`.

## Hlavní vylepšení

### 1. Zjednodušená architektura
- Odstraněny všechny nepotřebné <PERSON> (ArticleFacade, LeafletFacade, ShopFacade, LocalizationFacade, Translator)
- Ponechána pouze závislost na `Nette\Database\Context`
- Čistší a jednodušší konstruktor

### 2. Flexibilní vstupní parametry
- Místo hardcoded seznamu odkazů nyní command přijímá odkazy jako argument
- Podporuje více odkazů oddělených čárkou
- Příklad použití: `php console.php tipli:fix-redirect-chains:run "https://www.tipli.cz/cestovni-pojisteni,https://www.tipli.cz/other-link"`

### 3. Robustnější regex zpracování
- Implementovány multiple regex patterns pro různé formáty anchor tagů
- Lepší handling edge cases (anchor tagy s různými atributy, pořadím atributů)
- Escape speciálních znaků v URL pro bezpečné použití v regex

### 4. Vylepšené logování
- Detailní výstup o tom, které záznamy byly zpracovány
- Informace o nahrazených odkazech
- Celkový počet zpracovaných záznamů

### 5. Bezpečnější zpracování
- Kontrola, zda se obsah skutečně změnil před aktualizací databáze
- Validace HTML struktury (kontrola párových anchor tagů)
- Cleanup malformed links

## Nové metody

### `removeLinksFromHtmlContent()`
Hlavní metoda pro odstranění odkazů z HTML obsahu:
- Používá multiple regex patterns pro různé formáty anchor tagů
- Extrahuje text z anchor tagu a nahradí celý tag pouze textem
- Loguje všechny provedené změny

### `cleanupMalformedLinks()`
Dodatečné čištění pro edge cases:
- Odstraní zbývající href atributy obsahující cílový odkaz
- Vyčistí prázdné anchor tagy
- Odstraní osamocené anchor tagy

### `validateHtmlContent()`
Základní validace HTML struktury:
- Kontroluje párové anchor tagy
- Varuje před nesprávnou HTML strukturou

## Použití

```bash
# Odstranění jednoho odkazu
php console.php tipli:fix-redirect-chains:run "https://www.tipli.cz/cestovni-pojisteni"

# Odstranění více odkazů
php console.php tipli:fix-redirect-chains:run "https://www.tipli.cz/cestovni-pojisteni,https://www.tipli.cz/other-link"
```

## Zpracovávané tabulky

1. **tipli_articles_article** - sloupec `content`
2. **tipli_shops_shop_description_block** - sloupec `description`

## Příklad transformace

**Před:**
```html
<a href="https://www.tipli.cz/cestovni-pojisteni">cestovni pojisteni</a>
```

**Po:**
```html
cestovni pojisteni
```

## Bezpečnostní opatření

- SQL injection protection pomocí prepared statements
- Escape speciálních znaků v regex patterns
- Kontrola změn před aktualizací databáze
- Validace HTML struktury

## Výhody refaktoringu

1. **Jednodušší maintenance** - méně závislostí, čistší kód
2. **Flexibilita** - možnost specifikovat libovolné odkazy
3. **Robustnost** - lepší handling edge cases
4. **Transparentnost** - detailní logování všech změn
5. **Bezpečnost** - lepší validace a error handling
