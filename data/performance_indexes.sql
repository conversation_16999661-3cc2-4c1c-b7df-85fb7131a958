-- 1. <PERSON><PERSON><PERSON><PERSON><PERSON>ON TABLE OPTIMIZATIONS
-- For user balance calculations (getConfirmedBalance, getRegisteredBalance)
ALTER TABLE `tipli_transactions_transaction` ADD INDEX `user_confirmed_type_idx` (`user_id`, `confirmed_at`, `type`);
ALTER TABLE `tipli_transactions_transaction` ADD INDEX `user_billable_type_idx` (`user_id`, `billable`, `type`);
ALTER TABLE `tipli_transactions_transaction` ADD INDEX `user_created_type_idx` (`user_id`, `created_at`, `type`);

-- For transaction confirmation queries
ALTER TABLE `tipli_transactions_transaction` ADD INDEX `confirmed_created_idx` (`confirmed_at`, `created_at`);
ALTER TABLE `tipli_transactions_transaction` ADD INDEX `type_confirmed_user_idx` (`type`, `confirmed_at`, `user_id`);

-- For shop-specific transaction queries
ALTER TABLE `tipli_transactions_transaction` ADD INDEX `shop_type_confirmed_idx` (`shop_id`, `type`, `confirmed_at`);
ALTER TABLE `tipli_transactions_transaction` ADD INDEX `shop_user_type_idx` (`shop_id`, `user_id`, `type`);

-- 2. TRANSACTION_DATA TABLE OPTIMIZATIONS
-- For prepared transaction confirmations
ALTER TABLE `tipli_transactions_transaction_data` ADD INDEX `prepared_confirm_threshold_idx` (`prepared_for_confirm`, `confirmation_treshold`);
ALTER TABLE `tipli_transactions_transaction_data` ADD INDEX `paid_status_idx` (`is_paid`);

-- 3. REFUND TABLE OPTIMIZATIONS
-- For admin refund operations (the 11+ second bottleneck)
ALTER TABLE `tipli_refunds_refund` ADD INDEX `state_type_created_idx` (`state`, `type`, `created_at`);
ALTER TABLE `tipli_refunds_refund` ADD INDEX `user_state_type_idx` (`user_id`, `state`, `type`);
ALTER TABLE `tipli_refunds_refund` ADD INDEX `localization_state_idx` (`localization_id`, `state`);
ALTER TABLE `tipli_refunds_refund` ADD INDEX `resolved_at_state_idx` (`resolved_at`, `state`);

-- For refund processing queries
ALTER TABLE `tipli_refunds_refund` ADD INDEX `process_at_state_idx` (`process_at`, `state`);
ALTER TABLE `tipli_refunds_refund` ADD INDEX `operator_state_idx` (`assigned_operator_id`, `state`);

-- 4. PAYOUT TABLE OPTIMIZATIONS
-- For payout confirmation operations (2-3 second bottleneck)
ALTER TABLE `tipli_payouts_payout` ADD INDEX `user_state_created_idx` (`user_id`, `state`, `created_at`);
ALTER TABLE `tipli_payouts_payout` ADD INDEX `state_scheduled_idx` (`state`, `scheduled_at`);
ALTER TABLE `tipli_payouts_payout` ADD INDEX `confirmed_failed_idx` (`confirmed_at`, `failed_at`);

-- For payout processing
ALTER TABLE `tipli_payouts_payout` ADD INDEX `type_state_idx` (`type`, `state`);
ALTER TABLE `tipli_payouts_payout` ADD INDEX `locked_till_idx` (`locked_till`);

-- 5. USER SESSION OPTIMIZATIONS
-- For admin verification (5+ second bottleneck)
ALTER TABLE `tipli_account_user_session` ADD INDEX `user_verified_valid_idx` (`user_id`, `verified_at`, `valid_till`);
ALTER TABLE `tipli_account_user_session` ADD INDEX `sms_valid_till_idx` (`sms_code_valid_till`);

-- 6. USER DATA OPTIMIZATIONS
-- For user lookup operations
ALTER TABLE `tipli_account_user` ADD INDEX `email_localization_active_idx` (`email`, `localization_id`, `active`);
ALTER TABLE `tipli_account_user` ADD INDEX `active_created_idx` (`active`, `created_at`);

-- 7. REFUND SOLUTION OPTIMIZATIONS
-- For refund solution state queries
ALTER TABLE `tipli_refunds_refund_solution` ADD INDEX `state_updated_idx` (`state`, `state_updated_at`);
ALTER TABLE `tipli_refunds_refund_solution` ADD INDEX `refund_state_idx` (`refund_id`, `state`);

-- 8. SHOP OPTIMIZATIONS
-- For shop-related queries in admin
ALTER TABLE `tipli_shops_shop` ADD INDEX `localization_active_idx` (`localization_id`, `active`);

-- 9. COMPOSITE INDEXES FOR COMPLEX QUERIES
-- For statistics and reporting queries
ALTER TABLE `tipli_transactions_transaction` ADD INDEX `stats_composite_idx` (`type`, `confirmed_at`, `currency`, `user_id`);
ALTER TABLE `tipli_refunds_refund` ADD INDEX `refund_stats_idx` (`type`, `state`, `created_at`, `localization_id`);
ALTER TABLE `tipli_payouts_payout` ADD INDEX `payout_stats_idx` (`state`, `confirmed_at`, `user_id`, `type`);

-- 10. FOREIGN KEY OPTIMIZATION
-- Ensure all foreign key columns have proper indexes for JOIN performance
ALTER TABLE `tipli_transactions_transaction_data` ADD INDEX `transaction_fk_idx` (`transaction_id`);
ALTER TABLE `tipli_refunds_refund_solution` ADD INDEX `user_fk_idx` (`user_id`);
