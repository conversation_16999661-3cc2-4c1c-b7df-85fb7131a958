-- Adminer 4.8.1 MySQL 11.5.2-MariaDB-ubu2404 dump

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

SET NAMES utf8mb4;

DROP TABLE IF EXISTS `tipli_accounting_invoice`;
CREATE TABLE `tipli_accounting_invoice` (
                                            `id` int(11) NOT NULL AUTO_INCREMENT,
                                            `file_id` int(11) DEFAULT NULL,
                                            `assigned_to_user_id` int(11) DEFAULT NULL,
                                            `created_by_user_id` int(11) DEFAULT NULL,
                                            `name` varchar(255) NOT NULL,
                                            `category` varchar(255) NOT NULL,
                                            `status` varchar(255) NOT NULL,
                                            `amount` decimal(10,3) NOT NULL,
                                            `currency` varchar(3) NOT NULL,
                                            `iban` varchar(255) NOT NULL,
                                            `variable_symbol` varchar(255) DEFAULT NULL,
                                            `link` varchar(255) NOT NULL,
                                            `valid_till` datetime NOT NULL,
                                            `note` longtext DEFAULT NULL,
                                            `created_at` datetime NOT NULL,
                                            PRIMARY KEY (`id`),
                                            KEY `IDX_4ADC744D93CB796C` (`file_id`),
                                            KEY `IDX_4ADC744D11578D11` (`assigned_to_user_id`),
                                            KEY `IDX_4ADC744D7D182D95` (`created_by_user_id`),
                                            CONSTRAINT `FK_4ADC744D11578D11` FOREIGN KEY (`assigned_to_user_id`) REFERENCES `tipli_account_user` (`id`),
                                            CONSTRAINT `FK_4ADC744D7D182D95` FOREIGN KEY (`created_by_user_id`) REFERENCES `tipli_account_user` (`id`),
                                            CONSTRAINT `FK_4ADC744D93CB796C` FOREIGN KEY (`file_id`) REFERENCES `tipli_files_file` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_accounting_invoice_files`;
CREATE TABLE `tipli_accounting_invoice_files` (
                                                  `invoice_id` int(11) NOT NULL,
                                                  `file_id` int(11) NOT NULL,
                                                  PRIMARY KEY (`invoice_id`,`file_id`),
                                                  KEY `IDX_2216FD4F2989F1FD` (`invoice_id`),
                                                  KEY `IDX_2216FD4F93CB796C` (`file_id`),
                                                  CONSTRAINT `FK_2216FD4F2989F1FD` FOREIGN KEY (`invoice_id`) REFERENCES `tipli_accounting_invoice` (`id`) ON DELETE CASCADE,
                                                  CONSTRAINT `FK_2216FD4F93CB796C` FOREIGN KEY (`file_id`) REFERENCES `tipli_files_file` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_accounting_invoice_log`;
CREATE TABLE `tipli_accounting_invoice_log` (
                                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                                `invoice_id` int(11) DEFAULT NULL,
                                                `user_id` int(11) DEFAULT NULL,
                                                `action` varchar(255) NOT NULL,
                                                `created_at` datetime NOT NULL,
                                                PRIMARY KEY (`id`),
                                                KEY `IDX_435613CE2989F1FD` (`invoice_id`),
                                                KEY `IDX_435613CEA76ED395` (`user_id`),
                                                CONSTRAINT `FK_435613CE2989F1FD` FOREIGN KEY (`invoice_id`) REFERENCES `tipli_accounting_invoice` (`id`),
                                                CONSTRAINT `FK_435613CEA76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_accounting_payment`;
CREATE TABLE `tipli_accounting_payment` (
                                            `id` int(11) NOT NULL AUTO_INCREMENT,
                                            `payout_id` int(11) DEFAULT NULL,
                                            `move_id` varchar(255) NOT NULL,
                                            `bank` varchar(255) NOT NULL,
                                            `to_account_number` varchar(255) NOT NULL,
                                            `amount` decimal(10,3) NOT NULL,
                                            `currency` varchar(3) NOT NULL,
                                            `variable_symbol` varchar(255) DEFAULT NULL,
                                            `message` varchar(255) DEFAULT NULL,
                                            `paid_at` datetime NOT NULL,
                                            `created_at` datetime NOT NULL,
                                            PRIMARY KEY (`id`),
                                            KEY `IDX_B791E704C6D61B7F` (`payout_id`),
                                            CONSTRAINT `FK_B791E704C6D61B7F` FOREIGN KEY (`payout_id`) REFERENCES `tipli_payouts_payout` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_account_banned_user`;
CREATE TABLE `tipli_account_banned_user` (
                                             `id` int(11) NOT NULL AUTO_INCREMENT,
                                             `localization_id` int(11) DEFAULT NULL,
                                             `user_id` int(11) DEFAULT NULL,
                                             `pattern` varchar(255) NOT NULL,
                                             `type` varchar(255) NOT NULL,
                                             `created_at` datetime NOT NULL,
                                             PRIMARY KEY (`id`),
                                             KEY `IDX_4060B6936A2856C7` (`localization_id`),
                                             KEY `IDX_4060B693A76ED395` (`user_id`),
                                             KEY `type_idx` (`type`),
                                             CONSTRAINT `FK_4060B6936A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                             CONSTRAINT `FK_4060B693A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_account_phone_number_verification_code`;
CREATE TABLE `tipli_account_phone_number_verification_code` (
                                                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                                                `user_id` int(11) DEFAULT NULL,
                                                                `phone_number` varchar(255) DEFAULT NULL,
                                                                `valid_till` datetime NOT NULL,
                                                                `verification_code` varchar(255) NOT NULL,
                                                                `ip` varchar(255) DEFAULT NULL,
                                                                `used_at` datetime DEFAULT NULL,
                                                                `created_at` datetime NOT NULL,
                                                                PRIMARY KEY (`id`),
                                                                KEY `IDX_EA5A021A76ED395` (`user_id`),
                                                                CONSTRAINT `FK_EA5A021A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_account_privacy_policy`;
CREATE TABLE `tipli_account_privacy_policy` (
                                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                                `user_id` int(11) DEFAULT NULL,
                                                `privacy_type` varchar(255) NOT NULL,
                                                `created_at` datetime DEFAULT NULL,
                                                PRIMARY KEY (`id`),
                                                KEY `IDX_7436C209A76ED395` (`user_id`),
                                                CONSTRAINT `FK_7436C209A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_account_segment_data`;
CREATE TABLE `tipli_account_segment_data` (
                                              `id` int(11) NOT NULL AUTO_INCREMENT,
                                              `user_id` int(11) DEFAULT NULL,
                                              `addon_installed` tinyint(1) NOT NULL,
                                              `last_logged_in_at` datetime NOT NULL,
                                              `opened_at` datetime DEFAULT NULL,
                                              `clicked_at` datetime DEFAULT NULL,
                                              `addon_feed_downloaded_at` datetime DEFAULT NULL,
                                              `first_transaction_at` datetime DEFAULT NULL,
                                              `last_transaction_at` datetime DEFAULT NULL,
                                              `count_of_redirections` int(11) NOT NULL,
                                              `count_of_commission_transactions` int(11) NOT NULL,
                                              `count_of_bonus_refund_transactions` int(11) NOT NULL,
                                              `count_of_confirmed_commission_transactions` int(11) NOT NULL,
                                              `count_of_canceled_commission_transactions` int(11) NOT NULL,
                                              `count_of_recommended_users` int(11) NOT NULL,
                                              `last_recommended_user_at` datetime DEFAULT NULL,
                                              `has_money_reward_bonus` tinyint(1) NOT NULL,
                                              `has_unsubscribed_newsletters` tinyint(1) NOT NULL,
                                              `last_active_campaign_engagement_at` datetime DEFAULT NULL,
                                              `number_of_purchases` int(11) NOT NULL DEFAULT 0,
                                              `number_of_purchases_score` int(11) NOT NULL DEFAULT 0,
                                              `last_interaction_at` datetime DEFAULT NULL,
                                              `last_web_visit_at` datetime DEFAULT NULL,
                                              `last_mobile_app_visit_at` datetime DEFAULT NULL,
                                              `last_interaction_score` int(11) NOT NULL DEFAULT 0,
                                              `lifetime_commission_amount` decimal(10,3) NOT NULL DEFAULT 0.000,
                                              `lifetime_user_commission_amount` decimal(10,3) NOT NULL DEFAULT 0.000,
                                              `registered_balance` decimal(10,3) NOT NULL DEFAULT 0.000,
                                              `confirmed_balance` decimal(10,3) NOT NULL DEFAULT 0.000,
                                              `bonus_balance` decimal(10,3) NOT NULL DEFAULT 0.000,
                                              `static_segments` varchar(255) DEFAULT NULL,
                                              `cashback_used_at` datetime DEFAULT NULL,
                                              `deals_used_at` datetime DEFAULT NULL,
                                              `leaflets_used_at` datetime DEFAULT NULL,
                                              `suspected_at` datetime DEFAULT NULL,
                                              `suspected_reason` varchar(255) DEFAULT NULL,
                                              `vip_at` datetime DEFAULT NULL,
                                              `aliexpress_addon_enabled` tinyint(1) NOT NULL,
                                              `affiliate_segment_at` datetime DEFAULT NULL,
                                              `registration_platform` varchar(16) DEFAULT NULL,
                                              `share_coefficient` double NOT NULL,
                                              `has_special_share_coefficient` tinyint(1) NOT NULL,
                                              `unique_id` varchar(255) DEFAULT NULL,
                                              `disabled_newsletters_due_to_inactivity_at` datetime DEFAULT NULL,
                                              `rondo_data1` varchar(255) DEFAULT NULL,
                                              `rondo_data2` varchar(255) DEFAULT NULL,
                                              `risk_status` varchar(255) DEFAULT NULL,
                                              `risk_note` varchar(255) DEFAULT NULL,
                                              `risk_updated_at` datetime DEFAULT NULL,
                                              PRIMARY KEY (`id`),
                                              UNIQUE KEY `UNIQ_BDFA2477A76ED395` (`user_id`),
                                              KEY `registration_platform` (`registration_platform`),
                                              CONSTRAINT `FK_BDFA2477A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_account_sending_policy`;
CREATE TABLE `tipli_account_sending_policy` (
                                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                                `user_id` int(11) DEFAULT NULL,
                                                `message_type` varchar(255) NOT NULL,
                                                `content_type` varchar(255) NOT NULL,
                                                `created_at` datetime DEFAULT NULL,
                                                PRIMARY KEY (`id`),
                                                KEY `IDX_C888576FA76ED395` (`user_id`),
                                                CONSTRAINT `FK_C888576FA76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_account_user`;
CREATE TABLE `tipli_account_user` (
                                      `id` int(11) NOT NULL AUTO_INCREMENT,
                                      `synchronization_id` int(11) DEFAULT NULL,
                                      `localization_id` int(11) DEFAULT NULL,
                                      `parent_id` int(11) DEFAULT NULL,
                                      `partner_organization_id` int(11) DEFAULT NULL,
                                      `utm_id` int(11) DEFAULT NULL,
                                      `email` varchar(255) NOT NULL,
                                      `password` varchar(255) DEFAULT NULL,
                                      `hash_method` varchar(255) NOT NULL,
                                      `role` varchar(255) NOT NULL,
                                      `segment` varchar(255) DEFAULT NULL,
                                      `emails_unsubscribed_at` datetime DEFAULT NULL,
                                      `active` tinyint(1) NOT NULL,
                                      `email_verified_at` datetime DEFAULT NULL,
                                      `phone_number_verified_at` datetime DEFAULT NULL,
                                      `account_number_verified_at` datetime DEFAULT NULL,
                                      `created_at` datetime NOT NULL,
                                      PRIMARY KEY (`id`),
                                      UNIQUE KEY `UNIQ_1981056FC469E18` (`synchronization_id`),
                                      UNIQUE KEY `email_unique` (`localization_id`,`email`),
                                      KEY `IDX_1981056F6A2856C7` (`localization_id`),
                                      KEY `IDX_1981056F727ACA70` (`parent_id`),
                                      KEY `IDX_1981056F83C2DD73` (`partner_organization_id`),
                                      KEY `IDX_1981056FB6334822` (`utm_id`),
                                      KEY `segment_idx` (`segment`),
                                      KEY `created_at_idx` (`created_at`),
                                      KEY `email_idx` (`email`),
                                      CONSTRAINT `FK_1981056F6A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                      CONSTRAINT `FK_1981056F727ACA70` FOREIGN KEY (`parent_id`) REFERENCES `tipli_account_user` (`id`),
                                      CONSTRAINT `FK_1981056F83C2DD73` FOREIGN KEY (`partner_organization_id`) REFERENCES `tipli_partner_organizations_partner_organization` (`id`),
                                      CONSTRAINT `FK_1981056FB6334822` FOREIGN KEY (`utm_id`) REFERENCES `tipli_utm_utm` (`id`),
                                      CONSTRAINT `FK_1981056FC469E18` FOREIGN KEY (`synchronization_id`) REFERENCES `tipli_account_user_synchronization` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_account_user_change`;
CREATE TABLE `tipli_account_user_change` (
                                             `id` int(11) NOT NULL AUTO_INCREMENT,
                                             `user_id` int(11) DEFAULT NULL,
                                             `browser_token_id` int(11) DEFAULT NULL,
                                             `token` varchar(40) NOT NULL,
                                             `ip` varchar(255) DEFAULT NULL,
                                             `user_agent` varchar(255) DEFAULT NULL,
                                             `first_name` varchar(255) DEFAULT NULL,
                                             `last_name` varchar(255) DEFAULT NULL,
                                             `gender` varchar(255) DEFAULT NULL,
                                             `birthdate` date DEFAULT NULL,
                                             `phone_number` varchar(255) DEFAULT NULL,
                                             `account_number` varchar(255) DEFAULT NULL,
                                             `password_changed` tinyint(1) NOT NULL,
                                             `verification_required` tinyint(1) NOT NULL,
                                             `verified_at` datetime DEFAULT NULL,
                                             `verification_ip` varchar(255) DEFAULT NULL,
                                             `verification_user_agent` varchar(255) DEFAULT NULL,
                                             `verification_valid_till` datetime DEFAULT NULL,
                                             `created_at` datetime NOT NULL,
                                             PRIMARY KEY (`id`),
                                             KEY `IDX_35C0352BA76ED395` (`user_id`),
                                             KEY `IDX_35C0352BE8B24323` (`browser_token_id`),
                                             CONSTRAINT `FK_35C0352BA76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`),
                                             CONSTRAINT `FK_35C0352BE8B24323` FOREIGN KEY (`browser_token_id`) REFERENCES `tipli_browser_activities_browser_token` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_account_user_data`;
CREATE TABLE `tipli_account_user_data` (
                                           `id` int(11) NOT NULL AUTO_INCREMENT,
                                           `user_id` int(11) DEFAULT NULL,
                                           `removed_by` int(11) DEFAULT NULL,
                                           `banned_by` int(11) DEFAULT NULL,
                                           `first_name` varchar(255) DEFAULT NULL,
                                           `last_name` varchar(255) DEFAULT NULL,
                                           `vocal_first_name` varchar(255) DEFAULT NULL,
                                           `gender` varchar(255) DEFAULT NULL,
                                           `birthdate` date DEFAULT NULL,
                                           `phone_number` varchar(255) DEFAULT NULL,
                                           `account_number` varchar(255) DEFAULT NULL,
                                           `ip` varchar(255) DEFAULT NULL,
                                           `user_agent` varchar(255) DEFAULT NULL,
                                           `platform` varchar(7) DEFAULT NULL,
                                           `country` varchar(2) DEFAULT NULL,
                                           `referer` varchar(255) DEFAULT NULL,
                                           `note` longtext DEFAULT NULL,
                                           `email_unsubscribe_reason` longtext DEFAULT NULL,
                                           `registration_page` varchar(255) DEFAULT NULL,
                                           `first_visit_page` varchar(255) DEFAULT NULL,
                                           `locale_role` varchar(255) DEFAULT NULL,
                                           `first_purchase_reported_at` datetime DEFAULT NULL,
                                           `removed_at` datetime DEFAULT NULL,
                                           `banned_at` datetime DEFAULT NULL,
                                           `balance` decimal(10,3) DEFAULT 0.000,
                                           PRIMARY KEY (`id`),
                                           UNIQUE KEY `UNIQ_A79D4B79A76ED395` (`user_id`),
                                           KEY `IDX_A79D4B7910CDAFDB` (`removed_by`),
                                           KEY `IDX_A79D4B799DCE7562` (`banned_by`),
                                           KEY `registration_page_x` (`registration_page`),
                                           KEY `platform_x` (`platform`),
                                           KEY `country_x` (`country`),
                                           CONSTRAINT `FK_A79D4B7910CDAFDB` FOREIGN KEY (`removed_by`) REFERENCES `tipli_account_user` (`id`),
                                           CONSTRAINT `FK_A79D4B799DCE7562` FOREIGN KEY (`banned_by`) REFERENCES `tipli_account_user` (`id`),
                                           CONSTRAINT `FK_A79D4B79A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_account_user_device_token`;
CREATE TABLE `tipli_account_user_device_token` (
                                                   `id` int(11) NOT NULL AUTO_INCREMENT,
                                                   `user_id` int(11) DEFAULT NULL,
                                                   `token` varchar(255) NOT NULL,
                                                   `platform` varchar(16) NOT NULL,
                                                   `version` varchar(255) DEFAULT NULL,
                                                   `valid_till` datetime NOT NULL,
                                                   `failed_at` datetime DEFAULT NULL,
                                                   `created_at` datetime NOT NULL,
                                                   PRIMARY KEY (`id`),
                                                   KEY `IDX_1F1D1BF3A76ED395` (`user_id`),
                                                   KEY `token_idx` (`token`),
                                                   CONSTRAINT `FK_1F1D1BF3A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_account_user_duplicity`;
CREATE TABLE `tipli_account_user_duplicity` (
                                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                                `user_id` int(11) DEFAULT NULL,
                                                `related_user_id` int(11) DEFAULT NULL,
                                                `type` varchar(255) NOT NULL,
                                                `value` varchar(255) NOT NULL,
                                                `note` varchar(255) NOT NULL,
                                                `created_at` datetime NOT NULL,
                                                PRIMARY KEY (`id`),
                                                KEY `IDX_42656B1AA76ED395` (`user_id`),
                                                KEY `IDX_42656B1A98771930` (`related_user_id`),
                                                CONSTRAINT `FK_42656B1A98771930` FOREIGN KEY (`related_user_id`) REFERENCES `tipli_account_user` (`id`),
                                                CONSTRAINT `FK_42656B1AA76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_account_user_email_activity`;
CREATE TABLE `tipli_account_user_email_activity` (
                                                     `id` int(11) NOT NULL AUTO_INCREMENT,
                                                     `user_id` int(11) DEFAULT NULL,
                                                     `unique_id` varchar(64) NOT NULL,
                                                     `event_name` varchar(16) NOT NULL,
                                                     `date_time` datetime NOT NULL,
                                                     `source` varchar(255) NOT NULL,
                                                     `data` varchar(255) DEFAULT NULL,
                                                     `created_at` datetime DEFAULT NULL,
                                                     PRIMARY KEY (`id`),
                                                     UNIQUE KEY `unique_id` (`unique_id`),
                                                     KEY `IDX_5338870A76ED395` (`user_id`),
                                                     KEY `eventName` (`event_name`),
                                                     KEY `source` (`source`),
                                                     CONSTRAINT `FK_5338870A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_account_user_email_subscription_log`;
CREATE TABLE `tipli_account_user_email_subscription_log` (
                                                             `id` int(11) NOT NULL AUTO_INCREMENT,
                                                             `user_id` int(11) DEFAULT NULL,
                                                             `action` varchar(255) NOT NULL,
                                                             `site` varchar(255) NOT NULL,
                                                             `source` varchar(255) NOT NULL,
                                                             `reason` varchar(255) DEFAULT NULL,
                                                             `created_at` datetime DEFAULT NULL,
                                                             PRIMARY KEY (`id`),
                                                             KEY `IDX_42A9DEC5A76ED395` (`user_id`),
                                                             KEY `action` (`action`),
                                                             KEY `site` (`site`),
                                                             KEY `source` (`source`),
                                                             CONSTRAINT `FK_42A9DEC5A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_account_user_event`;
CREATE TABLE `tipli_account_user_event` (
                                            `id` int(11) NOT NULL AUTO_INCREMENT,
                                            `user_id` int(11) DEFAULT NULL,
                                            `created_by_user_id` int(11) DEFAULT NULL,
                                            `action` varchar(25) NOT NULL,
                                            `type` varchar(50) DEFAULT NULL,
                                            `reason` varchar(255) DEFAULT NULL,
                                            `created_at` datetime NOT NULL,
                                            PRIMARY KEY (`id`),
                                            KEY `IDX_C6C69D65A76ED395` (`user_id`),
                                            KEY `IDX_C6C69D657D182D95` (`created_by_user_id`),
                                            KEY `action_idx` (`action`),
                                            CONSTRAINT `FK_C6C69D657D182D95` FOREIGN KEY (`created_by_user_id`) REFERENCES `tipli_account_user` (`id`),
                                            CONSTRAINT `FK_C6C69D65A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_account_user_favorite_shop`;
CREATE TABLE `tipli_account_user_favorite_shop` (
                                                    `id` int(11) NOT NULL AUTO_INCREMENT,
                                                    `user_id` int(11) DEFAULT NULL,
                                                    `shop_id` int(11) DEFAULT NULL,
                                                    `is_favorite` tinyint(1) NOT NULL,
                                                    `source` varchar(16) NOT NULL,
                                                    `reason` varchar(16) NOT NULL,
                                                    `last_interaction_at` datetime DEFAULT NULL,
                                                    `created_at` datetime NOT NULL,
                                                    PRIMARY KEY (`id`),
                                                    KEY `IDX_48344BEA76ED395` (`user_id`),
                                                    KEY `IDX_48344BE4D16C4DD` (`shop_id`),
                                                    CONSTRAINT `FK_48344BE4D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                                    CONSTRAINT `FK_48344BEA76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_account_user_favourite_shops`;
CREATE TABLE `tipli_account_user_favourite_shops` (
                                                      `user_id` int(11) NOT NULL,
                                                      `shop_id` int(11) NOT NULL,
                                                      PRIMARY KEY (`user_id`,`shop_id`),
                                                      KEY `IDX_5616F78AA76ED395` (`user_id`),
                                                      KEY `IDX_5616F78A4D16C4DD` (`shop_id`),
                                                      CONSTRAINT `FK_5616F78A4D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`) ON DELETE CASCADE,
                                                      CONSTRAINT `FK_5616F78AA76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_account_user_login`;
CREATE TABLE `tipli_account_user_login` (
                                            `id` int(11) NOT NULL AUTO_INCREMENT,
                                            `user_id` int(11) DEFAULT NULL,
                                            `email` varchar(255) DEFAULT NULL,
                                            `method` varchar(255) NOT NULL,
                                            `successfully` tinyint(1) NOT NULL,
                                            `ip` varchar(255) DEFAULT NULL,
                                            `user_agent` varchar(255) DEFAULT NULL,
                                            `platform` varchar(7) DEFAULT NULL,
                                            `country` varchar(2) DEFAULT NULL,
                                            `created_at` datetime NOT NULL,
                                            PRIMARY KEY (`id`),
                                            KEY `IDX_57605CD2A76ED395` (`user_id`),
                                            KEY `platform_x` (`platform`),
                                            KEY `country_x` (`country`),
                                            KEY `method_successfully_created_at_x` (`method`,`successfully`,`created_at`),
                                            CONSTRAINT `FK_57605CD2A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_account_user_mailchimp`;
CREATE TABLE `tipli_account_user_mailchimp` (
                                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                                `user_id` int(11) DEFAULT NULL,
                                                `status` varchar(12) DEFAULT NULL,
                                                `subscriber_hash` varchar(32) DEFAULT NULL,
                                                `is_challenged` tinyint(1) NOT NULL,
                                                `last_challenged_at` datetime DEFAULT NULL,
                                                `synchronize_at` datetime DEFAULT NULL,
                                                `synchronized_at` datetime DEFAULT NULL,
                                                PRIMARY KEY (`id`),
                                                UNIQUE KEY `UNIQ_9B1CB591A76ED395` (`user_id`),
                                                KEY `status` (`is_challenged`),
                                                CONSTRAINT `FK_9B1CB591A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_account_user_mobile_device`;
CREATE TABLE `tipli_account_user_mobile_device` (
                                                    `id` int(11) NOT NULL AUTO_INCREMENT,
                                                    `user_id` int(11) DEFAULT NULL,
                                                    `platform` varchar(7) NOT NULL,
                                                    `installed_at` datetime NOT NULL,
                                                    `last_login_at` datetime NOT NULL,
                                                    `last_logout_at` datetime DEFAULT NULL,
                                                    `last_activity_at` datetime NOT NULL,
                                                    `created_at` datetime NOT NULL,
                                                    PRIMARY KEY (`id`),
                                                    KEY `IDX_30E44CA7A76ED395` (`user_id`),
                                                    CONSTRAINT `FK_30E44CA7A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_account_user_mobile_session`;
CREATE TABLE `tipli_account_user_mobile_session` (
                                                     `id` int(11) NOT NULL AUTO_INCREMENT,
                                                     `user_id` int(11) DEFAULT NULL,
                                                     `token` varchar(64) NOT NULL,
                                                     `method` varchar(16) NOT NULL,
                                                     `valid_till` datetime NOT NULL,
                                                     `created_at` datetime NOT NULL,
                                                     PRIMARY KEY (`id`),
                                                     KEY `IDX_92CF8642A76ED395` (`user_id`),
                                                     CONSTRAINT `FK_92CF8642A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_account_user_permission`;
CREATE TABLE `tipli_account_user_permission` (
                                                 `id` int(11) NOT NULL AUTO_INCREMENT,
                                                 `user_id` int(11) DEFAULT NULL,
                                                 `granted_by_user_id` int(11) DEFAULT NULL,
                                                 `module` varchar(32) NOT NULL,
                                                 `created_at` datetime NOT NULL,
                                                 PRIMARY KEY (`id`),
                                                 KEY `IDX_B4DDF564A76ED395` (`user_id`),
                                                 KEY `IDX_B4DDF564F6097589` (`granted_by_user_id`),
                                                 KEY `module` (`module`),
                                                 CONSTRAINT `FK_B4DDF564A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`),
                                                 CONSTRAINT `FK_B4DDF564F6097589` FOREIGN KEY (`granted_by_user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_account_user_safari_session`;
CREATE TABLE `tipli_account_user_safari_session` (
                                                     `id` int(11) NOT NULL AUTO_INCREMENT,
                                                     `user_id` int(11) DEFAULT NULL,
                                                     `token` varchar(255) NOT NULL,
                                                     `last_interaction_at` datetime DEFAULT NULL,
                                                     `created_at` datetime NOT NULL,
                                                     PRIMARY KEY (`id`),
                                                     KEY `IDX_A3967B6BA76ED395` (`user_id`),
                                                     CONSTRAINT `FK_A3967B6BA76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_account_user_security`;
CREATE TABLE `tipli_account_user_security` (
                                               `id` int(11) NOT NULL AUTO_INCREMENT,
                                               `user_id` int(11) DEFAULT NULL,
                                               `access_token` varchar(255) NOT NULL,
                                               `access_token_valid_till` datetime NOT NULL,
                                               `new_password_request_hash` varchar(255) DEFAULT NULL,
                                               `new_password_request_hash_valid_till` datetime DEFAULT NULL,
                                               `facebook_id` varchar(255) DEFAULT NULL,
                                               `google_id` varchar(255) DEFAULT NULL,
                                               `apple_id` varchar(255) DEFAULT NULL,
                                               PRIMARY KEY (`id`),
                                               UNIQUE KEY `UNIQ_2AD962A5B6A2DD68` (`access_token`),
                                               UNIQUE KEY `UNIQ_2AD962A5A76ED395` (`user_id`),
                                               KEY `facebook_idx` (`facebook_id`),
                                               KEY `google_idx` (`google_id`),
                                               KEY `new_password_request_hash` (`new_password_request_hash`),
                                               CONSTRAINT `FK_2AD962A5A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_account_user_session`;
CREATE TABLE `tipli_account_user_session` (
                                              `id` int(11) NOT NULL AUTO_INCREMENT,
                                              `user_id` int(11) DEFAULT NULL,
                                              `browser_token_id` int(11) DEFAULT NULL,
                                              `sms_code` varchar(255) NOT NULL,
                                              `sms_code_valid_till` datetime NOT NULL,
                                              `ip` varchar(255) NOT NULL,
                                              `user_agent` varchar(255) NOT NULL,
                                              `verified_at` datetime DEFAULT NULL,
                                              `valid_till` datetime DEFAULT NULL,
                                              `created_at` datetime NOT NULL,
                                              PRIMARY KEY (`id`),
                                              KEY `IDX_47E39B97A76ED395` (`user_id`),
                                              KEY `IDX_47E39B97E8B24323` (`browser_token_id`),
                                              CONSTRAINT `FK_47E39B97A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`),
                                              CONSTRAINT `FK_47E39B97E8B24323` FOREIGN KEY (`browser_token_id`) REFERENCES `tipli_browser_activities_browser_token` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_account_user_settings`;
CREATE TABLE `tipli_account_user_settings` (
                                               `id` int(11) NOT NULL AUTO_INCREMENT,
                                               `user_id` int(11) DEFAULT NULL,
                                               `smartsupp_agent_id` varchar(255) DEFAULT NULL,
                                               `freshdesk_agent_id` varchar(255) DEFAULT NULL,
                                               `favorite_pages` longtext DEFAULT NULL,
                                               `two_factor_hash` longtext DEFAULT NULL,
                                               `support_localization_ids` varchar(255) DEFAULT NULL,
                                               `snow_enabled` tinyint(1) DEFAULT NULL,
                                               `dark_mode` varchar(255) DEFAULT NULL,
                                               PRIMARY KEY (`id`),
                                               KEY `IDX_A0717A1A76ED395` (`user_id`),
                                               CONSTRAINT `FK_A0717A1A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_account_user_synchronization`;
CREATE TABLE `tipli_account_user_synchronization` (
                                                      `id` int(11) NOT NULL AUTO_INCREMENT,
                                                      `user_id` int(11) DEFAULT NULL,
                                                      `processed_at` datetime DEFAULT NULL,
                                                      `analyze_at` datetime DEFAULT NULL,
                                                      `analyzed_at` datetime DEFAULT NULL,
                                                      PRIMARY KEY (`id`),
                                                      UNIQUE KEY `UNIQ_5EEADD46A76ED395` (`user_id`),
                                                      CONSTRAINT `FK_5EEADD46A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_account_user_utm`;
CREATE TABLE `tipli_account_user_utm` (
                                          `id` int(11) NOT NULL AUTO_INCREMENT,
                                          `user_id` int(11) DEFAULT NULL,
                                          `utm_id` int(11) DEFAULT NULL,
                                          `changed_at` datetime NOT NULL,
                                          `created_at` datetime NOT NULL,
                                          PRIMARY KEY (`id`),
                                          KEY `IDX_BB3DEF30A76ED395` (`user_id`),
                                          KEY `IDX_BB3DEF30B6334822` (`utm_id`),
                                          CONSTRAINT `FK_BB3DEF30A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`),
                                          CONSTRAINT `FK_BB3DEF30B6334822` FOREIGN KEY (`utm_id`) REFERENCES `tipli_utm_utm` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_account_user_visit`;
CREATE TABLE `tipli_account_user_visit` (
                                            `id` int(11) NOT NULL AUTO_INCREMENT,
                                            `user_id` int(11) DEFAULT NULL,
                                            `target` varchar(255) NOT NULL,
                                            `parameters` longtext DEFAULT NULL,
                                            `is_ajax` tinyint(1) DEFAULT NULL,
                                            `created_at` datetime NOT NULL,
                                            PRIMARY KEY (`id`),
                                            KEY `IDX_BE167EFBA76ED395` (`user_id`),
                                            CONSTRAINT `FK_BE167EFBA76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_addon_feedback`;
CREATE TABLE `tipli_addon_feedback` (
                                        `id` int(11) NOT NULL AUTO_INCREMENT,
                                        `localization_id` int(11) NOT NULL,
                                        `user_id` int(11) NOT NULL,
                                        `checked_by_id` int(11) DEFAULT NULL,
                                        `content` longtext NOT NULL,
                                        `checked_at` datetime DEFAULT NULL,
                                        `created_at` datetime NOT NULL,
                                        PRIMARY KEY (`id`),
                                        UNIQUE KEY `UNIQ_C8A5E9A7A76ED395` (`user_id`),
                                        KEY `IDX_C8A5E9A76A2856C7` (`localization_id`),
                                        KEY `IDX_C8A5E9A72199DB86` (`checked_by_id`),
                                        CONSTRAINT `FK_C8A5E9A72199DB86` FOREIGN KEY (`checked_by_id`) REFERENCES `tipli_account_user` (`id`),
                                        CONSTRAINT `FK_C8A5E9A76A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                        CONSTRAINT `FK_C8A5E9A7A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_addon_search_query`;
CREATE TABLE `tipli_addon_search_query` (
                                            `id` int(11) NOT NULL AUTO_INCREMENT,
                                            `localization_id` int(11) DEFAULT NULL,
                                            `shop_id` int(11) DEFAULT NULL,
                                            `query` varchar(255) NOT NULL,
                                            `count_of_deals` int(11) DEFAULT NULL,
                                            `created_at` datetime NOT NULL,
                                            PRIMARY KEY (`id`),
                                            KEY `IDX_757990FD6A2856C7` (`localization_id`),
                                            KEY `IDX_757990FD4D16C4DD` (`shop_id`),
                                            CONSTRAINT `FK_757990FD4D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                            CONSTRAINT `FK_757990FD6A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_addon_shop_visit`;
CREATE TABLE `tipli_addon_shop_visit` (
                                          `id` int(11) NOT NULL AUTO_INCREMENT,
                                          `user_id` int(11) DEFAULT NULL,
                                          `shop_id` int(11) DEFAULT NULL,
                                          `synchronized_at` datetime DEFAULT NULL,
                                          `created_at` datetime NOT NULL,
                                          PRIMARY KEY (`id`),
                                          KEY `IDX_BF2B8BC0A76ED395` (`user_id`),
                                          KEY `IDX_BF2B8BC04D16C4DD` (`shop_id`),
                                          CONSTRAINT `FK_BF2B8BC04D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                          CONSTRAINT `FK_BF2B8BC0A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_addon_user_settings`;
CREATE TABLE `tipli_addon_user_settings` (
                                             `id` int(11) NOT NULL AUTO_INCREMENT,
                                             `user_id` int(11) DEFAULT NULL,
                                             `settings` longtext NOT NULL,
                                             `created_at` datetime NOT NULL,
                                             PRIMARY KEY (`id`),
                                             KEY `IDX_28AF5AAEA76ED395` (`user_id`),
                                             CONSTRAINT `FK_28AF5AAEA76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_articles_article`;
CREATE TABLE `tipli_articles_article` (
                                          `id` int(11) NOT NULL AUTO_INCREMENT,
                                          `localization_id` int(11) DEFAULT NULL,
                                          `page_extension_id` int(11) DEFAULT NULL,
                                          `preview_image_id` int(11) DEFAULT NULL,
                                          `author_id` int(11) DEFAULT NULL,
                                          `public_author_id` int(11) DEFAULT NULL,
                                          `removed_by` int(11) DEFAULT NULL,
                                          `name` varchar(255) NOT NULL,
                                          `slug` varchar(255) NOT NULL,
                                          `description` longtext DEFAULT NULL,
                                          `content` longtext DEFAULT NULL,
                                          `anchor_links` longtext DEFAULT NULL,
                                          `primary_keywords` varchar(255) DEFAULT NULL,
                                          `secondary_keywords` varchar(255) DEFAULT NULL,
                                          `discussion_allowed` tinyint(1) NOT NULL,
                                          `published_at` datetime DEFAULT NULL,
                                          `topped_since` datetime DEFAULT NULL,
                                          `topped_till` datetime DEFAULT NULL,
                                          `updated_at` datetime NOT NULL,
                                          `update_index_at` datetime DEFAULT NULL,
                                          `removed_at` datetime DEFAULT NULL,
                                          `created_at` datetime NOT NULL,
                                          PRIMARY KEY (`id`),
                                          UNIQUE KEY `UNIQ_BA91D474B60E79A5` (`page_extension_id`),
                                          UNIQUE KEY `slug_unique` (`localization_id`,`slug`),
                                          KEY `IDX_BA91D4746A2856C7` (`localization_id`),
                                          KEY `IDX_BA91D474FAE957CD` (`preview_image_id`),
                                          KEY `IDX_BA91D474F675F31B` (`author_id`),
                                          KEY `IDX_BA91D474DB52289A` (`public_author_id`),
                                          KEY `IDX_BA91D47410CDAFDB` (`removed_by`),
                                          KEY `published_at_idx` (`published_at`),
                                          KEY `update_index_at_idx` (`update_index_at`),
                                          CONSTRAINT `FK_BA91D47410CDAFDB` FOREIGN KEY (`removed_by`) REFERENCES `tipli_account_user` (`id`),
                                          CONSTRAINT `FK_BA91D4746A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                          CONSTRAINT `FK_BA91D474B60E79A5` FOREIGN KEY (`page_extension_id`) REFERENCES `tipli_seo_page_extension` (`id`),
                                          CONSTRAINT `FK_BA91D474DB52289A` FOREIGN KEY (`public_author_id`) REFERENCES `tipli_account_user` (`id`),
                                          CONSTRAINT `FK_BA91D474F675F31B` FOREIGN KEY (`author_id`) REFERENCES `tipli_account_user` (`id`),
                                          CONSTRAINT `FK_BA91D474FAE957CD` FOREIGN KEY (`preview_image_id`) REFERENCES `tipli_images_image` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_articles_article_shops`;
CREATE TABLE `tipli_articles_article_shops` (
                                                `article_id` int(11) NOT NULL,
                                                `shop_id` int(11) NOT NULL,
                                                PRIMARY KEY (`article_id`,`shop_id`),
                                                KEY `IDX_88B1E0567294869C` (`article_id`),
                                                KEY `IDX_88B1E0564D16C4DD` (`shop_id`),
                                                CONSTRAINT `FK_88B1E0564D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`) ON DELETE CASCADE,
                                                CONSTRAINT `FK_88B1E0567294869C` FOREIGN KEY (`article_id`) REFERENCES `tipli_articles_article` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_articles_article_tag`;
CREATE TABLE `tipli_articles_article_tag` (
                                              `article_id` int(11) NOT NULL,
                                              `tag_id` int(11) NOT NULL,
                                              PRIMARY KEY (`article_id`,`tag_id`),
                                              KEY `IDX_63D1E5977294869C` (`article_id`),
                                              KEY `IDX_63D1E597BAD26311` (`tag_id`),
                                              CONSTRAINT `FK_63D1E5977294869C` FOREIGN KEY (`article_id`) REFERENCES `tipli_articles_article` (`id`) ON DELETE CASCADE,
                                              CONSTRAINT `FK_63D1E597BAD26311` FOREIGN KEY (`tag_id`) REFERENCES `tipli_tags_tag` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_articles_related_article`;
CREATE TABLE `tipli_articles_related_article` (
                                                  `id` int(11) NOT NULL AUTO_INCREMENT,
                                                  `parent_article_id` int(11) DEFAULT NULL,
                                                  `related_article_id` int(11) DEFAULT NULL,
                                                  `priority` int(11) NOT NULL,
                                                  `created_at` datetime NOT NULL,
                                                  PRIMARY KEY (`id`),
                                                  KEY `IDX_5F620C6635D702CC` (`parent_article_id`),
                                                  KEY `IDX_5F620C66F8598E2C` (`related_article_id`),
                                                  CONSTRAINT `FK_5F620C6635D702CC` FOREIGN KEY (`parent_article_id`) REFERENCES `tipli_articles_article` (`id`),
                                                  CONSTRAINT `FK_5F620C66F8598E2C` FOREIGN KEY (`related_article_id`) REFERENCES `tipli_articles_article` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_browser_activities_browser_activity`;
CREATE TABLE `tipli_browser_activities_browser_activity` (
                                                             `id` int(11) NOT NULL AUTO_INCREMENT,
                                                             `user_id` int(11) DEFAULT NULL,
                                                             `browser_token_id` int(11) DEFAULT NULL,
                                                             `event` varchar(12) NOT NULL,
                                                             `created_at` datetime NOT NULL,
                                                             PRIMARY KEY (`id`),
                                                             KEY `IDX_918AF201A76ED395` (`user_id`),
                                                             KEY `IDX_918AF201E8B24323` (`browser_token_id`),
                                                             CONSTRAINT `FK_918AF201A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`),
                                                             CONSTRAINT `FK_918AF201E8B24323` FOREIGN KEY (`browser_token_id`) REFERENCES `tipli_browser_activities_browser_token` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_browser_activities_browser_token`;
CREATE TABLE `tipli_browser_activities_browser_token` (
                                                          `id` int(11) NOT NULL AUTO_INCREMENT,
                                                          `browser_token` varchar(255) DEFAULT NULL,
                                                          `created_at` datetime NOT NULL,
                                                          PRIMARY KEY (`id`),
                                                          KEY `browser_token` (`browser_token`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_campaign_campaign_subscription`;
CREATE TABLE `tipli_campaign_campaign_subscription` (
                                                        `id` int(11) NOT NULL AUTO_INCREMENT,
                                                        `user_id` int(11) DEFAULT NULL,
                                                        `utm_id` int(11) DEFAULT NULL,
                                                        `source` varchar(16) DEFAULT NULL,
                                                        `valid_since` datetime DEFAULT NULL,
                                                        `valid_till` datetime DEFAULT NULL,
                                                        `finished_at` datetime DEFAULT NULL,
                                                        `is_new_user` tinyint(1) NOT NULL,
                                                        `created_at` datetime NOT NULL,
                                                        PRIMARY KEY (`id`),
                                                        KEY `IDX_B0A4E7C6A76ED395` (`user_id`),
                                                        KEY `IDX_B0A4E7C6B6334822` (`utm_id`),
                                                        CONSTRAINT `FK_B0A4E7C6A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`),
                                                        CONSTRAINT `FK_B0A4E7C6B6334822` FOREIGN KEY (`utm_id`) REFERENCES `tipli_utm_utm` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_campaign_campaign_transaction`;
CREATE TABLE `tipli_campaign_campaign_transaction` (
                                                       `id` int(11) NOT NULL AUTO_INCREMENT,
                                                       `user_id` int(11) DEFAULT NULL,
                                                       `transaction_id` int(11) DEFAULT NULL,
                                                       `bonus_transaction_id` int(11) DEFAULT NULL,
                                                       `created_at` datetime NOT NULL,
                                                       PRIMARY KEY (`id`),
                                                       KEY `IDX_7C2C46C9A76ED395` (`user_id`),
                                                       KEY `IDX_7C2C46C92FC0CB0F` (`transaction_id`),
                                                       KEY `IDX_7C2C46C97E347B44` (`bonus_transaction_id`),
                                                       CONSTRAINT `FK_7C2C46C92FC0CB0F` FOREIGN KEY (`transaction_id`) REFERENCES `tipli_transactions_transaction` (`id`),
                                                       CONSTRAINT `FK_7C2C46C97E347B44` FOREIGN KEY (`bonus_transaction_id`) REFERENCES `tipli_transactions_transaction` (`id`),
                                                       CONSTRAINT `FK_7C2C46C9A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_collabim_keyword`;
CREATE TABLE `tipli_collabim_keyword` (
                                          `id` int(11) NOT NULL AUTO_INCREMENT,
                                          `keyword_id` int(11) NOT NULL,
                                          `keyword` varchar(255) NOT NULL,
                                          `project_id` int(11) NOT NULL,
                                          `searches_google_current` int(11) NOT NULL,
                                          `searches_seznam_current` int(11) DEFAULT NULL,
                                          `position_google_current` int(11) NOT NULL,
                                          `position_seznam_current` int(11) DEFAULT NULL,
                                          `tags` varchar(255) NOT NULL,
                                          `starred` tinyint(1) NOT NULL,
                                          `updated_at` datetime NOT NULL,
                                          `processed_at` datetime NOT NULL,
                                          `created_at` datetime NOT NULL,
                                          PRIMARY KEY (`id`),
                                          UNIQUE KEY `keyword_id_unique` (`keyword_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_collabim_position`;
CREATE TABLE `tipli_collabim_position` (
                                           `id` int(11) NOT NULL AUTO_INCREMENT,
                                           `keyword_id` int(11) DEFAULT NULL,
                                           `search_engine_id` int(11) NOT NULL,
                                           `date` datetime NOT NULL,
                                           `position` int(11) NOT NULL,
                                           `position_history` int(11) DEFAULT NULL,
                                           `created_at` datetime NOT NULL,
                                           PRIMARY KEY (`id`),
                                           UNIQUE KEY `keyword_date_unique` (`keyword_id`,`date`,`search_engine_id`),
                                           KEY `IDX_3B24AA0B115D4552` (`keyword_id`),
                                           CONSTRAINT `FK_3B24AA0B115D4552` FOREIGN KEY (`keyword_id`) REFERENCES `tipli_collabim_keyword` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_commands_log`;
CREATE TABLE `tipli_commands_log` (
                                      `id` int(11) NOT NULL AUTO_INCREMENT,
                                      `command` varchar(255) NOT NULL,
                                      `from_console` tinyint(1) NOT NULL,
                                      `note` varchar(255) DEFAULT NULL,
                                      `processed_at` datetime DEFAULT NULL,
                                      `finished_at` datetime DEFAULT NULL,
                                      `created_at` datetime NOT NULL,
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_competition_competition_deal`;
CREATE TABLE `tipli_competition_competition_deal` (
                                                      `id` int(11) NOT NULL AUTO_INCREMENT,
                                                      `localization_id` int(11) DEFAULT NULL,
                                                      `deal_id` int(11) DEFAULT NULL,
                                                      `shop_id` int(11) DEFAULT NULL,
                                                      `user_id` int(11) DEFAULT NULL,
                                                      `company_id` int(11) DEFAULT NULL,
                                                      `shop_name` varchar(255) NOT NULL,
                                                      `name` varchar(255) NOT NULL,
                                                      `description` longtext DEFAULT NULL,
                                                      `code` varchar(255) DEFAULT NULL,
                                                      `exclusive` tinyint(1) NOT NULL,
                                                      `reward` varchar(255) DEFAULT NULL,
                                                      `original_deal_id` varchar(255) NOT NULL,
                                                      `stated_valid_till` datetime DEFAULT NULL,
                                                      `last_seen_at` datetime DEFAULT NULL,
                                                      `is_on_web` tinyint(1) NOT NULL,
                                                      `listing_url` varchar(255) NOT NULL,
                                                      `detail_url` varchar(255) NOT NULL,
                                                      `state` varchar(255) NOT NULL,
                                                      `note` longtext DEFAULT NULL,
                                                      `removed_at` datetime DEFAULT NULL,
                                                      `updated_at` datetime NOT NULL,
                                                      `created_at` datetime NOT NULL,
                                                      PRIMARY KEY (`id`),
                                                      UNIQUE KEY `UNIQ_34F2554AF60E2305` (`deal_id`),
                                                      KEY `IDX_34F2554A6A2856C7` (`localization_id`),
                                                      KEY `IDX_34F2554A4D16C4DD` (`shop_id`),
                                                      KEY `IDX_34F2554AA76ED395` (`user_id`),
                                                      KEY `IDX_34F2554A979B1AD6` (`company_id`),
                                                      CONSTRAINT `FK_34F2554A4D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                                      CONSTRAINT `FK_34F2554A6A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                                      CONSTRAINT `FK_34F2554A979B1AD6` FOREIGN KEY (`company_id`) REFERENCES `tipli_competitors_company` (`id`),
                                                      CONSTRAINT `FK_34F2554AA76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`),
                                                      CONSTRAINT `FK_34F2554AF60E2305` FOREIGN KEY (`deal_id`) REFERENCES `tipli_deals_deal` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_competition_competition_deal_change`;
CREATE TABLE `tipli_competition_competition_deal_change` (
                                                             `id` int(11) NOT NULL AUTO_INCREMENT,
                                                             `competition_deal_id` int(11) DEFAULT NULL,
                                                             `type` varchar(255) NOT NULL,
                                                             `old_value` varchar(255) DEFAULT NULL,
                                                             `new_value` varchar(255) DEFAULT NULL,
                                                             `seen_at` datetime DEFAULT NULL,
                                                             `created_at` datetime NOT NULL,
                                                             PRIMARY KEY (`id`),
                                                             KEY `IDX_48E1C61BE9BBE3AB` (`competition_deal_id`),
                                                             CONSTRAINT `FK_48E1C61BE9BBE3AB` FOREIGN KEY (`competition_deal_id`) REFERENCES `tipli_competition_competition_deal` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_competition_competition_deal_log`;
CREATE TABLE `tipli_competition_competition_deal_log` (
                                                          `id` int(11) NOT NULL AUTO_INCREMENT,
                                                          `competition_deal_id` int(11) DEFAULT NULL,
                                                          `user_id` int(11) DEFAULT NULL,
                                                          `type` varchar(255) NOT NULL,
                                                          `created_at` datetime NOT NULL,
                                                          PRIMARY KEY (`id`),
                                                          KEY `IDX_5BB452B7E9BBE3AB` (`competition_deal_id`),
                                                          KEY `IDX_5BB452B7A76ED395` (`user_id`),
                                                          CONSTRAINT `FK_5BB452B7A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`),
                                                          CONSTRAINT `FK_5BB452B7E9BBE3AB` FOREIGN KEY (`competition_deal_id`) REFERENCES `tipli_competition_competition_deal` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_competitors_company`;
CREATE TABLE `tipli_competitors_company` (
                                             `id` int(11) NOT NULL AUTO_INCREMENT,
                                             `localization_id` int(11) DEFAULT NULL,
                                             `unique_id` varchar(255) NOT NULL,
                                             `name` varchar(255) NOT NULL,
                                             `track_rewards` tinyint(1) NOT NULL,
                                             `track_deals` tinyint(1) NOT NULL,
                                             `track_coupons` tinyint(1) NOT NULL,
                                             `track_leaflets` tinyint(1) NOT NULL,
                                             PRIMARY KEY (`id`),
                                             KEY `IDX_F6E280E96A2856C7` (`localization_id`),
                                             CONSTRAINT `FK_F6E280E96A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_competitors_offer`;
CREATE TABLE `tipli_competitors_offer` (
                                           `id` int(11) NOT NULL AUTO_INCREMENT,
                                           `company_id` int(11) DEFAULT NULL,
                                           `last_updated_by` int(11) DEFAULT NULL,
                                           `shop_id` int(11) DEFAULT NULL,
                                           `unique_id` varchar(255) NOT NULL,
                                           `shop_name` varchar(255) NOT NULL,
                                           `tipli_reward` decimal(10,2) DEFAULT NULL,
                                           `reward` decimal(10,2) DEFAULT NULL,
                                           `count_of_deals` int(11) DEFAULT NULL,
                                           `count_of_coupons` int(11) DEFAULT NULL,
                                           `count_of_leaflets` int(11) DEFAULT NULL,
                                           `reward_source_url` varchar(255) DEFAULT NULL,
                                           `deals_source_url` varchar(255) DEFAULT NULL,
                                           `coupons_source_url` varchar(255) DEFAULT NULL,
                                           `leaflets_source_url` varchar(255) DEFAULT NULL,
                                           `ignored` tinyint(1) NOT NULL,
                                           `note` varchar(255) DEFAULT NULL,
                                           `note_updated_at` datetime DEFAULT NULL,
                                           `invalidated_at` datetime DEFAULT NULL,
                                           `created_at` datetime NOT NULL,
                                           `updated_at` datetime NOT NULL,
                                           PRIMARY KEY (`id`),
                                           UNIQUE KEY `company_shop_unique` (`company_id`,`unique_id`),
                                           KEY `IDX_1A59391E979B1AD6` (`company_id`),
                                           KEY `IDX_1A59391EFF8A180B` (`last_updated_by`),
                                           KEY `IDX_1A59391E4D16C4DD` (`shop_id`),
                                           CONSTRAINT `FK_1A59391E4D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                           CONSTRAINT `FK_1A59391E979B1AD6` FOREIGN KEY (`company_id`) REFERENCES `tipli_competitors_company` (`id`),
                                           CONSTRAINT `FK_1A59391EFF8A180B` FOREIGN KEY (`last_updated_by`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_competitors_offer_note`;
CREATE TABLE `tipli_competitors_offer_note` (
                                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                                `offer_id` int(11) DEFAULT NULL,
                                                `user_id` int(11) DEFAULT NULL,
                                                `removed_by` int(11) DEFAULT NULL,
                                                `note` varchar(255) DEFAULT NULL,
                                                `removed_at` datetime DEFAULT NULL,
                                                `updated_at` datetime NOT NULL,
                                                `created_at` datetime NOT NULL,
                                                PRIMARY KEY (`id`),
                                                KEY `IDX_132BFB2353C674EE` (`offer_id`),
                                                KEY `IDX_132BFB23A76ED395` (`user_id`),
                                                KEY `IDX_132BFB2310CDAFDB` (`removed_by`),
                                                CONSTRAINT `FK_132BFB2310CDAFDB` FOREIGN KEY (`removed_by`) REFERENCES `tipli_account_user` (`id`),
                                                CONSTRAINT `FK_132BFB2353C674EE` FOREIGN KEY (`offer_id`) REFERENCES `tipli_competitors_offer` (`id`),
                                                CONSTRAINT `FK_132BFB23A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_competitors_report`;
CREATE TABLE `tipli_competitors_report` (
                                            `id` int(11) NOT NULL AUTO_INCREMENT,
                                            `localization_id` int(11) DEFAULT NULL,
                                            `company_id` int(11) DEFAULT NULL,
                                            `metric` varchar(255) NOT NULL,
                                            `value` int(11) DEFAULT NULL,
                                            `date` datetime NOT NULL,
                                            `created_at` datetime NOT NULL,
                                            PRIMARY KEY (`id`),
                                            KEY `IDX_FF72D8F26A2856C7` (`localization_id`),
                                            KEY `IDX_FF72D8F2979B1AD6` (`company_id`),
                                            CONSTRAINT `FK_FF72D8F26A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                            CONSTRAINT `FK_FF72D8F2979B1AD6` FOREIGN KEY (`company_id`) REFERENCES `tipli_competitors_company` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_conditions_approval`;
CREATE TABLE `tipli_conditions_approval` (
                                             `id` int(11) NOT NULL AUTO_INCREMENT,
                                             `user_id` int(11) DEFAULT NULL,
                                             `document_id` int(11) DEFAULT NULL,
                                             `ip` varchar(255) DEFAULT NULL,
                                             `user_agent` varchar(255) DEFAULT NULL,
                                             `created_at` datetime NOT NULL,
                                             PRIMARY KEY (`id`),
                                             KEY `IDX_C88FF132A76ED395` (`user_id`),
                                             KEY `IDX_C88FF132C33F7837` (`document_id`),
                                             CONSTRAINT `FK_C88FF132A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`),
                                             CONSTRAINT `FK_C88FF132C33F7837` FOREIGN KEY (`document_id`) REFERENCES `tipli_conditions_document` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_conditions_document`;
CREATE TABLE `tipli_conditions_document` (
                                             `id` int(11) NOT NULL AUTO_INCREMENT,
                                             `localization_id` int(11) DEFAULT NULL,
                                             `type` varchar(255) NOT NULL,
                                             `name` varchar(255) NOT NULL,
                                             `version` int(11) NOT NULL,
                                             `content` longtext DEFAULT NULL,
                                             `published_at` datetime NOT NULL,
                                             `created_at` datetime NOT NULL,
                                             PRIMARY KEY (`id`),
                                             KEY `IDX_606EE6F6A2856C7` (`localization_id`),
                                             CONSTRAINT `FK_606EE6F6A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_deals_competitive_deal`;
CREATE TABLE `tipli_deals_competitive_deal` (
                                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                                `localization_id` int(11) DEFAULT NULL,
                                                `raw_deal_id` int(11) DEFAULT NULL,
                                                `company_id` int(11) DEFAULT NULL,
                                                `shop_id` int(11) DEFAULT NULL,
                                                `company_deal_id` varchar(255) NOT NULL,
                                                `shop_name` varchar(255) DEFAULT NULL,
                                                `deep_url` varchar(255) DEFAULT NULL,
                                                `redirection_url` varchar(255) DEFAULT NULL,
                                                `detail_url` varchar(255) DEFAULT NULL,
                                                `name` varchar(255) DEFAULT NULL,
                                                `description` longtext DEFAULT NULL,
                                                `code` varchar(255) DEFAULT NULL,
                                                `exclusive` tinyint(1) NOT NULL,
                                                `valid_till` datetime DEFAULT NULL,
                                                `valid_till_string` varchar(255) NOT NULL,
                                                `crawled_at` datetime NOT NULL,
                                                `removed_at` datetime DEFAULT NULL,
                                                `created_at` datetime NOT NULL,
                                                PRIMARY KEY (`id`),
                                                UNIQUE KEY `UNIQ_B7AE2809804A869D` (`raw_deal_id`),
                                                UNIQUE KEY `competitor_deal_unique` (`localization_id`,`company_id`,`company_deal_id`),
                                                KEY `IDX_B7AE28096A2856C7` (`localization_id`),
                                                KEY `IDX_B7AE2809979B1AD6` (`company_id`),
                                                KEY `IDX_B7AE28094D16C4DD` (`shop_id`),
                                                CONSTRAINT `FK_B7AE28094D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                                CONSTRAINT `FK_B7AE28096A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                                CONSTRAINT `FK_B7AE2809804A869D` FOREIGN KEY (`raw_deal_id`) REFERENCES `tipli_deals_raw_deal` (`id`),
                                                CONSTRAINT `FK_B7AE2809979B1AD6` FOREIGN KEY (`company_id`) REFERENCES `tipli_competitors_company` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_deals_deal`;
CREATE TABLE `tipli_deals_deal` (
                                    `id` int(11) NOT NULL AUTO_INCREMENT,
                                    `localization_id` int(11) DEFAULT NULL,
                                    `page_extension_id` int(11) DEFAULT NULL,
                                    `image_id` int(11) DEFAULT NULL,
                                    `shop_id` int(11) DEFAULT NULL,
                                    `author_id` int(11) DEFAULT NULL,
                                    `confirmed_by` int(11) DEFAULT NULL,
                                    `rejected_by` int(11) DEFAULT NULL,
                                    `verified_by` int(11) DEFAULT NULL,
                                    `removed_by` int(11) DEFAULT NULL,
                                    `unique_id` varchar(255) DEFAULT NULL,
                                    `name` varchar(255) NOT NULL,
                                    `description` longtext DEFAULT NULL,
                                    `slug` varchar(255) NOT NULL,
                                    `type` varchar(255) DEFAULT NULL,
                                    `reward_label` varchar(255) DEFAULT NULL,
                                    `deep_url` longtext DEFAULT NULL,
                                    `free_shipping_minimal_order` double DEFAULT NULL,
                                    `free_shipping_minimal_order_currency` varchar(255) DEFAULT NULL,
                                    `free_shipping_carrier` varchar(255) DEFAULT NULL,
                                    `value` double DEFAULT NULL,
                                    `unit` varchar(16) DEFAULT NULL,
                                    `unknown_value` tinyint(1) NOT NULL,
                                    `custom_label` varchar(20) DEFAULT NULL,
                                    `code` varchar(255) DEFAULT NULL,
                                    `price` double DEFAULT NULL,
                                    `original_price` double DEFAULT NULL,
                                    `price_currency` varchar(255) DEFAULT NULL,
                                    `free_shipping` tinyint(1) DEFAULT NULL,
                                    `source_name` varchar(255) DEFAULT NULL,
                                    `source_type` varchar(255) DEFAULT NULL,
                                    `steve_id` int(11) DEFAULT NULL,
                                    `valid_since` datetime NOT NULL,
                                    `valid_till` datetime NOT NULL,
                                    `unknown_valid_till` tinyint(1) NOT NULL,
                                    `original_valid_since` datetime NOT NULL,
                                    `original_valid_till` datetime NOT NULL,
                                    `priority` int(11) NOT NULL,
                                    `level` smallint(6) NOT NULL,
                                    `trend_order` int(11) DEFAULT NULL,
                                    `topped_till` datetime DEFAULT NULL,
                                    `visible_on_web` tinyint(1) NOT NULL,
                                    `visible_on_addon` tinyint(1) NOT NULL,
                                    `exclusive` tinyint(1) NOT NULL,
                                    `brand_suffix_active` tinyint(1) NOT NULL,
                                    `removed_at` datetime DEFAULT NULL,
                                    `total_commission_amount` double NOT NULL,
                                    `count_of_redirections` int(11) NOT NULL,
                                    `count_of_transactions` int(11) NOT NULL,
                                    `ga_count_of_clicks` int(11) NOT NULL,
                                    `ga_count_of_views` int(11) NOT NULL,
                                    `ga_ctr` double NOT NULL,
                                    `image_flag` varchar(20) DEFAULT NULL,
                                    `seconds_spent_in_form` int(11) DEFAULT NULL,
                                    `update_index_at` datetime DEFAULT NULL,
                                    `promoted_at` datetime DEFAULT NULL,
                                    `created_at` datetime NOT NULL,
                                    PRIMARY KEY (`id`),
                                    UNIQUE KEY `UNIQ_98DB96F6B60E79A5` (`page_extension_id`),
                                    UNIQUE KEY `slug_unique` (`localization_id`,`slug`),
                                    KEY `IDX_98DB96F66A2856C7` (`localization_id`),
                                    KEY `IDX_98DB96F63DA5256D` (`image_id`),
                                    KEY `IDX_98DB96F64D16C4DD` (`shop_id`),
                                    KEY `IDX_98DB96F6F675F31B` (`author_id`),
                                    KEY `IDX_98DB96F6FB3F81CB` (`confirmed_by`),
                                    KEY `IDX_98DB96F67DC818F9` (`rejected_by`),
                                    KEY `IDX_98DB96F660D6A0BF` (`verified_by`),
                                    KEY `IDX_98DB96F610CDAFDB` (`removed_by`),
                                    KEY `type_idx` (`type`),
                                    KEY `valid_since_idx` (`valid_since`),
                                    KEY `valid_till_idx` (`valid_till`),
                                    CONSTRAINT `FK_98DB96F610CDAFDB` FOREIGN KEY (`removed_by`) REFERENCES `tipli_account_user` (`id`),
                                    CONSTRAINT `FK_98DB96F63DA5256D` FOREIGN KEY (`image_id`) REFERENCES `tipli_images_image` (`id`),
                                    CONSTRAINT `FK_98DB96F64D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                    CONSTRAINT `FK_98DB96F660D6A0BF` FOREIGN KEY (`verified_by`) REFERENCES `tipli_account_user` (`id`),
                                    CONSTRAINT `FK_98DB96F66A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                    CONSTRAINT `FK_98DB96F67DC818F9` FOREIGN KEY (`rejected_by`) REFERENCES `tipli_account_user` (`id`),
                                    CONSTRAINT `FK_98DB96F6B60E79A5` FOREIGN KEY (`page_extension_id`) REFERENCES `tipli_seo_page_extension` (`id`),
                                    CONSTRAINT `FK_98DB96F6F675F31B` FOREIGN KEY (`author_id`) REFERENCES `tipli_account_user` (`id`),
                                    CONSTRAINT `FK_98DB96F6FB3F81CB` FOREIGN KEY (`confirmed_by`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_deals_deal_alternative_shop`;
CREATE TABLE `tipli_deals_deal_alternative_shop` (
                                                     `id` int(11) NOT NULL AUTO_INCREMENT,
                                                     `deal_id` int(11) DEFAULT NULL,
                                                     `shop_id` int(11) DEFAULT NULL,
                                                     `priority` int(11) NOT NULL,
                                                     `created_at` datetime NOT NULL,
                                                     PRIMARY KEY (`id`),
                                                     UNIQUE KEY `deal_shop_unique` (`deal_id`,`shop_id`),
                                                     KEY `IDX_1B43B83FF60E2305` (`deal_id`),
                                                     KEY `IDX_1B43B83F4D16C4DD` (`shop_id`),
                                                     CONSTRAINT `FK_1B43B83F4D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                                     CONSTRAINT `FK_1B43B83FF60E2305` FOREIGN KEY (`deal_id`) REFERENCES `tipli_deals_deal` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_deals_deal_tag`;
CREATE TABLE `tipli_deals_deal_tag` (
                                        `id` int(11) NOT NULL AUTO_INCREMENT,
                                        `deal_id` int(11) DEFAULT NULL,
                                        `tag_id` int(11) DEFAULT NULL,
                                        `priority` int(11) DEFAULT NULL,
                                        `created_at` datetime NOT NULL,
                                        PRIMARY KEY (`id`),
                                        UNIQUE KEY `deal_tag_unique` (`deal_id`,`tag_id`),
                                        KEY `IDX_886924E2F60E2305` (`deal_id`),
                                        KEY `IDX_886924E2BAD26311` (`tag_id`),
                                        CONSTRAINT `FK_886924E2BAD26311` FOREIGN KEY (`tag_id`) REFERENCES `tipli_tags_tag` (`id`),
                                        CONSTRAINT `FK_886924E2F60E2305` FOREIGN KEY (`deal_id`) REFERENCES `tipli_deals_deal` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_deals_raw_deal`;
CREATE TABLE `tipli_deals_raw_deal` (
                                        `id` int(11) NOT NULL AUTO_INCREMENT,
                                        `deal_id` int(11) DEFAULT NULL,
                                        `localization_id` int(11) DEFAULT NULL,
                                        `shop_id` int(11) DEFAULT NULL,
                                        `author_id` int(11) DEFAULT NULL,
                                        `deep_url` longtext DEFAULT NULL,
                                        `name` varchar(255) DEFAULT NULL,
                                        `description` longtext DEFAULT NULL,
                                        `code` varchar(255) DEFAULT NULL,
                                        `email_subject` varchar(255) DEFAULT NULL,
                                        `email_body` longtext DEFAULT NULL,
                                        `email_from` varchar(255) DEFAULT NULL,
                                        `source` varchar(255) DEFAULT NULL,
                                        `valid_till` datetime DEFAULT NULL,
                                        `removed_at` datetime DEFAULT NULL,
                                        `created_at` datetime NOT NULL,
                                        PRIMARY KEY (`id`),
                                        KEY `IDX_58E074E2F60E2305` (`deal_id`),
                                        KEY `IDX_58E074E26A2856C7` (`localization_id`),
                                        KEY `IDX_58E074E24D16C4DD` (`shop_id`),
                                        KEY `IDX_58E074E2F675F31B` (`author_id`),
                                        CONSTRAINT `FK_58E074E24D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                        CONSTRAINT `FK_58E074E26A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                        CONSTRAINT `FK_58E074E2F60E2305` FOREIGN KEY (`deal_id`) REFERENCES `tipli_deals_deal` (`id`),
                                        CONSTRAINT `FK_58E074E2F675F31B` FOREIGN KEY (`author_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_deals_vote`;
CREATE TABLE `tipli_deals_vote` (
                                    `id` int(11) NOT NULL AUTO_INCREMENT,
                                    `deal_id` int(11) DEFAULT NULL,
                                    `user_id` int(11) DEFAULT NULL,
                                    `value` smallint(6) NOT NULL,
                                    `created_at` datetime NOT NULL,
                                    PRIMARY KEY (`id`),
                                    UNIQUE KEY `slug_unique` (`user_id`,`deal_id`),
                                    KEY `IDX_2135D284F60E2305` (`deal_id`),
                                    KEY `IDX_2135D284A76ED395` (`user_id`),
                                    CONSTRAINT `FK_2135D284A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`),
                                    CONSTRAINT `FK_2135D284F60E2305` FOREIGN KEY (`deal_id`) REFERENCES `tipli_deals_deal` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_files_file`;
CREATE TABLE `tipli_files_file` (
                                    `id` int(11) NOT NULL AUTO_INCREMENT,
                                    `identifier` varchar(255) NOT NULL,
                                    `storage_type` varchar(255) NOT NULL,
                                    `namespace` varchar(255) NOT NULL,
                                    `deleted` tinyint(1) NOT NULL,
                                    `created_at` datetime NOT NULL,
                                    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_freshdesk_feedback`;
CREATE TABLE `tipli_freshdesk_feedback` (
                                            `id` int(11) NOT NULL AUTO_INCREMENT,
                                            `ticket_id` int(11) DEFAULT NULL,
                                            `rating` varchar(2) NOT NULL,
                                            `message` longtext DEFAULT NULL,
                                            `token` varchar(255) NOT NULL,
                                            `created_at` datetime NOT NULL,
                                            PRIMARY KEY (`id`),
                                            KEY `IDX_1C347F7A700047D2` (`ticket_id`),
                                            CONSTRAINT `FK_1C347F7A700047D2` FOREIGN KEY (`ticket_id`) REFERENCES `tipli_freshdesk_ticket` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_freshdesk_message`;
CREATE TABLE `tipli_freshdesk_message` (
                                           `id` int(11) NOT NULL AUTO_INCREMENT,
                                           `ticket_id` int(11) DEFAULT NULL,
                                           `user_id` int(11) DEFAULT NULL,
                                           `request_message_id` int(11) DEFAULT NULL,
                                           `responder_name` varchar(255) DEFAULT NULL,
                                           `message_id` bigint(20) DEFAULT NULL,
                                           `message_length` int(11) NOT NULL,
                                           `response_time` int(11) DEFAULT NULL,
                                           `delay_time` int(11) DEFAULT NULL,
                                           `body` longtext DEFAULT NULL,
                                           `is_incoming` tinyint(1) NOT NULL,
                                           `is_automated` tinyint(1) DEFAULT NULL,
                                           `is_first_response` tinyint(1) NOT NULL,
                                           `synchronized_at` datetime DEFAULT NULL,
                                           `message_created_at` datetime NOT NULL,
                                           `created_at` datetime NOT NULL,
                                           PRIMARY KEY (`id`),
                                           KEY `IDX_CA628D08700047D2` (`ticket_id`),
                                           KEY `IDX_CA628D08A76ED395` (`user_id`),
                                           KEY `IDX_CA628D081B83D493` (`request_message_id`),
                                           CONSTRAINT `FK_CA628D081B83D493` FOREIGN KEY (`request_message_id`) REFERENCES `tipli_freshdesk_message` (`id`),
                                           CONSTRAINT `FK_CA628D08700047D2` FOREIGN KEY (`ticket_id`) REFERENCES `tipli_freshdesk_ticket` (`id`),
                                           CONSTRAINT `FK_CA628D08A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_freshdesk_request`;
CREATE TABLE `tipli_freshdesk_request` (
                                           `id` int(11) NOT NULL AUTO_INCREMENT,
                                           `ticket_id` int(11) DEFAULT NULL,
                                           `action` varchar(255) NOT NULL,
                                           `state` varchar(255) NOT NULL,
                                           `body` longtext NOT NULL,
                                           `error_message` varchar(1000) DEFAULT NULL,
                                           `count_of_failed_attempts` int(11) NOT NULL,
                                           `failed_at` datetime DEFAULT NULL,
                                           `process_at` datetime DEFAULT NULL,
                                           `processed_at` datetime DEFAULT NULL,
                                           `created_at` datetime NOT NULL,
                                           PRIMARY KEY (`id`),
                                           KEY `IDX_474832E8700047D2` (`ticket_id`),
                                           CONSTRAINT `FK_474832E8700047D2` FOREIGN KEY (`ticket_id`) REFERENCES `tipli_freshdesk_ticket` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_freshdesk_ticket`;
CREATE TABLE `tipli_freshdesk_ticket` (
                                          `id` int(11) NOT NULL AUTO_INCREMENT,
                                          `localization_id` int(11) DEFAULT NULL,
                                          `user_id` int(11) DEFAULT NULL,
                                          `resolved_by_id` int(11) DEFAULT NULL,
                                          `first_response_agent_id` int(11) DEFAULT NULL,
                                          `ticket_id` int(11) DEFAULT NULL,
                                          `email` varchar(255) DEFAULT NULL,
                                          `subject` varchar(255) DEFAULT NULL,
                                          `type` varchar(255) DEFAULT NULL,
                                          `tags` varchar(255) DEFAULT NULL,
                                          `closed_at` datetime DEFAULT NULL,
                                          `resolved_at` datetime DEFAULT NULL,
                                          `opened_at` datetime NOT NULL,
                                          `reopened_at` datetime DEFAULT NULL,
                                          `synchronized_at` datetime DEFAULT NULL,
                                          `synchronize_at` datetime DEFAULT NULL,
                                          `messages_synchronized_at` datetime DEFAULT NULL,
                                          `conversation_removed_at` datetime DEFAULT NULL,
                                          `conversation_remove_at` datetime DEFAULT NULL,
                                          `first_response_at` datetime DEFAULT NULL,
                                          `first_response_agent_name` varchar(255) DEFAULT NULL,
                                          `resolved_by_name` varchar(255) DEFAULT NULL,
                                          `created_at` datetime NOT NULL,
                                          PRIMARY KEY (`id`),
                                          KEY `IDX_94F26D6D6A2856C7` (`localization_id`),
                                          KEY `IDX_94F26D6DA76ED395` (`user_id`),
                                          KEY `IDX_94F26D6D6713A32B` (`resolved_by_id`),
                                          KEY `IDX_94F26D6D61879F17` (`first_response_agent_id`),
                                          KEY `ticket_id_idx` (`ticket_id`),
                                          CONSTRAINT `FK_94F26D6D61879F17` FOREIGN KEY (`first_response_agent_id`) REFERENCES `tipli_account_user` (`id`),
                                          CONSTRAINT `FK_94F26D6D6713A32B` FOREIGN KEY (`resolved_by_id`) REFERENCES `tipli_account_user` (`id`),
                                          CONSTRAINT `FK_94F26D6D6A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                          CONSTRAINT `FK_94F26D6DA76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_funnels_funnel`;
CREATE TABLE `tipli_funnels_funnel` (
                                        `id` int(11) NOT NULL AUTO_INCREMENT,
                                        `user_id` int(11) DEFAULT NULL,
                                        `assigned_by_user_id` int(11) DEFAULT NULL,
                                        `start_state` varchar(255) NOT NULL,
                                        `start_state_entered_at` datetime NOT NULL,
                                        `final_state` varchar(255) DEFAULT NULL,
                                        `final_state_entered_at` datetime DEFAULT NULL,
                                        `trigger_name` varchar(32) NOT NULL,
                                        `created_at` datetime NOT NULL,
                                        PRIMARY KEY (`id`),
                                        KEY `IDX_6AF4CF16A76ED395` (`user_id`),
                                        KEY `IDX_6AF4CF16E43D4745` (`assigned_by_user_id`),
                                        CONSTRAINT `FK_6AF4CF16A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`),
                                        CONSTRAINT `FK_6AF4CF16E43D4745` FOREIGN KEY (`assigned_by_user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_groups_group`;
CREATE TABLE `tipli_groups_group` (
                                      `id` int(11) NOT NULL AUTO_INCREMENT,
                                      `localization_id` int(11) DEFAULT NULL,
                                      `author_id` int(11) DEFAULT NULL,
                                      `name` varchar(255) NOT NULL,
                                      `slug` varchar(255) NOT NULL,
                                      `transaction_shops_ids` varchar(255) DEFAULT NULL,
                                      `redirection_shops_ids` varchar(255) DEFAULT NULL,
                                      `segment` varchar(8) DEFAULT NULL,
                                      `addon_installed` tinyint(1) DEFAULT NULL,
                                      `utm_source` varchar(255) DEFAULT NULL,
                                      `utm_medium` varchar(255) DEFAULT NULL,
                                      `days_from_registration` varchar(255) DEFAULT NULL,
                                      `days_from_last_transaction` varchar(255) DEFAULT NULL,
                                      `number_of_purchases` varchar(255) DEFAULT NULL,
                                      `count_of_users` int(11) NOT NULL,
                                      `even_odd_user_id` varchar(255) DEFAULT NULL,
                                      `completed_at` datetime DEFAULT NULL,
                                      `process_at` datetime DEFAULT NULL,
                                      `process_time_shift` varchar(255) DEFAULT NULL,
                                      `removed_at` datetime DEFAULT NULL,
                                      `created_at` datetime NOT NULL,
                                      PRIMARY KEY (`id`),
                                      KEY `IDX_24E277ED6A2856C7` (`localization_id`),
                                      KEY `IDX_24E277EDF675F31B` (`author_id`),
                                      CONSTRAINT `FK_24E277ED6A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                      CONSTRAINT `FK_24E277EDF675F31B` FOREIGN KEY (`author_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_groups_group_user`;
CREATE TABLE `tipli_groups_group_user` (
                                           `group_id` int(11) NOT NULL,
                                           `user_id` int(11) NOT NULL,
                                           PRIMARY KEY (`group_id`,`user_id`),
                                           KEY `IDX_AD94D871FE54D947` (`group_id`),
                                           KEY `IDX_AD94D871A76ED395` (`user_id`),
                                           CONSTRAINT `FK_AD94D871A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`) ON DELETE CASCADE,
                                           CONSTRAINT `FK_AD94D871FE54D947` FOREIGN KEY (`group_id`) REFERENCES `tipli_groups_group` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_groups_group_utm`;
CREATE TABLE `tipli_groups_group_utm` (
                                          `group_id` int(11) NOT NULL,
                                          `utm_id` int(11) NOT NULL,
                                          PRIMARY KEY (`group_id`,`utm_id`),
                                          KEY `IDX_BDA059CBFE54D947` (`group_id`),
                                          KEY `IDX_BDA059CBB6334822` (`utm_id`),
                                          CONSTRAINT `FK_BDA059CBB6334822` FOREIGN KEY (`utm_id`) REFERENCES `tipli_utm_utm` (`id`) ON DELETE CASCADE,
                                          CONSTRAINT `FK_BDA059CBFE54D947` FOREIGN KEY (`group_id`) REFERENCES `tipli_groups_group` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_home_credit_transaction`;
CREATE TABLE `tipli_home_credit_transaction` (
                                                 `id` int(11) NOT NULL AUTO_INCREMENT,
                                                 `transaction_id` int(11) DEFAULT NULL,
                                                 `redirection_id` int(11) DEFAULT NULL,
                                                 `sub_id` varchar(255) NOT NULL,
                                                 PRIMARY KEY (`id`),
                                                 UNIQUE KEY `transaction_id` (`transaction_id`),
                                                 KEY `IDX_13ACCF4E1DC0789A` (`redirection_id`),
                                                 CONSTRAINT `FK_13ACCF4E1DC0789A` FOREIGN KEY (`redirection_id`) REFERENCES `tipli_shops_redirection` (`id`),
                                                 CONSTRAINT `FK_13ACCF4E2FC0CB0F` FOREIGN KEY (`transaction_id`) REFERENCES `tipli_transactions_transaction` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_images_image`;
CREATE TABLE `tipli_images_image` (
                                      `id` int(11) NOT NULL AUTO_INCREMENT,
                                      `identifier` varchar(255) NOT NULL,
                                      `storage_type` varchar(255) NOT NULL,
                                      `namespace` varchar(255) NOT NULL,
                                      `compressed` tinyint(1) NOT NULL,
                                      `deleted` tinyint(1) NOT NULL,
                                      `created_at` datetime NOT NULL,
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_inbox_notification`;
CREATE TABLE `tipli_inbox_notification` (
                                            `id` int(11) NOT NULL AUTO_INCREMENT,
                                            `notification_campaign_id` int(11) DEFAULT NULL,
                                            `body_id` int(11) DEFAULT NULL,
                                            `user_id` int(11) DEFAULT NULL,
                                            `type` varchar(255) DEFAULT NULL,
                                            `priority` int(11) NOT NULL,
                                            `scheduled_at` datetime NOT NULL,
                                            `push_scheduled_at` datetime DEFAULT NULL,
                                            `pushed_at` datetime DEFAULT NULL,
                                            `push_failed_at` datetime DEFAULT NULL,
                                            `opened_at` datetime DEFAULT NULL,
                                            `mobile_opened_at` datetime DEFAULT NULL,
                                            `clicked_at` datetime DEFAULT NULL,
                                            `mobile_clicked_at` datetime DEFAULT NULL,
                                            `created_at` datetime NOT NULL,
                                            PRIMARY KEY (`id`),
                                            UNIQUE KEY `user_campaign_unique` (`user_id`,`notification_campaign_id`),
                                            KEY `IDX_3239FF5E23A2C435` (`notification_campaign_id`),
                                            KEY `IDX_3239FF5E9B621D84` (`body_id`),
                                            KEY `IDX_3239FF5EA76ED395` (`user_id`),
                                            KEY `scheduled_atx` (`scheduled_at`),
                                            CONSTRAINT `FK_3239FF5E23A2C435` FOREIGN KEY (`notification_campaign_id`) REFERENCES `tipli_inbox_notification_campaign` (`id`),
                                            CONSTRAINT `FK_3239FF5E9B621D84` FOREIGN KEY (`body_id`) REFERENCES `tipli_inbox_notification_body` (`id`),
                                            CONSTRAINT `FK_3239FF5EA76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_inbox_notification_body`;
CREATE TABLE `tipli_inbox_notification_body` (
                                                 `id` int(11) NOT NULL AUTO_INCREMENT,
                                                 `image_id` int(11) DEFAULT NULL,
                                                 `title` varchar(255) NOT NULL,
                                                 `content` longtext NOT NULL,
                                                 `created_at` datetime NOT NULL,
                                                 PRIMARY KEY (`id`),
                                                 KEY `IDX_1961869C3DA5256D` (`image_id`),
                                                 CONSTRAINT `FK_1961869C3DA5256D` FOREIGN KEY (`image_id`) REFERENCES `tipli_images_image` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_inbox_notification_campaign`;
CREATE TABLE `tipli_inbox_notification_campaign` (
                                                     `id` int(11) NOT NULL AUTO_INCREMENT,
                                                     `localization_id` int(11) DEFAULT NULL,
                                                     `group_id` int(11) DEFAULT NULL,
                                                     `money_reward_campaign_id` int(11) DEFAULT NULL,
                                                     `body_id` int(11) DEFAULT NULL,
                                                     `name` varchar(255) NOT NULL,
                                                     `note` longtext DEFAULT NULL,
                                                     `js` varchar(255) DEFAULT NULL,
                                                     `url` varchar(255) DEFAULT NULL,
                                                     `push_allowed` tinyint(1) DEFAULT NULL,
                                                     `recipients_count` int(11) NOT NULL,
                                                     `pivot` int(11) DEFAULT NULL,
                                                     `scheduled_at` datetime NOT NULL,
                                                     `processed_at` datetime DEFAULT NULL,
                                                     `valid_till` datetime DEFAULT NULL,
                                                     `created_at` datetime NOT NULL,
                                                     PRIMARY KEY (`id`),
                                                     KEY `IDX_D9478D986A2856C7` (`localization_id`),
                                                     KEY `IDX_D9478D98FE54D947` (`group_id`),
                                                     KEY `IDX_D9478D98F9BFC74A` (`money_reward_campaign_id`),
                                                     KEY `IDX_D9478D989B621D84` (`body_id`),
                                                     CONSTRAINT `FK_D9478D986A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                                     CONSTRAINT `FK_D9478D989B621D84` FOREIGN KEY (`body_id`) REFERENCES `tipli_inbox_notification_body` (`id`),
                                                     CONSTRAINT `FK_D9478D98F9BFC74A` FOREIGN KEY (`money_reward_campaign_id`) REFERENCES `tipli_rewards_money_reward_campaign` (`id`),
                                                     CONSTRAINT `FK_D9478D98FE54D947` FOREIGN KEY (`group_id`) REFERENCES `tipli_groups_group` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_jobs_job`;
CREATE TABLE `tipli_jobs_job` (
                                  `id` int(11) NOT NULL AUTO_INCREMENT,
                                  `localization_id` int(11) DEFAULT NULL,
                                  `preview_image_id` int(11) DEFAULT NULL,
                                  `name` varchar(255) NOT NULL,
                                  `slug` varchar(255) NOT NULL,
                                  `content` longtext DEFAULT NULL,
                                  `valid_from` datetime DEFAULT NULL,
                                  `valid_to` datetime DEFAULT NULL,
                                  `type` varchar(255) NOT NULL,
                                  `priority` int(11) NOT NULL,
                                  `contact_email` varchar(255) DEFAULT NULL,
                                  `is_hidden` tinyint(1) NOT NULL,
                                  `removed_at` datetime DEFAULT NULL,
                                  `updated_at` datetime NOT NULL,
                                  `created_at` datetime NOT NULL,
                                  PRIMARY KEY (`id`),
                                  UNIQUE KEY `slug_unique` (`localization_id`,`slug`),
                                  KEY `IDX_5EF23166A2856C7` (`localization_id`),
                                  KEY `IDX_5EF2316FAE957CD` (`preview_image_id`),
                                  CONSTRAINT `FK_5EF23166A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                  CONSTRAINT `FK_5EF2316FAE957CD` FOREIGN KEY (`preview_image_id`) REFERENCES `tipli_images_image` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_landing_pages_landing_page`;
CREATE TABLE `tipli_landing_pages_landing_page` (
                                                    `id` int(11) NOT NULL AUTO_INCREMENT,
                                                    `localization_id` int(11) DEFAULT NULL,
                                                    `tag_id` int(11) DEFAULT NULL,
                                                    `cover_id` int(11) DEFAULT NULL,
                                                    `name` varchar(255) DEFAULT NULL,
                                                    `slug` varchar(255) DEFAULT NULL,
                                                    `heading` varchar(255) DEFAULT NULL,
                                                    `description` longtext DEFAULT NULL,
                                                    `valid_till` datetime NOT NULL,
                                                    `published_at` datetime NOT NULL,
                                                    `removed_at` datetime DEFAULT NULL,
                                                    `created_at` datetime NOT NULL,
                                                    PRIMARY KEY (`id`),
                                                    KEY `IDX_441810566A2856C7` (`localization_id`),
                                                    KEY `IDX_44181056BAD26311` (`tag_id`),
                                                    KEY `IDX_44181056922726E9` (`cover_id`),
                                                    CONSTRAINT `FK_441810566A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                                    CONSTRAINT `FK_44181056922726E9` FOREIGN KEY (`cover_id`) REFERENCES `tipli_images_image` (`id`),
                                                    CONSTRAINT `FK_44181056BAD26311` FOREIGN KEY (`tag_id`) REFERENCES `tipli_tags_tag` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_landing_pages_module`;
CREATE TABLE `tipli_landing_pages_module` (
                                              `id` int(11) NOT NULL AUTO_INCREMENT,
                                              `landing_page_id` int(11) DEFAULT NULL,
                                              `tag_id` int(11) DEFAULT NULL,
                                              `title` varchar(255) DEFAULT NULL,
                                              `description` longtext DEFAULT NULL,
                                              `type` varchar(255) NOT NULL,
                                              `priority` int(11) NOT NULL,
                                              `deal_vertical_layout` tinyint(1) NOT NULL,
                                              `limit_of_listed_items` int(11) DEFAULT NULL,
                                              `valid_since` datetime DEFAULT NULL,
                                              `valid_till` datetime DEFAULT NULL,
                                              `created_at` datetime NOT NULL,
                                              PRIMARY KEY (`id`),
                                              KEY `IDX_B1162222DF122DC5` (`landing_page_id`),
                                              KEY `IDX_B1162222BAD26311` (`tag_id`),
                                              CONSTRAINT `FK_B1162222BAD26311` FOREIGN KEY (`tag_id`) REFERENCES `tipli_tags_tag` (`id`),
                                              CONSTRAINT `FK_B1162222DF122DC5` FOREIGN KEY (`landing_page_id`) REFERENCES `tipli_landing_pages_landing_page` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_landing_pages_module_banner`;
CREATE TABLE `tipli_landing_pages_module_banner` (
                                                     `module_id` int(11) NOT NULL,
                                                     `banner_id` int(11) NOT NULL,
                                                     PRIMARY KEY (`module_id`,`banner_id`),
                                                     KEY `IDX_CE869831AFC2B591` (`module_id`),
                                                     KEY `IDX_CE869831684EC833` (`banner_id`),
                                                     CONSTRAINT `FK_CE869831684EC833` FOREIGN KEY (`banner_id`) REFERENCES `tipli_marketing_banner` (`id`) ON DELETE CASCADE,
                                                     CONSTRAINT `FK_CE869831AFC2B591` FOREIGN KEY (`module_id`) REFERENCES `tipli_landing_pages_module` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_landing_pages_module_deal`;
CREATE TABLE `tipli_landing_pages_module_deal` (
                                                   `module_id` int(11) NOT NULL,
                                                   `deal_id` int(11) NOT NULL,
                                                   PRIMARY KEY (`module_id`,`deal_id`),
                                                   KEY `IDX_EA94EAE7AFC2B591` (`module_id`),
                                                   KEY `IDX_EA94EAE7F60E2305` (`deal_id`),
                                                   CONSTRAINT `FK_EA94EAE7AFC2B591` FOREIGN KEY (`module_id`) REFERENCES `tipli_landing_pages_module` (`id`) ON DELETE CASCADE,
                                                   CONSTRAINT `FK_EA94EAE7F60E2305` FOREIGN KEY (`deal_id`) REFERENCES `tipli_deals_deal` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_landing_pages_module_shop`;
CREATE TABLE `tipli_landing_pages_module_shop` (
                                                   `module_id` int(11) NOT NULL,
                                                   `shop_id` int(11) NOT NULL,
                                                   PRIMARY KEY (`module_id`,`shop_id`),
                                                   KEY `IDX_A5006753AFC2B591` (`module_id`),
                                                   KEY `IDX_A50067534D16C4DD` (`shop_id`),
                                                   CONSTRAINT `FK_A50067534D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`) ON DELETE CASCADE,
                                                   CONSTRAINT `FK_A5006753AFC2B591` FOREIGN KEY (`module_id`) REFERENCES `tipli_landing_pages_module` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_leaflets_leaflet`;
CREATE TABLE `tipli_leaflets_leaflet` (
                                          `id` int(11) NOT NULL AUTO_INCREMENT,
                                          `localization_id` int(11) DEFAULT NULL,
                                          `page_extension_id` int(11) DEFAULT NULL,
                                          `shop_id` int(11) DEFAULT NULL,
                                          `leaflet_id` varchar(255) NOT NULL,
                                          `slug` varchar(255) NOT NULL,
                                          `title` varchar(255) NOT NULL,
                                          `description` varchar(500) DEFAULT NULL,
                                          `note` varchar(255) DEFAULT NULL,
                                          `type` varchar(255) DEFAULT NULL,
                                          `is_primary` tinyint(1) NOT NULL,
                                          `shop_domain` varchar(255) DEFAULT NULL,
                                          `partner_link` varchar(255) DEFAULT NULL,
                                          `valid_since` datetime NOT NULL,
                                          `valid_till` datetime NOT NULL,
                                          `confirmed_at` datetime DEFAULT NULL,
                                          `cancelled_at` datetime DEFAULT NULL,
                                          `removed_at` datetime DEFAULT NULL,
                                          `remove_at` datetime DEFAULT NULL,
                                          `archive_at` datetime DEFAULT NULL,
                                          `archived_at` datetime DEFAULT NULL,
                                          `archivation_pages` int(11) DEFAULT NULL,
                                          `count_of_pages` int(11) DEFAULT NULL,
                                          `visible` tinyint(1) NOT NULL,
                                          `created_at` datetime NOT NULL,
                                          PRIMARY KEY (`id`),
                                          UNIQUE KEY `UNIQ_57254DAFB60E79A5` (`page_extension_id`),
                                          KEY `IDX_57254DAF6A2856C7` (`localization_id`),
                                          KEY `IDX_57254DAF4D16C4DD` (`shop_id`),
                                          KEY `slug_idx` (`slug`),
                                          CONSTRAINT `FK_57254DAF4D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                          CONSTRAINT `FK_57254DAF6A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                          CONSTRAINT `FK_57254DAFB60E79A5` FOREIGN KEY (`page_extension_id`) REFERENCES `tipli_seo_page_extension` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_leaflets_leaflet_page`;
CREATE TABLE `tipli_leaflets_leaflet_page` (
                                               `id` int(11) NOT NULL AUTO_INCREMENT,
                                               `leaflet_id` int(11) DEFAULT NULL,
                                               `image_id` int(11) DEFAULT NULL,
                                               `page_number` int(11) NOT NULL,
                                               `download_url` varchar(255) DEFAULT NULL,
                                               `created_at` datetime NOT NULL,
                                               PRIMARY KEY (`id`),
                                               KEY `IDX_195A0104993B8D6C` (`leaflet_id`),
                                               KEY `IDX_195A01043DA5256D` (`image_id`),
                                               CONSTRAINT `FK_195A01043DA5256D` FOREIGN KEY (`image_id`) REFERENCES `tipli_images_image` (`id`),
                                               CONSTRAINT `FK_195A0104993B8D6C` FOREIGN KEY (`leaflet_id`) REFERENCES `tipli_leaflets_leaflet` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_leaflets_leaflet_store`;
CREATE TABLE `tipli_leaflets_leaflet_store` (
                                                `leaflet_id` int(11) NOT NULL,
                                                `store_id` int(11) NOT NULL,
                                                PRIMARY KEY (`leaflet_id`,`store_id`),
                                                KEY `IDX_C359EC11993B8D6C` (`leaflet_id`),
                                                KEY `IDX_C359EC11B092A811` (`store_id`),
                                                CONSTRAINT `FK_C359EC11993B8D6C` FOREIGN KEY (`leaflet_id`) REFERENCES `tipli_leaflets_leaflet` (`id`) ON DELETE CASCADE,
                                                CONSTRAINT `FK_C359EC11B092A811` FOREIGN KEY (`store_id`) REFERENCES `tipli_shops_store` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_leaflets_leaflet_tag`;
CREATE TABLE `tipli_leaflets_leaflet_tag` (
                                              `leaflet_id` int(11) NOT NULL,
                                              `tag_id` int(11) NOT NULL,
                                              PRIMARY KEY (`leaflet_id`,`tag_id`),
                                              KEY `IDX_3E54AD09993B8D6C` (`leaflet_id`),
                                              KEY `IDX_3E54AD09BAD26311` (`tag_id`),
                                              CONSTRAINT `FK_3E54AD09993B8D6C` FOREIGN KEY (`leaflet_id`) REFERENCES `tipli_leaflets_leaflet` (`id`) ON DELETE CASCADE,
                                              CONSTRAINT `FK_3E54AD09BAD26311` FOREIGN KEY (`tag_id`) REFERENCES `tipli_tags_tag` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_leaflets_shop_index`;
CREATE TABLE `tipli_leaflets_shop_index` (
                                             `id` int(11) NOT NULL AUTO_INCREMENT,
                                             `shop_id` int(11) DEFAULT NULL,
                                             `created_at` datetime NOT NULL,
                                             `localization_id` int(11) DEFAULT NULL,
                                             PRIMARY KEY (`id`),
                                             KEY `IDX_64E88A0A6A2856C7` (`localization_id`),
                                             KEY `IDX_64E88A0A4D16C4DD` (`shop_id`),
                                             CONSTRAINT `FK_64E88A0A4D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                             CONSTRAINT `FK_64E88A0A6A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_localization_localization`;
CREATE TABLE `tipli_localization_localization` (
                                                   `id` int(11) NOT NULL AUTO_INCREMENT,
                                                   `name` varchar(255) NOT NULL,
                                                   `locale` varchar(255) NOT NULL,
                                                   `currency` varchar(3) NOT NULL,
                                                   PRIMARY KEY (`id`),
                                                   UNIQUE KEY `UNIQ_4CB013CD4180C698` (`locale`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_log_bad_request`;
CREATE TABLE `tipli_log_bad_request` (
                                         `id` int(11) NOT NULL AUTO_INCREMENT,
                                         `localization_id` int(11) DEFAULT NULL,
                                         `url` varchar(255) NOT NULL,
                                         `user_agent` varchar(255) DEFAULT NULL,
                                         `referer` varchar(255) DEFAULT NULL,
                                         `ip` varchar(255) DEFAULT NULL,
                                         `error_code` int(11) NOT NULL,
                                         `created_at` datetime NOT NULL,
                                         PRIMARY KEY (`id`),
                                         KEY `IDX_869625CA6A2856C7` (`localization_id`),
                                         CONSTRAINT `FK_869625CA6A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_log_change`;
CREATE TABLE `tipli_log_change` (
                                    `id` int(11) NOT NULL AUTO_INCREMENT,
                                    `user_id` int(11) DEFAULT NULL,
                                    `entity_type` varchar(255) NOT NULL,
                                    `entity_id` int(11) NOT NULL,
                                    `changed_columns` varchar(255) NOT NULL,
                                    `created_at` datetime NOT NULL,
                                    PRIMARY KEY (`id`),
                                    KEY `IDX_451C8C28A76ED395` (`user_id`),
                                    CONSTRAINT `FK_451C8C28A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_log_event`;
CREATE TABLE `tipli_log_event` (
                                   `id` int(11) NOT NULL AUTO_INCREMENT,
                                   `partner_system_id` int(11) DEFAULT NULL,
                                   `created_by_user_id` int(11) DEFAULT NULL,
                                   `action` varchar(25) NOT NULL,
                                   `created_at` datetime NOT NULL,
                                   PRIMARY KEY (`id`),
                                   KEY `IDX_708B1160FB42B4B1` (`partner_system_id`),
                                   KEY `IDX_708B11607D182D95` (`created_by_user_id`),
                                   KEY `action_idx` (`action`),
                                   CONSTRAINT `FK_708B11607D182D95` FOREIGN KEY (`created_by_user_id`) REFERENCES `tipli_account_user` (`id`),
                                   CONSTRAINT `FK_708B1160FB42B4B1` FOREIGN KEY (`partner_system_id`) REFERENCES `tipli_partner_systems_partner_system` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_log_forgotten_password_request`;
CREATE TABLE `tipli_log_forgotten_password_request` (
                                                        `id` int(11) NOT NULL AUTO_INCREMENT,
                                                        `localization_id` int(11) DEFAULT NULL,
                                                        `user_id` int(11) DEFAULT NULL,
                                                        `email` varchar(255) NOT NULL,
                                                        `ip` varchar(255) NOT NULL,
                                                        `country` varchar(2) DEFAULT NULL,
                                                        `created_at` datetime NOT NULL,
                                                        PRIMARY KEY (`id`),
                                                        KEY `IDX_E27A5ABA6A2856C7` (`localization_id`),
                                                        KEY `IDX_E27A5ABAA76ED395` (`user_id`),
                                                        CONSTRAINT `FK_E27A5ABA6A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                                        CONSTRAINT `FK_E27A5ABAA76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_log_googlebot`;
CREATE TABLE `tipli_log_googlebot` (
                                       `id` int(11) NOT NULL AUTO_INCREMENT,
                                       `date` datetime NOT NULL,
                                       `request_url` varchar(255) NOT NULL,
                                       `status` int(11) NOT NULL,
                                       `bytes` int(11) NOT NULL,
                                       `referrer` varchar(255) NOT NULL,
                                       `user_agent` varchar(255) NOT NULL,
                                       `googlebot` varchar(16) NOT NULL,
                                       `content_type` varchar(12) NOT NULL,
                                       `folder` varchar(128) NOT NULL,
                                       `host` varchar(3) NOT NULL,
                                       `protocol` varchar(5) NOT NULL,
                                       `unique_hash` varchar(255) NOT NULL,
                                       `created_at` datetime NOT NULL,
                                       PRIMARY KEY (`id`),
                                       KEY `unique_hash` (`unique_hash`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_log_mobile_request`;
CREATE TABLE `tipli_log_mobile_request` (
                                            `id` int(11) NOT NULL AUTO_INCREMENT,
                                            `localization_id` int(11) DEFAULT NULL,
                                            `user_id` int(11) DEFAULT NULL,
                                            `url` varchar(255) NOT NULL,
                                            `method` varchar(255) NOT NULL,
                                            `request_data` longtext DEFAULT NULL,
                                            `request_header` longtext DEFAULT NULL,
                                            `response_data` longtext DEFAULT NULL,
                                            `response_code` varchar(3) DEFAULT NULL,
                                            `ip` varchar(255) NOT NULL,
                                            `platform` varchar(7) NOT NULL,
                                            `country` varchar(2) DEFAULT NULL,
                                            `time_to_response` int(11) NOT NULL,
                                            `created_at` datetime NOT NULL,
                                            PRIMARY KEY (`id`),
                                            KEY `IDX_DE50ED026A2856C7` (`localization_id`),
                                            KEY `IDX_DE50ED02A76ED395` (`user_id`),
                                            KEY `platform_x` (`platform`),
                                            KEY `country_x` (`country`),
                                            KEY `created_at_platform_user_id_x` (`created_at`,`platform`,`user_id`),
                                            CONSTRAINT `FK_DE50ED026A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                            CONSTRAINT `FK_DE50ED02A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_log_suspected_request`;
CREATE TABLE `tipli_log_suspected_request` (
                                               `id` int(11) NOT NULL AUTO_INCREMENT,
                                               `localization_id` int(11) DEFAULT NULL,
                                               `request` varchar(255) NOT NULL,
                                               `url` varchar(255) NOT NULL,
                                               `method` varchar(255) NOT NULL,
                                               `request_data` longtext DEFAULT NULL,
                                               `request_header` longtext DEFAULT NULL,
                                               `ip` varchar(255) NOT NULL,
                                               `country` varchar(2) DEFAULT NULL,
                                               `created_at` datetime NOT NULL,
                                               PRIMARY KEY (`id`),
                                               KEY `IDX_E4BF76586A2856C7` (`localization_id`),
                                               KEY `country_x` (`country`),
                                               CONSTRAINT `FK_E4BF76586A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_log_webhook_request`;
CREATE TABLE `tipli_log_webhook_request` (
                                             `id` int(11) NOT NULL AUTO_INCREMENT,
                                             `name` varchar(255) NOT NULL,
                                             `url` longtext NOT NULL,
                                             `data` longtext DEFAULT NULL,
                                             `created_at` datetime NOT NULL,
                                             PRIMARY KEY (`id`),
                                             KEY `name_idx` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_marketing_banner`;
CREATE TABLE `tipli_marketing_banner` (
                                          `id` int(11) NOT NULL AUTO_INCREMENT,
                                          `localization_id` int(11) DEFAULT NULL,
                                          `money_reward_campaign_id` int(11) DEFAULT NULL,
                                          `image_id` int(11) DEFAULT NULL,
                                          `image_mobile_id` int(11) DEFAULT NULL,
                                          `format` varchar(255) NOT NULL,
                                          `size` varchar(255) DEFAULT NULL,
                                          `name` varchar(255) NOT NULL,
                                          `link` longtext NOT NULL,
                                          `open_in_new_window` tinyint(1) NOT NULL,
                                          `valid_since` datetime NOT NULL,
                                          `valid_till` datetime NOT NULL,
                                          `count_of_clicks` int(11) NOT NULL,
                                          `ga_count_of_clicks` int(11) NOT NULL,
                                          `ga_count_of_views` int(11) NOT NULL,
                                          `ga_ctr` double NOT NULL,
                                          `total_commission_amount` double NOT NULL,
                                          `count_of_transactions` int(11) NOT NULL,
                                          `priority` int(11) NOT NULL,
                                          `segment` varchar(255) DEFAULT NULL,
                                          `show_logo` tinyint(1) NOT NULL,
                                          `group_identifier` int(11) DEFAULT NULL,
                                          `count_in_group` int(11) NOT NULL DEFAULT 1,
                                          `number_in_group` int(11) NOT NULL DEFAULT 1,
                                          `visible_mobile_app` tinyint(1) NOT NULL,
                                          `visible_web` tinyint(1) NOT NULL,
                                          `created_at` datetime NOT NULL,
                                          PRIMARY KEY (`id`),
                                          KEY `IDX_AE35D07B6A2856C7` (`localization_id`),
                                          KEY `IDX_AE35D07BF9BFC74A` (`money_reward_campaign_id`),
                                          KEY `IDX_AE35D07B3DA5256D` (`image_id`),
                                          KEY `IDX_AE35D07B6084991F` (`image_mobile_id`),
                                          CONSTRAINT `FK_AE35D07B3DA5256D` FOREIGN KEY (`image_id`) REFERENCES `tipli_images_image` (`id`),
                                          CONSTRAINT `FK_AE35D07B6084991F` FOREIGN KEY (`image_mobile_id`) REFERENCES `tipli_images_image` (`id`),
                                          CONSTRAINT `FK_AE35D07B6A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                          CONSTRAINT `FK_AE35D07BF9BFC74A` FOREIGN KEY (`money_reward_campaign_id`) REFERENCES `tipli_rewards_money_reward_campaign` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_marketing_banner_click`;
CREATE TABLE `tipli_marketing_banner_click` (
                                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                                `banner_id` int(11) DEFAULT NULL,
                                                `user_id` int(11) DEFAULT NULL,
                                                `created_at` datetime NOT NULL,
                                                PRIMARY KEY (`id`),
                                                KEY `IDX_CCCDD41E684EC833` (`banner_id`),
                                                KEY `IDX_CCCDD41EA76ED395` (`user_id`),
                                                CONSTRAINT `FK_CCCDD41E684EC833` FOREIGN KEY (`banner_id`) REFERENCES `tipli_marketing_banner` (`id`),
                                                CONSTRAINT `FK_CCCDD41EA76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_marketing_banner_shops`;
CREATE TABLE `tipli_marketing_banner_shops` (
                                                `banner_id` int(11) NOT NULL,
                                                `shop_id` int(11) NOT NULL,
                                                PRIMARY KEY (`banner_id`,`shop_id`),
                                                KEY `IDX_554171BD684EC833` (`banner_id`),
                                                KEY `IDX_554171BD4D16C4DD` (`shop_id`),
                                                CONSTRAINT `FK_554171BD4D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`) ON DELETE CASCADE,
                                                CONSTRAINT `FK_554171BD684EC833` FOREIGN KEY (`banner_id`) REFERENCES `tipli_marketing_banner` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_marketing_content_section`;
CREATE TABLE `tipli_marketing_content_section` (
                                                   `id` int(11) NOT NULL AUTO_INCREMENT,
                                                   `localization_id` int(11) DEFAULT NULL,
                                                   `page` varchar(255) NOT NULL,
                                                   `type` varchar(255) NOT NULL,
                                                   `name` varchar(255) NOT NULL,
                                                   `navigation_name` varchar(255) NOT NULL,
                                                   `data` longtext NOT NULL,
                                                   `priority` int(11) NOT NULL,
                                                   `valid_since` datetime DEFAULT NULL,
                                                   `valid_till` datetime DEFAULT NULL,
                                                   `created_at` datetime NOT NULL,
                                                   PRIMARY KEY (`id`),
                                                   KEY `IDX_7324F9606A2856C7` (`localization_id`),
                                                   CONSTRAINT `FK_7324F9606A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_marketing_newsletter`;
CREATE TABLE `tipli_marketing_newsletter` (
											  `id` int(11) NOT NULL AUTO_INCREMENT,
											  `localization_id` int(11) DEFAULT NULL,
											  `author_id` int(11) DEFAULT NULL,
											  `utm_id` int(11) DEFAULT NULL,
											  `cover_id` int(11) DEFAULT NULL,
											  `type` varchar(255) NOT NULL,
											  `status` varchar(255) NOT NULL,
											  `cover_link` varchar(255) DEFAULT NULL,
											  `name` varchar(255) NOT NULL,
											  `subject` longtext NOT NULL,
											  `pre_header` varchar(255) NOT NULL,
											  `note` longtext DEFAULT NULL,
											  `color_theme` varchar(255) NOT NULL,
											  `background_color` varchar(7) DEFAULT NULL,
											  `primary_color` varchar(7) DEFAULT NULL,
											  `text_color` varchar(7) DEFAULT NULL,
											  `border_color` varchar(7) DEFAULT NULL,
											  `secondary_text_color` varchar(7) DEFAULT NULL,
											  `block_background_color` varchar(7) DEFAULT NULL,
											  `reward_text_color` varchar(7) DEFAULT NULL,
											  `include_access_token` tinyint(1) NOT NULL,
											  `archived_at` datetime DEFAULT NULL,
											  `updated_at` datetime NOT NULL,
											  `created_at` datetime NOT NULL,
											  `trigger_key` varchar(255) DEFAULT NULL,
											  `from_email` varchar(255) DEFAULT NULL,
											  `from_name` varchar(255) DEFAULT NULL,
											  `mandrill_tags` varchar(255) DEFAULT NULL,
											  `scenario` varchar(255) DEFAULT NULL,
											  `published_at` datetime DEFAULT NULL,
											  `locked` tinyint(1) NOT NULL,
											  `sender` varchar(255) DEFAULT NULL,
											  PRIMARY KEY (`id`),
											  KEY `IDX_456A858B6A2856C7` (`localization_id`),
											  KEY `IDX_456A858BF675F31B` (`author_id`),
											  KEY `IDX_456A858BB6334822` (`utm_id`),
											  KEY `IDX_456A858B922726E9` (`cover_id`),
											  KEY `name_idx` (`name`),
											  CONSTRAINT `FK_456A858B6A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
											  CONSTRAINT `FK_456A858B922726E9` FOREIGN KEY (`cover_id`) REFERENCES `tipli_images_image` (`id`),
											  CONSTRAINT `FK_456A858BB6334822` FOREIGN KEY (`utm_id`) REFERENCES `tipli_utm_utm` (`id`),
											  CONSTRAINT `FK_456A858BF675F31B` FOREIGN KEY (`author_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

DROP TABLE IF EXISTS `tipli_marketing_newsletter_block`;
CREATE TABLE `tipli_marketing_newsletter_block` (
                                                    `id` int(11) NOT NULL AUTO_INCREMENT,
                                                    `newsletter_id` int(11) DEFAULT NULL,
                                                    `type` varchar(255) NOT NULL,
                                                    `options` longtext DEFAULT NULL,
                                                    `data` longtext DEFAULT NULL,
                                                    `title` varchar(255) DEFAULT NULL,
                                                    `cta_text` varchar(255) DEFAULT NULL,
                                                    `cta_link` varchar(255) DEFAULT NULL,
                                                    `visible_cta` tinyint(1) NOT NULL,
                                                    `priority` int(11) NOT NULL,
                                                    `created_at` datetime NOT NULL,
                                                    PRIMARY KEY (`id`),
                                                    KEY `IDX_2698F80B22DB1917` (`newsletter_id`),
                                                    CONSTRAINT `FK_2698F80B22DB1917` FOREIGN KEY (`newsletter_id`) REFERENCES `tipli_marketing_newsletter` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_marketing_registration_page`;
CREATE TABLE `tipli_marketing_registration_page` (
                                                     `id` int(11) NOT NULL AUTO_INCREMENT,
                                                     `localization_id` int(11) DEFAULT NULL,
                                                     `image_desktop_id` int(11) DEFAULT NULL,
                                                     `image_mobile_id` int(11) DEFAULT NULL,
                                                     `thank_you_image_desktop_id` int(11) DEFAULT NULL,
                                                     `thank_you_image_mobile_id` int(11) DEFAULT NULL,
                                                     `thank_you_open_in_new_tab` tinyint(1) NOT NULL,
                                                     `thank_you_url` varchar(255) DEFAULT NULL,
                                                     `heading` varchar(255) NOT NULL,
                                                     `slug` varchar(255) DEFAULT NULL,
                                                     `description` longtext DEFAULT NULL,
                                                     `redirect_url` varchar(255) DEFAULT NULL,
                                                     `show_menu` tinyint(1) NOT NULL,
                                                     `valid_till` datetime NOT NULL,
                                                     `removed_at` datetime DEFAULT NULL,
                                                     `updated_at` datetime DEFAULT NULL,
                                                     `created_at` datetime NOT NULL,
                                                     PRIMARY KEY (`id`),
                                                     KEY `IDX_2379E1B66A2856C7` (`localization_id`),
                                                     KEY `IDX_2379E1B6932C8238` (`image_desktop_id`),
                                                     KEY `IDX_2379E1B66084991F` (`image_mobile_id`),
                                                     KEY `IDX_2379E1B659045F` (`thank_you_image_desktop_id`),
                                                     KEY `IDX_2379E1B61CFD6392` (`thank_you_image_mobile_id`),
                                                     CONSTRAINT `FK_2379E1B61CFD6392` FOREIGN KEY (`thank_you_image_mobile_id`) REFERENCES `tipli_images_image` (`id`),
                                                     CONSTRAINT `FK_2379E1B659045F` FOREIGN KEY (`thank_you_image_desktop_id`) REFERENCES `tipli_images_image` (`id`),
                                                     CONSTRAINT `FK_2379E1B66084991F` FOREIGN KEY (`image_mobile_id`) REFERENCES `tipli_images_image` (`id`),
                                                     CONSTRAINT `FK_2379E1B66A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                                     CONSTRAINT `FK_2379E1B6932C8238` FOREIGN KEY (`image_desktop_id`) REFERENCES `tipli_images_image` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_messages_blocked_email`;
CREATE TABLE `tipli_messages_blocked_email` (
                                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                                `email` varchar(255) NOT NULL,
                                                `created_at` datetime NOT NULL,
                                                PRIMARY KEY (`id`),
                                                UNIQUE KEY `UNIQ_7403108CE7927C74` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_messages_email`;
CREATE TABLE `tipli_messages_email` (
                                        `id` int(11) NOT NULL AUTO_INCREMENT,
                                        `user_id` int(11) DEFAULT NULL,
                                        `protection_duplicity_hash` varchar(32) DEFAULT NULL,
                                        `message_id` varchar(255) DEFAULT NULL,
                                        `to_email` varchar(255) NOT NULL,
                                        `from_email` varchar(255) DEFAULT NULL,
                                        `from_name` varchar(255) DEFAULT NULL,
                                        `subject` varchar(255) DEFAULT NULL,
                                        `body` longtext DEFAULT NULL,
                                        `type` varchar(255) NOT NULL,
                                        `variant` varchar(255) DEFAULT NULL,
                                        `campaign` varchar(255) DEFAULT NULL,
                                        `priority` int(11) NOT NULL,
                                        `scheduled_at` datetime NOT NULL,
                                        `sent_at` datetime DEFAULT NULL,
                                        `aborted_at` datetime DEFAULT NULL,
                                        `created_at` datetime NOT NULL,
                                        PRIMARY KEY (`id`),
                                        KEY `IDX_A9A00424A76ED395` (`user_id`),
                                        KEY `scheduled_at_idx` (`scheduled_at`),
                                        KEY `protection_duplicity_hashx` (`protection_duplicity_hash`),
                                        KEY `sent_at_idx` (`sent_at`),
                                        KEY `aborted_at_idx` (`aborted_at`),
                                        CONSTRAINT `FK_A9A00424A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_messages_mandrill_tag`;
CREATE TABLE `tipli_messages_mandrill_tag` (
                                               `id` int(11) NOT NULL AUTO_INCREMENT,
                                               `tag` varchar(255) NOT NULL,
                                               `sent` int(11) NOT NULL,
                                               `hard_bounces` int(11) NOT NULL,
                                               `soft_bounces` int(11) NOT NULL,
                                               `rejects` int(11) NOT NULL,
                                               `complaints` int(11) NOT NULL,
                                               `unsubscribes` int(11) NOT NULL,
                                               `opens` int(11) NOT NULL,
                                               `clicks` int(11) NOT NULL,
                                               `unique_opens` int(11) NOT NULL,
                                               `unique_clicks` int(11) NOT NULL,
                                               `turnover` decimal(8,2) NOT NULL,
                                               `count_activated_users` int(11) NOT NULL,
                                               `count_reactivated_users` int(11) NOT NULL,
                                               `count_addon_installs` int(11) NOT NULL,
                                               `updated_at` datetime NOT NULL,
                                               `created_at` datetime NOT NULL,
                                               PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_messages_one_signal_notification`;
CREATE TABLE `tipli_messages_one_signal_notification` (
                                                          `id` int(11) NOT NULL AUTO_INCREMENT,
                                                          `localization_id` int(11) DEFAULT NULL,
                                                          `unique_id` varchar(255) NOT NULL,
                                                          `name` varchar(255) NOT NULL,
                                                          `contents` varchar(255) NOT NULL,
                                                          `clicks` int(11) NOT NULL,
                                                          `count_of_successful` int(11) NOT NULL,
                                                          `count_of_failed` int(11) NOT NULL,
                                                          `count_of_remaining` int(11) NOT NULL,
                                                          `turnover` decimal(8,2) NOT NULL,
                                                          `count_of_activated_users` int(11) NOT NULL,
                                                          `count_of_reactivated_users` int(11) NOT NULL,
                                                          `count_of_addon_installs` int(11) NOT NULL,
                                                          `start_sending_at` datetime NOT NULL,
                                                          `completed_at` datetime DEFAULT NULL,
                                                          `updated_at` datetime NOT NULL,
                                                          `created_at` datetime NOT NULL,
                                                          PRIMARY KEY (`id`),
                                                          KEY `IDX_B38F68466A2856C7` (`localization_id`),
                                                          CONSTRAINT `FK_B38F68466A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_messages_sms`;
CREATE TABLE `tipli_messages_sms` (
                                      `id` int(11) NOT NULL AUTO_INCREMENT,
                                      `user_id` int(11) DEFAULT NULL,
                                      `protection_duplicity_hash` varchar(32) DEFAULT NULL,
                                      `message_id` varchar(255) DEFAULT NULL,
                                      `to_phone_number` varchar(255) DEFAULT NULL,
                                      `campaign` varchar(255) NOT NULL,
                                      `text` longtext DEFAULT NULL,
                                      `priority` int(11) NOT NULL,
                                      `scheduled_at` datetime NOT NULL,
                                      `sent_at` datetime DEFAULT NULL,
                                      `aborted_at` datetime DEFAULT NULL,
                                      `created_at` datetime NOT NULL,
                                      PRIMARY KEY (`id`),
                                      KEY `IDX_48E72EDDA76ED395` (`user_id`),
                                      KEY `scheduled_at_idx` (`scheduled_at`),
                                      KEY `protection_duplicity_hashx` (`protection_duplicity_hash`),
                                      KEY `sent_at_idx` (`sent_at`),
                                      KEY `aborted_at_idx` (`aborted_at`),
                                      CONSTRAINT `FK_48E72EDDA76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_messages_user_auto_responder`;
CREATE TABLE `tipli_messages_user_auto_responder` (
                                                      `id` int(11) NOT NULL AUTO_INCREMENT,
                                                      `user_id` int(11) DEFAULT NULL,
                                                      `transaction_id` int(11) DEFAULT NULL,
                                                      `type` varchar(255) NOT NULL,
                                                      `sent_at` datetime NOT NULL,
                                                      `created_at` datetime NOT NULL,
                                                      PRIMARY KEY (`id`),
                                                      KEY `IDX_A2A0F2B9A76ED395` (`user_id`),
                                                      KEY `IDX_A2A0F2B92FC0CB0F` (`transaction_id`),
                                                      CONSTRAINT `FK_A2A0F2B92FC0CB0F` FOREIGN KEY (`transaction_id`) REFERENCES `tipli_transactions_transaction` (`id`),
                                                      CONSTRAINT `FK_A2A0F2B9A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_monitors_monitor`;
CREATE TABLE `tipli_monitors_monitor` (
                                          `id` int(11) NOT NULL AUTO_INCREMENT,
                                          `name` varchar(255) NOT NULL,
                                          `method_name` varchar(255) NOT NULL,
                                          `allowed_hours` varchar(255) DEFAULT NULL,
                                          `processed_at` datetime DEFAULT NULL,
                                          `process_at` datetime DEFAULT NULL,
                                          `process_time_shift` varchar(16) DEFAULT NULL,
                                          `error_message` varchar(255) DEFAULT NULL,
                                          `last_error_at` datetime DEFAULT NULL,
                                          `created_at` datetime NOT NULL,
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_partner_organizations_partner_organization`;
CREATE TABLE `tipli_partner_organizations_partner_organization` (
                                                                    `id` int(11) NOT NULL AUTO_INCREMENT,
                                                                    `localization_id` int(11) DEFAULT NULL,
                                                                    `manager_id` int(11) DEFAULT NULL,
                                                                    `logo_id` int(11) DEFAULT NULL,
                                                                    `name` varchar(255) NOT NULL,
                                                                    `tooltip` varchar(255) NOT NULL,
                                                                    `slug` varchar(255) NOT NULL,
                                                                    `bonus_amount` double NOT NULL,
                                                                    `share_coefficient` double NOT NULL,
                                                                    `is_white_label` tinyint(1) NOT NULL,
                                                                    `created_at` datetime NOT NULL,
                                                                    PRIMARY KEY (`id`),
                                                                    UNIQUE KEY `slug_unique` (`localization_id`,`slug`),
                                                                    KEY `IDX_EFF2C4686A2856C7` (`localization_id`),
                                                                    KEY `IDX_EFF2C468783E3463` (`manager_id`),
                                                                    KEY `IDX_EFF2C468F98F144A` (`logo_id`),
                                                                    CONSTRAINT `FK_EFF2C4686A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                                                    CONSTRAINT `FK_EFF2C468783E3463` FOREIGN KEY (`manager_id`) REFERENCES `tipli_account_user` (`id`),
                                                                    CONSTRAINT `FK_EFF2C468F98F144A` FOREIGN KEY (`logo_id`) REFERENCES `tipli_images_image` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_partner_systems_income`;
CREATE TABLE `tipli_partner_systems_income` (
                                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                                `partner_system_id` int(11) DEFAULT NULL,
                                                `original_amount` decimal(10,3) NOT NULL,
                                                `original_currency` varchar(3) NOT NULL,
                                                `note` varchar(255) DEFAULT NULL,
                                                `amount` decimal(10,3) NOT NULL,
                                                `requested_at` datetime DEFAULT NULL,
                                                `received_at` datetime DEFAULT NULL,
                                                `created_at` datetime NOT NULL,
                                                PRIMARY KEY (`id`),
                                                KEY `IDX_9D81A9FAFB42B4B1` (`partner_system_id`),
                                                CONSTRAINT `FK_9D81A9FAFB42B4B1` FOREIGN KEY (`partner_system_id`) REFERENCES `tipli_partner_systems_partner_system` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_partner_systems_partner_system`;
CREATE TABLE `tipli_partner_systems_partner_system` (
                                                        `id` int(11) NOT NULL AUTO_INCREMENT,
                                                        `name` varchar(255) NOT NULL,
                                                        `type` varchar(255) NOT NULL,
                                                        `options` longtext NOT NULL,
                                                        `automated` tinyint(1) NOT NULL,
                                                        `daily_pivot_allowed` tinyint(1) NOT NULL,
                                                        `has_commissions` tinyint(1) NOT NULL,
                                                        `has_sales` tinyint(1) NOT NULL,
                                                        `processed_at` datetime NOT NULL,
                                                        `process_sales_at` datetime DEFAULT NULL,
                                                        `process_time_shift` varchar(16) DEFAULT NULL,
                                                        `pivot_date` datetime NOT NULL,
                                                        `pivot_date_shift` varchar(16) DEFAULT NULL,
                                                        `pivot_last_reset_date` datetime DEFAULT NULL,
                                                        `last_import_at` datetime DEFAULT NULL,
                                                        `last_process_error_at` datetime DEFAULT NULL,
                                                        `managing_note` longtext NOT NULL,
                                                        `sales_note` longtext NOT NULL,
                                                        `accounting_note` longtext NOT NULL,
                                                        `last_transaction_at` datetime DEFAULT NULL,
                                                        `count_of_transactions` int(11) NOT NULL,
                                                        `count_of_shops` int(11) NOT NULL,
                                                        `is_important` tinyint(1) NOT NULL,
                                                        `oldest_transaction_registered_at` datetime DEFAULT NULL,
                                                        `shop_checker_allowed` tinyint(1) NOT NULL,
                                                        `confirmed_turnover` double NOT NULL,
                                                        `created_at` datetime NOT NULL,
                                                        PRIMARY KEY (`id`),
                                                        KEY `processed_at_idx` (`processed_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_partner_systems_partner_system_process`;
CREATE TABLE `tipli_partner_systems_partner_system_process` (
                                                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                                                `partner_system_id` int(11) DEFAULT NULL,
                                                                `from_date` datetime DEFAULT NULL,
                                                                `to_date` datetime DEFAULT NULL,
                                                                `finished_at` datetime DEFAULT NULL,
                                                                `duration` int(11) DEFAULT NULL,
                                                                `error_message` longtext DEFAULT NULL,
                                                                `created_at` datetime NOT NULL,
                                                                PRIMARY KEY (`id`),
                                                                KEY `IDX_C2875D66FB42B4B1` (`partner_system_id`),
                                                                CONSTRAINT `FK_C2875D66FB42B4B1` FOREIGN KEY (`partner_system_id`) REFERENCES `tipli_partner_systems_partner_system` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_partner_systems_process_error`;
CREATE TABLE `tipli_partner_systems_process_error` (
                                                       `id` int(11) NOT NULL AUTO_INCREMENT,
                                                       `partner_system_id` int(11) DEFAULT NULL,
                                                       `message` text NOT NULL,
                                                       `created_at` datetime NOT NULL,
                                                       PRIMARY KEY (`id`),
                                                       KEY `IDX_30D085DEFB42B4B1` (`partner_system_id`),
                                                       CONSTRAINT `FK_30D085DEFB42B4B1` FOREIGN KEY (`partner_system_id`) REFERENCES `tipli_partner_systems_partner_system` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_payouts_payout`;
CREATE TABLE `tipli_payouts_payout` (
                                        `id` int(11) NOT NULL AUTO_INCREMENT,
                                        `user_id` int(11) DEFAULT NULL,
                                        `transaction_id` int(11) DEFAULT NULL,
                                        `confirmed_by` int(11) DEFAULT NULL,
                                        `authorized_by` int(11) DEFAULT NULL,
                                        `unique_id` varchar(10) NOT NULL,
                                        `type` varchar(15) NOT NULL,
                                        `state` varchar(20) DEFAULT NULL,
                                        `first_name` varchar(255) DEFAULT NULL,
                                        `last_name` varchar(255) DEFAULT NULL,
                                        `account_number` varchar(255) DEFAULT NULL,
                                        `phone_number` varchar(255) DEFAULT NULL,
                                        `ip` varchar(255) DEFAULT NULL,
                                        `user_agent` varchar(255) DEFAULT NULL,
                                        `birthdate` date DEFAULT NULL,
                                        `scheduled_at` datetime DEFAULT NULL,
                                        `confirmed_at` datetime DEFAULT NULL,
                                        `authorized_at` datetime DEFAULT NULL,
                                        `failed_at` datetime DEFAULT NULL,
                                        `locked_till` datetime DEFAULT NULL,
                                        `user_contacted_at` datetime DEFAULT NULL,
                                        `created_at` datetime NOT NULL,
                                        PRIMARY KEY (`id`),
                                        UNIQUE KEY `unique_id` (`unique_id`),
                                        KEY `IDX_DCCF9160A76ED395` (`user_id`),
                                        KEY `IDX_DCCF91602FC0CB0F` (`transaction_id`),
                                        KEY `IDX_DCCF9160FB3F81CB` (`confirmed_by`),
                                        KEY `IDX_DCCF9160C96F1818` (`authorized_by`),
                                        KEY `scheduled_at_idx` (`scheduled_at`),
                                        KEY `confirmed_at_idx` (`confirmed_at`),
                                        KEY `failed_at_idx` (`failed_at`),
                                        CONSTRAINT `FK_DCCF91602FC0CB0F` FOREIGN KEY (`transaction_id`) REFERENCES `tipli_transactions_transaction` (`id`),
                                        CONSTRAINT `FK_DCCF9160A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`),
                                        CONSTRAINT `FK_DCCF9160C96F1818` FOREIGN KEY (`authorized_by`) REFERENCES `tipli_account_user` (`id`),
                                        CONSTRAINT `FK_DCCF9160FB3F81CB` FOREIGN KEY (`confirmed_by`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_payouts_payout_comment`;
CREATE TABLE `tipli_payouts_payout_comment` (
                                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                                `payout_id` int(11) DEFAULT NULL,
                                                `author_id` int(11) DEFAULT NULL,
                                                `text` longtext NOT NULL,
                                                `email_subject` varchar(255) DEFAULT NULL,
                                                `email_body` longtext DEFAULT NULL,
                                                `email_from_email` varchar(255) DEFAULT NULL,
                                                `private` tinyint(1) NOT NULL,
                                                `created_at` datetime NOT NULL,
                                                PRIMARY KEY (`id`),
                                                KEY `IDX_D5D3501BC6D61B7F` (`payout_id`),
                                                KEY `IDX_D5D3501BF675F31B` (`author_id`),
                                                CONSTRAINT `FK_D5D3501BC6D61B7F` FOREIGN KEY (`payout_id`) REFERENCES `tipli_payouts_payout` (`id`),
                                                CONSTRAINT `FK_D5D3501BF675F31B` FOREIGN KEY (`author_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_pictures_picture`;
CREATE TABLE `tipli_pictures_picture` (
                                          `id` int(11) NOT NULL AUTO_INCREMENT,
                                          `image_id` int(11) DEFAULT NULL,
                                          `name` varchar(255) DEFAULT NULL,
                                          `keywords` varchar(255) DEFAULT NULL,
                                          `updated_at` datetime NOT NULL,
                                          `created_at` datetime NOT NULL,
                                          PRIMARY KEY (`id`),
                                          KEY `IDX_9358F4FC3DA5256D` (`image_id`),
                                          CONSTRAINT `FK_9358F4FC3DA5256D` FOREIGN KEY (`image_id`) REFERENCES `tipli_images_image` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_popups_popup_campaign`;
CREATE TABLE `tipli_popups_popup_campaign` (
                                               `id` int(11) NOT NULL AUTO_INCREMENT,
                                               `localization_id` int(11) DEFAULT NULL,
                                               `image_id` int(11) DEFAULT NULL,
                                               `image_mobile_id` int(11) DEFAULT NULL,
                                               `name` varchar(255) NOT NULL,
                                               `link` varchar(255) NOT NULL,
                                               `open_in_new_tab` tinyint(1) NOT NULL,
                                               `cta_label` varchar(255) NOT NULL,
                                               `valid_since` datetime DEFAULT NULL,
                                               `valid_till` datetime DEFAULT NULL,
                                               `count_of_opens` int(11) NOT NULL,
                                               `count_of_closures` int(11) NOT NULL,
                                               `count_of_clicks` int(11) NOT NULL,
                                               `created_at` datetime NOT NULL,
                                               PRIMARY KEY (`id`),
                                               KEY `IDX_FEAAEFEB6A2856C7` (`localization_id`),
                                               KEY `IDX_FEAAEFEB3DA5256D` (`image_id`),
                                               KEY `IDX_FEAAEFEB6084991F` (`image_mobile_id`),
                                               CONSTRAINT `FK_FEAAEFEB3DA5256D` FOREIGN KEY (`image_id`) REFERENCES `tipli_images_image` (`id`),
                                               CONSTRAINT `FK_FEAAEFEB6084991F` FOREIGN KEY (`image_mobile_id`) REFERENCES `tipli_images_image` (`id`),
                                               CONSTRAINT `FK_FEAAEFEB6A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_popups_popup_campaign_condition`;
CREATE TABLE `tipli_popups_popup_campaign_condition` (
                                                         `id` int(11) NOT NULL AUTO_INCREMENT,
                                                         `popup_campaign_id` int(11) DEFAULT NULL,
                                                         `user` smallint(6) DEFAULT NULL,
                                                         `user_active` smallint(6) DEFAULT NULL,
                                                         `addon` smallint(6) DEFAULT NULL,
                                                         `url` varchar(255) DEFAULT NULL,
                                                         `paid_source` smallint(6) DEFAULT NULL,
                                                         `leaflet_section` smallint(6) DEFAULT NULL,
                                                         PRIMARY KEY (`id`),
                                                         UNIQUE KEY `UNIQ_D50B2B221BC52D96` (`popup_campaign_id`),
                                                         CONSTRAINT `FK_D50B2B221BC52D96` FOREIGN KEY (`popup_campaign_id`) REFERENCES `tipli_popups_popup_campaign` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_popups_popup_interaction`;
CREATE TABLE `tipli_popups_popup_interaction` (
                                                  `id` int(11) NOT NULL AUTO_INCREMENT,
                                                  `popup_campaign_id` int(11) DEFAULT NULL,
                                                  `user_id` int(11) DEFAULT NULL,
                                                  `browser_token_id` int(11) DEFAULT NULL,
                                                  `opened_at` datetime NOT NULL,
                                                  `closed_at` datetime DEFAULT NULL,
                                                  `clicked_at` datetime DEFAULT NULL,
                                                  `created_at` datetime NOT NULL,
                                                  PRIMARY KEY (`id`),
                                                  KEY `IDX_EDA755891BC52D96` (`popup_campaign_id`),
                                                  KEY `IDX_EDA75589A76ED395` (`user_id`),
                                                  KEY `IDX_EDA75589E8B24323` (`browser_token_id`),
                                                  CONSTRAINT `FK_EDA755891BC52D96` FOREIGN KEY (`popup_campaign_id`) REFERENCES `tipli_popups_popup_campaign` (`id`),
                                                  CONSTRAINT `FK_EDA75589A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`),
                                                  CONSTRAINT `FK_EDA75589E8B24323` FOREIGN KEY (`browser_token_id`) REFERENCES `tipli_browser_activities_browser_token` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_products2_product`;
CREATE TABLE `tipli_products2_product` (
                                           `id` int(11) NOT NULL AUTO_INCREMENT,
                                           `localization_id` int(11) DEFAULT NULL,
                                           `shop_id` int(11) DEFAULT NULL,
                                           `name` varchar(255) NOT NULL,
                                           `product_id` varchar(255) DEFAULT NULL,
                                           `description` longtext DEFAULT NULL,
                                           `url` varchar(255) NOT NULL,
                                           `image_url` longtext NOT NULL,
                                           `price` double NOT NULL,
                                           `old_price` double DEFAULT NULL,
                                           `currency` varchar(255) DEFAULT NULL,
                                           `in_stock` tinyint(1) NOT NULL,
                                           `position` int(11) NOT NULL,
                                           `last_seen_at` datetime NOT NULL,
                                           `valid_till` datetime NOT NULL,
                                           `updated_at` datetime NOT NULL,
                                           `removed_at` datetime DEFAULT NULL,
                                           `created_at` datetime NOT NULL,
                                           PRIMARY KEY (`id`),
                                           UNIQUE KEY `UNIQ_4DBABF184584665A` (`product_id`),
                                           KEY `IDX_4DBABF186A2856C7` (`localization_id`),
                                           KEY `IDX_4DBABF184D16C4DD` (`shop_id`),
                                           CONSTRAINT `FK_4DBABF184D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                           CONSTRAINT `FK_4DBABF186A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_products_brand`;
CREATE TABLE `tipli_products_brand` (
                                        `id` int(11) NOT NULL AUTO_INCREMENT,
                                        `localization_id` int(11) DEFAULT NULL,
                                        `name` varchar(255) NOT NULL,
                                        `slug` varchar(255) NOT NULL,
                                        PRIMARY KEY (`id`),
                                        KEY `IDX_73B5DA36A2856C7` (`localization_id`),
                                        CONSTRAINT `FK_73B5DA36A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_products_category`;
CREATE TABLE `tipli_products_category` (
                                           `id` int(11) NOT NULL AUTO_INCREMENT,
                                           `localization_id` int(11) DEFAULT NULL,
                                           `parent_id` int(11) DEFAULT NULL,
                                           `root_id` int(11) DEFAULT NULL,
                                           `name` varchar(255) NOT NULL,
                                           `path` varchar(255) NOT NULL,
                                           `original_path` varchar(255) NOT NULL,
                                           `count_of_products` int(11) NOT NULL,
                                           `count_of_assigned_products` int(11) NOT NULL,
                                           `searches` int(11) NOT NULL,
                                           `ignored` tinyint(1) NOT NULL,
                                           `assign_shops_processed_at` datetime DEFAULT NULL,
                                           `process_tags_at` datetime DEFAULT NULL,
                                           `created_at` datetime NOT NULL,
                                           PRIMARY KEY (`id`),
                                           KEY `IDX_41D825FA6A2856C7` (`localization_id`),
                                           KEY `IDX_41D825FA727ACA70` (`parent_id`),
                                           KEY `IDX_41D825FA79066886` (`root_id`),
                                           CONSTRAINT `FK_41D825FA6A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                           CONSTRAINT `FK_41D825FA727ACA70` FOREIGN KEY (`parent_id`) REFERENCES `tipli_products_category` (`id`),
                                           CONSTRAINT `FK_41D825FA79066886` FOREIGN KEY (`root_id`) REFERENCES `tipli_products_category` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_products_category_shop`;
CREATE TABLE `tipli_products_category_shop` (
                                                `category_id` int(11) NOT NULL,
                                                `shop_id` int(11) NOT NULL,
                                                PRIMARY KEY (`category_id`,`shop_id`),
                                                KEY `IDX_EDF318512469DE2` (`category_id`),
                                                KEY `IDX_EDF31854D16C4DD` (`shop_id`),
                                                CONSTRAINT `FK_EDF318512469DE2` FOREIGN KEY (`category_id`) REFERENCES `tipli_products_category` (`id`) ON DELETE CASCADE,
                                                CONSTRAINT `FK_EDF31854D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_products_category_tag`;
CREATE TABLE `tipli_products_category_tag` (
                                               `category_id` int(11) NOT NULL,
                                               `tag_id` int(11) NOT NULL,
                                               `confirmed_by` int(11) DEFAULT NULL,
                                               `assigned_by` varchar(5) NOT NULL,
                                               `state` varchar(255) DEFAULT NULL,
                                               `created_at` datetime NOT NULL,
                                               PRIMARY KEY (`category_id`,`tag_id`),
                                               KEY `IDX_D185E2A012469DE2` (`category_id`),
                                               KEY `IDX_D185E2A0BAD26311` (`tag_id`),
                                               KEY `IDX_D185E2A0FB3F81CB` (`confirmed_by`),
                                               CONSTRAINT `FK_D185E2A012469DE2` FOREIGN KEY (`category_id`) REFERENCES `tipli_products_category` (`id`),
                                               CONSTRAINT `FK_D185E2A0BAD26311` FOREIGN KEY (`tag_id`) REFERENCES `tipli_tags_tag` (`id`),
                                               CONSTRAINT `FK_D185E2A0FB3F81CB` FOREIGN KEY (`confirmed_by`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_products_product`;
CREATE TABLE `tipli_products_product` (
                                          `id` int(11) NOT NULL AUTO_INCREMENT,
                                          `localization_id` int(11) DEFAULT NULL,
                                          `brand_id` int(11) DEFAULT NULL,
                                          `shop_id` int(11) DEFAULT NULL,
                                          `primary_tag_id` int(11) DEFAULT NULL,
                                          `name` varchar(255) NOT NULL,
                                          `ean` varchar(255) DEFAULT NULL,
                                          `picture_url` varchar(255) DEFAULT NULL,
                                          `deep_url` varchar(255) NOT NULL,
                                          `description` longtext DEFAULT NULL,
                                          `gender` varchar(255) DEFAULT NULL,
                                          `current_minimal_price` double DEFAULT NULL,
                                          `stated_old_pmaximal_price` double DEFAULT NULL,
                                          `currency` varchar(255) NOT NULL,
                                          `free_shipping` tinyint(1) DEFAULT NULL,
                                          `available` tinyint(1) DEFAULT NULL,
                                          `is_picture_remote` tinyint(1) NOT NULL,
                                          `priority` int(11) NOT NULL,
                                          `trend_order` int(11) NOT NULL,
                                          `product_check_at` datetime DEFAULT NULL,
                                          `count_of_image_check_errors` int(11) NOT NULL,
                                          `trend_at` datetime DEFAULT NULL,
                                          `brand_name` varchar(255) DEFAULT NULL,
                                          `updated_at` datetime NOT NULL,
                                          `created_at` datetime NOT NULL,
                                          PRIMARY KEY (`id`),
                                          KEY `IDX_F5B940436A2856C7` (`localization_id`),
                                          KEY `IDX_F5B9404344F5D008` (`brand_id`),
                                          KEY `IDX_F5B940434D16C4DD` (`shop_id`),
                                          KEY `IDX_F5B940434225DBA1` (`primary_tag_id`),
                                          CONSTRAINT `FK_F5B940434225DBA1` FOREIGN KEY (`primary_tag_id`) REFERENCES `tipli_tags_tag` (`id`),
                                          CONSTRAINT `FK_F5B9404344F5D008` FOREIGN KEY (`brand_id`) REFERENCES `tipli_products_brand` (`id`),
                                          CONSTRAINT `FK_F5B940434D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                          CONSTRAINT `FK_F5B940436A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_products_product_parameter`;
CREATE TABLE `tipli_products_product_parameter` (
                                                    `id` int(11) NOT NULL AUTO_INCREMENT,
                                                    `product_id` int(11) DEFAULT NULL,
                                                    `name` varchar(255) NOT NULL,
                                                    `value` varchar(255) NOT NULL,
                                                    `created_at` datetime NOT NULL,
                                                    PRIMARY KEY (`id`),
                                                    KEY `IDX_33EB575B4584665A` (`product_id`),
                                                    CONSTRAINT `FK_33EB575B4584665A` FOREIGN KEY (`product_id`) REFERENCES `tipli_products_product` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_products_product_tag`;
CREATE TABLE `tipli_products_product_tag` (
                                              `product_id` int(11) NOT NULL,
                                              `tag_id` int(11) NOT NULL,
                                              `priority` int(11) DEFAULT NULL,
                                              `created_at` datetime NOT NULL,
                                              PRIMARY KEY (`product_id`,`tag_id`),
                                              KEY `IDX_4F09C7084584665A` (`product_id`),
                                              KEY `IDX_4F09C708BAD26311` (`tag_id`),
                                              CONSTRAINT `FK_4F09C7084584665A` FOREIGN KEY (`product_id`) REFERENCES `tipli_products_product` (`id`),
                                              CONSTRAINT `FK_4F09C708BAD26311` FOREIGN KEY (`tag_id`) REFERENCES `tipli_tags_tag` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_products_shop_product`;
CREATE TABLE `tipli_products_shop_product` (
                                               `id` int(11) NOT NULL AUTO_INCREMENT,
                                               `localization_id` int(11) DEFAULT NULL,
                                               `shop_id` int(11) DEFAULT NULL,
                                               `deal_id` int(11) DEFAULT NULL,
                                               `shop_product_feed_id` int(11) DEFAULT NULL,
                                               `category_id` int(11) DEFAULT NULL,
                                               `parent_id` int(11) DEFAULT NULL,
                                               `product_id` int(11) DEFAULT NULL,
                                               `name` varchar(255) NOT NULL,
                                               `ean` varchar(255) DEFAULT NULL,
                                               `brand` varchar(255) DEFAULT NULL,
                                               `url` varchar(255) NOT NULL,
                                               `picture_url` varchar(255) DEFAULT NULL,
                                               `description` longtext DEFAULT NULL,
                                               `labels` longtext DEFAULT NULL,
                                               `current_price` double DEFAULT NULL,
                                               `stated_old_price` double DEFAULT NULL,
                                               `currency` varchar(3) DEFAULT NULL,
                                               `cpc` double DEFAULT NULL,
                                               `params` longtext DEFAULT NULL,
                                               `adult` tinyint(1) NOT NULL,
                                               `product_detail_crawled_at` datetime DEFAULT NULL,
                                               `price_updated_at` datetime DEFAULT NULL,
                                               `categories_processed_at` datetime DEFAULT NULL,
                                               `updated_at` datetime NOT NULL,
                                               `created_at` datetime NOT NULL,
                                               PRIMARY KEY (`id`),
                                               UNIQUE KEY `url` (`url`),
                                               KEY `IDX_27B7BCFE6A2856C7` (`localization_id`),
                                               KEY `IDX_27B7BCFE4D16C4DD` (`shop_id`),
                                               KEY `IDX_27B7BCFEF60E2305` (`deal_id`),
                                               KEY `IDX_27B7BCFE698FA387` (`shop_product_feed_id`),
                                               KEY `IDX_27B7BCFE12469DE2` (`category_id`),
                                               KEY `IDX_27B7BCFE727ACA70` (`parent_id`),
                                               KEY `IDX_27B7BCFE4584665A` (`product_id`),
                                               CONSTRAINT `FK_27B7BCFE12469DE2` FOREIGN KEY (`category_id`) REFERENCES `tipli_products_category` (`id`),
                                               CONSTRAINT `FK_27B7BCFE4584665A` FOREIGN KEY (`product_id`) REFERENCES `tipli_products_product` (`id`),
                                               CONSTRAINT `FK_27B7BCFE4D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                               CONSTRAINT `FK_27B7BCFE698FA387` FOREIGN KEY (`shop_product_feed_id`) REFERENCES `tipli_shops_shop_product_feed` (`id`),
                                               CONSTRAINT `FK_27B7BCFE6A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                               CONSTRAINT `FK_27B7BCFE727ACA70` FOREIGN KEY (`parent_id`) REFERENCES `tipli_products_category` (`id`),
                                               CONSTRAINT `FK_27B7BCFEF60E2305` FOREIGN KEY (`deal_id`) REFERENCES `tipli_deals_deal` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_products_shop_product_data`;
CREATE TABLE `tipli_products_shop_product_data` (
                                                    `id` int(11) NOT NULL AUTO_INCREMENT,
                                                    `shop_product_id` int(11) DEFAULT NULL,
                                                    `price` double NOT NULL,
                                                    `price_previous` double DEFAULT NULL,
                                                    `price_trend` double DEFAULT NULL,
                                                    `source` varchar(15) NOT NULL,
                                                    `received_at` datetime NOT NULL,
                                                    `created_at` datetime NOT NULL,
                                                    PRIMARY KEY (`id`),
                                                    KEY `IDX_4AA2C6963FF78B7C` (`shop_product_id`),
                                                    CONSTRAINT `FK_4AA2C6963FF78B7C` FOREIGN KEY (`shop_product_id`) REFERENCES `tipli_products_shop_product` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_questions_group`;
CREATE TABLE `tipli_questions_group` (
                                         `id` int(11) NOT NULL AUTO_INCREMENT,
                                         `note_title` varchar(255) NOT NULL,
                                         `cs_title` varchar(255) DEFAULT NULL,
                                         `hu_title` varchar(255) DEFAULT NULL,
                                         `pl_title` varchar(255) DEFAULT NULL,
                                         `ro_title` varchar(255) DEFAULT NULL,
                                         `sk_title` varchar(255) DEFAULT NULL,
                                         `hr_title` varchar(255) DEFAULT NULL,
                                         `bg_title` varchar(255) DEFAULT NULL,
                                         `si_title` varchar(255) DEFAULT NULL,
                                         `priority` int(11) NOT NULL,
                                         `created_at` datetime NOT NULL,
                                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_questions_question`;
CREATE TABLE `tipli_questions_question` (
                                            `id` int(11) NOT NULL AUTO_INCREMENT,
                                            `group_id` int(11) DEFAULT NULL,
                                            `note_question` varchar(255) DEFAULT NULL,
                                            `cs_question` varchar(255) DEFAULT NULL,
                                            `hu_question` varchar(255) DEFAULT NULL,
                                            `pl_question` varchar(255) DEFAULT NULL,
                                            `ro_question` varchar(255) DEFAULT NULL,
                                            `sk_question` varchar(255) DEFAULT NULL,
                                            `si_question` varchar(255) DEFAULT NULL,
                                            `hr_question` varchar(255) DEFAULT NULL,
                                            `bg_question` varchar(255) DEFAULT NULL,
                                            `note_answer` longtext DEFAULT NULL,
                                            `cs_answer` longtext DEFAULT NULL,
                                            `hu_answer` longtext DEFAULT NULL,
                                            `pl_answer` longtext DEFAULT NULL,
                                            `si_answer` longtext DEFAULT NULL,
                                            `bg_answer` longtext DEFAULT NULL,
                                            `hr_answer` longtext DEFAULT NULL,
                                            `ro_answer` longtext DEFAULT NULL,
                                            `sk_answer` longtext DEFAULT NULL,
                                            `priority` int(11) NOT NULL,
                                            `created_at` datetime NOT NULL,
                                            PRIMARY KEY (`id`),
                                            KEY `IDX_27B235D5FE54D947` (`group_id`),
                                            CONSTRAINT `FK_27B235D5FE54D947` FOREIGN KEY (`group_id`) REFERENCES `tipli_questions_group` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_redirections_redirection`;
CREATE TABLE `tipli_redirections_redirection` (
                                                  `id` int(11) NOT NULL AUTO_INCREMENT,
                                                  `localization_id` int(11) DEFAULT NULL,
                                                  `old_url` varchar(255) NOT NULL,
                                                  `new_url` varchar(255) NOT NULL,
                                                  `count_of_clicks` int(11) NOT NULL,
                                                  `created_at` datetime NOT NULL,
                                                  PRIMARY KEY (`id`),
                                                  UNIQUE KEY `old_url_unique` (`localization_id`,`old_url`),
                                                  KEY `IDX_CABCC0786A2856C7` (`localization_id`),
                                                  CONSTRAINT `FK_CABCC0786A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_refunds_refund`;
CREATE TABLE `tipli_refunds_refund` (
                                        `id` int(11) NOT NULL AUTO_INCREMENT,
                                        `localization_id` int(11) DEFAULT NULL,
                                        `user_id` int(11) DEFAULT NULL,
                                        `assigned_operator_id` int(11) DEFAULT NULL,
                                        `shop_id` int(11) DEFAULT NULL,
                                        `related_transaction_id` int(11) DEFAULT NULL,
                                        `refund_transaction_id` int(11) DEFAULT NULL,
                                        `freshdesk_ticket_id` int(11) DEFAULT NULL,
                                        `invoice_id` int(11) DEFAULT NULL,
                                        `second_invoice_id` int(11) DEFAULT NULL,
                                        `resolved_by_user_id` int(11) DEFAULT NULL,
                                        `resolve_checked_by_user_id` int(11) DEFAULT NULL,
                                        `unique_id` varchar(8) NOT NULL,
                                        `type` varchar(32) NOT NULL,
                                        `state` varchar(26) NOT NULL,
                                        `message` longtext DEFAULT NULL,
                                        `order_id` varchar(255) DEFAULT NULL,
                                        `purchased_at` datetime DEFAULT NULL,
                                        `order_amount` double DEFAULT NULL,
                                        `order_currency` varchar(3) DEFAULT NULL,
                                        `expected_amount` double DEFAULT NULL,
                                        `coupon_used` tinyint(1) DEFAULT NULL,
                                        `coupon_code` varchar(255) DEFAULT NULL,
                                        `product_urls` longtext DEFAULT NULL,
                                        `resolved_at` datetime DEFAULT NULL,
                                        `resolve_checked_at` datetime DEFAULT NULL,
                                        `resolve_check_state` varchar(8) DEFAULT NULL,
                                        `resolve_check_reason_type` varchar(26) DEFAULT NULL,
                                        `resolve_check_message` longtext DEFAULT NULL,
                                        `shop_response_message` longtext DEFAULT NULL,
                                        `decline_reason_type` varchar(26) DEFAULT NULL,
                                        `decline_reason_message` varchar(255) DEFAULT NULL,
                                        `process_at` datetime DEFAULT NULL,
                                        `process_error_message` varchar(255) DEFAULT NULL,
                                        `user_last_response_at` datetime DEFAULT NULL,
                                        `operator_last_response_at` datetime DEFAULT NULL,
                                        `platform` varchar(8) DEFAULT NULL,
                                        `addon_used` tinyint(1) DEFAULT NULL,
                                        `mobile_app_used` tinyint(1) DEFAULT NULL,
                                        `created_at` datetime NOT NULL,
                                        PRIMARY KEY (`id`),
                                        KEY `IDX_A94154AD6A2856C7` (`localization_id`),
                                        KEY `IDX_A94154ADA76ED395` (`user_id`),
                                        KEY `IDX_A94154AD7F7F786A` (`assigned_operator_id`),
                                        KEY `IDX_A94154AD4D16C4DD` (`shop_id`),
                                        KEY `IDX_A94154AD4F981710` (`related_transaction_id`),
                                        KEY `IDX_A94154ADA99A014E` (`refund_transaction_id`),
                                        KEY `IDX_A94154AD642C3738` (`freshdesk_ticket_id`),
                                        KEY `IDX_A94154AD2989F1FD` (`invoice_id`),
                                        KEY `IDX_A94154ADCAD7116E` (`second_invoice_id`),
                                        KEY `IDX_A94154ADAC78F73B` (`resolved_by_user_id`),
                                        KEY `IDX_A94154AD47D8ED83` (`resolve_checked_by_user_id`),
                                        CONSTRAINT `FK_A94154AD2989F1FD` FOREIGN KEY (`invoice_id`) REFERENCES `tipli_files_file` (`id`),
                                        CONSTRAINT `FK_A94154AD47D8ED83` FOREIGN KEY (`resolve_checked_by_user_id`) REFERENCES `tipli_account_user` (`id`),
                                        CONSTRAINT `FK_A94154AD4D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                        CONSTRAINT `FK_A94154AD4F981710` FOREIGN KEY (`related_transaction_id`) REFERENCES `tipli_transactions_transaction` (`id`),
                                        CONSTRAINT `FK_A94154AD642C3738` FOREIGN KEY (`freshdesk_ticket_id`) REFERENCES `tipli_freshdesk_ticket` (`id`),
                                        CONSTRAINT `FK_A94154AD6A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                        CONSTRAINT `FK_A94154AD7F7F786A` FOREIGN KEY (`assigned_operator_id`) REFERENCES `tipli_account_user` (`id`),
                                        CONSTRAINT `FK_A94154ADA76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`),
                                        CONSTRAINT `FK_A94154ADA99A014E` FOREIGN KEY (`refund_transaction_id`) REFERENCES `tipli_transactions_transaction` (`id`),
                                        CONSTRAINT `FK_A94154ADAC78F73B` FOREIGN KEY (`resolved_by_user_id`) REFERENCES `tipli_account_user` (`id`),
                                        CONSTRAINT `FK_A94154ADCAD7116E` FOREIGN KEY (`second_invoice_id`) REFERENCES `tipli_files_file` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_refunds_refund_comment`;
CREATE TABLE `tipli_refunds_refund_comment` (
                                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                                `refund_id` int(11) DEFAULT NULL,
                                                `user_id` int(11) DEFAULT NULL,
                                                `message` longtext NOT NULL,
                                                `created_at` datetime NOT NULL,
                                                PRIMARY KEY (`id`),
                                                KEY `IDX_BFDDA8C6189801D5` (`refund_id`),
                                                KEY `IDX_BFDDA8C6A76ED395` (`user_id`),
                                                CONSTRAINT `FK_BFDDA8C6189801D5` FOREIGN KEY (`refund_id`) REFERENCES `tipli_refunds_refund` (`id`),
                                                CONSTRAINT `FK_BFDDA8C6A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_refunds_refund_process`;
CREATE TABLE `tipli_refunds_refund_process` (
                                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                                `refund_id` int(11) DEFAULT NULL,
                                                `process_result` varchar(16) NOT NULL,
                                                `bonus_amount` double NOT NULL,
                                                `reason_types` longtext DEFAULT NULL,
                                                `reason_messages` longtext DEFAULT NULL,
                                                `user_message` longtext DEFAULT NULL,
                                                `created_at` datetime NOT NULL,
                                                PRIMARY KEY (`id`),
                                                KEY `IDX_ADB4E23C189801D5` (`refund_id`),
                                                CONSTRAINT `FK_ADB4E23C189801D5` FOREIGN KEY (`refund_id`) REFERENCES `tipli_refunds_refund` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_refunds_refund_solution`;
CREATE TABLE `tipli_refunds_refund_solution` (
                                                 `id` int(11) NOT NULL AUTO_INCREMENT,
                                                 `refund_id` int(11) DEFAULT NULL,
                                                 `user_id` int(11) DEFAULT NULL,
                                                 `exported_at` datetime DEFAULT NULL,
                                                 `state` varchar(255) DEFAULT NULL,
                                                 `note` varchar(255) DEFAULT NULL,
                                                 `reason` varchar(255) DEFAULT NULL,
                                                 `state_updated_at` datetime DEFAULT NULL,
                                                 `in_affiliate_revised_at` datetime DEFAULT NULL,
                                                 `created_at` datetime NOT NULL,
                                                 PRIMARY KEY (`id`),
                                                 KEY `IDX_A91BCAD7189801D5` (`refund_id`),
                                                 KEY `IDX_A91BCAD7A76ED395` (`user_id`),
                                                 CONSTRAINT `FK_A91BCAD7189801D5` FOREIGN KEY (`refund_id`) REFERENCES `tipli_refunds_refund` (`id`),
                                                 CONSTRAINT `FK_A91BCAD7A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_regions_region`;
CREATE TABLE `tipli_regions_region` (
                                        `id` int(11) NOT NULL AUTO_INCREMENT,
                                        `localization_id` int(11) DEFAULT NULL,
                                        `parent_region_id` int(11) DEFAULT NULL,
                                        `name` varchar(255) DEFAULT NULL,
                                        `slug` varchar(255) DEFAULT NULL,
                                        `type` varchar(255) DEFAULT NULL,
                                        `lat` varchar(255) DEFAULT NULL,
                                        `lng` varchar(255) DEFAULT NULL,
                                        `created_at` datetime NOT NULL,
                                        PRIMARY KEY (`id`),
                                        UNIQUE KEY `slug_unique` (`localization_id`,`slug`),
                                        KEY `IDX_B8AA4A406A2856C7` (`localization_id`),
                                        KEY `IDX_B8AA4A40696D2EBB` (`parent_region_id`),
                                        KEY `type_idx` (`type`),
                                        CONSTRAINT `FK_B8AA4A40696D2EBB` FOREIGN KEY (`parent_region_id`) REFERENCES `tipli_regions_region` (`id`),
                                        CONSTRAINT `FK_B8AA4A406A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_reports_aggregated_metric`;
CREATE TABLE `tipli_reports_aggregated_metric` (
                                                   `id` int(11) NOT NULL AUTO_INCREMENT,
                                                   `localization_id` int(11) DEFAULT NULL,
                                                   `interval_name` varchar(1) DEFAULT NULL,
                                                   `started_at` datetime NOT NULL,
                                                   `ended_at` datetime NOT NULL,
                                                   `commission_amount` decimal(16,3) NOT NULL,
                                                   `confirmed_commission_amount` decimal(16,3) NOT NULL,
                                                   `unconfirmed_commission_amount` decimal(16,3) NOT NULL,
                                                   `user_commission_amount` decimal(16,3) NOT NULL,
                                                   `bonus_amount` decimal(16,3) NOT NULL,
                                                   `confirmed_bonus_amount` decimal(16,3) NOT NULL,
                                                   `confirmed_bonus_czk` decimal(16,3) NOT NULL,
                                                   `turnover` decimal(16,3) NOT NULL,
                                                   `confirmed_turnover` decimal(16,3) NOT NULL,
                                                   `activated_users_confirmed_turnover` decimal(16,3) NOT NULL,
                                                   `activated_users_turnover` decimal(16,3) NOT NULL,
                                                   `original_turnover` decimal(16,3) NOT NULL,
                                                   `income` decimal(16,3) NOT NULL,
                                                   `confirmation_prediction_coefficient` double NOT NULL,
                                                   `share_coefficient` double NOT NULL,
                                                   `payouts_paid_commissions_amount` decimal(16,3) NOT NULL,
                                                   `payouts_paid_bonuses_amount` decimal(16,3) NOT NULL,
                                                   `count_of_payouts` int(11) NOT NULL,
                                                   `count_of_declined_payouts` int(11) NOT NULL,
                                                   `count_of_redirections` int(11) NOT NULL,
                                                   `count_of_unique_users_in_payouts` int(11) NOT NULL,
                                                   `average_payout_solution_time` int(11) NOT NULL,
                                                   `percentile_payout_solution_time` int(11) NOT NULL,
                                                   `payouts_commission_amount` decimal(16,3) NOT NULL,
                                                   `payouts_bonus_amount` decimal(16,3) NOT NULL,
                                                   `count_of_redirections_mobile_app` int(11) NOT NULL,
                                                   `count_of_redirections_mobile_browser` int(11) NOT NULL,
                                                   `count_of_redirections_addon` int(11) NOT NULL,
                                                   `count_of_redirections_host` int(11) NOT NULL,
                                                   `count_of_transactions` int(11) NOT NULL,
                                                   `count_of_transactions_mobile_app` int(11) NOT NULL,
                                                   `count_of_transactions_mobile_browser` int(11) NOT NULL,
                                                   `count_of_transactions_addon` int(11) NOT NULL,
                                                   `count_of_active_users` int(11) NOT NULL,
                                                   `count_of_active_users_tipli` int(11) NOT NULL,
                                                   `count_of_users` int(11) NOT NULL,
                                                   `count_of_users_tipli` int(11) NOT NULL,
                                                   `count_of_users_referral` int(11) NOT NULL,
                                                   `count_of_activations` int(11) NOT NULL,
                                                   `count_of_activations_referral` int(11) NOT NULL,
                                                   `count_of_activations_tipli` int(11) NOT NULL,
                                                   `count_of_activations_in2days` int(11) NOT NULL,
                                                   `count_of_activations_in7days` int(11) NOT NULL,
                                                   `count_of_reactivations` int(11) NOT NULL,
                                                   `count_of_unique_parent_referral_users` int(11) NOT NULL,
                                                   `count_of_deals` int(11) NOT NULL,
                                                   `count_of_purchases` int(11) NOT NULL,
                                                   `count_of_addon_installs` int(11) NOT NULL,
                                                   `count_of_users_using_addon` int(11) NOT NULL,
                                                   `count_of_mobile_app_installs` int(11) NOT NULL,
                                                   `count_of_active_users_mobile_app` int(11) NOT NULL,
                                                   `count_of_mobile_app_interactions` int(11) NOT NULL,
                                                   `count_of_users_using_mobile_app` int(11) NOT NULL,
                                                   `count_of_transactions_registered_under_minute` int(11) NOT NULL,
                                                   `count_of_transactions_registered_under_five_minutes` int(11) NOT NULL,
                                                   `count_of_transactions_registered_under_two_hours` int(11) NOT NULL,
                                                   `count_of_users_web` int(11) NOT NULL,
                                                   `count_of_users_ios` int(11) NOT NULL,
                                                   `count_of_users_android` int(11) NOT NULL,
                                                   `host_turnover` decimal(16,3) NOT NULL,
                                                   `count_of_refunds` int(11) NOT NULL,
                                                   `count_of_refunds_missing_commission` int(11) NOT NULL,
                                                   `count_of_refunds_missing_commission_tablet` int(11) NOT NULL,
                                                   `count_of_refunds_missing_commission_mobile` int(11) NOT NULL,
                                                   `count_of_refunds_missing_commission_mobile_app` int(11) NOT NULL,
                                                   `count_of_refund_transactions` int(11) NOT NULL,
                                                   `count_of_declined_refunds` int(11) NOT NULL,
                                                   `count_of_unique_users_in_refunds` int(11) NOT NULL,
                                                   `refund_bonus_czk` int(11) NOT NULL,
                                                   `average_refund_solution_time` int(11) NOT NULL,
                                                   `partner_systems_income_requested` decimal(16,3) NOT NULL,
                                                   `partner_systems_income_received` decimal(16,3) NOT NULL,
                                                   `created_at` datetime NOT NULL,
                                                   PRIMARY KEY (`id`),
                                                   KEY `IDX_932185CE6A2856C7` (`localization_id`),
                                                   CONSTRAINT `FK_932185CE6A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_reports_apify_task`;
CREATE TABLE `tipli_reports_apify_task` (
                                            `id` int(11) NOT NULL AUTO_INCREMENT,
                                            `task_id` varchar(255) NOT NULL,
                                            `run_id` varchar(255) NOT NULL,
                                            `name` varchar(255) NOT NULL,
                                            `origin` varchar(255) NOT NULL,
                                            `status` varchar(255) NOT NULL,
                                            `computed_units` decimal(6,4) NOT NULL,
                                            `started_at` datetime NOT NULL,
                                            `finished_at` datetime NOT NULL,
                                            `created_at` datetime NOT NULL,
                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_reports_business_plan`;
CREATE TABLE `tipli_reports_business_plan` (
                                               `id` int(11) NOT NULL AUTO_INCREMENT,
                                               `localization_id` int(11) DEFAULT NULL,
                                               `interval_name` varchar(1) NOT NULL,
                                               `started_at` datetime NOT NULL,
                                               `ended_at` datetime NOT NULL,
                                               `cashback_estimation` double DEFAULT NULL,
                                               `leaflets_turnover` double DEFAULT NULL,
                                               `leaflets_income` double DEFAULT NULL,
                                               `leaflets_estimation` double DEFAULT NULL,
                                               `coupons_turnover` double DEFAULT NULL,
                                               `coupons_income` double DEFAULT NULL,
                                               `coupons_estimation` double DEFAULT NULL,
                                               `capex_marketing_costs` double DEFAULT NULL,
                                               `opex_other_turnover` double DEFAULT NULL,
                                               `opex_other_costs` double DEFAULT NULL,
                                               `opex_it_costs` double DEFAULT NULL,
                                               `opex_finances_costs` double DEFAULT NULL,
                                               `opex_employees_costs` double DEFAULT NULL,
                                               `created_at` datetime NOT NULL,
                                               PRIMARY KEY (`id`),
                                               KEY `IDX_2FC44D8E6A2856C7` (`localization_id`),
                                               CONSTRAINT `FK_2FC44D8E6A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_reports_campaign_metric`;
CREATE TABLE `tipli_reports_campaign_metric` (
                                                 `id` int(11) NOT NULL AUTO_INCREMENT,
                                                 `localization_id` int(11) DEFAULT NULL,
                                                 `interval_name` varchar(1) DEFAULT NULL,
                                                 `started_at` datetime NOT NULL,
                                                 `ended_at` datetime NOT NULL,
                                                 `utm_source` varchar(255) NOT NULL,
                                                 `utm_medium` varchar(255) DEFAULT NULL,
                                                 `utm_campaign` varchar(255) DEFAULT NULL,
                                                 `count_of_registered_users` int(11) NOT NULL,
                                                 `count_of_active_users` int(11) NOT NULL,
                                                 `count_of_active_users_in1day` int(11) NOT NULL,
                                                 `count_of_active_users_in1to3days` int(11) NOT NULL,
                                                 `count_of_active_users_in3to7days` int(11) NOT NULL,
                                                 `count_of_active_users_in7to14days` int(11) NOT NULL,
                                                 `count_of_active_users_in14to30days` int(11) NOT NULL,
                                                 `count_of_active_users_in30to90days` int(11) NOT NULL,
                                                 `count_of_active_users_after90days` int(11) NOT NULL,
                                                 `cost` double DEFAULT NULL,
                                                 `last_refreshed_at` datetime DEFAULT NULL,
                                                 `refresh_at` datetime DEFAULT NULL,
                                                 `cost_processed_at` datetime DEFAULT NULL,
                                                 `created_at` datetime NOT NULL,
                                                 PRIMARY KEY (`id`),
                                                 KEY `IDX_CA70ED056A2856C7` (`localization_id`),
                                                 CONSTRAINT `FK_CA70ED056A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_reports_data_snapshot`;
CREATE TABLE `tipli_reports_data_snapshot` (
                                               `id` int(11) NOT NULL AUTO_INCREMENT,
                                               `count_of_active_verified_users` int(11) NOT NULL,
                                               `count_of_active_users` int(11) NOT NULL,
                                               `count_of_users_subscribed_notification` int(11) NOT NULL,
                                               `count_of_users_subscribed_newsletter` int(11) NOT NULL,
                                               `count_of_users_subscribed_autoresponder` int(11) NOT NULL,
                                               `count_of_users_subscribed_sms` int(11) NOT NULL,
                                               `count_of_users_unsubscribed_notification` int(11) NOT NULL,
                                               `count_of_users_unsubscribed_newsletter` int(11) NOT NULL,
                                               `count_of_users_unsubscribed_autoresponder` int(11) NOT NULL,
                                               `count_of_users_unsubscribed_sms` int(11) NOT NULL,
                                               `count_of_unsubscribed_users` int(11) NOT NULL,
                                               `count_white_label_users` int(11) NOT NULL,
                                               `updated_at` datetime NOT NULL,
                                               `created_at` datetime NOT NULL,
                                               PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_reports_keyword`;
CREATE TABLE `tipli_reports_keyword` (
                                         `id` int(11) NOT NULL AUTO_INCREMENT,
                                         `localization_id` int(11) DEFAULT NULL,
                                         `keyword` varchar(255) NOT NULL,
                                         `tipli` int(11) DEFAULT NULL,
                                         `vasekupony` int(11) DEFAULT NULL,
                                         `picodi` int(11) DEFAULT NULL,
                                         `tipli_previous` int(11) DEFAULT NULL,
                                         `vasekupony_previous` int(11) DEFAULT NULL,
                                         `picodi_previous` int(11) DEFAULT NULL,
                                         `tipli_traffic` int(11) DEFAULT NULL,
                                         `vasekupony_traffic` int(11) DEFAULT NULL,
                                         `picodi_traffic` int(11) DEFAULT NULL,
                                         `volume` int(11) NOT NULL,
                                         `tipli_url` varchar(255) DEFAULT NULL,
                                         `picodi_url` varchar(255) DEFAULT NULL,
                                         `vasekupony_url` varchar(255) DEFAULT NULL,
                                         `updated_at` datetime NOT NULL,
                                         `created_at` datetime NOT NULL,
                                         PRIMARY KEY (`id`),
                                         KEY `IDX_E639B1966A2856C7` (`localization_id`),
                                         CONSTRAINT `FK_E639B1966A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_reports_keyword_position`;
CREATE TABLE `tipli_reports_keyword_position` (
                                                  `id` int(11) NOT NULL AUTO_INCREMENT,
                                                  `keyword_id` int(11) DEFAULT NULL,
                                                  `position` int(11) NOT NULL,
                                                  `company` varchar(255) NOT NULL,
                                                  `created_at` datetime NOT NULL,
                                                  PRIMARY KEY (`id`),
                                                  KEY `IDX_36D95406115D4552` (`keyword_id`),
                                                  CONSTRAINT `FK_36D95406115D4552` FOREIGN KEY (`keyword_id`) REFERENCES `tipli_reports_keyword` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_reports_metric`;
CREATE TABLE `tipli_reports_metric` (
                                        `id` int(11) NOT NULL AUTO_INCREMENT,
                                        `name` varchar(255) NOT NULL,
                                        `description` varchar(255) DEFAULT NULL,
                                        `sql_global` longtext DEFAULT NULL,
                                        `sql_localization` longtext DEFAULT NULL,
                                        `sql_partner_system` longtext DEFAULT NULL,
                                        `sql_partner_system_type` longtext DEFAULT NULL,
                                        `sql_shop` longtext DEFAULT NULL,
                                        `is_snapshot` tinyint(1) NOT NULL,
                                        `created_at` datetime NOT NULL,
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_reports_metric_value`;
CREATE TABLE `tipli_reports_metric_value` (
                                              `id` int(11) NOT NULL AUTO_INCREMENT,
                                              `metric_id` int(11) DEFAULT NULL,
                                              `localization_id` int(11) DEFAULT NULL,
                                              `shop_id` int(11) DEFAULT NULL,
                                              `partner_system_id` int(11) DEFAULT NULL,
                                              `unique_id` varchar(16) NOT NULL,
                                              `name` varchar(255) NOT NULL,
                                              `partner_system_type` varchar(255) DEFAULT NULL,
                                              `interval_name` varchar(1) NOT NULL,
                                              `started_at` datetime NOT NULL,
                                              `ended_at` datetime NOT NULL,
                                              `value` decimal(16,3) DEFAULT NULL,
                                              `execution_time` int(11) DEFAULT NULL,
                                              `last_refreshed_at` datetime DEFAULT NULL,
                                              `refresh_at` datetime DEFAULT NULL,
                                              `created_at` datetime NOT NULL,
                                              PRIMARY KEY (`id`),
                                              UNIQUE KEY `unique_metric` (`unique_id`),
                                              KEY `IDX_FC4E2B82A952D583` (`metric_id`),
                                              KEY `IDX_FC4E2B826A2856C7` (`localization_id`),
                                              KEY `IDX_FC4E2B824D16C4DD` (`shop_id`),
                                              KEY `IDX_FC4E2B82FB42B4B1` (`partner_system_id`),
                                              CONSTRAINT `FK_FC4E2B824D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                              CONSTRAINT `FK_FC4E2B826A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                              CONSTRAINT `FK_FC4E2B82A952D583` FOREIGN KEY (`metric_id`) REFERENCES `tipli_reports_metric` (`id`),
                                              CONSTRAINT `FK_FC4E2B82FB42B4B1` FOREIGN KEY (`partner_system_id`) REFERENCES `tipli_partner_systems_partner_system` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_reports_report_check`;
CREATE TABLE `tipli_reports_report_check` (
                                              `id` int(11) NOT NULL AUTO_INCREMENT,
                                              `shop_id` int(11) DEFAULT NULL,
                                              `partner_system_id` int(11) DEFAULT NULL,
                                              `checked_by_user_id` int(11) DEFAULT NULL,
                                              `partner_system_type` varchar(255) DEFAULT NULL,
                                              `checked_at` datetime DEFAULT NULL,
                                              `note` longtext DEFAULT NULL,
                                              PRIMARY KEY (`id`),
                                              KEY `IDX_74F727494D16C4DD` (`shop_id`),
                                              KEY `IDX_74F72749FB42B4B1` (`partner_system_id`),
                                              KEY `IDX_74F72749C0C552BA` (`checked_by_user_id`),
                                              CONSTRAINT `FK_74F727494D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                              CONSTRAINT `FK_74F72749C0C552BA` FOREIGN KEY (`checked_by_user_id`) REFERENCES `tipli_account_user` (`id`),
                                              CONSTRAINT `FK_74F72749FB42B4B1` FOREIGN KEY (`partner_system_id`) REFERENCES `tipli_partner_systems_partner_system` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_reviews_review`;
CREATE TABLE `tipli_reviews_review` (
                                        `id` int(11) NOT NULL AUTO_INCREMENT,
                                        `localization_id` int(11) DEFAULT NULL,
                                        `shop_id` int(11) DEFAULT NULL,
                                        `user_id` int(11) DEFAULT NULL,
                                        `user_picture_id` int(11) DEFAULT NULL,
                                        `unique_id` varchar(255) DEFAULT NULL,
                                        `user_name` varchar(255) NOT NULL,
                                        `user_picture_allowed` tinyint(1) NOT NULL,
                                        `rate` int(11) NOT NULL,
                                        `text` longtext DEFAULT NULL,
                                        `note` longtext DEFAULT NULL,
                                        `platform` varchar(7) DEFAULT NULL,
                                        `state` varchar(20) DEFAULT NULL,
                                        `ip` varchar(20) DEFAULT NULL,
                                        `user_agent` varchar(255) DEFAULT NULL,
                                        `campaign_id` varchar(255) DEFAULT NULL,
                                        `approved_at` datetime DEFAULT NULL,
                                        `prioritized_at` datetime DEFAULT NULL,
                                        `created_at` datetime NOT NULL,
                                        `deleted_at` datetime DEFAULT NULL,
                                        PRIMARY KEY (`id`),
                                        KEY `IDX_4E77FF806A2856C7` (`localization_id`),
                                        KEY `IDX_4E77FF804D16C4DD` (`shop_id`),
                                        KEY `IDX_4E77FF80A76ED395` (`user_id`),
                                        KEY `IDX_4E77FF8049227B53` (`user_picture_id`),
                                        CONSTRAINT `FK_4E77FF8049227B53` FOREIGN KEY (`user_picture_id`) REFERENCES `tipli_images_image` (`id`),
                                        CONSTRAINT `FK_4E77FF804D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                        CONSTRAINT `FK_4E77FF806A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                        CONSTRAINT `FK_4E77FF80A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_reviews_review_request`;
CREATE TABLE `tipli_reviews_review_request` (
                                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                                `localization_id` int(11) DEFAULT NULL,
                                                `review_id` int(11) DEFAULT NULL,
                                                `user_id` int(11) DEFAULT NULL,
                                                `payout_id` int(11) DEFAULT NULL,
                                                `refund_id` int(11) DEFAULT NULL,
                                                `ticket_id` int(11) DEFAULT NULL,
                                                `unique_id` varchar(16) NOT NULL,
                                                `trigger_name` varchar(16) NOT NULL,
                                                `state` varchar(32) NOT NULL,
                                                `scheduled_at` datetime DEFAULT NULL,
                                                `reviewed_at` datetime DEFAULT NULL,
                                                `review_request_sent_at` datetime DEFAULT NULL,
                                                `review_request_response_sent_at` datetime DEFAULT NULL,
                                                `suspended_at` datetime DEFAULT NULL,
                                                `created_at` datetime NOT NULL,
                                                PRIMARY KEY (`id`),
                                                UNIQUE KEY `UNIQ_7F367A59C6D61B7F` (`payout_id`),
                                                UNIQUE KEY `UNIQ_7F367A59189801D5` (`refund_id`),
                                                UNIQUE KEY `UNIQ_7F367A59700047D2` (`ticket_id`),
                                                KEY `IDX_7F367A596A2856C7` (`localization_id`),
                                                KEY `IDX_7F367A593E2E969B` (`review_id`),
                                                KEY `IDX_7F367A59A76ED395` (`user_id`),
                                                CONSTRAINT `FK_7F367A59189801D5` FOREIGN KEY (`refund_id`) REFERENCES `tipli_refunds_refund` (`id`),
                                                CONSTRAINT `FK_7F367A593E2E969B` FOREIGN KEY (`review_id`) REFERENCES `tipli_reviews_review` (`id`),
                                                CONSTRAINT `FK_7F367A596A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                                CONSTRAINT `FK_7F367A59700047D2` FOREIGN KEY (`ticket_id`) REFERENCES `tipli_freshdesk_ticket` (`id`),
                                                CONSTRAINT `FK_7F367A59A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`),
                                                CONSTRAINT `FK_7F367A59C6D61B7F` FOREIGN KEY (`payout_id`) REFERENCES `tipli_payouts_payout` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_rewards2_reward_campaign`;
CREATE TABLE `tipli_rewards2_reward_campaign` (
                                                  `id` int(11) NOT NULL AUTO_INCREMENT,
                                                  `localization_id` int(11) DEFAULT NULL,
                                                  `condition_id` int(11) DEFAULT NULL,
                                                  `image_id` int(11) DEFAULT NULL,
                                                  `name` varchar(255) NOT NULL,
                                                  `description` longtext NOT NULL,
                                                  `code` varchar(255) DEFAULT NULL,
                                                  `maximum_count_of_activations` int(11) NOT NULL,
                                                  `count_of_activations` int(11) NOT NULL,
                                                  `segment` varchar(16) DEFAULT NULL,
                                                  `able_to_use_till` datetime DEFAULT NULL,
                                                  `able_to_use_in_days` int(11) DEFAULT NULL,
                                                  `activate_after_assign` tinyint(1) NOT NULL,
                                                  `reward_bonus_amount` double DEFAULT NULL,
                                                  `reward_coefficient` double DEFAULT NULL,
                                                  `valid_since` datetime NOT NULL,
                                                  `valid_till` datetime NOT NULL,
                                                  `created_at` datetime NOT NULL,
                                                  PRIMARY KEY (`id`),
                                                  KEY `IDX_F4D3B8A66A2856C7` (`localization_id`),
                                                  KEY `IDX_F4D3B8A6887793B6` (`condition_id`),
                                                  KEY `IDX_F4D3B8A63DA5256D` (`image_id`),
                                                  KEY `code_idx` (`code`),
                                                  KEY `valid_since_idx` (`valid_since`),
                                                  KEY `valid_till_idx` (`valid_till`),
                                                  CONSTRAINT `FK_F4D3B8A63DA5256D` FOREIGN KEY (`image_id`) REFERENCES `tipli_images_image` (`id`),
                                                  CONSTRAINT `FK_F4D3B8A66A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                                  CONSTRAINT `FK_F4D3B8A6887793B6` FOREIGN KEY (`condition_id`) REFERENCES `tipli_rewards2_reward_campaign_condition` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_rewards2_reward_campaign_condition`;
CREATE TABLE `tipli_rewards2_reward_campaign_condition` (
                                                            `id` int(11) NOT NULL AUTO_INCREMENT,
                                                            `type` varchar(32) NOT NULL,
                                                            `minimal_confirmed_balance` double DEFAULT NULL,
                                                            PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_rewards2_reward_campaign_condition_excluded_shops`;
CREATE TABLE `tipli_rewards2_reward_campaign_condition_excluded_shops` (
                                                                           `reward_campaign_condition_id` int(11) NOT NULL,
                                                                           `shop_id` int(11) NOT NULL,
                                                                           PRIMARY KEY (`reward_campaign_condition_id`,`shop_id`),
                                                                           KEY `IDX_242626B417DC72BC` (`reward_campaign_condition_id`),
                                                                           KEY `IDX_242626B44D16C4DD` (`shop_id`),
                                                                           CONSTRAINT `FK_242626B417DC72BC` FOREIGN KEY (`reward_campaign_condition_id`) REFERENCES `tipli_rewards2_reward_campaign_condition` (`id`) ON DELETE CASCADE,
                                                                           CONSTRAINT `FK_242626B44D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_rewards2_reward_campaign_condition_shops`;
CREATE TABLE `tipli_rewards2_reward_campaign_condition_shops` (
                                                                  `reward_campaign_condition_id` int(11) NOT NULL,
                                                                  `shop_id` int(11) NOT NULL,
                                                                  PRIMARY KEY (`reward_campaign_condition_id`,`shop_id`),
                                                                  KEY `IDX_45BE344517DC72BC` (`reward_campaign_condition_id`),
                                                                  KEY `IDX_45BE34454D16C4DD` (`shop_id`),
                                                                  CONSTRAINT `FK_45BE344517DC72BC` FOREIGN KEY (`reward_campaign_condition_id`) REFERENCES `tipli_rewards2_reward_campaign_condition` (`id`) ON DELETE CASCADE,
                                                                  CONSTRAINT `FK_45BE34454D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_rewards2_reward_campaign_condition_shop_tags`;
CREATE TABLE `tipli_rewards2_reward_campaign_condition_shop_tags` (
                                                                      `reward_campaign_condition_id` int(11) NOT NULL,
                                                                      `tag_id` int(11) NOT NULL,
                                                                      PRIMARY KEY (`reward_campaign_condition_id`,`tag_id`),
                                                                      KEY `IDX_98D5854917DC72BC` (`reward_campaign_condition_id`),
                                                                      KEY `IDX_98D58549BAD26311` (`tag_id`),
                                                                      CONSTRAINT `FK_98D5854917DC72BC` FOREIGN KEY (`reward_campaign_condition_id`) REFERENCES `tipli_rewards2_reward_campaign_condition` (`id`) ON DELETE CASCADE,
                                                                      CONSTRAINT `FK_98D58549BAD26311` FOREIGN KEY (`tag_id`) REFERENCES `tipli_tags_tag` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_rewards2_reward_campaign_utms`;
CREATE TABLE `tipli_rewards2_reward_campaign_utms` (
                                                       `reward_campaign_id` int(11) NOT NULL,
                                                       `utm_id` int(11) NOT NULL,
                                                       PRIMARY KEY (`reward_campaign_id`,`utm_id`),
                                                       KEY `IDX_B299A9DAE3AC1AA6` (`reward_campaign_id`),
                                                       KEY `IDX_B299A9DAB6334822` (`utm_id`),
                                                       CONSTRAINT `FK_B299A9DAB6334822` FOREIGN KEY (`utm_id`) REFERENCES `tipli_utm_utm` (`id`) ON DELETE CASCADE,
                                                       CONSTRAINT `FK_B299A9DAE3AC1AA6` FOREIGN KEY (`reward_campaign_id`) REFERENCES `tipli_rewards2_reward_campaign` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_rewards2_user_reward`;
CREATE TABLE `tipli_rewards2_user_reward` (
                                              `id` int(11) NOT NULL AUTO_INCREMENT,
                                              `user_id` int(11) DEFAULT NULL,
                                              `reward_campaign_id` int(11) DEFAULT NULL,
                                              `transaction_id` int(11) DEFAULT NULL,
                                              `valid_till` datetime NOT NULL,
                                              `activated_at` datetime DEFAULT NULL,
                                              `used_at` datetime DEFAULT NULL,
                                              `expiration_notification_sent_at` datetime DEFAULT NULL,
                                              `created_at` datetime NOT NULL,
                                              PRIMARY KEY (`id`),
                                              KEY `IDX_BAEC802EA76ED395` (`user_id`),
                                              KEY `IDX_BAEC802EE3AC1AA6` (`reward_campaign_id`),
                                              KEY `IDX_BAEC802E2FC0CB0F` (`transaction_id`),
                                              CONSTRAINT `FK_BAEC802E2FC0CB0F` FOREIGN KEY (`transaction_id`) REFERENCES `tipli_transactions_transaction` (`id`),
                                              CONSTRAINT `FK_BAEC802EA76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`),
                                              CONSTRAINT `FK_BAEC802EE3AC1AA6` FOREIGN KEY (`reward_campaign_id`) REFERENCES `tipli_rewards2_reward_campaign` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_rewards_constant_reward`;
CREATE TABLE `tipli_rewards_constant_reward` (
                                                 `id` int(11) NOT NULL AUTO_INCREMENT,
                                                 `localization_id` int(11) DEFAULT NULL,
                                                 `parent_id` int(11) DEFAULT NULL,
                                                 `name` varchar(255) NOT NULL,
                                                 `type` varchar(255) NOT NULL,
                                                 `amount` double NOT NULL,
                                                 `currency` varchar(255) NOT NULL,
                                                 `confirmation_treshold` double NOT NULL,
                                                 `recommendation_bonus_treshold` double NOT NULL,
                                                 `valid_since` datetime NOT NULL,
                                                 `valid_till` datetime NOT NULL,
                                                 `created_at` datetime NOT NULL,
                                                 PRIMARY KEY (`id`),
                                                 KEY `IDX_D1AA78E96A2856C7` (`localization_id`),
                                                 KEY `IDX_D1AA78E9727ACA70` (`parent_id`),
                                                 KEY `type_idx` (`type`),
                                                 KEY `valid_since_idx` (`valid_since`),
                                                 KEY `valid_till_idx` (`valid_till`),
                                                 CONSTRAINT `FK_D1AA78E96A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                                 CONSTRAINT `FK_D1AA78E9727ACA70` FOREIGN KEY (`parent_id`) REFERENCES `tipli_rewards_constant_reward` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_rewards_money_reward`;
CREATE TABLE `tipli_rewards_money_reward` (
                                              `id` int(11) NOT NULL AUTO_INCREMENT,
                                              `money_reward_campaign_id` int(11) DEFAULT NULL,
                                              `user_id` int(11) DEFAULT NULL,
                                              `amount` double NOT NULL,
                                              `minimum_commission_balance` double NOT NULL,
                                              `used_at` datetime DEFAULT NULL,
                                              `valid_till` datetime NOT NULL,
                                              `created_at` datetime NOT NULL,
                                              PRIMARY KEY (`id`),
                                              KEY `IDX_C84218D4F9BFC74A` (`money_reward_campaign_id`),
                                              KEY `IDX_C84218D4A76ED395` (`user_id`),
                                              KEY `valid_till_idx` (`valid_till`),
                                              CONSTRAINT `FK_C84218D4A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`),
                                              CONSTRAINT `FK_C84218D4F9BFC74A` FOREIGN KEY (`money_reward_campaign_id`) REFERENCES `tipli_rewards_money_reward_campaign` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_rewards_money_reward_campaign`;
CREATE TABLE `tipli_rewards_money_reward_campaign` (
                                                       `id` int(11) NOT NULL AUTO_INCREMENT,
                                                       `name` varchar(255) NOT NULL,
                                                       `promo_id` varchar(255) DEFAULT NULL,
                                                       `goal` varchar(16) NOT NULL,
                                                       `amount` double NOT NULL,
                                                       `minimum_commission_balance` double NOT NULL,
                                                       `confirmation_treshold` double DEFAULT NULL,
                                                       `num_days_validity_money_reward` int(11) NOT NULL,
                                                       `note` varchar(255) DEFAULT NULL,
                                                       `created_at` datetime NOT NULL,
                                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_rewards_money_reward_campaign_shops`;
CREATE TABLE `tipli_rewards_money_reward_campaign_shops` (
                                                             `money_reward_campaign_id` int(11) NOT NULL,
                                                             `shop_id` int(11) NOT NULL,
                                                             PRIMARY KEY (`money_reward_campaign_id`,`shop_id`),
                                                             KEY `IDX_25F687DDF9BFC74A` (`money_reward_campaign_id`),
                                                             KEY `IDX_25F687DD4D16C4DD` (`shop_id`),
                                                             CONSTRAINT `FK_25F687DD4D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`) ON DELETE CASCADE,
                                                             CONSTRAINT `FK_25F687DDF9BFC74A` FOREIGN KEY (`money_reward_campaign_id`) REFERENCES `tipli_rewards_money_reward_campaign` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_rewards_reward_campaign`;
CREATE TABLE `tipli_rewards_reward_campaign` (
                                                 `id` int(11) NOT NULL AUTO_INCREMENT,
                                                 `localization_id` int(11) DEFAULT NULL,
                                                 `money_reward_campaign_id` int(11) DEFAULT NULL,
                                                 `share_reward_campaign_id` int(11) DEFAULT NULL,
                                                 `partner_organization_id` int(11) DEFAULT NULL,
                                                 `code` varchar(255) DEFAULT NULL,
                                                 `name` varchar(255) NOT NULL,
                                                 `valid_since` datetime NOT NULL,
                                                 `valid_till` datetime NOT NULL,
                                                 `only_new_users` tinyint(1) NOT NULL,
                                                 `only_inactive_users` tinyint(1) NOT NULL,
                                                 `note` varchar(255) DEFAULT NULL,
                                                 `options` longtext DEFAULT NULL,
                                                 `created_at` datetime NOT NULL,
                                                 PRIMARY KEY (`id`),
                                                 UNIQUE KEY `UNIQ_62C0F462F9BFC74A` (`money_reward_campaign_id`),
                                                 UNIQUE KEY `UNIQ_62C0F4625F34540D` (`share_reward_campaign_id`),
                                                 UNIQUE KEY `code_unique` (`localization_id`,`code`),
                                                 KEY `IDX_62C0F4626A2856C7` (`localization_id`),
                                                 KEY `IDX_62C0F46283C2DD73` (`partner_organization_id`),
                                                 KEY `valid_since_idx` (`valid_since`),
                                                 KEY `valid_till_idx` (`valid_till`),
                                                 CONSTRAINT `FK_62C0F4625F34540D` FOREIGN KEY (`share_reward_campaign_id`) REFERENCES `tipli_rewards_share_reward_campaign` (`id`),
                                                 CONSTRAINT `FK_62C0F4626A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                                 CONSTRAINT `FK_62C0F46283C2DD73` FOREIGN KEY (`partner_organization_id`) REFERENCES `tipli_partner_organizations_partner_organization` (`id`),
                                                 CONSTRAINT `FK_62C0F462F9BFC74A` FOREIGN KEY (`money_reward_campaign_id`) REFERENCES `tipli_rewards_money_reward_campaign` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_rewards_reward_campaign_data`;
CREATE TABLE `tipli_rewards_reward_campaign_data` (
                                                      `id` int(11) NOT NULL AUTO_INCREMENT,
                                                      `reward_campaign_id` int(11) DEFAULT NULL,
                                                      `partner_logo` int(11) DEFAULT NULL,
                                                      `partner_title` varchar(255) DEFAULT NULL,
                                                      `partner_description` longtext DEFAULT NULL,
                                                      PRIMARY KEY (`id`),
                                                      UNIQUE KEY `UNIQ_DC44B9C0E3AC1AA6` (`reward_campaign_id`),
                                                      KEY `IDX_DC44B9C0B4BAA90B` (`partner_logo`),
                                                      CONSTRAINT `FK_DC44B9C0B4BAA90B` FOREIGN KEY (`partner_logo`) REFERENCES `tipli_images_image` (`id`),
                                                      CONSTRAINT `FK_DC44B9C0E3AC1AA6` FOREIGN KEY (`reward_campaign_id`) REFERENCES `tipli_rewards_reward_campaign` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_rewards_reward_campaign_utms`;
CREATE TABLE `tipli_rewards_reward_campaign_utms` (
                                                      `reward_campaign_id` int(11) NOT NULL,
                                                      `utm_id` int(11) NOT NULL,
                                                      PRIMARY KEY (`reward_campaign_id`,`utm_id`),
                                                      KEY `IDX_46B530F1E3AC1AA6` (`reward_campaign_id`),
                                                      KEY `IDX_46B530F1B6334822` (`utm_id`),
                                                      CONSTRAINT `FK_46B530F1B6334822` FOREIGN KEY (`utm_id`) REFERENCES `tipli_utm_utm` (`id`) ON DELETE CASCADE,
                                                      CONSTRAINT `FK_46B530F1E3AC1AA6` FOREIGN KEY (`reward_campaign_id`) REFERENCES `tipli_rewards_reward_campaign` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_rewards_share_reward`;
CREATE TABLE `tipli_rewards_share_reward` (
                                              `id` int(11) NOT NULL AUTO_INCREMENT,
                                              `user_id` int(11) DEFAULT NULL,
                                              `shop_id` int(11) DEFAULT NULL,
                                              `share_reward_campaign_id` int(11) DEFAULT NULL,
                                              `name` varchar(255) DEFAULT NULL,
                                              `share_coefficient` double NOT NULL,
                                              `valid_since` datetime NOT NULL,
                                              `valid_till` datetime NOT NULL,
                                              `created_at` datetime NOT NULL,
                                              PRIMARY KEY (`id`),
                                              KEY `IDX_16EE63E4A76ED395` (`user_id`),
                                              KEY `IDX_16EE63E44D16C4DD` (`shop_id`),
                                              KEY `IDX_16EE63E45F34540D` (`share_reward_campaign_id`),
                                              KEY `valid_since_idx` (`valid_since`),
                                              KEY `valid_till_idx` (`valid_till`),
                                              CONSTRAINT `FK_16EE63E44D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                              CONSTRAINT `FK_16EE63E45F34540D` FOREIGN KEY (`share_reward_campaign_id`) REFERENCES `tipli_rewards_share_reward_campaign` (`id`),
                                              CONSTRAINT `FK_16EE63E4A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_rewards_share_reward_campaign`;
CREATE TABLE `tipli_rewards_share_reward_campaign` (
                                                       `id` int(11) NOT NULL AUTO_INCREMENT,
                                                       `name` varchar(255) NOT NULL,
                                                       `share_coefficient` double NOT NULL,
                                                       `num_days_validity_share_reward` int(11) NOT NULL,
                                                       `individual_shops` tinyint(1) NOT NULL,
                                                       `valid_till` datetime DEFAULT NULL,
                                                       `created_at` datetime NOT NULL,
                                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_rewards_share_reward_campaign_shops`;
CREATE TABLE `tipli_rewards_share_reward_campaign_shops` (
                                                             `share_reward_campaign_id` int(11) NOT NULL,
                                                             `shop_id` int(11) NOT NULL,
                                                             PRIMARY KEY (`share_reward_campaign_id`,`shop_id`),
                                                             KEY `IDX_70259F4F5F34540D` (`share_reward_campaign_id`),
                                                             KEY `IDX_70259F4F4D16C4DD` (`shop_id`),
                                                             CONSTRAINT `FK_70259F4F4D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`) ON DELETE CASCADE,
                                                             CONSTRAINT `FK_70259F4F5F34540D` FOREIGN KEY (`share_reward_campaign_id`) REFERENCES `tipli_rewards_share_reward_campaign` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_rewards_share_reward_shops`;
CREATE TABLE `tipli_rewards_share_reward_shops` (
                                                    `share_reward_id` int(11) NOT NULL,
                                                    `shop_id` int(11) NOT NULL,
                                                    PRIMARY KEY (`share_reward_id`,`shop_id`),
                                                    KEY `IDX_D40E36DFF91612D` (`share_reward_id`),
                                                    KEY `IDX_D40E36DF4D16C4DD` (`shop_id`),
                                                    CONSTRAINT `FK_D40E36DF4D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`) ON DELETE CASCADE,
                                                    CONSTRAINT `FK_D40E36DFF91612D` FOREIGN KEY (`share_reward_id`) REFERENCES `tipli_rewards_share_reward` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_risk_risk_case`;
CREATE TABLE `tipli_risk_risk_case` (
                                        `id` int(11) NOT NULL AUTO_INCREMENT,
                                        `user_id` int(11) DEFAULT NULL,
                                        `transaction_id` int(11) DEFAULT NULL,
                                        `created_by_user_id` int(11) DEFAULT NULL,
                                        `status` varchar(255) NOT NULL,
                                        `message` varchar(255) DEFAULT NULL,
                                        `remind_at` datetime DEFAULT NULL,
                                        `created_at` datetime NOT NULL,
                                        PRIMARY KEY (`id`),
                                        KEY `IDX_8D9F60E1A76ED395` (`user_id`),
                                        KEY `IDX_8D9F60E12FC0CB0F` (`transaction_id`),
                                        KEY `IDX_8D9F60E17D182D95` (`created_by_user_id`),
                                        CONSTRAINT `FK_8D9F60E12FC0CB0F` FOREIGN KEY (`transaction_id`) REFERENCES `tipli_transactions_transaction` (`id`),
                                        CONSTRAINT `FK_8D9F60E17D182D95` FOREIGN KEY (`created_by_user_id`) REFERENCES `tipli_account_user` (`id`),
                                        CONSTRAINT `FK_8D9F60E1A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_risk_risk_event`;
CREATE TABLE `tipli_risk_risk_event` (
                                         `id` int(11) NOT NULL AUTO_INCREMENT,
                                         `user_id` int(11) DEFAULT NULL,
                                         `created_by_user_id` int(11) DEFAULT NULL,
                                         `transaction_id` int(11) DEFAULT NULL,
                                         `type` varchar(255) NOT NULL,
                                         `message` varchar(255) DEFAULT NULL,
                                         `is_false_alert` tinyint(1) DEFAULT NULL,
                                         `created_at` datetime NOT NULL,
                                         PRIMARY KEY (`id`),
                                         KEY `IDX_EB3B8BA9A76ED395` (`user_id`),
                                         KEY `IDX_EB3B8BA97D182D95` (`created_by_user_id`),
                                         KEY `IDX_EB3B8BA92FC0CB0F` (`transaction_id`),
                                         CONSTRAINT `FK_EB3B8BA92FC0CB0F` FOREIGN KEY (`transaction_id`) REFERENCES `tipli_transactions_transaction` (`id`),
                                         CONSTRAINT `FK_EB3B8BA97D182D95` FOREIGN KEY (`created_by_user_id`) REFERENCES `tipli_account_user` (`id`),
                                         CONSTRAINT `FK_EB3B8BA9A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_risk_risk_policy`;
CREATE TABLE `tipli_risk_risk_policy` (
                                          `id` int(11) NOT NULL AUTO_INCREMENT,
                                          `shop_id` int(11) DEFAULT NULL,
                                          `partner_system_id` int(11) DEFAULT NULL,
                                          `risk_transaction_commission_amount_threshold` int(11) DEFAULT NULL,
                                          `minimal_percent_threshold` int(11) DEFAULT NULL,
                                          `is_high_risk` tinyint(1) NOT NULL,
                                          `created_at` datetime NOT NULL,
                                          PRIMARY KEY (`id`),
                                          KEY `IDX_1715BD904D16C4DD` (`shop_id`),
                                          KEY `IDX_1715BD90FB42B4B1` (`partner_system_id`),
                                          CONSTRAINT `FK_1715BD904D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                          CONSTRAINT `FK_1715BD90FB42B4B1` FOREIGN KEY (`partner_system_id`) REFERENCES `tipli_partner_systems_partner_system` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_rondo_transaction`;
CREATE TABLE `tipli_rondo_transaction` (
                                           `id` int(11) NOT NULL AUTO_INCREMENT,
                                           `transaction_id` int(11) DEFAULT NULL,
                                           `redirection_id` int(11) DEFAULT NULL,
                                           `sub_id` varchar(255) NOT NULL,
                                           PRIMARY KEY (`id`),
                                           UNIQUE KEY `transaction_id` (`transaction_id`),
                                           KEY `IDX_D8B94AB31DC0789A` (`redirection_id`),
                                           CONSTRAINT `FK_D8B94AB31DC0789A` FOREIGN KEY (`redirection_id`) REFERENCES `tipli_shops_redirection` (`id`),
                                           CONSTRAINT `FK_D8B94AB32FC0CB0F` FOREIGN KEY (`transaction_id`) REFERENCES `tipli_transactions_transaction` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_seo_keyword`;
CREATE TABLE `tipli_seo_keyword` (
                                     `id` int(11) NOT NULL AUTO_INCREMENT,
                                     `localization_id` int(11) DEFAULT NULL,
                                     `name` varchar(255) NOT NULL,
                                     `searches_google` int(11) DEFAULT NULL,
                                     `searches_seznam` int(11) DEFAULT NULL,
                                     `position_google` int(11) DEFAULT NULL,
                                     `position_seznam` int(11) DEFAULT NULL,
                                     `position_google_last_month` int(11) DEFAULT NULL,
                                     `position_seznam_last_month` int(11) DEFAULT NULL,
                                     `important` tinyint(1) NOT NULL,
                                     `processed_at` datetime DEFAULT NULL,
                                     `failed_at` datetime DEFAULT NULL,
                                     `created_at` datetime NOT NULL,
                                     PRIMARY KEY (`id`),
                                     UNIQUE KEY `key_unique` (`name`,`localization_id`),
                                     KEY `IDX_C5B9B0A76A2856C7` (`localization_id`),
                                     CONSTRAINT `FK_C5B9B0A76A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_seo_page_extension`;
CREATE TABLE `tipli_seo_page_extension` (
                                            `id` int(11) NOT NULL AUTO_INCREMENT,
                                            `localization_id` int(11) DEFAULT NULL,
                                            `checked_by` int(11) DEFAULT NULL,
                                            `content_revised_by` int(11) DEFAULT NULL,
                                            `note` varchar(255) DEFAULT NULL,
                                            `page_type` varchar(255) DEFAULT NULL,
                                            `page` varchar(255) NOT NULL,
                                            `data` longtext DEFAULT NULL,
                                            `meta_title` longtext DEFAULT NULL,
                                            `meta_description` longtext DEFAULT NULL,
                                            `meta_keywords` longtext DEFAULT NULL,
                                            `override` tinyint(1) NOT NULL,
                                            `heading` varchar(255) DEFAULT NULL,
                                            `sub_heading` varchar(255) DEFAULT NULL,
                                            `bottom_title` varchar(255) DEFAULT NULL,
                                            `top_description` longtext DEFAULT NULL,
                                            `middle_description` longtext DEFAULT NULL,
                                            `bottom_description` longtext DEFAULT NULL,
                                            `priority` int(11) DEFAULT NULL,
                                            `active` tinyint(1) NOT NULL,
                                            `adsense_allowed` tinyint(1) NOT NULL,
                                            `organic_last4days` int(11) DEFAULT NULL,
                                            `organic_last4days_period` int(11) DEFAULT NULL,
                                            `organic_last14days` int(11) DEFAULT NULL,
                                            `organic_last14days_period` int(11) DEFAULT NULL,
                                            `organic_last_month` int(11) DEFAULT NULL,
                                            `organic_last_month_period` int(11) DEFAULT NULL,
                                            `organic_last_year` int(11) DEFAULT NULL,
                                            `organic_last_year_period` int(11) DEFAULT NULL,
                                            `keyword_check_data` varchar(4096) DEFAULT NULL,
                                            `keyword_check_count` int(11) DEFAULT NULL,
                                            `local_link_count` int(11) DEFAULT NULL,
                                            `internal_link_count` int(11) DEFAULT NULL,
                                            `external_link_count` int(11) DEFAULT NULL,
                                            `link_check_data` varchar(8192) DEFAULT NULL,
                                            `google_position_avg` int(11) DEFAULT NULL,
                                            `google_position_monthly_diff` int(11) DEFAULT NULL,
                                            `google_position_last_month_avg` int(11) DEFAULT NULL,
                                            `seznam_position_avg` int(11) DEFAULT NULL,
                                            `seznam_position_monthly_diff` int(11) DEFAULT NULL,
                                            `seznam_position_last_month_avg` int(11) DEFAULT NULL,
                                            `google_searches_avg` int(11) DEFAULT NULL,
                                            `seznam_searches_avg` int(11) DEFAULT NULL,
                                            `grammar_mistakes` varchar(255) DEFAULT NULL,
                                            `current_meta_title` varchar(255) DEFAULT NULL,
                                            `google_visits` int(11) DEFAULT NULL,
                                            `disable_generated_meta_title` tinyint(1) NOT NULL,
                                            `page_group_identifier` varchar(255) DEFAULT NULL,
                                            `content_revision_pivot_shift` int(11) NOT NULL,
                                            `content_revised_at` datetime DEFAULT NULL,
                                            `processed_at` datetime DEFAULT NULL,
                                            `process_html_at` datetime DEFAULT NULL,
                                            `checked_at` datetime DEFAULT NULL,
                                            `created_at` datetime NOT NULL,
                                            PRIMARY KEY (`id`),
                                            UNIQUE KEY `page_group_unique` (`localization_id`,`page_type`,`page_group_identifier`),
                                            KEY `IDX_38D795B66A2856C7` (`localization_id`),
                                            KEY `IDX_38D795B682E3C2DE` (`checked_by`),
                                            KEY `IDX_38D795B69B3890DD` (`content_revised_by`),
                                            CONSTRAINT `FK_38D795B66A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                            CONSTRAINT `FK_38D795B682E3C2DE` FOREIGN KEY (`checked_by`) REFERENCES `tipli_account_user` (`id`),
                                            CONSTRAINT `FK_38D795B69B3890DD` FOREIGN KEY (`content_revised_by`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_seo_page_extension_keyword`;
CREATE TABLE `tipli_seo_page_extension_keyword` (
                                                    `id` int(11) NOT NULL AUTO_INCREMENT,
                                                    `page_extension_id` int(11) DEFAULT NULL,
                                                    `keyword_id` int(11) DEFAULT NULL,
                                                    `created_by_user_id` int(11) DEFAULT NULL,
                                                    `created_at` datetime NOT NULL,
                                                    PRIMARY KEY (`id`),
                                                    UNIQUE KEY `page_extension_unique` (`page_extension_id`,`keyword_id`),
                                                    KEY `IDX_1DD8C92DB60E79A5` (`page_extension_id`),
                                                    KEY `IDX_1DD8C92D115D4552` (`keyword_id`),
                                                    KEY `IDX_1DD8C92D7D182D95` (`created_by_user_id`),
                                                    CONSTRAINT `FK_1DD8C92D115D4552` FOREIGN KEY (`keyword_id`) REFERENCES `tipli_seo_keyword` (`id`),
                                                    CONSTRAINT `FK_1DD8C92D7D182D95` FOREIGN KEY (`created_by_user_id`) REFERENCES `tipli_account_user` (`id`),
                                                    CONSTRAINT `FK_1DD8C92DB60E79A5` FOREIGN KEY (`page_extension_id`) REFERENCES `tipli_seo_page_extension` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_seo_sitemap_url`;
CREATE TABLE `tipli_seo_sitemap_url` (
                                         `id` int(11) NOT NULL AUTO_INCREMENT,
                                         `localization_id` int(11) DEFAULT NULL,
                                         `url` varchar(255) NOT NULL,
                                         `created_at` datetime NOT NULL,
                                         PRIMARY KEY (`id`),
                                         KEY `IDX_3493E4696A2856C7` (`localization_id`),
                                         CONSTRAINT `FK_3493E4696A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_seo_visit`;
CREATE TABLE `tipli_seo_visit` (
                                   `id` int(11) NOT NULL AUTO_INCREMENT,
                                   `localization_id` int(11) DEFAULT NULL,
                                   `date` datetime NOT NULL,
                                   `page` varchar(255) NOT NULL,
                                   `channel` varchar(255) NOT NULL,
                                   `sessions` int(11) NOT NULL,
                                   `adsense_revenue` decimal(8,2) NOT NULL,
                                   `unique_hash` varchar(40) NOT NULL,
                                   `updated_at` datetime DEFAULT NULL,
                                   `created_at` datetime NOT NULL,
                                   PRIMARY KEY (`id`),
                                   UNIQUE KEY `hash_unique` (`unique_hash`),
                                   KEY `IDX_D90D2DF16A2856C7` (`localization_id`),
                                   KEY `searchIndex` (`channel`,`date`),
                                   KEY `pageIndex` (`page`),
                                   CONSTRAINT `FK_D90D2DF16A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_shops_contact`;
CREATE TABLE `tipli_shops_contact` (
                                       `id` int(11) NOT NULL AUTO_INCREMENT,
                                       `title` varchar(255) DEFAULT NULL,
                                       `company_name` varchar(255) DEFAULT NULL,
                                       `ic` varchar(20) DEFAULT NULL,
                                       `dic` varchar(20) DEFAULT NULL,
                                       `street` varchar(255) DEFAULT NULL,
                                       `city` varchar(255) DEFAULT NULL,
                                       `zip` varchar(10) DEFAULT NULL,
                                       `country` varchar(255) DEFAULT NULL,
                                       `facebook_link` varchar(255) DEFAULT NULL,
                                       `youtube_link` varchar(255) DEFAULT NULL,
                                       `pinterest_link` varchar(255) DEFAULT NULL,
                                       `tiktok_link` varchar(255) DEFAULT NULL,
                                       `twitter_link` varchar(255) DEFAULT NULL,
                                       `instagram_link` varchar(255) DEFAULT NULL,
                                       `linkedin_link` varchar(255) DEFAULT NULL,
                                       `lat` varchar(255) DEFAULT NULL,
                                       `lng` varchar(255) DEFAULT NULL,
                                       `email` varchar(255) DEFAULT NULL,
                                       `telephone` varchar(255) DEFAULT NULL,
                                       `contact_form_link` varchar(255) DEFAULT NULL,
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_shops_foreign_shop`;
CREATE TABLE `tipli_shops_foreign_shop` (
                                            `id` int(11) NOT NULL AUTO_INCREMENT,
                                            `parent_shop_id` int(11) DEFAULT NULL,
                                            `foreign_shop_id` int(11) DEFAULT NULL,
                                            `priority` int(11) NOT NULL,
                                            `created_at` datetime NOT NULL,
                                            PRIMARY KEY (`id`),
                                            KEY `IDX_5F1F122F3F5EB09B` (`parent_shop_id`),
                                            KEY `IDX_5F1F122FD7786CB5` (`foreign_shop_id`),
                                            CONSTRAINT `FK_5F1F122F3F5EB09B` FOREIGN KEY (`parent_shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                            CONSTRAINT `FK_5F1F122FD7786CB5` FOREIGN KEY (`foreign_shop_id`) REFERENCES `tipli_shops_shop` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_shops_internal_notes`;
CREATE TABLE `tipli_shops_internal_notes` (
                                              `id` int(11) NOT NULL AUTO_INCREMENT,
                                              `user_id` int(11) DEFAULT NULL,
                                              `archived_by` int(11) DEFAULT NULL,
                                              `partner_system_id` int(11) DEFAULT NULL,
                                              `parent_id` int(11) DEFAULT NULL,
                                              `note` varchar(255) NOT NULL,
                                              `group` varchar(255) NOT NULL,
                                              `archived_at` datetime DEFAULT NULL,
                                              `valid_till` datetime DEFAULT NULL,
                                              `created_at` datetime NOT NULL,
                                              `updated_at` datetime NOT NULL,
                                              PRIMARY KEY (`id`),
                                              KEY `IDX_DEA48A24A76ED395` (`user_id`),
                                              KEY `IDX_DEA48A2451B07D6D` (`archived_by`),
                                              KEY `IDX_DEA48A24FB42B4B1` (`partner_system_id`),
                                              KEY `IDX_DEA48A24727ACA70` (`parent_id`),
                                              CONSTRAINT `FK_DEA48A2451B07D6D` FOREIGN KEY (`archived_by`) REFERENCES `tipli_account_user` (`id`),
                                              CONSTRAINT `FK_DEA48A24727ACA70` FOREIGN KEY (`parent_id`) REFERENCES `tipli_shops_internal_notes` (`id`),
                                              CONSTRAINT `FK_DEA48A24A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`),
                                              CONSTRAINT `FK_DEA48A24FB42B4B1` FOREIGN KEY (`partner_system_id`) REFERENCES `tipli_partner_systems_partner_system` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_shops_internal_note_shop`;
CREATE TABLE `tipli_shops_internal_note_shop` (
                                                  `id` int(11) NOT NULL AUTO_INCREMENT,
                                                  `internal_note_id` int(11) DEFAULT NULL,
                                                  `shop_id` int(11) DEFAULT NULL,
                                                  `partner_system_id` int(11) DEFAULT NULL,
                                                  `created_at` datetime NOT NULL,
                                                  PRIMARY KEY (`id`),
                                                  KEY `IDX_C0A9AEA63EF54963` (`internal_note_id`),
                                                  KEY `IDX_C0A9AEA64D16C4DD` (`shop_id`),
                                                  KEY `IDX_C0A9AEA6FB42B4B1` (`partner_system_id`),
                                                  CONSTRAINT `FK_C0A9AEA63EF54963` FOREIGN KEY (`internal_note_id`) REFERENCES `tipli_shops_internal_notes` (`id`),
                                                  CONSTRAINT `FK_C0A9AEA64D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                                  CONSTRAINT `FK_C0A9AEA6FB42B4B1` FOREIGN KEY (`partner_system_id`) REFERENCES `tipli_partner_systems_partner_system` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_shops_offer`;
CREATE TABLE `tipli_shops_offer` (
                                     `id` int(11) NOT NULL AUTO_INCREMENT,
                                     `parent_offer_id` int(11) DEFAULT NULL,
                                     `shop_id` int(11) DEFAULT NULL,
                                     `name` text DEFAULT NULL,
                                     `type` varchar(8) DEFAULT NULL,
                                     `offer_type` varchar(8) NOT NULL,
                                     `minimal_reward` double DEFAULT NULL,
                                     `maximal_reward` double DEFAULT NULL,
                                     `static_maximal_reward` double DEFAULT NULL,
                                     `valid_since` datetime DEFAULT NULL,
                                     `valid_till` datetime DEFAULT NULL,
                                     `priority` int(11) DEFAULT NULL,
                                     `created_at` datetime NOT NULL,
                                     PRIMARY KEY (`id`),
                                     KEY `IDX_1842E51FCC0BD83F` (`parent_offer_id`),
                                     KEY `IDX_1842E51F4D16C4DD` (`shop_id`),
                                     KEY `type_idx` (`type`),
                                     KEY `offer_type_idx` (`offer_type`),
                                     CONSTRAINT `FK_1842E51F4D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                     CONSTRAINT `FK_1842E51FCC0BD83F` FOREIGN KEY (`parent_offer_id`) REFERENCES `tipli_shops_offer` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_shops_redirection`;
CREATE TABLE `tipli_shops_redirection` (
                                           `id` int(11) NOT NULL AUTO_INCREMENT,
                                           `user_id` int(11) DEFAULT NULL,
                                           `shop_id` int(11) DEFAULT NULL,
                                           `deal_id` int(11) DEFAULT NULL,
                                           `utm_id` int(11) DEFAULT NULL,
                                           `sync_id` int(11) DEFAULT NULL,
                                           `unique_id` varchar(32) DEFAULT NULL,
                                           `secure_hash` varchar(8) DEFAULT NULL,
                                           `url` varchar(1000) DEFAULT NULL,
                                           `ip` varchar(255) DEFAULT NULL,
                                           `referer` varchar(255) DEFAULT NULL,
                                           `platform` varchar(7) DEFAULT NULL,
                                           `country` varchar(2) DEFAULT NULL,
                                           `ga_cookie` varchar(255) DEFAULT NULL,
                                           `fbc_cookie` varchar(128) DEFAULT NULL,
                                           `fbp_cookie` varchar(64) DEFAULT NULL,
                                           `adblock_used` tinyint(1) DEFAULT NULL,
                                           `do_not_track_used` tinyint(1) DEFAULT NULL,
                                           `sub_id` varchar(255) DEFAULT NULL,
                                           `has_transaction` tinyint(1) DEFAULT NULL,
                                           `created_at` datetime NOT NULL,
                                           PRIMARY KEY (`id`),
                                           KEY `IDX_1ACD5B1DA76ED395` (`user_id`),
                                           KEY `IDX_1ACD5B1D4D16C4DD` (`shop_id`),
                                           KEY `IDX_1ACD5B1DF60E2305` (`deal_id`),
                                           KEY `IDX_1ACD5B1DB6334822` (`utm_id`),
                                           CONSTRAINT `FK_1ACD5B1D4D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                           CONSTRAINT `FK_1ACD5B1DA76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`),
                                           CONSTRAINT `FK_1ACD5B1DB6334822` FOREIGN KEY (`utm_id`) REFERENCES `tipli_utm_utm` (`id`),
                                           CONSTRAINT `FK_1ACD5B1DF60E2305` FOREIGN KEY (`deal_id`) REFERENCES `tipli_deals_deal` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_shops_redirection_feedback`;
CREATE TABLE `tipli_shops_redirection_feedback` (
                                                    `id` int(11) NOT NULL AUTO_INCREMENT,
                                                    `localization_id` int(11) DEFAULT NULL,
                                                    `user_id` int(11) DEFAULT NULL,
                                                    `shop_id` int(11) DEFAULT NULL,
                                                    `redirection_id` int(11) DEFAULT NULL,
                                                    `type` varchar(255) NOT NULL,
                                                    `ip` varchar(255) DEFAULT NULL,
                                                    `platform` varchar(7) DEFAULT NULL,
                                                    `country` varchar(2) DEFAULT NULL,
                                                    `updated_at` datetime NOT NULL,
                                                    `created_at` datetime NOT NULL,
                                                    PRIMARY KEY (`id`),
                                                    KEY `IDX_A6AD94386A2856C7` (`localization_id`),
                                                    KEY `IDX_A6AD9438A76ED395` (`user_id`),
                                                    KEY `IDX_A6AD94384D16C4DD` (`shop_id`),
                                                    KEY `IDX_A6AD94381DC0789A` (`redirection_id`),
                                                    CONSTRAINT `FK_A6AD94381DC0789A` FOREIGN KEY (`redirection_id`) REFERENCES `tipli_shops_redirection` (`id`),
                                                    CONSTRAINT `FK_A6AD94384D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                                    CONSTRAINT `FK_A6AD94386A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                                    CONSTRAINT `FK_A6AD9438A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_shops_related_shop`;
CREATE TABLE `tipli_shops_related_shop` (
                                            `id` int(11) NOT NULL AUTO_INCREMENT,
                                            `parent_shop_id` int(11) DEFAULT NULL,
                                            `related_shop_id` int(11) DEFAULT NULL,
                                            `priority` int(11) NOT NULL,
                                            `created_at` datetime NOT NULL,
                                            PRIMARY KEY (`id`),
                                            KEY `IDX_79F6ACA3F5EB09B` (`parent_shop_id`),
                                            KEY `IDX_79F6ACA720F0E78` (`related_shop_id`),
                                            CONSTRAINT `FK_79F6ACA3F5EB09B` FOREIGN KEY (`parent_shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                            CONSTRAINT `FK_79F6ACA720F0E78` FOREIGN KEY (`related_shop_id`) REFERENCES `tipli_shops_shop` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_shops_shop`;
CREATE TABLE `tipli_shops_shop` (
                                    `id` int(11) NOT NULL AUTO_INCREMENT,
                                    `page_extension_id` int(11) DEFAULT NULL,
                                    `localization_id` int(11) DEFAULT NULL,
                                    `logo_id` int(11) DEFAULT NULL,
                                    `svg_logo_id` int(11) DEFAULT NULL,
                                    `cover_id` int(11) DEFAULT NULL,
                                    `mobile_cover_id` int(11) DEFAULT NULL,
                                    `screenshot_id` int(11) DEFAULT NULL,
                                    `partner_system_id` int(11) DEFAULT NULL,
                                    `contact_id` int(11) DEFAULT NULL,
                                    `name` varchar(255) NOT NULL,
                                    `slug` varchar(255) NOT NULL,
                                    `domain` varchar(255) DEFAULT NULL,
                                    `conditions` longtext DEFAULT NULL,
                                    `cashback_allowed` tinyint(1) NOT NULL,
                                    `partner_system_redirect_url` longtext DEFAULT NULL,
                                    `deep_url_allowed` tinyint(1) NOT NULL,
                                    `count_of_transactions` int(11) NOT NULL,
                                    `count_of_redirections` int(11) NOT NULL,
                                    `average_registration_period` double DEFAULT NULL,
                                    `average_confirmation_period` double DEFAULT NULL,
                                    `tracking_reliability` double NOT NULL,
                                    `priority` int(11) NOT NULL,
                                    `priority_leaflets` int(11) NOT NULL,
                                    `type` varchar(16) NOT NULL,
                                    `active` tinyint(1) NOT NULL,
                                    `visible` tinyint(1) NOT NULL,
                                    `boost_coupons` tinyint(1) NOT NULL,
                                    `description` longtext DEFAULT NULL,
                                    `short_description` longtext DEFAULT NULL,
                                    `published_at` datetime NOT NULL,
                                    `updated_at` datetime NOT NULL,
                                    `update_index_at` datetime DEFAULT NULL,
                                    `sitemap_updated_at` datetime DEFAULT NULL,
                                    `created_at` datetime NOT NULL,
                                    PRIMARY KEY (`id`),
                                    UNIQUE KEY `UNIQ_5686820CB60E79A5` (`page_extension_id`),
                                    UNIQUE KEY `UNIQ_5686820CE7A1254A` (`contact_id`),
                                    UNIQUE KEY `slug_unique` (`localization_id`,`slug`),
                                    KEY `IDX_5686820C6A2856C7` (`localization_id`),
                                    KEY `IDX_5686820CF98F144A` (`logo_id`),
                                    KEY `IDX_5686820C27F7B6F2` (`svg_logo_id`),
                                    KEY `IDX_5686820C922726E9` (`cover_id`),
                                    KEY `IDX_5686820C5F108AB7` (`mobile_cover_id`),
                                    KEY `IDX_5686820C4A8B36A0` (`screenshot_id`),
                                    KEY `IDX_5686820CFB42B4B1` (`partner_system_id`),
                                    KEY `published_at_idx` (`published_at`),
                                    KEY `active_idx` (`active`),
                                    KEY `type_idx` (`type`),
                                    CONSTRAINT `FK_5686820C27F7B6F2` FOREIGN KEY (`svg_logo_id`) REFERENCES `tipli_images_image` (`id`),
                                    CONSTRAINT `FK_5686820C4A8B36A0` FOREIGN KEY (`screenshot_id`) REFERENCES `tipli_images_image` (`id`),
                                    CONSTRAINT `FK_5686820C5F108AB7` FOREIGN KEY (`mobile_cover_id`) REFERENCES `tipli_images_image` (`id`),
                                    CONSTRAINT `FK_5686820C6A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                    CONSTRAINT `FK_5686820C922726E9` FOREIGN KEY (`cover_id`) REFERENCES `tipli_images_image` (`id`),
                                    CONSTRAINT `FK_5686820CB60E79A5` FOREIGN KEY (`page_extension_id`) REFERENCES `tipli_seo_page_extension` (`id`),
                                    CONSTRAINT `FK_5686820CE7A1254A` FOREIGN KEY (`contact_id`) REFERENCES `tipli_shops_contact` (`id`),
                                    CONSTRAINT `FK_5686820CF98F144A` FOREIGN KEY (`logo_id`) REFERENCES `tipli_images_image` (`id`),
                                    CONSTRAINT `FK_5686820CFB42B4B1` FOREIGN KEY (`partner_system_id`) REFERENCES `tipli_partner_systems_partner_system` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_shops_shop_cashback_correction_rule`;
CREATE TABLE `tipli_shops_shop_cashback_correction_rule` (
                                                             `id` int(11) NOT NULL AUTO_INCREMENT,
                                                             `shop_id` int(11) DEFAULT NULL,
                                                             `name` varchar(255) DEFAULT NULL,
                                                             `valid_since` datetime NOT NULL,
                                                             `valid_till` datetime NOT NULL,
                                                             `cashback_from` double DEFAULT NULL,
                                                             `cashback_to` double DEFAULT NULL,
                                                             `target_cashback` double NOT NULL,
                                                             `created_at` datetime NOT NULL,
                                                             PRIMARY KEY (`id`),
                                                             KEY `IDX_70C059334D16C4DD` (`shop_id`),
                                                             CONSTRAINT `FK_70C059334D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_shops_shop_change`;
CREATE TABLE `tipli_shops_shop_change` (
                                           `id` int(11) NOT NULL AUTO_INCREMENT,
                                           `shop_id` int(11) DEFAULT NULL,
                                           `user_id` int(11) DEFAULT NULL,
                                           `change` varchar(255) NOT NULL,
                                           `old_value` varchar(1000) DEFAULT NULL,
                                           `new_value` varchar(1000) DEFAULT NULL,
                                           `created_at` datetime NOT NULL,
                                           PRIMARY KEY (`id`),
                                           KEY `IDX_FD7CD3484D16C4DD` (`shop_id`),
                                           KEY `IDX_FD7CD348A76ED395` (`user_id`),
                                           CONSTRAINT `FK_FD7CD3484D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                           CONSTRAINT `FK_FD7CD348A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_shops_shop_checker_activity`;
CREATE TABLE `tipli_shops_shop_checker_activity` (
                                                     `id` int(11) NOT NULL AUTO_INCREMENT,
                                                     `shop_id` int(11) DEFAULT NULL,
                                                     `state` varchar(8) NOT NULL,
                                                     `domain` longtext DEFAULT NULL,
                                                     `shop_domain` varchar(255) DEFAULT NULL,
                                                     `response_code` int(11) DEFAULT NULL,
                                                     `action` varchar(255) DEFAULT NULL,
                                                     `created_at` datetime NOT NULL,
                                                     PRIMARY KEY (`id`),
                                                     KEY `IDX_70B022CD4D16C4DD` (`shop_id`),
                                                     CONSTRAINT `FK_70B022CD4D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_shops_shop_checker_fail`;
CREATE TABLE `tipli_shops_shop_checker_fail` (
                                                 `id` int(11) NOT NULL AUTO_INCREMENT,
                                                 `shop_id` int(11) DEFAULT NULL,
                                                 `checked_by` int(11) DEFAULT NULL,
                                                 `note` longtext NOT NULL,
                                                 `checked_at` datetime DEFAULT NULL,
                                                 `created_at` datetime NOT NULL,
                                                 PRIMARY KEY (`id`),
                                                 KEY `IDX_F4A8816B4D16C4DD` (`shop_id`),
                                                 KEY `IDX_F4A8816B82E3C2DE` (`checked_by`),
                                                 CONSTRAINT `FK_F4A8816B4D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                                 CONSTRAINT `FK_F4A8816B82E3C2DE` FOREIGN KEY (`checked_by`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_shops_shop_data`;
CREATE TABLE `tipli_shops_shop_data` (
                                         `id` int(11) NOT NULL AUTO_INCREMENT,
                                         `shop_id` int(11) DEFAULT NULL,
                                         `author_id` int(11) DEFAULT NULL,
                                         `leaflet_cover_id` int(11) DEFAULT NULL,
                                         `reviewed_by_user_id` int(11) DEFAULT NULL,
                                         `partner_system_changed_by_user_id` int(11) DEFAULT NULL,
                                         `leaflet_cover_alt` varchar(255) DEFAULT NULL,
                                         `note` longtext DEFAULT NULL,
                                         `transactions_confirmation_note` varchar(255) DEFAULT NULL,
                                         `deals_title` varchar(255) DEFAULT NULL,
                                         `coupons_description` longtext DEFAULT NULL,
                                         `shipping_description` longtext DEFAULT NULL,
                                         `marketing_note` longtext DEFAULT NULL,
                                         `cashback_with_coupon_allowed` tinyint(1) NOT NULL,
                                         `seo_verified_at` datetime DEFAULT NULL,
                                         `last_leaflet_created_at` datetime DEFAULT NULL,
                                         `last_sale_created_at` datetime DEFAULT NULL,
                                         `recent_deal_expire_at` datetime DEFAULT NULL,
                                         `recent_coupon_expire_at` datetime DEFAULT NULL,
                                         `count_of_internal_links` int(11) DEFAULT NULL,
                                         `count_of_external_links` int(11) DEFAULT NULL,
                                         `count_of_similar_shops` int(11) DEFAULT NULL,
                                         `count_of_required_deals` int(11) DEFAULT NULL,
                                         `count_of_valid_products` int(11) DEFAULT NULL,
                                         `is_international_linked` tinyint(1) DEFAULT NULL,
                                         `pause_reason` varchar(255) DEFAULT NULL,
                                         `seo_note` longtext DEFAULT NULL,
                                         `info_message` longtext DEFAULT NULL,
                                         `warning_message` longtext DEFAULT NULL,
                                         `warning_message_updated_at` datetime DEFAULT NULL,
                                         `mobile_warning_message` longtext DEFAULT NULL,
                                         `mobile_warning_message_updated_at` datetime DEFAULT NULL,
                                         `search_keywords` varchar(255) DEFAULT NULL,
                                         `total_commission_amount_in_last30days` double NOT NULL,
                                         `count_of_leaflets` int(11) NOT NULL,
                                         `count_of_leaflets_total` int(11) NOT NULL,
                                         `count_of_deals` int(11) NOT NULL,
                                         `count_of_coupon_deals` int(11) NOT NULL,
                                         `show_deals` tinyint(1) NOT NULL,
                                         `show_leaflets` tinyint(1) NOT NULL,
                                         `show_products` tinyint(1) NOT NULL,
                                         `show_questions_after_deals` tinyint(1) NOT NULL,
                                         `average_transaction_registration_period` int(11) DEFAULT NULL,
                                         `average_transaction_registration_time` int(11) DEFAULT NULL,
                                         `confirmation_rate` double DEFAULT NULL,
                                         `paused_by_shop_checker` tinyint(1) DEFAULT NULL,
                                         `competitor_review_at` datetime DEFAULT NULL,
                                         `request_to_affiliate_sent_at` datetime DEFAULT NULL,
                                         `reward_review_at` datetime DEFAULT NULL,
                                         `coupons_review_at` datetime DEFAULT NULL,
                                         `deals_review_at` datetime DEFAULT NULL,
                                         `marked_without_cashback_at` datetime DEFAULT NULL,
                                         `mobile_message_before_redirect` varchar(255) DEFAULT NULL,
                                         `shop_checker_scheduled_at` datetime DEFAULT NULL,
                                         `transaction_checker_scheduled_at` datetime DEFAULT NULL,
                                         `only_leaflet_shop` tinyint(1) NOT NULL,
                                         `coupon_confirmation_required` tinyint(1) NOT NULL,
                                         `reviewed_at` datetime DEFAULT NULL,
                                         `last_transaction_at` datetime DEFAULT NULL,
                                         `paused_at` datetime DEFAULT NULL,
                                         `partner_system_changed_at` datetime DEFAULT NULL,
                                         `only_first_purchase_condition` tinyint(1) NOT NULL,
                                         `alternative_names` varchar(255) DEFAULT NULL,
                                         `heading` varchar(255) DEFAULT NULL,
                                         `average_cashback` double DEFAULT NULL,
                                         `average_commission_amount` double DEFAULT NULL,
                                         `first_description_at` datetime DEFAULT NULL,
                                         PRIMARY KEY (`id`),
                                         UNIQUE KEY `UNIQ_7B2F8D294D16C4DD` (`shop_id`),
                                         KEY `IDX_7B2F8D29F675F31B` (`author_id`),
                                         KEY `IDX_7B2F8D291532CC98` (`leaflet_cover_id`),
                                         KEY `IDX_7B2F8D29E03A844A` (`reviewed_by_user_id`),
                                         KEY `IDX_7B2F8D29DB019B` (`partner_system_changed_by_user_id`),
                                         CONSTRAINT `FK_7B2F8D291532CC98` FOREIGN KEY (`leaflet_cover_id`) REFERENCES `tipli_images_image` (`id`),
                                         CONSTRAINT `FK_7B2F8D294D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                         CONSTRAINT `FK_7B2F8D29DB019B` FOREIGN KEY (`partner_system_changed_by_user_id`) REFERENCES `tipli_account_user` (`id`),
                                         CONSTRAINT `FK_7B2F8D29E03A844A` FOREIGN KEY (`reviewed_by_user_id`) REFERENCES `tipli_account_user` (`id`),
                                         CONSTRAINT `FK_7B2F8D29F675F31B` FOREIGN KEY (`author_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_shops_shop_description_block`;
CREATE TABLE `tipli_shops_shop_description_block` (
                                                      `id` int(11) NOT NULL AUTO_INCREMENT,
                                                      `shop_id` int(11) DEFAULT NULL,
                                                      `title` varchar(255) DEFAULT NULL,
                                                      `type` varchar(255) DEFAULT NULL,
                                                      `description` longtext DEFAULT NULL,
                                                      `priority` int(11) NOT NULL,
                                                      `html_valid` tinyint(1) NOT NULL,
                                                      `short_description_in_content` tinyint(1) NOT NULL,
                                                      `short_description_after_deals` tinyint(1) NOT NULL,
                                                      `coupon_instructions_above_long_description` tinyint(1) NOT NULL,
                                                      `cancelled_at` datetime DEFAULT NULL,
                                                      `archived_at` datetime DEFAULT NULL,
                                                      `updated_at` datetime NOT NULL,
                                                      `created_at` datetime NOT NULL,
                                                      PRIMARY KEY (`id`),
                                                      KEY `IDX_84D46BD54D16C4DD` (`shop_id`),
                                                      CONSTRAINT `FK_84D46BD54D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_shops_shop_external_data`;
CREATE TABLE `tipli_shops_shop_external_data` (
                                                  `id` int(11) NOT NULL AUTO_INCREMENT,
                                                  `shop_id` int(11) DEFAULT NULL,
                                                  `title` longtext DEFAULT NULL,
                                                  `description` longtext DEFAULT NULL,
                                                  `keywords` longtext DEFAULT NULL,
                                                  `process_at` datetime DEFAULT NULL,
                                                  `processed_at` datetime DEFAULT NULL,
                                                  `failed_at` datetime DEFAULT NULL,
                                                  `fail_reason` varchar(255) DEFAULT NULL,
                                                  `updated_at` datetime NOT NULL,
                                                  `created_at` datetime NOT NULL,
                                                  PRIMARY KEY (`id`),
                                                  UNIQUE KEY `shop_id` (`shop_id`),
                                                  CONSTRAINT `FK_628E0F774D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_shops_shop_leaflet_tag`;
CREATE TABLE `tipli_shops_shop_leaflet_tag` (
                                                `shop_id` int(11) NOT NULL,
                                                `tag_id` int(11) NOT NULL,
                                                `priority` int(11) DEFAULT NULL,
                                                PRIMARY KEY (`shop_id`,`tag_id`),
                                                KEY `IDX_B2A9FB754D16C4DD` (`shop_id`),
                                                KEY `IDX_B2A9FB75BAD26311` (`tag_id`),
                                                CONSTRAINT `FK_B2A9FB754D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                                CONSTRAINT `FK_B2A9FB75BAD26311` FOREIGN KEY (`tag_id`) REFERENCES `tipli_tags_tag` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_shops_shop_note`;
CREATE TABLE `tipli_shops_shop_note` (
                                         `id` int(11) NOT NULL AUTO_INCREMENT,
                                         `user_id` int(11) DEFAULT NULL,
                                         `shop_id` int(11) DEFAULT NULL,
                                         `note` varchar(255) NOT NULL,
                                         `created_at` datetime NOT NULL,
                                         PRIMARY KEY (`id`),
                                         KEY `IDX_1961845EA76ED395` (`user_id`),
                                         KEY `IDX_1961845E4D16C4DD` (`shop_id`),
                                         CONSTRAINT `FK_1961845E4D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                         CONSTRAINT `FK_1961845EA76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_shops_shop_partner_system`;
CREATE TABLE `tipli_shops_shop_partner_system` (
                                                   `id` int(11) NOT NULL AUTO_INCREMENT,
                                                   `shop_id` int(11) DEFAULT NULL,
                                                   `partner_system_id` int(11) DEFAULT NULL,
                                                   `partner_system_key` varchar(255) DEFAULT NULL,
                                                   `created_at` datetime NOT NULL,
                                                   PRIMARY KEY (`id`),
                                                   KEY `IDX_DCDE62F24D16C4DD` (`shop_id`),
                                                   KEY `IDX_DCDE62F2FB42B4B1` (`partner_system_id`),
                                                   CONSTRAINT `FK_DCDE62F24D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                                   CONSTRAINT `FK_DCDE62F2FB42B4B1` FOREIGN KEY (`partner_system_id`) REFERENCES `tipli_partner_systems_partner_system` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_shops_shop_pause_log`;
CREATE TABLE `tipli_shops_shop_pause_log` (
                                              `id` int(11) NOT NULL AUTO_INCREMENT,
                                              `shop_id` int(11) DEFAULT NULL,
                                              `author_id` int(11) DEFAULT NULL,
                                              `action` varchar(255) NOT NULL,
                                              `reason` varchar(255) DEFAULT NULL,
                                              `pause_at` datetime DEFAULT NULL,
                                              `created_at` datetime NOT NULL,
                                              PRIMARY KEY (`id`),
                                              KEY `IDX_419D98294D16C4DD` (`shop_id`),
                                              KEY `IDX_419D9829F675F31B` (`author_id`),
                                              CONSTRAINT `FK_419D98294D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                              CONSTRAINT `FK_419D9829F675F31B` FOREIGN KEY (`author_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_shops_shop_product_data`;
CREATE TABLE `tipli_shops_shop_product_data` (
                                                 `id` int(11) NOT NULL AUTO_INCREMENT,
                                                 `shop_id` int(11) DEFAULT NULL,
                                                 `options` longtext DEFAULT NULL,
                                                 `source_html_container_with_price_regex` varchar(255) DEFAULT NULL,
                                                 `processed_at` datetime DEFAULT NULL,
                                                 `created_at` datetime NOT NULL,
                                                 PRIMARY KEY (`id`),
                                                 KEY `IDX_4758F23C4D16C4DD` (`shop_id`),
                                                 CONSTRAINT `FK_4758F23C4D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_shops_shop_product_feed`;
CREATE TABLE `tipli_shops_shop_product_feed` (
                                                 `id` int(11) NOT NULL AUTO_INCREMENT,
                                                 `shop_id` int(11) DEFAULT NULL,
                                                 `type` varchar(12) NOT NULL,
                                                 `feed_url` longtext DEFAULT NULL,
                                                 `options` longtext DEFAULT NULL,
                                                 `status` varchar(16) DEFAULT NULL,
                                                 `count_of_processed_products` int(11) NOT NULL,
                                                 `process_at` datetime DEFAULT NULL,
                                                 `process_time_shift` varchar(16) DEFAULT NULL,
                                                 `created_at` datetime NOT NULL,
                                                 PRIMARY KEY (`id`),
                                                 KEY `IDX_C9EB45F44D16C4DD` (`shop_id`),
                                                 CONSTRAINT `FK_C9EB45F44D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_shops_shop_question`;
CREATE TABLE `tipli_shops_shop_question` (
                                             `id` int(11) NOT NULL AUTO_INCREMENT,
                                             `shop_id` int(11) DEFAULT NULL,
                                             `user_id` int(11) DEFAULT NULL,
                                             `question` varchar(255) DEFAULT NULL,
                                             `answer` longtext DEFAULT NULL,
                                             `updated_at` datetime NOT NULL,
                                             `created_at` datetime NOT NULL,
                                             PRIMARY KEY (`id`),
                                             KEY `IDX_5E0C49BF4D16C4DD` (`shop_id`),
                                             KEY `IDX_5E0C49BFA76ED395` (`user_id`),
                                             CONSTRAINT `FK_5E0C49BF4D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                             CONSTRAINT `FK_5E0C49BFA76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_shops_shop_report`;
CREATE TABLE `tipli_shops_shop_report` (
                                           `id` int(11) NOT NULL AUTO_INCREMENT,
                                           `shop_id` int(11) DEFAULT NULL,
                                           `started_at` datetime NOT NULL,
                                           `ended_at` datetime NOT NULL,
                                           `average_registration_time` int(11) DEFAULT NULL,
                                           `average_confirmation_time` int(11) DEFAULT NULL,
                                           `count_of_transactions` int(11) NOT NULL,
                                           `count_of_confirmed_transactions` int(11) NOT NULL,
                                           `count_of_canceled_transactions` int(11) NOT NULL,
                                           `count_of_refunds` int(11) NOT NULL,
                                           `count_of_refund_transactions` int(11) NOT NULL,
                                           `count_of_redirections` int(11) NOT NULL,
                                           `created_at` datetime NOT NULL,
                                           PRIMARY KEY (`id`),
                                           KEY `IDX_79045AEC4D16C4DD` (`shop_id`),
                                           CONSTRAINT `FK_79045AEC4D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_shops_shop_settings`;
CREATE TABLE `tipli_shops_shop_settings` (
                                             `id` int(11) NOT NULL AUTO_INCREMENT,
                                             `shop_id` int(11) DEFAULT NULL,
                                             `in_app_redirection_disabled_at` datetime DEFAULT NULL,
                                             `addon_feed_disabled_at` datetime DEFAULT NULL,
                                             `addon_domain_match_type` varchar(16) DEFAULT NULL,
                                             `addon_popup_box_disabled_at` datetime DEFAULT NULL,
                                             `addon_content_box_disabled_at` datetime DEFAULT NULL,
                                             `twisto_disabled_at` datetime DEFAULT NULL,
                                             `rondo_disabled_at` datetime DEFAULT NULL,
                                             `homecredit_disabled_at` datetime DEFAULT NULL,
                                             PRIMARY KEY (`id`),
                                             KEY `IDX_DBEA0344D16C4DD` (`shop_id`),
                                             CONSTRAINT `FK_DBEA0344D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_shops_shop_steve_data`;
CREATE TABLE `tipli_shops_shop_steve_data` (
                                               `id` int(11) NOT NULL AUTO_INCREMENT,
                                               `shop_id` int(11) DEFAULT NULL,
                                               `steve_id` int(11) NOT NULL,
                                               `coupon_category` varchar(255) DEFAULT NULL,
                                               `updated_at` datetime NOT NULL,
                                               `created_at` datetime NOT NULL,
                                               PRIMARY KEY (`id`),
                                               UNIQUE KEY `shop_id` (`shop_id`),
                                               CONSTRAINT `FK_27625D714D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_shops_shop_tag`;
CREATE TABLE `tipli_shops_shop_tag` (
                                        `shop_id` int(11) NOT NULL,
                                        `tag_id` int(11) NOT NULL,
                                        `priority` int(11) DEFAULT NULL,
                                        PRIMARY KEY (`shop_id`,`tag_id`),
                                        KEY `IDX_95415234D16C4DD` (`shop_id`),
                                        KEY `IDX_9541523BAD26311` (`tag_id`),
                                        CONSTRAINT `FK_95415234D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                        CONSTRAINT `FK_9541523BAD26311` FOREIGN KEY (`tag_id`) REFERENCES `tipli_tags_tag` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_shops_shop_trigger`;
CREATE TABLE `tipli_shops_shop_trigger` (
                                            `id` int(11) NOT NULL AUTO_INCREMENT,
                                            `shop_id` int(11) DEFAULT NULL,
                                            `user_id` int(11) DEFAULT NULL,
                                            `data` varchar(255) NOT NULL,
                                            `reason` varchar(255) NOT NULL,
                                            `scheduled_at` datetime NOT NULL,
                                            `processed_at` datetime DEFAULT NULL,
                                            `failed_at` datetime DEFAULT NULL,
                                            `failed_reason` varchar(255) DEFAULT NULL,
                                            `created_at` datetime NOT NULL,
                                            PRIMARY KEY (`id`),
                                            KEY `IDX_59BFCD1A4D16C4DD` (`shop_id`),
                                            KEY `IDX_59BFCD1AA76ED395` (`user_id`),
                                            CONSTRAINT `FK_59BFCD1A4D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                            CONSTRAINT `FK_59BFCD1AA76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_shops_store`;
CREATE TABLE `tipli_shops_store` (
                                     `id` int(11) NOT NULL AUTO_INCREMENT,
                                     `localization_id` int(11) DEFAULT NULL,
                                     `region_id` int(11) DEFAULT NULL,
                                     `parent_store_id` int(11) DEFAULT NULL,
                                     `shop_id` int(11) DEFAULT NULL,
                                     `preview_picture_id` int(11) DEFAULT NULL,
                                     `store_id` varchar(255) NOT NULL,
                                     `name` varchar(255) DEFAULT NULL,
                                     `description` varchar(255) DEFAULT NULL,
                                     `slug` varchar(255) DEFAULT NULL,
                                     `city` varchar(255) DEFAULT NULL,
                                     `address` varchar(255) DEFAULT NULL,
                                     `zip_code` varchar(255) DEFAULT NULL,
                                     `phone_number` varchar(255) DEFAULT NULL,
                                     `email` varchar(255) DEFAULT NULL,
                                     `web` varchar(255) DEFAULT NULL,
                                     `lat` varchar(255) DEFAULT NULL,
                                     `lng` varchar(255) DEFAULT NULL,
                                     `updated_at` datetime NOT NULL,
                                     `updated_from_api_at` datetime NOT NULL,
                                     `created_at` datetime NOT NULL,
                                     PRIMARY KEY (`id`),
                                     KEY `IDX_CEC33A566A2856C7` (`localization_id`),
                                     KEY `IDX_CEC33A5698260155` (`region_id`),
                                     KEY `IDX_CEC33A562F5F04C0` (`parent_store_id`),
                                     KEY `IDX_CEC33A564D16C4DD` (`shop_id`),
                                     KEY `IDX_CEC33A56FE49D60A` (`preview_picture_id`),
                                     CONSTRAINT `FK_CEC33A562F5F04C0` FOREIGN KEY (`parent_store_id`) REFERENCES `tipli_shops_store` (`id`),
                                     CONSTRAINT `FK_CEC33A564D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                     CONSTRAINT `FK_CEC33A566A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                     CONSTRAINT `FK_CEC33A5698260155` FOREIGN KEY (`region_id`) REFERENCES `tipli_regions_region` (`id`),
                                     CONSTRAINT `FK_CEC33A56FE49D60A` FOREIGN KEY (`preview_picture_id`) REFERENCES `tipli_images_image` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_shortener_shortcut`;
CREATE TABLE `tipli_shortener_shortcut` (
                                            `id` int(11) NOT NULL AUTO_INCREMENT,
                                            `localization_id` int(11) DEFAULT NULL,
                                            `prefix` varchar(16) DEFAULT NULL,
                                            `slug` varchar(255) NOT NULL,
                                            `url` longtext NOT NULL,
                                            `created_at` datetime NOT NULL,
                                            PRIMARY KEY (`id`),
                                            KEY `IDX_3F326AC16A2856C7` (`localization_id`),
                                            CONSTRAINT `FK_3F326AC16A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_smartsupp_conversation`;
CREATE TABLE `tipli_smartsupp_conversation` (
                                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                                `localization_id` int(11) DEFAULT NULL,
                                                `user_id` int(11) DEFAULT NULL,
                                                `agent_id` int(11) DEFAULT NULL,
                                                `agent_name` varchar(255) DEFAULT NULL,
                                                `conversation_id` varchar(255) NOT NULL,
                                                `rating` int(11) DEFAULT NULL,
                                                `status` varchar(255) NOT NULL,
                                                `synchronized_at` datetime DEFAULT NULL,
                                                `synchronize_at` datetime DEFAULT NULL,
                                                `conversation_created_at` datetime NOT NULL,
                                                `conversation_updated_at` datetime DEFAULT NULL,
                                                `conversation_finished_at` datetime DEFAULT NULL,
                                                `created_at` datetime NOT NULL,
                                                PRIMARY KEY (`id`),
                                                KEY `IDX_8CF210866A2856C7` (`localization_id`),
                                                KEY `IDX_8CF21086A76ED395` (`user_id`),
                                                KEY `IDX_8CF210863414710B` (`agent_id`),
                                                CONSTRAINT `FK_8CF210863414710B` FOREIGN KEY (`agent_id`) REFERENCES `tipli_account_user` (`id`),
                                                CONSTRAINT `FK_8CF210866A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                                CONSTRAINT `FK_8CF21086A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_smartsupp_message`;
CREATE TABLE `tipli_smartsupp_message` (
                                           `id` int(11) NOT NULL AUTO_INCREMENT,
                                           `conversation_id` int(11) DEFAULT NULL,
                                           `message_id` varchar(255) NOT NULL,
                                           `response_time` int(11) DEFAULT NULL,
                                           `is_incoming` tinyint(1) NOT NULL,
                                           `message_created_at` datetime NOT NULL,
                                           `created_at` datetime NOT NULL,
                                           PRIMARY KEY (`id`),
                                           KEY `IDX_6B4A9D7E9AC0396` (`conversation_id`),
                                           CONSTRAINT `FK_6B4A9D7E9AC0396` FOREIGN KEY (`conversation_id`) REFERENCES `tipli_smartsupp_conversation` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_survey_survey`;
CREATE TABLE `tipli_survey_survey` (
                                       `id` int(11) NOT NULL AUTO_INCREMENT,
                                       `localization_id` int(11) DEFAULT NULL,
                                       `user_id` int(11) DEFAULT NULL,
                                       `shop_id` int(11) DEFAULT NULL,
                                       `answer1` varchar(255) DEFAULT NULL,
                                       `answer2` varchar(255) DEFAULT NULL,
                                       `answer3` varchar(255) DEFAULT NULL,
                                       `answer_message` longtext DEFAULT NULL,
                                       `created_at` datetime NOT NULL,
                                       PRIMARY KEY (`id`),
                                       KEY `IDX_61471DCF6A2856C7` (`localization_id`),
                                       KEY `IDX_61471DCFA76ED395` (`user_id`),
                                       KEY `IDX_61471DCF4D16C4DD` (`shop_id`),
                                       CONSTRAINT `FK_61471DCF4D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                       CONSTRAINT `FK_61471DCF6A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                       CONSTRAINT `FK_61471DCFA76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_tags_tag`;
CREATE TABLE `tipli_tags_tag` (
                                  `id` int(11) NOT NULL AUTO_INCREMENT,
                                  `localization_id` int(11) DEFAULT NULL,
                                  `cover_id` int(11) DEFAULT NULL,
                                  `mobile_cover_id` int(11) DEFAULT NULL,
                                  `parent_id` int(11) DEFAULT NULL,
                                  `background_image_id` int(11) DEFAULT NULL,
                                  `name` varchar(255) NOT NULL,
                                  `slug` varchar(255) NOT NULL,
                                  `type` varchar(255) NOT NULL,
                                  `level` int(11) DEFAULT NULL,
                                  `icon` varchar(255) DEFAULT NULL,
                                  `description` longtext DEFAULT NULL,
                                  `meta_title` varchar(255) DEFAULT NULL,
                                  `meta_description` varchar(255) DEFAULT NULL,
                                  `meta_keywords` varchar(255) DEFAULT NULL,
                                  `count_of_coupon_deals` int(11) NOT NULL,
                                  `count_of_products` int(11) NOT NULL,
                                  `count_of_shops` int(11) NOT NULL,
                                  `count_of_non_cashback_shops` int(11) NOT NULL,
                                  `visible` tinyint(1) NOT NULL,
                                  `leaflet_page_allowed` tinyint(1) NOT NULL,
                                  `priority` int(11) NOT NULL,
                                  `is_generated_product_landing_pages_allowed` int(11) NOT NULL,
                                  `update_index_at` datetime DEFAULT NULL,
                                  `removed_at` datetime DEFAULT NULL,
                                  `process_product_tag` datetime DEFAULT NULL,
                                  `updated_at` datetime NOT NULL,
                                  `created_at` datetime NOT NULL,
                                  PRIMARY KEY (`id`),
                                  KEY `IDX_947DBD9A6A2856C7` (`localization_id`),
                                  KEY `IDX_947DBD9A922726E9` (`cover_id`),
                                  KEY `IDX_947DBD9A5F108AB7` (`mobile_cover_id`),
                                  KEY `IDX_947DBD9A727ACA70` (`parent_id`),
                                  KEY `IDX_947DBD9AE6DA28AA` (`background_image_id`),
                                  CONSTRAINT `FK_947DBD9A5F108AB7` FOREIGN KEY (`mobile_cover_id`) REFERENCES `tipli_images_image` (`id`),
                                  CONSTRAINT `FK_947DBD9A6A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                  CONSTRAINT `FK_947DBD9A727ACA70` FOREIGN KEY (`parent_id`) REFERENCES `tipli_tags_tag` (`id`),
                                  CONSTRAINT `FK_947DBD9A922726E9` FOREIGN KEY (`cover_id`) REFERENCES `tipli_images_image` (`id`),
                                  CONSTRAINT `FK_947DBD9AE6DA28AA` FOREIGN KEY (`background_image_id`) REFERENCES `tipli_images_image` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_tags_tag_filter_tag`;
CREATE TABLE `tipli_tags_tag_filter_tag` (
                                             `tag_id` int(11) NOT NULL,
                                             `filter_tag_id` int(11) NOT NULL,
                                             PRIMARY KEY (`tag_id`,`filter_tag_id`),
                                             KEY `IDX_6D80F1F9BAD26311` (`tag_id`),
                                             KEY `IDX_6D80F1F997F93D6F` (`filter_tag_id`),
                                             CONSTRAINT `FK_6D80F1F997F93D6F` FOREIGN KEY (`filter_tag_id`) REFERENCES `tipli_tags_tag` (`id`),
                                             CONSTRAINT `FK_6D80F1F9BAD26311` FOREIGN KEY (`tag_id`) REFERENCES `tipli_tags_tag` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_tags_tag_group`;
CREATE TABLE `tipli_tags_tag_group` (
                                        `id` int(11) NOT NULL AUTO_INCREMENT,
                                        `type` varchar(255) NOT NULL,
                                        `name` varchar(255) NOT NULL,
                                        PRIMARY KEY (`id`),
                                        UNIQUE KEY `type_unique` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_tags_tag_group_tags`;
CREATE TABLE `tipli_tags_tag_group_tags` (
                                             `tag_id` int(11) NOT NULL,
                                             `tag_group_id` int(11) NOT NULL,
                                             PRIMARY KEY (`tag_id`,`tag_group_id`),
                                             KEY `IDX_CF3B08FEBAD26311` (`tag_id`),
                                             KEY `IDX_CF3B08FEC865A29C` (`tag_group_id`),
                                             CONSTRAINT `FK_CF3B08FEBAD26311` FOREIGN KEY (`tag_id`) REFERENCES `tipli_tags_tag` (`id`) ON DELETE CASCADE,
                                             CONSTRAINT `FK_CF3B08FEC865A29C` FOREIGN KEY (`tag_group_id`) REFERENCES `tipli_tags_tag_group` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_tags_tag_rule`;
CREATE TABLE `tipli_tags_tag_rule` (
                                       `id` int(11) NOT NULL AUTO_INCREMENT,
                                       `tag_id` int(11) DEFAULT NULL,
                                       `parent_id` int(11) DEFAULT NULL,
                                       `column` varchar(255) NOT NULL,
                                       `expressions` varchar(255) NOT NULL,
                                       `rule` varchar(255) NOT NULL,
                                       `condition` varchar(255) NOT NULL,
                                       `match_across_parent_tags` tinyint(1) NOT NULL,
                                       `created_at` datetime NOT NULL,
                                       PRIMARY KEY (`id`),
                                       KEY `IDX_2657C13CBAD26311` (`tag_id`),
                                       KEY `IDX_2657C13C727ACA70` (`parent_id`),
                                       CONSTRAINT `FK_2657C13C727ACA70` FOREIGN KEY (`parent_id`) REFERENCES `tipli_tags_tag_rule` (`id`),
                                       CONSTRAINT `FK_2657C13CBAD26311` FOREIGN KEY (`tag_id`) REFERENCES `tipli_tags_tag` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_tickets_ticket`;
CREATE TABLE `tipli_tickets_ticket` (
                                        `id` int(11) NOT NULL AUTO_INCREMENT,
                                        `user_id` int(11) DEFAULT NULL,
                                        `resolved_by_id` int(11) DEFAULT NULL,
                                        `type` varchar(255) NOT NULL,
                                        `reason_type` varchar(255) NOT NULL,
                                        `reason_message` varchar(255) NOT NULL,
                                        `scheduled_at` datetime NOT NULL,
                                        `accepted_at` datetime DEFAULT NULL,
                                        `declined_at` datetime DEFAULT NULL,
                                        `resolve_message` longtext DEFAULT NULL,
                                        `created_at` datetime NOT NULL,
                                        PRIMARY KEY (`id`),
                                        KEY `IDX_37B63E40A76ED395` (`user_id`),
                                        KEY `IDX_37B63E406713A32B` (`resolved_by_id`),
                                        CONSTRAINT `FK_37B63E406713A32B` FOREIGN KEY (`resolved_by_id`) REFERENCES `tipli_account_user` (`id`),
                                        CONSTRAINT `FK_37B63E40A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_tickspot_entry`;
CREATE TABLE `tipli_tickspot_entry` (
                                        `id` int(11) NOT NULL AUTO_INCREMENT,
                                        `user_id` int(11) DEFAULT NULL,
                                        `entry_id` int(11) NOT NULL,
                                        `entity_type` varchar(255) DEFAULT NULL,
                                        `entity_id` int(11) DEFAULT NULL,
                                        `user_name` varchar(255) DEFAULT NULL,
                                        `tick_user_id` int(11) DEFAULT NULL,
                                        `user_email` int(11) DEFAULT NULL,
                                        `task` longtext NOT NULL,
                                        `task_id` int(11) NOT NULL,
                                        `project_name` varchar(255) DEFAULT NULL,
                                        `date` datetime NOT NULL,
                                        `hours` double NOT NULL,
                                        `note` longtext DEFAULT NULL,
                                        `updated_at` datetime NOT NULL,
                                        `entry_created_at` datetime NOT NULL,
                                        `created_at` datetime NOT NULL,
                                        PRIMARY KEY (`id`),
                                        UNIQUE KEY `entry_id_unique` (`entry_id`),
                                        KEY `IDX_90FD0BAEA76ED395` (`user_id`),
                                        KEY `date_x` (`date`),
                                        CONSTRAINT `FK_90FD0BAEA76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_transactions_archived_transaction`;
CREATE TABLE `tipli_transactions_archived_transaction` (
                                                           `id` int(11) NOT NULL AUTO_INCREMENT,
                                                           `aggregated_transaction_id` int(11) DEFAULT NULL,
                                                           `user_id` int(11) DEFAULT NULL,
                                                           `shop_id` int(11) DEFAULT NULL,
                                                           `partner_system_id` int(11) DEFAULT NULL,
                                                           `utm_id` int(11) DEFAULT NULL,
                                                           `transaction_id` varchar(255) DEFAULT NULL,
                                                           `type` varchar(255) NOT NULL,
                                                           `currency` varchar(3) NOT NULL,
                                                           `commission_amount` decimal(10,3) NOT NULL,
                                                           `user_commission_amount` decimal(10,3) NOT NULL,
                                                           `bonus_amount` decimal(10,3) NOT NULL,
                                                           `billable` tinyint(1) NOT NULL,
                                                           `registered_at` datetime NOT NULL,
                                                           `confirmed_at` datetime DEFAULT NULL,
                                                           `created_at` datetime NOT NULL,
                                                           PRIMARY KEY (`id`),
                                                           KEY `IDX_4D0010EDA00C892` (`aggregated_transaction_id`),
                                                           KEY `IDX_4D0010EDA76ED395` (`user_id`),
                                                           KEY `IDX_4D0010ED4D16C4DD` (`shop_id`),
                                                           KEY `IDX_4D0010EDFB42B4B1` (`partner_system_id`),
                                                           KEY `IDX_4D0010EDB6334822` (`utm_id`),
                                                           KEY `transaction_id_idx` (`transaction_id`),
                                                           KEY `type_idx` (`type`),
                                                           KEY `currency_idx` (`currency`),
                                                           KEY `billable_idx` (`billable`),
                                                           KEY `registered_at_idx` (`registered_at`),
                                                           KEY `confirmed_at_idx` (`confirmed_at`),
                                                           KEY `created_at_idx` (`created_at`),
                                                           CONSTRAINT `FK_4D0010ED4D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                                           CONSTRAINT `FK_4D0010EDA00C892` FOREIGN KEY (`aggregated_transaction_id`) REFERENCES `tipli_transactions_transaction` (`id`),
                                                           CONSTRAINT `FK_4D0010EDA76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`),
                                                           CONSTRAINT `FK_4D0010EDB6334822` FOREIGN KEY (`utm_id`) REFERENCES `tipli_utm_utm` (`id`),
                                                           CONSTRAINT `FK_4D0010EDFB42B4B1` FOREIGN KEY (`partner_system_id`) REFERENCES `tipli_partner_systems_partner_system` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_transactions_archived_transaction_data`;
CREATE TABLE `tipli_transactions_archived_transaction_data` (
                                                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                                                `archived_transaction_id` int(11) DEFAULT NULL,
                                                                `related_transaction_id` int(11) DEFAULT NULL,
                                                                `related_recommended_user_id` int(11) DEFAULT NULL,
                                                                `registered_by_user_id` int(11) DEFAULT NULL,
                                                                `confirmed_by_user_id` int(11) DEFAULT NULL,
                                                                `name` varchar(255) DEFAULT NULL,
                                                                `share_coefficient` double DEFAULT NULL,
                                                                `order_amount` double NOT NULL,
                                                                `original_currency` varchar(3) DEFAULT NULL,
                                                                `original_commission_amount` double NOT NULL,
                                                                `original_confirmed_commission_amount` double DEFAULT NULL,
                                                                `confirmation_treshold` double NOT NULL,
                                                                `recommendation_bonus_treshold` double NOT NULL,
                                                                `prepared_for_confirm` tinyint(1) NOT NULL,
                                                                `original_turnover` double NOT NULL,
                                                                `original_income` double NOT NULL,
                                                                `turnover` double NOT NULL,
                                                                `income` double NOT NULL,
                                                                `confirmed_by_scoring` tinyint(1) NOT NULL,
                                                                PRIMARY KEY (`id`),
                                                                UNIQUE KEY `UNIQ_355CE6C1BDE8E551` (`archived_transaction_id`),
                                                                KEY `IDX_355CE6C14F981710` (`related_transaction_id`),
                                                                KEY `IDX_355CE6C1FC173A94` (`related_recommended_user_id`),
                                                                KEY `IDX_355CE6C11DE0E288` (`registered_by_user_id`),
                                                                KEY `IDX_355CE6C110435CFB` (`confirmed_by_user_id`),
                                                                CONSTRAINT `FK_355CE6C110435CFB` FOREIGN KEY (`confirmed_by_user_id`) REFERENCES `tipli_account_user` (`id`),
                                                                CONSTRAINT `FK_355CE6C11DE0E288` FOREIGN KEY (`registered_by_user_id`) REFERENCES `tipli_account_user` (`id`),
                                                                CONSTRAINT `FK_355CE6C14F981710` FOREIGN KEY (`related_transaction_id`) REFERENCES `tipli_transactions_transaction` (`id`),
                                                                CONSTRAINT `FK_355CE6C1BDE8E551` FOREIGN KEY (`archived_transaction_id`) REFERENCES `tipli_transactions_archived_transaction` (`id`),
                                                                CONSTRAINT `FK_355CE6C1FC173A94` FOREIGN KEY (`related_recommended_user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_transactions_imported_transaction`;
CREATE TABLE `tipli_transactions_imported_transaction` (
                                                           `id` int(11) NOT NULL AUTO_INCREMENT,
                                                           `transaction_id` int(11) DEFAULT NULL,
                                                           `partner_system_id` int(11) DEFAULT NULL,
                                                           `unique_identifier` varchar(16) DEFAULT NULL,
                                                           `new_unique_identifier` varchar(16) DEFAULT NULL,
                                                           `user_id` varchar(255) DEFAULT NULL,
                                                           `original_transaction_id` varchar(255) DEFAULT NULL,
                                                           `commission_amount` varchar(255) DEFAULT NULL,
                                                           `order_amount` varchar(255) DEFAULT NULL,
                                                           `currency` varchar(3) NOT NULL,
                                                           `status` varchar(255) NOT NULL,
                                                           `registered_at` datetime NOT NULL,
                                                           `confirmed_at` datetime DEFAULT NULL,
                                                           `shop_domain` varchar(255) DEFAULT NULL,
                                                           `channel` varchar(255) DEFAULT NULL,
                                                           `partner_system_key` varchar(255) DEFAULT NULL,
                                                           `created_at` datetime NOT NULL,
                                                           PRIMARY KEY (`id`),
                                                           UNIQUE KEY `unique_identifier` (`unique_identifier`),
                                                           KEY `IDX_70CB32182FC0CB0F` (`transaction_id`),
                                                           KEY `IDX_70CB3218FB42B4B1` (`partner_system_id`),
                                                           CONSTRAINT `FK_70CB32182FC0CB0F` FOREIGN KEY (`transaction_id`) REFERENCES `tipli_transactions_transaction` (`id`),
                                                           CONSTRAINT `FK_70CB3218FB42B4B1` FOREIGN KEY (`partner_system_id`) REFERENCES `tipli_partner_systems_partner_system` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_transactions_imported_transaction_fail`;
CREATE TABLE `tipli_transactions_imported_transaction_fail` (
                                                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                                                `partner_system_id` int(11) DEFAULT NULL,
                                                                `checked_by` int(11) DEFAULT NULL,
                                                                `transaction_id` varchar(255) DEFAULT NULL,
                                                                `note` longtext NOT NULL,
                                                                `token` varchar(255) NOT NULL,
                                                                `checked_at` datetime DEFAULT NULL,
                                                                `created_at` datetime NOT NULL,
                                                                PRIMARY KEY (`id`),
                                                                KEY `IDX_6375B76CFB42B4B1` (`partner_system_id`),
                                                                KEY `IDX_6375B76C82E3C2DE` (`checked_by`),
                                                                CONSTRAINT `FK_6375B76C82E3C2DE` FOREIGN KEY (`checked_by`) REFERENCES `tipli_account_user` (`id`),
                                                                CONSTRAINT `FK_6375B76CFB42B4B1` FOREIGN KEY (`partner_system_id`) REFERENCES `tipli_partner_systems_partner_system` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_transactions_transaction`;
CREATE TABLE `tipli_transactions_transaction` (
                                                  `id` int(11) NOT NULL AUTO_INCREMENT,
                                                  `user_id` int(11) DEFAULT NULL,
                                                  `shop_id` int(11) DEFAULT NULL,
                                                  `partner_system_id` int(11) DEFAULT NULL,
                                                  `utm_id` int(11) DEFAULT NULL,
                                                  `transaction_id` varchar(255) DEFAULT NULL,
                                                  `unique_id` varchar(16) DEFAULT NULL,
                                                  `type` varchar(255) NOT NULL,
                                                  `currency` varchar(3) NOT NULL,
                                                  `commission_amount` decimal(10,3) NOT NULL,
                                                  `user_commission_amount` decimal(10,3) NOT NULL,
                                                  `bonus_amount` decimal(10,3) NOT NULL,
                                                  `billable` tinyint(1) NOT NULL,
                                                  `registered_at` datetime NOT NULL,
                                                  `confirmed_at` datetime DEFAULT NULL,
                                                  `created_at` datetime NOT NULL,
                                                  PRIMARY KEY (`id`),
                                                  UNIQUE KEY `unique_id` (`unique_id`),
                                                  KEY `IDX_F3589ED0A76ED395` (`user_id`),
                                                  KEY `IDX_F3589ED04D16C4DD` (`shop_id`),
                                                  KEY `IDX_F3589ED0FB42B4B1` (`partner_system_id`),
                                                  KEY `IDX_F3589ED0B6334822` (`utm_id`),
                                                  KEY `transaction_id_idx` (`transaction_id`),
                                                  KEY `type_idx` (`type`),
                                                  KEY `currency_idx` (`currency`),
                                                  KEY `billable_idx` (`billable`),
                                                  KEY `registered_at_idx` (`registered_at`),
                                                  KEY `confirmed_at_idx` (`confirmed_at`),
                                                  KEY `created_at_idx` (`created_at`),
                                                  CONSTRAINT `FK_F3589ED04D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                                  CONSTRAINT `FK_F3589ED0A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`),
                                                  CONSTRAINT `FK_F3589ED0B6334822` FOREIGN KEY (`utm_id`) REFERENCES `tipli_utm_utm` (`id`),
                                                  CONSTRAINT `FK_F3589ED0FB42B4B1` FOREIGN KEY (`partner_system_id`) REFERENCES `tipli_partner_systems_partner_system` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_transactions_transaction_check`;
CREATE TABLE `tipli_transactions_transaction_check` (
                                                        `id` int(11) NOT NULL AUTO_INCREMENT,
                                                        `imported_transaction_id` int(11) DEFAULT NULL,
                                                        `shop_id` int(11) DEFAULT NULL,
                                                        `partner_system_id` int(11) DEFAULT NULL,
                                                        `checked_by_user_id` int(11) DEFAULT NULL,
                                                        `transaction_id` varchar(255) NOT NULL,
                                                        `status` varchar(16) NOT NULL,
                                                        `reasons` varchar(64) NOT NULL,
                                                        `checked_at` datetime DEFAULT NULL,
                                                        `created_at` datetime NOT NULL,
                                                        PRIMARY KEY (`id`),
                                                        KEY `IDX_73852F15A5E3094F` (`imported_transaction_id`),
                                                        KEY `IDX_73852F154D16C4DD` (`shop_id`),
                                                        KEY `IDX_73852F15FB42B4B1` (`partner_system_id`),
                                                        KEY `IDX_73852F15C0C552BA` (`checked_by_user_id`),
                                                        CONSTRAINT `FK_73852F154D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                                        CONSTRAINT `FK_73852F15A5E3094F` FOREIGN KEY (`imported_transaction_id`) REFERENCES `tipli_transactions_imported_transaction` (`id`),
                                                        CONSTRAINT `FK_73852F15C0C552BA` FOREIGN KEY (`checked_by_user_id`) REFERENCES `tipli_account_user` (`id`),
                                                        CONSTRAINT `FK_73852F15FB42B4B1` FOREIGN KEY (`partner_system_id`) REFERENCES `tipli_partner_systems_partner_system` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_transactions_transaction_comment`;
CREATE TABLE `tipli_transactions_transaction_comment` (
                                                          `id` int(11) NOT NULL AUTO_INCREMENT,
                                                          `transaction_id` int(11) DEFAULT NULL,
                                                          `user_id` int(11) DEFAULT NULL,
                                                          `text` longtext NOT NULL,
                                                          `created_at` datetime NOT NULL,
                                                          PRIMARY KEY (`id`),
                                                          KEY `IDX_B69F687B2FC0CB0F` (`transaction_id`),
                                                          KEY `IDX_B69F687BA76ED395` (`user_id`),
                                                          CONSTRAINT `FK_B69F687B2FC0CB0F` FOREIGN KEY (`transaction_id`) REFERENCES `tipli_transactions_transaction` (`id`),
                                                          CONSTRAINT `FK_B69F687BA76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_transactions_transaction_correction`;
CREATE TABLE `tipli_transactions_transaction_correction` (
                                                             `id` int(11) NOT NULL AUTO_INCREMENT,
                                                             `transaction_id` int(11) DEFAULT NULL,
                                                             `shop_id` int(11) DEFAULT NULL,
                                                             `user_id` int(11) DEFAULT NULL,
                                                             `original_user_commission_amount` decimal(10,3) NOT NULL,
                                                             `corrected_user_commission_amount` decimal(10,3) NOT NULL,
                                                             `currency` varchar(255) NOT NULL,
                                                             `reason` varchar(255) DEFAULT NULL,
                                                             `created_at` datetime NOT NULL,
                                                             PRIMARY KEY (`id`),
                                                             UNIQUE KEY `UNIQ_A985F1E62FC0CB0F` (`transaction_id`),
                                                             KEY `IDX_A985F1E64D16C4DD` (`shop_id`),
                                                             KEY `IDX_A985F1E6A76ED395` (`user_id`),
                                                             CONSTRAINT `FK_A985F1E62FC0CB0F` FOREIGN KEY (`transaction_id`) REFERENCES `tipli_transactions_transaction` (`id`),
                                                             CONSTRAINT `FK_A985F1E64D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                                             CONSTRAINT `FK_A985F1E6A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_transactions_transaction_data`;
CREATE TABLE `tipli_transactions_transaction_data` (
                                                       `id` int(11) NOT NULL AUTO_INCREMENT,
                                                       `transaction_id` int(11) DEFAULT NULL,
                                                       `related_transaction_id` int(11) DEFAULT NULL,
                                                       `related_recommended_user_id` int(11) DEFAULT NULL,
                                                       `registered_by_user_id` int(11) DEFAULT NULL,
                                                       `confirmed_by_user_id` int(11) DEFAULT NULL,
                                                       `deal_id` int(11) DEFAULT NULL,
                                                       `name` varchar(255) DEFAULT NULL,
                                                       `share_coefficient` double DEFAULT NULL,
                                                       `order_amount` double NOT NULL,
                                                       `original_currency` varchar(3) DEFAULT NULL,
                                                       `original_commission_amount` double NOT NULL,
                                                       `original_confirmed_commission_amount` double DEFAULT NULL,
                                                       `confirmation_treshold` double NOT NULL,
                                                       `recommendation_bonus_treshold` double NOT NULL,
                                                       `prepared_for_confirm` tinyint(1) NOT NULL,
                                                       `original_turnover` double NOT NULL,
                                                       `original_income` double NOT NULL,
                                                       `turnover` double NOT NULL,
                                                       `income` double NOT NULL,
                                                       `shop_confirmation_rate` double DEFAULT NULL,
                                                       `confirmed_by_scoring` tinyint(1) NOT NULL,
                                                       `platform` varchar(7) DEFAULT NULL,
                                                       `imported_at` datetime DEFAULT NULL,
                                                       `expired_at` datetime DEFAULT NULL,
                                                       `is_paid` tinyint(1) NOT NULL,
                                                       PRIMARY KEY (`id`),
                                                       UNIQUE KEY `UNIQ_7982A7C02FC0CB0F` (`transaction_id`),
                                                       UNIQUE KEY `UNIQ_7982A7C0FC173A94` (`related_recommended_user_id`),
                                                       KEY `IDX_7982A7C04F981710` (`related_transaction_id`),
                                                       KEY `IDX_7982A7C01DE0E288` (`registered_by_user_id`),
                                                       KEY `IDX_7982A7C010435CFB` (`confirmed_by_user_id`),
                                                       KEY `IDX_7982A7C0F60E2305` (`deal_id`),
                                                       CONSTRAINT `FK_7982A7C010435CFB` FOREIGN KEY (`confirmed_by_user_id`) REFERENCES `tipli_account_user` (`id`),
                                                       CONSTRAINT `FK_7982A7C01DE0E288` FOREIGN KEY (`registered_by_user_id`) REFERENCES `tipli_account_user` (`id`),
                                                       CONSTRAINT `FK_7982A7C02FC0CB0F` FOREIGN KEY (`transaction_id`) REFERENCES `tipli_transactions_transaction` (`id`),
                                                       CONSTRAINT `FK_7982A7C04F981710` FOREIGN KEY (`related_transaction_id`) REFERENCES `tipli_transactions_transaction` (`id`),
                                                       CONSTRAINT `FK_7982A7C0F60E2305` FOREIGN KEY (`deal_id`) REFERENCES `tipli_deals_deal` (`id`),
                                                       CONSTRAINT `FK_7982A7C0FC173A94` FOREIGN KEY (`related_recommended_user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_transactions_transaction_log`;
CREATE TABLE `tipli_transactions_transaction_log` (
                                                      `id` int(11) NOT NULL AUTO_INCREMENT,
                                                      `transaction_id` int(11) DEFAULT NULL,
                                                      `original_confirmed_commission_amount` double DEFAULT NULL,
                                                      `created_at` datetime NOT NULL,
                                                      PRIMARY KEY (`id`),
                                                      KEY `IDX_455B29A62FC0CB0F` (`transaction_id`),
                                                      CONSTRAINT `FK_455B29A62FC0CB0F` FOREIGN KEY (`transaction_id`) REFERENCES `tipli_transactions_transaction` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_transactions_transaction_process`;
CREATE TABLE `tipli_transactions_transaction_process` (
                                                          `id` int(11) NOT NULL AUTO_INCREMENT,
                                                          `transaction_id` int(11) DEFAULT NULL,
                                                          `check_bonuses_processed_at` datetime DEFAULT NULL,
                                                          `check_suspected_transaction_processed_at` datetime DEFAULT NULL,
                                                          `pair_redirection_processed_at` datetime DEFAULT NULL,
                                                          `bonus_registration_email_processed_at` datetime DEFAULT NULL,
                                                          `campaign_create_bonus_processed_at` datetime DEFAULT NULL,
                                                          `campaign_confirm_bonus_processed_at` datetime DEFAULT NULL,
                                                          `campaign_finish_processed_at` datetime DEFAULT NULL,
                                                          `refresh_accounting_processed_at` datetime DEFAULT NULL,
                                                          `created_at` datetime NOT NULL,
                                                          PRIMARY KEY (`id`),
                                                          KEY `IDX_A4F622812FC0CB0F` (`transaction_id`),
                                                          CONSTRAINT `FK_A4F622812FC0CB0F` FOREIGN KEY (`transaction_id`) REFERENCES `tipli_transactions_transaction` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_transactions_transaction_suspected_transaction`;
CREATE TABLE `tipli_transactions_transaction_suspected_transaction` (
                                                                        `id` int(11) NOT NULL AUTO_INCREMENT,
                                                                        `transaction_id` int(11) DEFAULT NULL,
                                                                        `checked_by` int(11) DEFAULT NULL,
                                                                        `reason` varchar(255) DEFAULT NULL,
                                                                        `checked_at` datetime DEFAULT NULL,
                                                                        `created_at` datetime NOT NULL,
                                                                        PRIMARY KEY (`id`),
                                                                        UNIQUE KEY `UNIQ_B74894FC2FC0CB0F` (`transaction_id`),
                                                                        KEY `IDX_B74894FC82E3C2DE` (`checked_by`),
                                                                        CONSTRAINT `FK_B74894FC2FC0CB0F` FOREIGN KEY (`transaction_id`) REFERENCES `tipli_transactions_transaction` (`id`),
                                                                        CONSTRAINT `FK_B74894FC82E3C2DE` FOREIGN KEY (`checked_by`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_triggers_trigger`;
CREATE TABLE `tipli_triggers_trigger` (
                                          `id` int(11) NOT NULL AUTO_INCREMENT,
                                          `user_id` int(11) DEFAULT NULL,
                                          `type` varchar(64) NOT NULL,
                                          `data` longtext DEFAULT NULL,
                                          `scheduled_at` datetime NOT NULL,
                                          `enqueued_at` datetime DEFAULT NULL,
                                          `discarded_at` datetime DEFAULT NULL,
                                          `confirmed_at` datetime DEFAULT NULL,
                                          `message` varchar(255) DEFAULT NULL,
                                          `created_at` datetime NOT NULL,
                                          PRIMARY KEY (`id`),
                                          KEY `IDX_A829E92BA76ED395` (`user_id`),
                                          CONSTRAINT `FK_A829E92BA76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_utm_utm`;
CREATE TABLE `tipli_utm_utm` (
                                 `id` int(11) NOT NULL AUTO_INCREMENT,
                                 `localization_id` int(11) DEFAULT NULL,
                                 `utm_source` varchar(255) DEFAULT NULL,
                                 `utm_medium` varchar(255) DEFAULT NULL,
                                 `utm_campaign` varchar(255) DEFAULT NULL,
                                 `utm_content` varchar(255) DEFAULT NULL,
                                 `utm_keyword` varchar(255) DEFAULT NULL,
                                 `chained_utm` varchar(255) NOT NULL,
                                 `created_at` datetime NOT NULL,
                                 PRIMARY KEY (`id`),
                                 UNIQUE KEY `utm_unique` (`localization_id`,`chained_utm`),
                                 KEY `IDX_7FB8716E6A2856C7` (`localization_id`),
                                 CONSTRAINT `FK_7FB8716E6A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_utm_utm_cost`;
CREATE TABLE `tipli_utm_utm_cost` (
                                      `id` int(11) NOT NULL AUTO_INCREMENT,
                                      `utm_id` int(11) DEFAULT NULL,
                                      `amount` double NOT NULL,
                                      `link_clicks` int(11) DEFAULT NULL,
                                      `video10sec_watched` int(11) DEFAULT NULL,
                                      `cost_per10sec_video_view` double DEFAULT NULL,
                                      `paid_from` datetime NOT NULL,
                                      `paid_to` datetime NOT NULL,
                                      `created_at` datetime NOT NULL,
                                      PRIMARY KEY (`id`),
                                      KEY `IDX_D36820A3B6334822` (`utm_id`),
                                      KEY `paid_from_idx` (`paid_from`),
                                      KEY `paid_to_idx` (`paid_to`),
                                      CONSTRAINT `FK_D36820A3B6334822` FOREIGN KEY (`utm_id`) REFERENCES `tipli_utm_utm` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_utm_utm_data`;
CREATE TABLE `tipli_utm_utm_data` (
                                      `id` int(11) NOT NULL AUTO_INCREMENT,
                                      `utm_id` int(11) DEFAULT NULL,
                                      `active` tinyint(1) NOT NULL DEFAULT 1,
                                      `ad_account_id` varchar(255) DEFAULT NULL,
                                      `adset_id` varchar(255) DEFAULT NULL,
                                      `ads` longtext DEFAULT NULL,
                                      `created_at` datetime NOT NULL,
                                      PRIMARY KEY (`id`),
                                      KEY `IDX_66BD473CB6334822` (`utm_id`),
                                      CONSTRAINT `FK_66BD473CB6334822` FOREIGN KEY (`utm_id`) REFERENCES `tipli_utm_utm` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_vouchers_user_voucher`;
CREATE TABLE `tipli_vouchers_user_voucher` (
                                               `id` int(11) NOT NULL AUTO_INCREMENT,
                                               `user_id` int(11) DEFAULT NULL,
                                               `voucher_campaign_id` int(11) DEFAULT NULL,
                                               `status` varchar(255) NOT NULL,
                                               `activated_at` datetime DEFAULT NULL,
                                               `valid_till` datetime DEFAULT NULL,
                                               `finished_at` datetime DEFAULT NULL,
                                               `created_at` datetime NOT NULL,
                                               PRIMARY KEY (`id`),
                                               KEY `IDX_E7064686A76ED395` (`user_id`),
                                               KEY `IDX_E7064686220D36F2` (`voucher_campaign_id`),
                                               CONSTRAINT `FK_E7064686220D36F2` FOREIGN KEY (`voucher_campaign_id`) REFERENCES `tipli_vouchers_voucher_campaign` (`id`),
                                               CONSTRAINT `FK_E7064686A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_vouchers_user_voucher_event`;
CREATE TABLE `tipli_vouchers_user_voucher_event` (
                                                     `id` int(11) NOT NULL AUTO_INCREMENT,
                                                     `user_voucher_id` int(11) DEFAULT NULL,
                                                     `unique_id` varchar(32) NOT NULL,
                                                     `event` varchar(32) NOT NULL,
                                                     `created_at` datetime NOT NULL,
                                                     PRIMARY KEY (`id`),
                                                     UNIQUE KEY `unique_idx` (`unique_id`),
                                                     KEY `IDX_270A25EE8FCDDD83` (`user_voucher_id`),
                                                     CONSTRAINT `FK_270A25EE8FCDDD83` FOREIGN KEY (`user_voucher_id`) REFERENCES `tipli_vouchers_user_voucher` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_vouchers_user_voucher_transaction`;
CREATE TABLE `tipli_vouchers_user_voucher_transaction` (
                                                           `id` int(11) NOT NULL AUTO_INCREMENT,
                                                           `user_id` int(11) DEFAULT NULL,
                                                           `user_voucher_id` int(11) DEFAULT NULL,
                                                           `bonus_transaction_id` int(11) DEFAULT NULL,
                                                           `transaction_id` int(11) DEFAULT NULL,
                                                           `created_at` datetime NOT NULL,
                                                           PRIMARY KEY (`id`),
                                                           KEY `IDX_CEC432E6A76ED395` (`user_id`),
                                                           KEY `IDX_CEC432E68FCDDD83` (`user_voucher_id`),
                                                           KEY `IDX_CEC432E67E347B44` (`bonus_transaction_id`),
                                                           KEY `IDX_CEC432E62FC0CB0F` (`transaction_id`),
                                                           CONSTRAINT `FK_CEC432E62FC0CB0F` FOREIGN KEY (`transaction_id`) REFERENCES `tipli_transactions_transaction` (`id`),
                                                           CONSTRAINT `FK_CEC432E67E347B44` FOREIGN KEY (`bonus_transaction_id`) REFERENCES `tipli_transactions_transaction` (`id`),
                                                           CONSTRAINT `FK_CEC432E68FCDDD83` FOREIGN KEY (`user_voucher_id`) REFERENCES `tipli_vouchers_user_voucher` (`id`),
                                                           CONSTRAINT `FK_CEC432E6A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_vouchers_voucher_campaign`;
CREATE TABLE `tipli_vouchers_voucher_campaign` (
                                                   `id` int(11) NOT NULL AUTO_INCREMENT,
                                                   `created_by_user_id` int(11) DEFAULT NULL,
                                                   `localization_id` int(11) DEFAULT NULL,
                                                   `shop_id` int(11) DEFAULT NULL,
                                                   `unique_hash` varchar(64) NOT NULL,
                                                   `name` varchar(255) NOT NULL,
                                                   `internal_name` varchar(255) NOT NULL,
                                                   `description` varchar(255) NOT NULL,
                                                   `reward_type` varchar(255) NOT NULL,
                                                   `public_since` datetime NOT NULL,
                                                   `valid_since` datetime NOT NULL,
                                                   `valid_till` datetime NOT NULL,
                                                   `valid_days_from_activation` int(11) DEFAULT NULL,
                                                   `minimal_order_amount` double DEFAULT NULL,
                                                   `bonus_amount` double DEFAULT NULL,
                                                   `bonus_multiplier` double DEFAULT NULL,
                                                   `total_bonus_budget` double DEFAULT NULL,
                                                   `is_one_time_bonus` tinyint(1) DEFAULT NULL,
                                                   `auto_activate` tinyint(1) NOT NULL,
                                                   `is_hidden` tinyint(1) NOT NULL,
                                                   `reusable_for_user` tinyint(1) NOT NULL,
                                                   `removed_at` datetime DEFAULT NULL,
                                                   `created_at` datetime NOT NULL,
                                                   PRIMARY KEY (`id`),
                                                   KEY `IDX_722ECB27D182D95` (`created_by_user_id`),
                                                   KEY `IDX_722ECB26A2856C7` (`localization_id`),
                                                   KEY `IDX_722ECB24D16C4DD` (`shop_id`),
                                                   KEY `unique_hash_idx` (`unique_hash`),
                                                   KEY `public_since_idx` (`public_since`),
                                                   KEY `valid_since_idx` (`valid_since`),
                                                   KEY `valid_till_idx` (`valid_till`),
                                                   CONSTRAINT `FK_722ECB24D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                                   CONSTRAINT `FK_722ECB26A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                                   CONSTRAINT `FK_722ECB27D182D95` FOREIGN KEY (`created_by_user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_vouchers_voucher_campaign_condition`;
CREATE TABLE `tipli_vouchers_voucher_campaign_condition` (
                                                             `id` int(11) NOT NULL AUTO_INCREMENT,
                                                             `voucher_campaign_id` int(11) DEFAULT NULL,
                                                             `last_transaction_before` datetime DEFAULT NULL,
                                                             `segment` varchar(255) DEFAULT NULL,
                                                             `without_addon_installed` tinyint(1) DEFAULT NULL,
                                                             `without_recommended_user` tinyint(1) DEFAULT NULL,
                                                             `required_transaction_in_shop_ids` longtext DEFAULT NULL,
                                                             `no_transaction_in_shop_ids` longtext DEFAULT NULL,
                                                             `only_new_users` tinyint(1) DEFAULT NULL,
                                                             PRIMARY KEY (`id`),
                                                             UNIQUE KEY `UNIQ_D8BF5B00220D36F2` (`voucher_campaign_id`),
                                                             CONSTRAINT `FK_D8BF5B00220D36F2` FOREIGN KEY (`voucher_campaign_id`) REFERENCES `tipli_vouchers_voucher_campaign` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_zasilkovna_approval_synchronization`;
CREATE TABLE `tipli_zasilkovna_approval_synchronization` (
                                                             `id` int(11) NOT NULL AUTO_INCREMENT,
                                                             `user_id` int(11) DEFAULT NULL,
                                                             `email` varchar(255) DEFAULT NULL,
                                                             `action` varchar(10) NOT NULL,
                                                             `scheduled_at` datetime NOT NULL,
                                                             `synchronized_at` datetime DEFAULT NULL,
                                                             `result_message` varchar(255) DEFAULT NULL,
                                                             `created_at` datetime NOT NULL,
                                                             PRIMARY KEY (`id`),
                                                             KEY `IDX_4652A32AA76ED395` (`user_id`),
                                                             CONSTRAINT `FK_4652A32AA76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_zasilkovna_packet`;
CREATE TABLE `tipli_zasilkovna_packet` (
                                           `id` int(11) NOT NULL AUTO_INCREMENT,
                                           `user_id` int(11) DEFAULT NULL,
                                           `packet_id` varchar(255) DEFAULT NULL,
                                           `created_at` datetime NOT NULL,
                                           PRIMARY KEY (`id`),
                                           KEY `IDX_4A6D4AADA76ED395` (`user_id`),
                                           CONSTRAINT `FK_4A6D4AADA76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

DROP TABLE IF EXISTS `hash_experiment_variant`;
CREATE TABLE `hash_experiment_variant` (
                                           `id` int(11) NOT NULL AUTO_INCREMENT,
                                           `user_id` int(11) DEFAULT NULL,
                                           `hash` varchar(255) NOT NULL,
                                           `experiment_id` varchar(255) NOT NULL,
                                           `variant` varchar(255) NOT NULL,
                                           `timestamp` int(11) DEFAULT NULL,
                                           `source` varchar(255) DEFAULT NULL,
                                           `created_at` datetime NOT NULL,
                                           PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 2024-10-14 17:20:36

DROP TABLE IF EXISTS `tipli_account_user_lucky_shop_data`;
CREATE TABLE `tipli_account_user_lucky_shop_data` (
                                                      `id` int(11) NOT NULL AUTO_INCREMENT,
                                                      `user_id` int(11) DEFAULT NULL,
                                                      `joined_at` datetime NOT NULL,
                                                      `check_streak` int(11) DEFAULT NULL,
                                                      `last_lucky_shop_check_at` datetime DEFAULT NULL,
                                                      `last_user_lucky_shop_from_transaction_created_at` datetime DEFAULT NULL,
                                                      PRIMARY KEY (`id`),
                                                      KEY `IDX_C74CED27A76ED395` (`user_id`),
                                                      CONSTRAINT `FK_C74CED27A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_lucky_shop_lucky_shops`;
CREATE TABLE `tipli_lucky_shop_lucky_shops` (
                                                `id` int(11) NOT NULL AUTO_INCREMENT,
                                                `shop_id` int(11) DEFAULT NULL,
                                                `lucky_shop_campaign_id` int(11) DEFAULT NULL,
                                                `user_reward_requests_close_at` datetime NOT NULL,
                                                `rewards_processed_at` datetime DEFAULT NULL,
                                                `count_of_users` int(11) NOT NULL,
                                                `count_of_users_with_check` int(11) NOT NULL,
                                                `valid_since` datetime NOT NULL,
                                                `created_at` datetime NOT NULL,
                                                PRIMARY KEY (`id`),
                                                KEY `IDX_D801E1604D16C4DD` (`shop_id`),
                                                KEY `IDX_D801E1609E29FD0B` (`lucky_shop_campaign_id`),
                                                CONSTRAINT `FK_D801E1604D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                                CONSTRAINT `FK_D801E1609E29FD0B` FOREIGN KEY (`lucky_shop_campaign_id`) REFERENCES `tipli_lucky_shop_lucky_shop_campaigns` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_lucky_shop_lucky_shop_campaigns`;
CREATE TABLE `tipli_lucky_shop_lucky_shop_campaigns` (
                                                         `id` int(11) NOT NULL AUTO_INCREMENT,
                                                         `localization_id` int(11) DEFAULT NULL,
                                                         `name` varchar(255) NOT NULL,
                                                         `total_reward_amount` int(11) NOT NULL,
                                                         `last_lucky_shop_valid_since` datetime NOT NULL,
                                                         `process_at` datetime NOT NULL,
                                                         `process_time_shift` varchar(255) NOT NULL,
                                                         `processed_at` datetime DEFAULT NULL,
                                                         `user_reward_requests_time_shift` varchar(255) NOT NULL,
                                                         `created_at` datetime NOT NULL,
                                                         PRIMARY KEY (`id`),
                                                         KEY `IDX_FD11A6836A2856C7` (`localization_id`),
                                                         CONSTRAINT `FK_FD11A6836A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_lucky_shop_user_lucky_shops`;
CREATE TABLE `tipli_lucky_shop_user_lucky_shops` (
                                                     `id` int(11) NOT NULL AUTO_INCREMENT,
                                                     `user_id` int(11) NOT NULL,
                                                     `localization_id` int(11) DEFAULT NULL,
                                                     `shop_id` int(11) DEFAULT NULL,
                                                     `source` varchar(255) NOT NULL,
                                                     `valid_since` datetime NOT NULL,
                                                     `valid_till` datetime NOT NULL,
                                                     `original_valid_since` datetime NOT NULL,
                                                     `updated_at` datetime DEFAULT NULL,
                                                     `created_at` datetime NOT NULL,
                                                     PRIMARY KEY (`id`),
                                                     KEY `IDX_E62D237EA76ED395` (`user_id`),
                                                     KEY `IDX_E62D237E6A2856C7` (`localization_id`),
                                                     KEY `IDX_E62D237E4D16C4DD` (`shop_id`),
                                                     CONSTRAINT `FK_E62D237E4D16C4DD` FOREIGN KEY (`shop_id`) REFERENCES `tipli_shops_shop` (`id`),
                                                     CONSTRAINT `FK_E62D237E6A2856C7` FOREIGN KEY (`localization_id`) REFERENCES `tipli_localization_localization` (`id`),
                                                     CONSTRAINT `FK_E62D237EA76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `tipli_lucky_shop_user_lucky_shop_checks`;
CREATE TABLE `tipli_lucky_shop_user_lucky_shop_checks` (
                                                           `id` int(11) NOT NULL AUTO_INCREMENT,
                                                           `user_id` int(11) NOT NULL,
                                                           `lucky_shop_campaign_id` int(11) DEFAULT NULL,
                                                           `user_lucky_shop_id` int(11) DEFAULT NULL,
                                                           `lucky_shop_id` int(11) DEFAULT NULL,
                                                           `transaction_id` int(11) DEFAULT NULL,
                                                           `has_win` tinyint(1) NOT NULL,
                                                           `created_at` datetime NOT NULL,
                                                           PRIMARY KEY (`id`),
                                                           KEY `IDX_DFEFB105A76ED395` (`user_id`),
                                                           KEY `IDX_DFEFB1059E29FD0B` (`lucky_shop_campaign_id`),
                                                           KEY `IDX_DFEFB1052917FE6A` (`user_lucky_shop_id`),
                                                           KEY `IDX_DFEFB105C799533A` (`lucky_shop_id`),
                                                           KEY `IDX_DFEFB1052FC0CB0F` (`transaction_id`),
                                                           CONSTRAINT `FK_DFEFB1052917FE6A` FOREIGN KEY (`user_lucky_shop_id`) REFERENCES `tipli_lucky_shop_user_lucky_shops` (`id`),
                                                           CONSTRAINT `FK_DFEFB1052FC0CB0F` FOREIGN KEY (`transaction_id`) REFERENCES `tipli_transactions_transaction` (`id`),
                                                           CONSTRAINT `FK_DFEFB1059E29FD0B` FOREIGN KEY (`lucky_shop_campaign_id`) REFERENCES `tipli_lucky_shop_lucky_shop_campaigns` (`id`),
                                                           CONSTRAINT `FK_DFEFB105A76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`),
                                                           CONSTRAINT `FK_DFEFB105C799533A` FOREIGN KEY (`lucky_shop_id`) REFERENCES `tipli_lucky_shop_lucky_shops` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `tipli_account_user_mailkit` (
                                              `id` int(11) NOT NULL AUTO_INCREMENT,
                                              `user_id` int(11) DEFAULT NULL,
                                              `status` varchar(12) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                              `email_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
                                              `synchronize_at` datetime DEFAULT NULL,
                                              `synchronized_at` datetime DEFAULT NULL,
                                              PRIMARY KEY (`id`),
                                              UNIQUE KEY `UNIQ_61057C6AA76ED395` (`user_id`),
                                              KEY `status` (`status`),
                                              CONSTRAINT `FK_61057C6AA76ED395` FOREIGN KEY (`user_id`) REFERENCES `tipli_account_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;