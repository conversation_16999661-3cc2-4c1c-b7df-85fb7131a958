module.exports = function(grunt) {

  // Nacteme vsechny grunt tasky a urychlime
  require('jit-grunt')(grunt, {
    sprite: 'grunt-spritesmith',
  });

  grunt.initConfig({

    pkg: grunt.file.readJSON('package.json'),

    // Promenne
    assetsDir: 'www/',
    assetsPartnerDir: 'www/scss/partner/',
    assetsB2bDir: 'www/scss/b2b/',

    // SASS - SASS kompilace
    sass: {
      front: {
        options: {
          sourcemap: 'none',
          style: 'expanded'
        },
        files: {
          '<%= assetsDir %>css/bootstrap/bootstrap.css': '<%= assetsDir %>scss/bootstrap/main.bootstrap.scss',
          '<%= assetsDir %>css/layout/layout.css': '<%= assetsDir %>scss/layout/main.layout.scss',
          '<%= assetsDir %>css/main.front.css': '<%= assetsDir %>scss/main.front.scss',
          '<%= assetsDir %>css/shop/shop.css': '<%= assetsDir %>scss/shop/main.shop.scss',
          '<%= assetsDir %>css/sale/sale.css': '<%= assetsDir %>scss/sale/main.sale.scss',
          '<%= assetsDir %>css/account/account.css': '<%= assetsDir %>scss/account/main.account.scss',
          '<%= assetsDir %>css/verify/verify.css': '<%= assetsDir %>scss/verify/main.verify.scss',
          '<%= assetsDir %>css/leaflet/leaflet.css': '<%= assetsDir %>scss/leaflet/main.leaflet.scss',
          '<%= assetsDir %>css/hub/hub.css': '<%= assetsDir %>scss/hub/main.hub.scss',
          '<%= assetsDir %>css/redirect/redirect.css': '<%= assetsDir %>scss/redirect/redirect.scss',
          '<%= assetsDir %>css/redirect/redirect-unloged.css': '<%= assetsDir %>scss/redirect/redirect-unloged.scss',
          '<%= assetsDir %>css/addon/addon.css': '<%= assetsDir %>scss/addon/main.addon.scss',
          '<%= assetsDir %>css/staticPage/howitworks/howitworks.css': '<%= assetsDir %>scss/staticPage/howitworks/main.howitworks.scss',
          '<%= assetsDir %>css/staticPage/welcome/welcome.css': '<%= assetsDir %>scss/staticPage/welcome/main.welcome.scss',
          '<%= assetsDir %>css/staticPage/homepage/homepage.css': '<%= assetsDir %>scss/staticPage/homepage/main.homepage.scss',
          '<%= assetsDir %>css/staticPage/contact/contact.css': '<%= assetsDir %>scss/staticPage/contact/contact.scss',
          '<%= assetsDir %>css/staticPage/sign/sign.css': '<%= assetsDir %>scss/staticPage/sign/sign.scss',
          '<%= assetsDir %>css/staticPage/staticPage/staticPage.css': '<%= assetsDir %>scss/staticPage/staticPage/staticPage.scss',
          '<%= assetsDir %>css/staticPage/topOffer/topOffer.css': '<%= assetsDir %>scss/staticPage/topOffer/main.topOffer.scss',
          '<%= assetsDir %>css/staticPage/finance/finance.css': '<%= assetsDir %>scss/staticPage/finance/main.finance.scss',
          '<%= assetsDir %>css/vasa/vasa.css': '<%= assetsDir %>scss/vasa/main.vasa.scss',
          '<%= assetsDir %>css/interaction/interaction.css': '<%= assetsDir %>scss/interaction/main.interaction.scss',
          '<%= assetsDir %>css/text-hub/text-hub.css': '<%= assetsDir %>scss/text-hub/main.text-hub.scss'
        }
      },

      personalization: {
        options: {
          sourcemap: 'none',
          style: 'expanded'
        },
        files: {
          '<%= assetsDir %>css/personalization/personalization.css': '<%= assetsDir %>scss/personalization/main.personalization.scss'
        }
      },

      landingPage: {
        options: {
          sourcemap: 'none',
          style: 'expanded'
        },
        files: {
          '<%= assetsDir %>css/landingPage/landingPage.css': '<%= assetsDir %>scss/landingPage/main.landingPage.scss',
          '<%= assetsDir %>css/landingPage/event/event.css': '<%= assetsDir %>scss/landingPage/event/main.event.scss',
          '<%= assetsDir %>css/landingPage/erabat/erabat.css': '<%= assetsDir %>scss/landingPage/erabat/main.erabat.scss',
          '<%= assetsDir %>css/landingPage/coupon/coupon.css': '<%= assetsDir %>scss/landingPage/coupon/main.coupon.scss',
          '<%= assetsDir %>css/landingPage/contestHead/contesthead.css': '<%= assetsDir %>scss/landingPage/contestHead/main.contesthead.scss',
          '<%= assetsDir %>css/landingPage/pj/pj.css': '<%= assetsDir %>scss/landingPage/pj/main.pj.scss'
        }
      },

      partner: {
        options: {
          sourcemap: 'none',
          style: 'expanded'
        },
        files: {
          '<%= assetsPartnerDir %>dist/frontend.css': '<%= assetsPartnerDir %>frontend.scss'
        }
      },

      board: {
        options: {
          sourcemap: 'none',
          style: 'expanded'
        },
        files: {
          '<%= assetsDir %>css/board/board.css': '<%= assetsDir %>scss/board/main.board.scss'
        }
      },

      b2b: {
        options: {
          sourcemap: 'none',
          style: 'expanded'
        },
        files: {
          '<%= assetsB2bDir %>dist/frontend.css': '<%= assetsB2bDir %>frontend.scss'
        }
      },

      exitpopup: {
        options: {
          sourcemap: 'none',
          style: 'expanded'
        },
        files: {
          '<%= assetsDir %>scss/asyncPopup/exitPopup.css': '<%= assetsDir %>scss/asyncPopup/exitPopup.scss'
        }
      }
    },

    // Watch - sledovani zmen
    watch: {
      front: {
        files: [
          '<%= assetsDir %>scss/**/*.scss'
        ],
        tasks: ['clean:webtemp', 'sass:front']
      },

      personalization: {
        files: [
          '<%= assetsDir %>scss/personalization/**/*.scss'
        ],
        tasks: ['clean:webtemp', 'sass:personalization']
      },

      landingPage: {
        files: [
          '<%= assetsDir %>scss/landingPage/**/*.scss'
        ],
        tasks: ['clean:webtemp', 'sass:landingPage']
      },

      partner: {
        files: [
          '<%= assetsPartnerDir %>sass/**/*.scss'
        ],
        tasks: ['clean:webtemp', 'sass:partner']
      },

      board: {
        files: [
          '<%= assetsDir %>scss/**/*.scss'
        ],
        tasks: ['clean:webtemp', 'sass:board']
      },

      b2b: {
        files: [
          '<%= assetsB2bDir %>sass/**/*.scss'
        ],
        tasks: ['clean:webtemp', 'sass:b2b']
      },

      exitpopup: {
        files: [
          '<%= assetsDir %>scss/asyncPopup/exitPopup.scss'
        ],
        tasks: ['sass:exitpopup']
      }

    },

    // Clean folders
    clean: {
      webtemp: ['www/webtemp/css/', 'www/webtemp/js/']
    },

    // TinyPNG
    tinypng: {
      options: {
        apiKey: "0te0rYwuZxscfgokT4L8eu0i-7ZT1fTb",
        summarize: true,
        showProgress: true,
        stopOnImageError: true
      },
      default: {
        src: '<%= assetsDir %>images/dist/spritesheet.png',
        ext: '.png',
        expand: true
      }
    },

    svgstore: {
      options: {
        formatting : {
          indent_size : 2
        }
      },
        default: {
          files: {
          '<%= assetsDir %>images/svg/dist/icon.svg': ['<%= assetsDir %>images/svg/*.svg'],
        },
      },
    },

    // Generate sprite
    sprite:{
      all: {
        src: '<%= assetsDir %>images/icon/*.png',
        dest: '<%= assetsDir %>images/dist/spritesheet.png',
        destCss: '<%= assetsDir %>scss/_sprites.scss'
      }
    },

    // BrowserSync - Promitnuti zmen CSS do prohlizece bez potreby reloadu browseru
    browserSync: {
      default: {
          bsFiles: {
              src : [
                '<%= assetsDir %>css/main.front.css'
              ]
          },
          options: {
              watchTask: true,
              proxy: 'localhost',
              port: 3200,
              ui: {
                  port: 3201
              }
          }
      }
    }

  });


  // Hlavni tasky
  grunt.registerTask('sprites', ['sprite:all']);
  grunt.registerTask('css', ['sass']);
  grunt.registerTask('live', ['browserSync', 'watch']);
  grunt.registerTask('default', ['watch:front']);

  grunt.registerTask('personalization', ['watch:personalization']);
  grunt.registerTask('landingPage', ['watch:landingPage']);
  grunt.registerTask('partner', ['watch:partner']);
  grunt.registerTask('board', ['watch:board']);
  grunt.registerTask('b2b', ['watch:b2b']);
  grunt.registerTask('exitpopup', ['watch:exitpopup']);

};
