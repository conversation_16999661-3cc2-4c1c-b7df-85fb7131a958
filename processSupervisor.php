<?php
const DOMAIN = 'https://tipli-lb.vshosting.cz/supervisor/';
const USER = 'tipli';
const PASSWORD = 'S7Yh6klo1FYCt4';
const OPSGENIE_KEY = '************************************';

$context = stream_context_create([
	'http' => [
		'header' => 'Authorization: Basic ' . base64_encode(USER . ':' . PASSWORD)
	]
]);

$contents = file_get_contents(DOMAIN, false, $context);

preg_match_all('/<tr [^>]*>(.*?)<\/tr>/s', $contents, $rows);

$errors = [];
foreach ($rows[1] as $row) {
	preg_match_all('/<td>(.*?)<\/td>/s', $row, $tds);
	preg_match('/<a [^>]*>(.*?)<\/a>/s', $row, $link);

	if (empty($link)) {
		continue;
	}

	$consumerName = $link[1];
	$clearName = explode(':', $consumerName);
	$clearName = $clearName[0];

	if (isset($tds[0][0])) {
		$tdWithUptime = $tds[0][0];
		$parsedUptime = explode('uptime ', $tdWithUptime);
		$timeRunning = end($parsedUptime);

		if(count($parsedUptime) >= 2) {
			list($h, $m) = explode(':', $timeRunning);

			if (is_int($h) === false) {
				continue;
			}

			$seconds = mktime($h, $m, 0) - mktime(0,0,0);
			$minutes = $seconds / 60;

			if ($minutes > 45 && !in_array('uptime_' . $clearName, $errors)) {
				$errors[] = 'uptime_' . $clearName;
			}
		}
	}

	if (strpos($row, 'statuserror') !== false) {
		if (!in_array($clearName, $errors)) {
			$errors[] = $clearName;
		}
	}
}

foreach ($errors as $jobName) {
	$ch = curl_init('https://api.eu.opsgenie.com/v2/alerts');

	if (strpos($jobName, 'uptime_') !== false) {
		$message = 'Supervisor - job běží příliš dlouho: ';
	} else {
		$message = 'Supervisor - nefunkční job: ';
	}

	curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
	curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
	curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(
		[
			'priority' => 'P2',
			'alias' => '#snj' . $jobName,
			'message' => $message . str_replace('uptime_', '', $jobName),
			'description' => null,
			'source' => 'Tipli',
		]
	));

	curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type:application/json', 'Authorization: GenieKey ' . OPSGENIE_KEY]);
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	$result = curl_exec($ch);
	curl_close($ch);
}

$alerts = json_decode(file_get_contents('https://api.eu.opsgenie.com/v2/alerts?limit=100&query=' . rawurlencode('status: open'), false, stream_context_create(["http" => ["method" => "GET", "header" => "Authorization: GenieKey c0a7e491-2f13-4547-ac61-b7a75ef6ac4f"]])));

if ($alerts && $alerts->data) {
	foreach ($alerts->data as $alert) {
		if (strpos($alert->alias, '#snj') === false) {
			continue;
		}

		$jobName = str_replace('#snj', '', $alert->alias);

		if (in_array($jobName, $errors) === false) {
			$ch = curl_init();

			curl_setopt($ch, CURLOPT_URL,'https://api.eu.opsgenie.com/v2/alerts/' . $alert->id . '/close');
			curl_setopt($ch, CURLOPT_POSTFIELDS, '{}');
			curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type:application/json', 'Authorization: GenieKey bba940cc-5d1d-4b95-83f9-ff58cb6516ca']);
			curl_setopt($ch, CURLOPT_POST, 1);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
			$result = curl_exec($ch);
			curl_close($ch);
		}
	}
}

echo count($errors);