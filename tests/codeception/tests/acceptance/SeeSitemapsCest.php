<?php

class SeeSitemapsCest
{
	public function seeSitemaps(AcceptanceTester $I)
	{
        $I->wantTo('see all sitemaps');

        $I->amOnPage('sitemap.xml');
        $I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);

        $I->amOnPage('/shops.xml');
        $I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);

        $I->amOnPage('/leaflets.xml');
        $I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);

        $I->amOnPage('/deals.xml');
        $I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);

        $I->amOnPage('/posts.xml');
        $I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);

        $I->amOnPage('/other.xml');
        $I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);
	}

}
