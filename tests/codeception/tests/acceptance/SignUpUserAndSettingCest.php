<?php

class SignUpUserAndSettingCest
{
	public function signUpUserAndSettings(AcceptanceTester $I)
	{
		$email = uniqid() . '<EMAIL>';
		$password = '123456';
		$newPassword = '********';

		$this->signUp($I, $email, $password);

		$this->changeUserPassword($I, $password, $newPassword);

		$this->logOut($I);

		$this->logIn($I, $email, $newPassword);

		$this->testWrongBankAccount($I);

		$this->changeUserSettings($I);

		$this->seeGuaranteeForm($I);
	}

	private function logIn(AcceptanceTester $I, $email, $password)
	{
		$I->wantTo('log in as user ' . $email);

		$I->amOnPage('/prihlaseni');
		$I->fillField('email', $email);
		$I->fillField('password', $password);
		$I->click(['name' => '_submit']);
	}

	private function logOut(AcceptanceTester $I)
	{
		$I->wantTo('log out');

		$I->amOnPage('/odhlaseni');
	}

	private function signUp(AcceptanceTester $I, $email, $password)
	{
		$I->wantTo('sign up user');

		$I->amOnPage('/');

		$I->fillField('email', $email);
		$I->click('Založit účet ZDARMA');

		$I->fillField('newPassword', $password);
		$I->click('Nastavit heslo');

		$I->seeInSource('Slevové kupóny');
	}

	private function changeUserSettings(AcceptanceTester $I)
	{
		$I->wantTo('change user settings');

		$I->amOnPage('/nastaveni-uctu');

		$I->fillField('firstName', 'Test');
		$I->fillField('lastName', 'User');
		$I->fillField('birthdate', '2024-09-10');
		$I->selectOption('gender', 'male');

		$I->click('Uložit');

		$I->seeInSource('Změna profilu proběhla úspěšně.');
	}

	private function changeUserPassword(AcceptanceTester $I, $password, $newPassword)
	{
		$I->wantTo('change user password');

		$I->amOnPage('/nastaveni-uctu');

		$I->fillField('oldPassword', $password);
		$I->fillField('newPassword', $newPassword);
		$I->fillField('newPasswordVerify', $newPassword);

		$I->click('Změnit heslo');

		$I->seeInSource('Změna hesla proběhla úspěšně.');
	}

	private function testWrongBankAccount(AcceptanceTester $I)
	{
		$I->wantTo('test wrong bank account number');

		$I->amOnPage('/nastaveni-uctu');

		$I->fillField('accountNumber', '1111');

		$I->click('Uložit');

		//how to test js error popup
		$I->amOnPage('/nastaveni-uctu');

		$I->cantSee('Změna profilu proběhla úspěšně');
	}

	private function seeGuaranteeForm(AcceptanceTester $I)
	{
		$I->wantTo('see guarantee form');

		$I->amOnPage('/garance-spokojenosti');

		$I->seeInSource('Garance spokojenosti');
	}
}
