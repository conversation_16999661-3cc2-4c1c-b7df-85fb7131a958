<?php


class SeeStaticPagesCest
{
	public function seeStaticPages(AcceptanceTester $I)
	{
		$I->wantTo('see static pages');

		$I->amOnPage('/podminky');
		$I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);

		$I->amOnPage('/souhlas-se-zpracovanim-osobnich-udaju');
		$I->seeInSource('Souhlas se zpracováním osobních údajů');
		$I->seeInSource('Rádi bychom Vás informovali');

		$I->amOnPage('/podminky');
		$I->seeInSource('Podmínky užívání');

		$I->amOnPage('/jak-to-funguje');
		$I->seeInSource('Jak získat odměny s Tipli?');

		$I->amOnPage('/tipli-do-prohlizece');
		$I->seeInSource('Tipli do prohlížeče');
	}
}
