<?php

namespace acceptance;

class LuckyShopsCest
{
	public function signUpFlow(\AcceptanceTester $I)
	{
		$email = 'lucky' . rand(1, 999) . '@tipli.cz';
		$password = '123456';

		$this->logOut($I);

		// Sign up:
		$I->amOnPage('/tipli-rozdava/zacit');

		$I->see('Tipli rozdává');
		$I->see('Zadajte váš e-mail');

		$I->fillField('email', $email);

		$I->click('Založit účet ZDARMA');

		$I->fillField('newPassword', $password);

		$I->click('Nastavit heslo');

		// Intro screen
		$I->see('<PERSON>ž stačí pouze zvolit šťastný obchod:');
		$I->see('Můžete vybírat až ze 100 obchodů');

		$I->fillField('shopId', 1);

		$I->click('Potvrdit výběr obchodu');

		// Default screen
		$I->see('Vaše šťastné obchody');
		// $I->see('<PERSON><PERSON><PERSON> šťastný obchod vybíráme za:');
	}

	public function revealWinScreen(\AcceptanceTester $I)
	{
		$this->logOut($I);

		$I->amOnPage('/prihlaseni');

		$I->fillField('email', '<EMAIL>');
		$I->fillField('password', '123456');

		$I->click(['name' => '_submit']);

		$I->amOnPage('/tipli-rozdava');

		$I->see('Odhalit šťastný obchod', '#revealLuckyShop');
		$I->see('Zjistěte, zda jste dnes vyhráli');
		$I->see('1x', '#countOfWinners');
		$I->see('0x', '#countOfWinnersWithCheck');

		$I->click('#revealLuckyShop');

		$I->see('Gratulujeme');
		$I->see('Předběžný stav výhry');
		$I->see('10 Kč', '#currentRewardAmount');
		$I->see('1x', '#countOfWinners');
		$I->see('1x', '#countOfWinnersWithCheck');
		$I->see('1', '#streak');
	}

	public function revealLoseScreen(\AcceptanceTester $I)
	{
		$this->logOut($I);

		$I->amOnPage('/prihlaseni');

		$I->fillField('email', '<EMAIL>');
		$I->fillField('password', '123456');

		$I->click(['name' => '_submit']);

		$I->amOnPage('/tipli-rozdava');

		$I->see('Odhalit šťastný obchod', '#revealLuckyShop');

		$I->click('#revealLuckyShop');

		$I->see('Dnes to nevyšlo');
		$I->see('1', '#streak');
		$I->see('8DOLU'); // kód kuponu na stránce
		$I->see('Zobrazit všechny kupóny');
	}

	public function revealWinScreenPhoneNumberVerification(\AcceptanceTester $I)
	{
		$this->logOut($I);

		$I->amOnPage('/prihlaseni');

		$I->fillField('email', '<EMAIL>');
		$I->fillField('password', '123456');

		$I->click(['name' => '_submit']);

		$I->amOnPage('/tipli-rozdava');

		$I->see('Odhalit šťastný obchod', '#showPhoneNumberVerificationPopup');

		$I->click('#showPhoneNumberVerificationPopup');

		$I->see('Už stačí pouze ověření');
	}

	public function changeLuckyShop(\AcceptanceTester $I)
	{
		$this->logOut($I);

		$I->amOnPage('/prihlaseni');

		$I->fillField('email', '<EMAIL>');
		$I->fillField('password', '123456');

		$I->click(['name' => '_submit']);

		$I->amOnPage('/tipli-rozdava');

		$I->click('#defaultUserLuckyShop');

		$I->see('Upravit šťastný obchod');

		$I->click('.open-all-shops-list-modal');

		$I->see('Seznam všech obchodů v Tipli rozdává');

		$I->click('//button[@data-id="8"]');

		$I->click('#defaultUserLuckyShop');

		$I->see('Allegria - Firma na zážitky');
	}

	private function logOut(\AcceptanceTester $I)
	{
		$I->amOnPage('/odhlaseni');
	}
}
