<?php

class SignUpPartnerOrganizationAndBonusCest
{
    private $userId;

    private $userEmail;

    public function signUpPartnerOrganizationAndBonus(AcceptanceTester $I)
    {
        $this->registerViaPartnerOrganization($I);

        $this->createTransaction($I);

        # $this->checkBonus($I);
    }

    private function registerViaPartnerOrganization(AcceptanceTester $I)
    {
	    $this->logOut($I);

        $I->amOnPage('?utm_source=test_source&utm_medium=test_medium&utm_campaign=test_campaign');

        // Attributes of Partner Organization are visible on homepage
 //       $I->seeElement('img', ['class' => 'homepage-header-partner__image']);
 //       $I->seeInSource('Pestrý jídelníček');
 //       $I->seeInSource('Pestrý jídelníček popis');

        $this->register($I);

        $I->seeElement('.navbar__partner-table-cell');

        $this->userId = $this->getUserId($I);

        $this->logOut($I);
    }

    private function createTransaction(AcceptanceTester $I)
    {
        $this->logIn($I);

		$this->verify($I);

        $this->addTransaction($I, $this->userId);

        $this->confirmTransaction($I, $this->userId);

        $this->logOut($I);
    }

    private function checkBonus(AcceptanceTester $I)
    {
        $this->logIn($I, $this->userEmail, 123456);

        $I->amOnPage('/odmeny');

        // 100 Kč from Bonus nad 495 Kč from transaction
        $I->seeInSource('595 Kč');
    }

    private function logIn(AcceptanceTester $I, string $email = '<EMAIL>', string $password = '123456')
    {
        $I->amOnPage('/');
        $I->click('Přihlásit se');
        $I->fillField('email', $email);
        $I->fillField('password', $password);
        $I->click(['name' => '_submit']);
    }

	private function verify(AcceptanceTester $I)
	{
		$code = 'dc468c';

		$I->amOnPage('/admin');
		$I->seeInSource('Ověření přihlášení');
		$I->fillField('code', $code);
		$I->click('.sms-verify__form-submit');
	}

    private function logOut(AcceptanceTester $I)
    {
        $I->amOnPage('/odhlaseni');
    }

    private function register(AcceptanceTester $I)
    {
        $this->userEmail = uniqid().'<EMAIL>';

        $I->fillField('email', $this->userEmail);
        $I->click('Založit účet ZDARMA');

	    $I->fillField('newPassword', 123456);
	    $I->click('Nastavit heslo');

        $I->seeInSource('Přehled mých odměn');
    }

    private function addTransaction(AcceptanceTester $I, $userId)
    {
		$I->amOnPage('/admin/transactions.transaction/transaction');
		$I->seeInSource('transaction');
		$I->fillField('transactionId', \Nette\Utils\Random::generate(20));
		$I->fillField('commissionAmount', '1000');
		$I->fillField('userId', $userId);
		$I->fillField('orderAmount', '10000');
		$I->selectOption('shop', '1');
		$I->selectOption('currency', 'CZK');
		$I->click('Save');
    }

    private function confirmTransaction(AcceptanceTester $I, $userId)
    {
        $I->amOnPage('/admin/transactions.transaction?transactionsFilter%5BuserId%5D=' . $userId);
//        $I->fillField('#frm-transactionsGrid-filter-filter-user', $userId);

        $I->click('td.col-action a.btn-success');
    }

    private function getUserId(AcceptanceTester $I)
    {
        $I->amOnPage('/doporucit-pratelum');

        $recommendUrl = $I->grabValueFrom('.account-friend__input');

        $userId = explode('/', $recommendUrl)[4];

        return $userId;
    }
}
