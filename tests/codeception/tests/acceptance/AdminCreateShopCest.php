<?php

class AdminCreateShopCest
{
	public function createShop(AcceptanceTester $I)
	{
		$I->wantTo('admin - create shop');

		$this->logIn($I);

		$this->verify($I);

		$this->addShop($I);

		$I->wantTo('check out the shop');

		$this->seeShop($I);
	}

	private function logIn(AcceptanceTester $I)
	{
		$email = '<EMAIL>';
		$password = '123456';

		$I->amOnPage('/');
		$I->click('Přihlásit se');
		$I->fillField('email', $email);
		$I->fillField('password', $password);
		$I->click(['name' => '_submit']);
	}

	private function verify(AcceptanceTester $I)
	{
		$code = 'dc468c';

		$I->amOnPage('/admin');
		$I->seeInSource('Ověření přihlášení');
		$I->fillField('code', $code);
		$I->click('.sms-verify__form-submit');
	}

	private function addShop(AcceptanceTester $I)
	{
		$I->amOnPage('/admin/shops.add-new-shop');
		$I->seeInSource('shop');
		$I->fillField('name', 'TestShop');
		$I->selectOption('localization', '1');
		$I->fillField('domain', 'https://testshop.com');

		$I->click('Add');
	}

	private function seeShop(AcceptanceTester $I)
	{
		$I->amOnPage('/obchod/testshop');
		$I->seeInSource('TestShop', 'h1');
	}

}
