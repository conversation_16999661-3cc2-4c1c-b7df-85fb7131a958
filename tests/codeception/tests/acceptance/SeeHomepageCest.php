<?php

use Codeception\Util\HttpCode;

class SeeHomepageCest
{
	public function seeCzechHomepage(AcceptanceTester $I)
	{
		$I->wantTo('see homepage');
		$I->amOnPage('/');

		$I->seeResponseCodeIs(HttpCode::OK);

		$I->canSeeElement('#header');

		$I->canSeeElement('#frm-hpEmailSignUpControl-form');

		$I->canSeeElement('.shop-item');

		//	$I->canSeeElement('p[class*="hp-coupon"] .hp-coupon__item');

		$I->canSeeElement('.total-reward-value');

		$I->canSee('Nejčast<PERSON><PERSON><PERSON><PERSON>');

		$I->canSee('Jak získat odměny?');

		$I->seeInSource('Dostávejte peníze');

		$I->canSeeElement('footer');
	}
}
