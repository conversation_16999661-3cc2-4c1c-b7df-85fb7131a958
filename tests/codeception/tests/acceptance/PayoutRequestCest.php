<?php
# vendor/bin/codecept run -c tests/codeception/codeception.yml acceptance PayoutRequestCest --steps

use Codeception\Util\HttpCode;

class PayoutRequestCest
{
	public function payoutRequest(AcceptanceTester $I)
	{
		$I->wantTo('request payout');

		$this->logOut($I);

		$this->logIn($I);

		$this->changeUserSettings($I);

        $this->verifySms($I);

        $this->changeAccountNumber($I);

		$I->amOnPage('/vyplaty');

		$I->checkOption('conditions');

		$I->click('.payout-submit');

		$I->seeElement('h2.payout-title'); //H1 u jinych stavů neni tento element s třidou

		$I->amOnPage('/odmeny');

		$I->seeInSource('20', '.balance-value');

		$I->seeResponseCodeIs(HttpCode::OK);

		$I->amOnPage('/vyplaty');

		$I->seeInSource('475');

		$I->seeResponseCodeIs(HttpCode::OK);
	}

	private function logIn(AcceptanceTester $I)
	{
		$email = '<EMAIL>';
		$password = '123456';

		$I->amOnPage('/prihlaseni');
		$I->fillField('email', $email);
		$I->fillField('password', $password);
		$I->click(['name' => '_submit']);
	}

	private function logOut(AcceptanceTester $I)
	{
		$I->wantTo('log out');

		$I->amOnPage('/odhlaseni');
	}

	private function changeUserSettings(AcceptanceTester $I)
	{
		$I->wantTo('change user settings');

		$I->amOnPage('/nastaveni-uctu');

		$I->fillField('firstName', 'Test');
		$I->fillField('lastName', 'User');
		$I->fillField('birthdate', '2024-09-10');
		$I->fillField('phoneNumber', '*********');
//		$I->fillField('accountNumber', '9890777/2010');
		$I->selectOption('gender', 'male');

		$I->click('Uložit');

		$I->seeResponseCodeIs(HttpCode::OK);
	}

    private function changeAccountNumber(AcceptanceTester $I)
    {
        $I->wantTo('change account number');

        $I->amOnPage('/nastaveni-uctu');

        $I->fillField('accountNumber', '*********/0300');

        $I->click('.account-number-submit');

	    $I->seeResponseCodeIs(HttpCode::OK);
    }

	private function verifySms(AcceptanceTester $I)
	{
		$I->seeInCurrentUrl('overeni-telefonniho-cisla');

		$I->fillField('code', '1234');
		$I->click('.sms-verify__form-submit');

		$I->seeInSource('Telefonní číslo bylo úspěšně ověřeno.');
		//$I->seeElement('.flashmessage-w');

		$I->seeResponseCodeIs(HttpCode::OK);
	}

}
