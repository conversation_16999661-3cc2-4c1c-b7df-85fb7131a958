<?php

# vendor/bin/codecept run -c tests/codeception/codeception.yml acceptance SignUpRecommendedUserCest --steps

class SignUpRecommendedUserCest
{
    public function signUpRecommendedUser(AcceptanceTester $I)
    {
        $email = '<EMAIL>';

        $I->wantTo('sign up recommended user');

        $this->logOut($I);

		$I->amOnPage('/p/1');

        $this->signUp($I, $email);

        $userId = $this->getUserId($I);

        $this->logOut($I);

        $this->logIn($I, '<EMAIL>', '123456');

        $this->verify($I);

        $this->addTransaction($I, $userId);

        $this->confirmTransaction($I, $userId);

        # $this->checkBonus($I);

		$this->payoutRequest($I);

        $this->logOut($I);
    }

    private function signUp(AcceptanceTester $I, $email)
    {
        $I->wantTo('sign up user');

        $I->fillField('email', $email);

        $I->click('Založit účet ZDARMA');

        $I->seeInSource('Slevové kupóny');
    }

    private function getUserId(AcceptanceTester $I)
	{
		$I->amOnPage('/doporucit-pratelum');

		$recommendUrl = $I->grabValueFrom('.account-friend__input');

		$userId = explode('/', $recommendUrl)[4];

		return $userId;
	}

    private function logOut(AcceptanceTester $I)
    {
        $I->amOnPage('/odhlaseni');
    }

	private function logIn(AcceptanceTester $I, $email, $password)
	{
		$I->amOnPage('/');

		$I->click('Přihlásit se');

		$I->fillField('email', $email);
		$I->fillField('password', $password);

		$I->click(['name' => '_submit']);
	}

	private function verify(AcceptanceTester $I)
	{
		$code = 'dc468c';

		$I->amOnPage('/admin');

		$I->seeInSource('Ověření přihlášení');

		$I->fillField('code', $code);

		$I->click('.sms-verify__form-submit');
	}

	private function addTransaction(AcceptanceTester $I, $userId)
	{
		$I->amOnPage('/admin/transactions.transaction/transaction');

		$I->seeInSource('transaction');

		$I->fillField('transactionId', \Nette\Utils\Random::generate(20));
		$I->fillField('commissionAmount', '250');
		$I->fillField('userId', $userId);
		$I->fillField('orderAmount', '2500');
		$I->selectOption('shop', '1');
		$I->selectOption('currency', 'CZK');

		$I->click('Save');
	}

	private function confirmTransaction(AcceptanceTester $I, $userId)
	{
		$I->amOnPage('/admin/transactions.transaction?transactionsFilter%5BuserId%5D=' . $userId);

		$I->click('td.col-action a.btn-success');
	}



	private function checkBonus(AcceptanceTester $I)
	{
		$I->amOnPage('/doporucit-pratelum');

		$I->seeInSource('rec***@tip**', 'td');

		$I->amOnPage('/odmeny');

		$I->seeInSource('150', 'strong.account-transaction__amount-value');
		$I->seeInSource('Bonus za');
	}

	private function payoutRequest(AcceptanceTester $I)
	{
		$I->amOnPage('/vyplaty');
		$I->seeElement('.payout-title');
	}
}
