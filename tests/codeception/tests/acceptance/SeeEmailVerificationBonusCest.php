<?php

class SeeEmailVerificationBonusCest
{
	public function SeeEmailVerificationBonus(AcceptanceTester $I)
	{
		$I->wantTo('test create bonus for email verification');

		$this->logOut($I);
		$this->logIn($I, '<EMAIL>', '123456');

		$I->amOnPage('/1kc-za-overeni?at=dE58ZjsEMUVO6sdQXoLJT0VNN2D0WsjBwdsB6sNzkGloedx2FWSpei4KhPfdXbck');

		$I->amOnPage('/odmeny');

		$I->see('1 Kč');
	}

	private function logIn(AcceptanceTester $I, $email, $password)
	{
		$I->amOnPage('/');
		$I->click('Přihlásit se');
		$I->fillField('email', $email);
		$I->fillField('password', $password);
		$I->click(['name' => '_submit']);
	}

	private function logOut(AcceptanceTester $I)
	{
		$I->wantTo('log out');

		$I->amOnPage('/odhlaseni');
	}

	private function throwError($message)
	{
		throw new \Exception($message);
	}
}
