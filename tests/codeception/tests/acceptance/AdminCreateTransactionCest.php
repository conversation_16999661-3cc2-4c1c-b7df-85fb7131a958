<?php

class AdminCreateTransactionCest
{
	public function createTransaction(AcceptanceTester $I)
	{
		$I->wantTo('admin - create transaction');

		$this->logIn($I);

		$this->verify($I);

		$this->addTransaction($I);

		$I->wantTo('see my reward');

		$this->seeTransaction($I);
	}

	private function logIn(AcceptanceTester $I)
	{
		$email = '<EMAIL>';
		$password = '123456';

		$I->amOnPage('/');
		$I->click('Přihlásit se');
		$I->fillField('email', $email);
		$I->fillField('password', $password);
		$I->click(['name' => '_submit']);
	}

	private function verify(AcceptanceTester $I)
	{
		$code = 'dc468c';

		$I->amOnPage('/admin');
		$I->seeInSource('<PERSON><PERSON>ěř<PERSON><PERSON>');
		$I->fillField('code', $code);
		$I->click('.sms-verify__form-submit');
	}

	private function addTransaction(AcceptanceTester $I)
	{
		$I->amOnPage('/admin/transactions.transaction/transaction');
		$I->seeInSource('transaction');
		$I->fillField('transactionId', \Nette\Utils\Random::generate(20));
		$I->fillField('commissionAmount', '1000');
		$I->fillField('userId', '1');
		$I->fillField('orderAmount', '10000');
		$I->selectOption('shop', '1');
		$I->selectOption('currency', 'CZK');
		$I->click('Save');
	}

	private function seeTransaction(AcceptanceTester $I)
	{
		$I->amOnPage('/odmeny');
		$I->seeInSource('475', '.account-transaction__amount'); //reward for user
	}
}
