<?php

class AdminPermissionsCest
{
    public function testAdminWithoutPermissions(AcceptanceTester $I)
    {
        $this->logIn($I, '<EMAIL>');

        $this->verify($I);

        $I->amOnPage('/admin');
        
        $I->dontSee('Shops');
        $I->dontSee('Content');
        $I->dontSee('Marketing');

        $I->amOnPage('/admin/transactions.transaction/');
        
        $I->seeInSource('Nepovolený přístup');

        $this->logOut($I);
    }

    public function testAdminWithOnlyShopPermissions(AcceptanceTester $I)
    {
        $this->logIn($I, '<EMAIL>');

        $this->verify($I);

        $I->amOnPage('/admin');
        
        $I->seeInSource('Shops');

        $I->dontSee('Content');
        $I->dont<PERSON>ee('Marketing');

        $I->amOnPage('/admin/transactions.transaction/');
        
        $I->seeInSource('Nepovolený přístup');

        $I->amOnPage('/admin/shops.shop/');
        
        $I->seeInSource('Shops');

        $this->logOut($I);
    }

    public function testAdminWithPermissions(AcceptanceTester $I)
    {
        $this->logIn($I, '<EMAIL>');

        $this->verify($I);

        $I->amOnPage('/admin');
        
        $I->seeInSource('Shops');
        $I->seeInSource('Content');
        $I->seeInSource('Marketing');
        $I->seeInSource('Transactions');

        $I->amOnPage('/admin/transactions.transaction/');
        
        $I->seeInSource('Transactions');

        $I->amOnPage('/admin/marketing.banner/');
        
        $I->seeInSource('Banners');

        $this->logOut($I);
    }


    private function logIn(AcceptanceTester $I, $email)
    {
        $password = '123456';

        $I->wantTo('log in');

        $I->amOnPage('/');
        
        $I->click('Přihlásit se');
        
        $I->fillField('email', $email);
        $I->fillField('password', $password);
        
        $I->click(['name' => '_submit']);
    }

    private function verify(AcceptanceTester $I)
    {
        $code = 'dc468c';

        $I->amOnPage('/admin');
        
        $I->seeInSource('Ověření přihlášení');
        
        $I->fillField('code', $code);
        
        $I->click('.sms-verify__form-submit');
    }

    private function logOut(AcceptanceTester $I)
    {
        $I->amOnPage('/odhlaseni');
    }

}
