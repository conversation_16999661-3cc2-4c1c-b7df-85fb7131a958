<?php

class CreateAndResponseJobCest
{
	public function createAndResponse(AcceptanceTester $I)
	{
		$I->wantTo('Create job and test response form');

		$this->logIn($I);
		$this->verify($I);
		$this->createJob($I);
		// $this->responseForm($I);
	}

	private function logIn(AcceptanceTester $I)
	{
		$email = '<EMAIL>';
		$password = '123456';

		$I->amOnPage('/prihlaseni');
		$I->fillField('email', $email);
		$I->fillField('password', $password);
		$I->click(['name' => '_submit']);
	}

	private function verify(AcceptanceTester $I)
	{
		$code = 'dc468c';

		$I->amOnPage('/admin');
		$I->seeInSource('Ověření přihlášení');
		$I->fillField('code', $code);
		$I->click('.sms-verify__form-submit');
	}

	private function createJob(AcceptanceTester $I)
	{
		$validFrom = new \DateTime();
		$validTo = new \DateTime('+ 1 day');

		$I->amOnPage('/admin/jobs.job/job');
		$I->fillField('name', 'Test job');
		$I->fillField('slug', 'test-job');
		$I->fillField('type', 'testovací');
		$I->fillField('priority', '0');
		$I->selectOption('localization', '1');
		$I->fillField('validFrom', $validFrom->format('Y-m-d H:i'));
		$I->fillField('validTo', $validTo->format('Y-m-d H:i'));
		$I->fillField('content', 'Lorem ipsum dolor sit amet');
		$I->click('_submit');
	}

	private function responseForm(AcceptanceTester $I)
	{
		$I->amOnPage('/kariera/test-job');
		$I->fillField('name', 'Jon');
		$I->fillField('surname', 'Doe');
		$I->fillField('email', '<EMAIL>');
		$I->fillField('phone', '+420 725 575 648');
		$I->fillField('linkedIn', 'test');

		$I->click('.career-detail__submit');
		$I->seeElement('.responseForm-success');
	}
}
