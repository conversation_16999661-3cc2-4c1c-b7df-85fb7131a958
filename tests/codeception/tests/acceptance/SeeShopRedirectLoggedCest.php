<?php

class SeeShopRedirectLoggedCest
{
	public function seeSitemaps(AcceptanceTester $I) {
		$email = uniqid().'<EMAIL>';

		$I->wantTo('sign up user and redirect to aliexpress');
		$I->amOnPage('/registrace');
		$I->fillField('email', $email);
//		$I->fillField('password', $password);
		$I->click('Založit účet ZDARMA');
		$I->seeInSource('Přehled mých odměn');

//		$I->click('AliExpress');
//		$I->seeInSource('AliExpress');

		$I->amOnPage('/prejit/obchod/aliexpress');
//		$I->click('.shop-detail__button');
		$I->seeInSource('Provádíme přesměrování...');
	}
}
