<?php
class SeeAddonApiCest
{
    public function seeAddonApi(AcceptanceTester $I)
    {
        $I->wantTo('see addon api');

        $I->amOnPage('/api/v1/addon/user');
        $I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);

        $I->amOnPage('/api/v1/addon/shops-new');
        $I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);

        $I->amOnPage('/api/v1/addon/menu');
        $I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);
    }
}
