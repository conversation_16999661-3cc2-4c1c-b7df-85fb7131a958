<?php

use Codeception\Util\HttpCode;

class SeeShopDetailCest
{
	public function seeShopRedirect(AcceptanceTester $I)
	{
		$I->wantTo('see shop detail');
		$I->amOnPage('/obchod/aliexpress');

		$I->seeInSource('AliExpress');
		$I->seeInSource('O AliExpress');
		$I->dontSee('ladíme Tipli');

		$I->see('Nakoupit');
		$I->see('O AliExpress');
		$I->see('Jak uplatnit slevové kupóny na AliExpress');

		$I->seeNumberOfElements('.deal-item', [1, 10]);

		$I->seeInSource('Všechna práva vyhrazena'); // patička

		$I->seeResponseCodeIs(HttpCode::OK);
	}
}
