<?php

use Codeception\Util\HttpCode;

class AccessRoleCest
{
    public function accessRoleAndPermissions(AcceptanceTester $I)
	{
		$I->wantTo('permission and role access');

		$this->logOut($I);

		$this->testMember($I);

//		$this->testEditor($I);

		$this->testAdmin($I);

	    $I->seeResponseCodeIs(HttpCode::OK);
	}

	private function testMember(AcceptanceTester $I)
	{
		/**
		 * přihlášení jako member (klas<PERSON><PERSON> uživatel) - nesmí se dostat do adminu
		 */
		$this->logIn($I, '<EMAIL>', '123456');

		$this->testAdminAsMember($I, '/admin');
		$this->testAdminAsMember($I, '/admin/articles.article/article');
		$this->testAdminAsMember($I, '/reports');

		$this->logOut($I);
	}

	private function testEditor(AcceptanceTester $I)
	{
		/**
		 * přihlášení jako editor (uživatel s přístupem s omezenými právy) - nesm<PERSON> se dostat k citlivým údajům - transakce, uživatelé, výplaty, reporty
		 */
		$this->logIn($I, '<EMAIL>', '123456');
		$this->verify($I);

		$this->testAdminAsEditor($I, '/admin');
		$this->testAdminAsEditor($I, '/admin/articles.article/article');
		$this->testAdminAsEditor($I, '/admin/sales.sale');
		$this->testAdminAsEditor($I, '/admin/seo.seo/');
		$this->testAdminAsEditor($I, '/admin/transactions.transaction', true);
		$this->testAdminAsEditor($I, '/admin/account.user', true);
		$this->testAdminAsEditor($I, '/reports');
		$this->testAdminAsEditor($I, '/reports/homepage/top-users-dashboard', true);

		$this->logOut($I);
	}

	private function testAdmin(AcceptanceTester $I)
	{
		/**
		 * přihlášení jako admin
		 */
		$this->logIn($I, '<EMAIL>', '123456');
		$this->verify($I);

		$I->amOnPage('/admin');
		$I->seeInCurrentUrl('admin');

		$I->amOnPage('/admin/account.user');
		$I->seeInCurrentUrl('account.user');
	}

	private function logIn(AcceptanceTester $I, $email, $password)
	{
		$I->amOnPage('/prihlaseni');
		$I->fillField('email', $email);
		$I->fillField('password', $password);
		$I->click(['name' => '_submit']);
	}

	private function verify(AcceptanceTester $I)
	{
		$code = 'dc468c';

		$I->amOnPage('/admin');
		$I->seeInSource('Ověření přihlášení');

		$I->fillField('code', '*invalid*');
		$I->click('.sms-verify__form-submit');
		$I->seeInSource('Kód je neplatný nebo již vypršel.');

		$I->fillField('code', $code);
		$I->click('.sms-verify__form-submit');
	}

	private function logOut(AcceptanceTester $I)
	{
		$I->wantTo('log out');

		$I->amOnPage('/odhlaseni');
	}

	private function testAdminAsMember(AcceptanceTester $I, $page)
	{
		$I->amOnPage($page);
		// Bude presmerovan na Moje oblibene
        $I->seeInSource('Slevové kupóny');
	}

	/**
	 * @param AcceptanceTester $I
	 * @param $page
	 * @param bool $failed - zda li má editor stránku vidět či být přemsěrován
	 */
	private function testAdminAsEditor(AcceptanceTester $I, $page, $testFail = false)
	{
		$I->amOnPage($page);

		if ($testFail) {
			$I->seeInCurrentUrl('admin/?backLink=');
		} else {
			$I->seeInCurrentUrl($page);
		}
	}

}
