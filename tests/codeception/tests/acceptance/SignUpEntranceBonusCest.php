<?php

class SignUpEntranceBonusCest
{
    private $userId;

    private $userEmail;

    private $userPassword;

    public function signUpEntranceBonus(AcceptanceTester $I)
    {
        $this->registerWithoutUtm($I);

        $this->createTransaction($I);

        // $this->checkBonus($I);
    }

    private function registerWithoutUtm(AcceptanceTester $I)
    {
        $I->amOnPage('/');
        $this->register($I);
        $this->userId = $this->getUserId($I);
        $this->logOut($I);
    }

    private function register(AcceptanceTester $I)
    {
        $this->userEmail = uniqid() . '<EMAIL>';

        $I->fillField('email', $this->userEmail);
        $I->click('Založit účet ZDARMA');
        $I->seeInSource('Přehled mých odměn');
    }

    private function getUserId(AcceptanceTester $I)
    {
        $I->amOnPage('/doporucit-pratelum');
        $recommendUrl = $I->grabValueFrom('.account-friend__input');
        $userId = explode('/', $recommendUrl)[4];
        return $userId;
    }

    private function createTransaction(AcceptanceTester $I)
    {
        $this->logIn($I);
        $this->verify($I);
        $this->addTransaction($I, $this->userId);
        $this->confirmTransaction($I, $this->userId);
        $this->logOut($I);
    }

    private function logIn(AcceptanceTester $I, string $email = '<EMAIL>', string $password = '123456')
    {
        $I->amOnPage('/');
        $I->click('Přihlásit se');
        $I->fillField('email', $email);
        $I->fillField('password', $password);
        $I->click(['name' => '_submit']);
    }

	private function verify(AcceptanceTester $I)
	{
		$code = 'dc468c';

		$I->amOnPage('/admin');
		$I->seeInSource('Ověření přihlášení');
		$I->fillField('code', $code);
		$I->click('.sms-verify__form-submit');
	}

    private function logOut(AcceptanceTester $I)
    {
        $I->amOnPage('/odhlaseni');
    }

    private function addTransaction(AcceptanceTester $I, $userId)
    {
		$I->amOnPage('/admin/transactions.transaction/transaction');
		$I->seeInSource('transaction');
		$I->fillField('transactionId', \Nette\Utils\Random::generate(20));
		$I->fillField('commissionAmount', '1000');
		$I->fillField('userId', $userId);
		$I->fillField('orderAmount', '10000');
		$I->selectOption('shop', '1');
		$I->selectOption('currency', 'CZK');
		$I->click('Save');
    }

    private function confirmTransaction(AcceptanceTester $I, $userId)
    {
        $I->amOnPage('/admin/transactions.transaction?transactionsFilter%5BuserId%5D=' . $userId);
//        $I->fillField('#frm-transactionsGrid-filter-filter-user', $userId);
        $I->click('td.col-action a.btn-success');
    }

    private function checkBonus(AcceptanceTester $I)
    {
        $this->logIn($I, $this->userEmail, $this->userPassword);
        $I->amOnPage('/odmeny');
        // 100 Kč from Bonus and 112,5 Kč from transaction
        $I->seeInSource('212,5 Kč');
    }
}
