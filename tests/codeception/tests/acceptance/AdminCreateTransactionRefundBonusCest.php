<?php

class AdminCreateTransactionRefundBonusCest
{
	public function createTransaction(AcceptanceTester $I)
	{
		$I->wantTo('admin - create refund bonus transaction');

		$this->logIn($I);

		$this->verify($I);

		$this->addTransaction($I);

		$I->wantTo('see my reward');

		$this->seeTransaction($I);
	}

	private function logIn(AcceptanceTester $I)
	{
		$email = '<EMAIL>';
		$password = '123456';

		$I->amOnPage('/');
		$I->click('Přihlásit se');
		$I->fillField('email', $email);
		$I->fillField('password', $password);
		$I->click(['name' => '_submit']);
	}

	private function verify(AcceptanceTester $I)
	{
		$code = 'dc468c';

		$I->amOnPage('/admin');
		$I->seeInSource('<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>');
		$I->fillField('code', $code);
		$I->click('.sms-verify__form-submit');
	}

	private function addTransaction(AcceptanceTester $I)
	{
		$I->amOnPage('/admin/transactions.transaction/refund-bonus');
		$I->seeInSource('Refund');
		$I->selectOption('localization', '1');
		$I->selectOption('shop', '1');
		$I->fillField('user', '1');
		$I->fillField('name', 'test-refund-bonus');
		$I->fillField('amount', '900');
		$I->fillField('confirmationTreshold', '900');
		$I->click('Save');
	}

	private function seeTransaction(AcceptanceTester $I)
	{
		$I->amOnPage('/odmeny');
		$I->seeInSource('900', '.account-transaction__amount'); //reward for user
	}
}
