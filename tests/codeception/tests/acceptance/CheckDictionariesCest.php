<?php

class CheckDictionariesCest
{
    public function checkDictionaries(AcceptanceTester $I)
    {
//        $badFiles = [];
//
//        foreach (Finder::findFiles('*.neon')->in('app/locale/') as $key => $file) {
//            $fileName = explode("/", $file);
//            $fileName = end($fileName);
//
//            if (Strings::contains(file_get_contents($file), '@todo')) {
//                $badFiles[] = $fileName;
//            }
//        }
//
//        if (count($badFiles) > 0) {
//            throw new \Exception('Nalezeno @todo v souborech: ' . implode(", ", $badFiles));
//        }
    }
}
