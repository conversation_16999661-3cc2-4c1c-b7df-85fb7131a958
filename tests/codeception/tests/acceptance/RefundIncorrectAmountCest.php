<?php

use Codeception\Util\HttpCode;

class RefundIncorrectAmountCest
{
    public function createRefundIncorrectAmount(AcceptanceTester $I)
    {
        $I->wantTo('test incorrect amount refund');

        $this->logOut($I);
        $this->logIn($I, '<EMAIL>', '123456');

        $I->amOnPage('/reklamace');
        $I->seeInSource('Mám dotaz k odměně');
        $I->seeInSource('Nesprávná výše odměny');

        $I->fillField('#refund-incorrect-amount-transaction-id', 1);
        $I->fillField('expectedAmount', 100);
        $I->fillField('#frm-refundIncorrectAmountControl-form-message', 'Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Aliquam erat volutpat.');

        $I->click('#submit-incorrect-amount-refund');
        $I->seeInSource('Děkujeme za odeslání formuláře');

	    $I->seeResponseCodeIs(HttpCode::OK);

	    $refundId = $I->grabMultiple('#refundId')[0];

	    $this->seeRefundInAdmin($I, $refundId);
    }

    private function logIn(AcceptanceTester $I, $email, $password)
    {
        $I->amOnPage('/prihlaseni');
        $I->fillField('email', $email);
        $I->fillField('password', $password);
        $I->click(['name' => '_submit']);
    }

    private function logOut(AcceptanceTester $I)
    {
        $I->wantTo('log out');

        $I->amOnPage('/odhlaseni');
    }

	private function seeRefundInAdmin(AcceptanceTester $I, int $refundId)
	{
		$this->logOut($I);
		$this->logIn($I, '<EMAIL>', '123456');

		$this->verify($I);

		$I->amOnPage('/admin/refunds.refund/refund/' . $refundId);

		$I->seeResponseCodeIs(HttpCode::OK);
	}

	private function verify(AcceptanceTester $I)
	{
		$code = 'dc468c';

		$I->amOnPage('/admin');
		$I->seeInSource('Ověření přihlášení');
		$I->fillField('code', $code);
		$I->click('.sms-verify__form-submit');
	}
}
