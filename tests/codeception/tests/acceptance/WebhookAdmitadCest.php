<?php

use Nette\Utils\Strings;

class WebhookAdmitadCest
{
    const USER_ID = 2;
    const USER_EMAIL = '<EMAIL>';
    const PARTNER_SYSTEM_KEY = 6115;

    private $confirmedTransactionId;

    private $registeredTransactionId;

    private $canceledTransactionId;

    public function webhookAdmitad(AcceptanceTester $I)
    {
        $I->wantTo('Register a transaction with admitad webhook');

        $this->generateIdsForTransactions();

		$defaultParams = [
			'website_id' => 1,
			'currency' => 'CZK',
			'offer_id' => self::PARTNER_SYSTEM_KEY,
			'type' => 'unknown',
			'subid' => self::USER_ID,
			'time' => (new DateTime())->getTimestamp(),
			'admitad_id' => 1
		];

        // Register all 3 transactions
        $this->callApiWithParameters($I, $defaultParams, $this->registeredTransactionId, 200, 'pending');
        $this->callApiWithParameters($I, $defaultParams, $this->confirmedTransactionId, 300, 'pending');
        $this->callApiWithParameters($I, $defaultParams, $this->canceledTransactionId, 400, 'pending');
        // Confirm second transaction
        $this->callApiWithParameters($I, $defaultParams, $this->confirmedTransactionId, 300, 'approved');
        // Cancel third transaction
        $this->callApiWithParameters($I, $defaultParams, $this->canceledTransactionId, 400, 'declined');

        $this->checkBalances($I);
    }

    private function callApiWithParameters(AcceptanceTester $I, array $params, int $orderId, int $orderSum, string $paymentStatus = '')
    {
        $params['order_id'] = $orderId;
        $params['payment_status'] = $paymentStatus;
        $params['order_sum'] = $params['payment_sum'] = $orderSum;

        $I->sendAjaxPostRequest('/api/v1/webhook/admitad?at=tipli8190', $params);
    }

    private function logIn(AcceptanceTester $I, string $email = '<EMAIL>', string $password = '123456')
    {
        $I->amOnPage('/');
        $I->click('Přihlásit se');
        $I->fillField('email', $email);
        $I->fillField('password', $password);
        $I->click(['name' => '_submit']);
    }

    private function logOut(AcceptanceTester $I)
    {
        $I->amOnPage('/odhlaseni');
    }

    private function generateIdsForTransactions()
    {
        $this->confirmedTransactionId = rand(10, 1000000);
        $this->registeredTransactionId = rand(10, 1000000);
        $this->canceledTransactionId = rand(10, 1000000);
    }

    private function checkBalances(AcceptanceTester $I)
    {
        $this->logIn($I, self::USER_EMAIL);
        $I->amOnPage('/odmeny');

        $balances = $I->grabMultiple('.account-info-table__value--green');

        if (Strings::trim($balances[0]) != '135 Kč') {
            $this->throwError('Odměny k vyplacení nesedí!' . $balances[0]);
        } else if (Strings::trim($balances[1]) != '90 Kč') {
            $this->throwError('Registrované odměny nesedí!');
        } else if (Strings::trim($balances[2]) != '0 Kč') {
            $this->throwError('Bonusové odměny nesedí!');
        }

        $this->logOut($I);
    }

    private function throwError($message)
    {
        throw new \Exception($message);
    }

}
