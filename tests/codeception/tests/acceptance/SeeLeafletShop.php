<?php
# vendor/bin/codecept run -c tests/codeception/codeception.yml acceptance SeeLeafletsCest --steps

use Codeception\Util\HttpCode;

class SeeLeafletShop
{
	public function seeLeafletsCest(AcceptanceTester $I)
	{
		$I->amOnPage('/letaky/orfa-nabytek');

		$I->see('Akční letáky obchodu Orfa nábytek');

		$I->see('Aktuální');

		$I->seeNumberOfElements('.leaflet-list__wrapper .leaflet-list__item', 1);

		$I->seeNumberOfElements('.leaflet-list__wrapper--expire .leaflet-list__item', 12);

		$I->seeResponseCodeIs(HttpCode::OK);
	}
}
