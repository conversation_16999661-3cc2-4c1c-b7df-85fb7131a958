<?php

use Codeception\Util\HttpCode;

class AdminCreateConstantRewardCest
{
	public function createSpecialConstantReward(AcceptanceTester $I)
	{
		$I->wantTo('create special constant reward');

		$this->logOut($I);
		$this->logIn($I, '<EMAIL>', '123456');

		$this->verify($I);

		$I->amOnPage('/admin/rewards.constant-reward/constant-reward?parentId=19');
		$I->seeResponseCodeIs(HttpCode::OK);

		$I->fillField('#frm-constantRewardControl-form-amount', 166);
		$I->fillField('#frm-constantRewardControl-form-validSince', (new \DateTime())->format('Y-m-d H:i:s'));
		$I->fillField('#frm-constantRewardControl-form-validTill', (new \DateTime('+ 1 day'))->format('Y-m-d H:i:s'));

		$I->click('#frm-constantRewardControl-form input[type="submit"]');
		$I->seeInSource('Uložit');

		$this->logOut($I);

		$this->testBonus($I);
	}

	private function verify(AcceptanceTester $I)
	{
		$code = 'dc468c';

		$I->amOnPage('/admin');
		$I->seeInSource('Ověření přihlášení');
		$I->fillField('code', $code);
		$I->click('.sms-verify__form-submit');
	}

	private function testBonus(AcceptanceTester $I)
	{
		$email = '<EMAIL>';

		$I->amOnPage('/p/1');

		$this->signUp($I, $email);

		$userId = $this->getUserId($I);

		$this->logOut($I);

		$this->logIn($I, '<EMAIL>', '123456');

		$this->addTransaction($I, $userId);

		$this->checkBonus($I);

		$this->logOut($I);
	}

	private function logIn(AcceptanceTester $I, $email, $password)
	{
		$I->amOnPage('/');
		$I->click('Přihlásit se');
		$I->fillField('email', $email);
		$I->fillField('password', $password);
		$I->click(['name' => '_submit']);
	}

	private function getUserId(AcceptanceTester $I)
	{
		$I->amOnPage('/doporucit-pratelum');

		$recommendUrl = $I->grabValueFrom('.account-friend__input');

		$userId = explode('/', $recommendUrl)[4];

		return $userId;
	}

	private function logOut(AcceptanceTester $I)
	{
		$I->amOnPage('/odhlaseni');
	}

	private function signUp(AcceptanceTester $I, $email)
	{
		$I->wantTo('sign up user');

		$I->fillField('email', $email);
		$I->click('Založit účet ZDARMA');

		$I->seeInSource('Slevové kupóny');
	}

	private function checkBonus(AcceptanceTester $I)
	{
		$I->amOnPage('/odmeny');

		$I->seeInSource('166', 'strong.account-transaction__amount-value');
	}

	private function addTransaction(AcceptanceTester $I, $userId)
	{
		$I->amOnPage('/admin/transactions.transaction/transaction');
		$I->seeInSource('transaction');
		$I->fillField('transactionId', \Nette\Utils\Random::generate(20));
		$I->fillField('commissionAmount', '1000');
		$I->fillField('userId', $userId);
		$I->fillField('orderAmount', '10000');
		$I->selectOption('shop', '1');
		$I->selectOption('currency', 'CZK');
		$I->click('Save');
	}
}
