<?php

use Codeception\Util\HttpCode;

class RefundMissingBonusCest
{

    public function createRefundMissingBonus(AcceptanceTester $I)
    {
        $I->wantTo('test missing bonus refund');

        $this->logOut($I);

        $this->logIn($I, '<EMAIL>', '123456');

        $I->amOnPage('/reklamace');

        $I->fillField('#frm-refundMissingBonusControl-form-message', 'Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Aliquam erat volutpat. Donec ipsum massa, ullamcorper in, auctor et, scelerisque sed, est. Donec iaculis gravida nulla. Nunc auctor. Fusce tellus. Proin mattis lacinia justo. Temporibus autem quibusdam et aut officiis debitis aut rerum necessitatibus saepe eveniet ut et voluptates repudiandae sint et molestiae non recusandae. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Pellentesque arcu. Aliquam erat volutpat. Mauris dictum facilisis augue. Mauris tincidunt sem sed arcu. Class aptent taciti sociosqu ad litora torquent per conubia nostra, per inceptos hymenaeos.');

        $I->click('#frm-refundMissingBonusControl-form input[type="submit"]');
        
        $I->seeInSource('Děkujeme za odeslání formuláře');

	    $refundId = $I->grabMultiple('#refundId')[0];

	    $this->seeRefundInAdmin($I, $refundId);
    }

    private function logIn(AcceptanceTester $I, $email, $password)
    {
        $I->amOnPage('/prihlaseni');
        
        $I->fillField('email', $email);
        $I->fillField('password', $password);
        
        $I->click(['name' => '_submit']);
    }

    private function logOut(AcceptanceTester $I)
    {
        $I->wantTo('log out');

        $I->amOnPage('/odhlaseni');
    }

	private function seeRefundInAdmin(AcceptanceTester $I, int $refundId)
	{
		$this->logOut($I);
        
		$this->logIn($I, '<EMAIL>', '123456');

		$this->verify($I);

		$I->amOnPage('/admin/refunds.refund/refund/' . $refundId);

		$I->seeResponseCodeIs(HttpCode::OK);
	}

	private function verify(AcceptanceTester $I)
	{
		$code = 'dc468c';

		$I->amOnPage('/admin');
        
		$I->seeInSource('Ověření přihlášení');
        
		$I->fillField('code', $code);
        
		$I->click('.sms-verify__form-submit');
	}
}
