<?php

use Codeception\Util\Locator;

class SeeCampaignCest
{
	public function testCampaignBar(AcceptanceTester $I)
	{
		$this->logOut($I);

		$email = rand(1,9999) . '<EMAIL>';

		$this->signUp($I, $email);

		$userId = $this->getUserId($I);

		$I->dontSee('.c-progress');
		$I->see('0 Kč');

		$this->logOut($I);

		$this->logIn($I, '<EMAIL>', '123456');
		$this->verify($I);

		$this->addTransaction($I, 2000, $userId);

		$this->logOut($I);
		$this->logIn($I, $email, '123456');

#		$I->see('Ještě můžete získat bonus 246 Kč za nákupy přes Tipli.');
#		$I->see('Nasbí<PERSON>j<PERSON> přes Tip<PERSON> ještě 246 Kč a my vám dáme da<PERSON>ch 246 Kč jako bonus.');

		$I->see('0 Kč', '.c-progress__status-start-inner');
		$I->see('300 Kč', '.c-progress__status-end-inner');

		$this->logOut($I);

		$this->logIn($I, '<EMAIL>', '123456');

		$this->addTransaction($I, 20000, $userId);

		$this->logOut($I);

		$this->logIn($I, $email, '123456');

		$I->see('Gratulujeme! Získali jste celou bonusovou odměnu 300 Kč.');
	}

	private function verify(AcceptanceTester $I)
	{
		$code = 'dc468c';

		$I->amOnPage('/admin');
		$I->seeInSource('Ověření přihlášení');
		$I->fillField('code', $code);
		$I->click('.sms-verify__form-submit');
	}

	private function addTransaction(AcceptanceTester $I, float $orderAmount, int $userId)
	{
		$I->amOnPage('admin/transactions.transaction/transaction/');
		$I->seeInSource('Transactions');
		$I->fillField('transactionId', \Nette\Utils\Random::generate(20));
		$I->fillField('commissionAmount', $orderAmount / 100 * 6);
		$I->fillField('userId', $userId);
		$I->fillField('orderAmount', $orderAmount);
		$I->selectOption('shop', '1');
		$I->selectOption('currency', 'CZK');
		$I->click('Save');
	}

	private function logIn(AcceptanceTester $I, $email, $password)
	{
		$I->amOnPage('/');
		$I->click('Přihlásit se');
		$I->fillField('email', $email);
		$I->fillField('password', $password);
		$I->click(['name' => '_submit']);
	}

	private function logOut(AcceptanceTester $I)
	{
		$I->wantTo('log out');

		$I->amOnPage('/odhlaseni');
	}

	private function signUp(AcceptanceTester $I, $email)
	{
		$I->wantTo('sign up user');

		$I->fillField('email', $email);
		$I->click('Založit účet ZDARMA');

		$I->fillField('newPassword', 123456);
		$I->click('Nastavit heslo');

		$I->seeInSource('Slevové kupóny');
	}

	private function getUserId(AcceptanceTester $I)
	{
		$I->amOnPage('/doporucit-pratelum');

		$recommendUrl = $I->grabValueFrom('.account-friend__input');

		$userId = explode('/', $recommendUrl)[4];

		return $userId;
	}
}
