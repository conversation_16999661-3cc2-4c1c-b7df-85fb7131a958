<?php

use Codeception\Util\HttpCode;

class RefundCanceledCommissionCest
{
    public function createCanceledCommissionAmount(AcceptanceTester $I)
    {
        $I->wantTo('test canceled commission refund');

        $this->logOut($I);
        $this->logIn($I, '<EMAIL>', '123456');

        $I->amOnPage('/reklamace');
        $I->seeInSource('Mám dotaz k odměně');
        $I->seeInSource('Zrušená odměna');

        $I->fillField('#frm-refundCanceledCommissionControl-form-transactionId', 1);
        $I->fillField('#frm-refundCanceledCommissionControl-form-message', 'Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Aliquam erat volutpat.');

		$I->click('#submit-canceled');
        $I->seeInSource('Děkujeme za odeslání formuláře');

	    $refundId = $I->grabMultiple('#refundId')[0];

		$this->seeRefundInAdmin($I, $refundId);
    }

    private function logIn(AcceptanceTester $I, $email, $password)
    {
        $I->amOnPage('/prihlaseni');
        $I->fillField('email', $email);
        $I->fillField('password', $password);
        $I->click(['name' => '_submit']);
    }

    private function logOut(AcceptanceTester $I)
    {
        $I->wantTo('log out');

        $I->amOnPage('/odhlaseni');
    }

	private function seeRefundInAdmin(AcceptanceTester $I, int $refundId)
	{
		$this->logOut($I);
		$this->logIn($I, '<EMAIL>', '123456');

		$this->verify($I);

		$I->amOnPage('/admin/refunds.refund/refund/' . $refundId);

		$I->seeResponseCodeIs(HttpCode::OK);
	}

	private function verify(AcceptanceTester $I)
	{
		$code = 'dc468c';

		$I->amOnPage('/admin');
		$I->seeInSource('Ověření přihlášení');
		$I->fillField('code', $code);
		$I->click('.sms-verify__form-submit');
	}
}
