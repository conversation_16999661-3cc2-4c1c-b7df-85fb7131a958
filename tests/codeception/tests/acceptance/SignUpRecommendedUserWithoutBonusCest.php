<?php

class SignUpRecommendedUserWithoutBonusCest
{
    public function signUpRecommendedUserWithoutBonus(AcceptanceTester $I)
    {
        $firstEmail = uniqid() . '<EMAIL>';
        $secondEmail = uniqid() . '<EMAIL>';

        $I->wantTo('sign up recommended user without bonus');

        $this->logOut($I);

        $this->signUp($I, $firstEmail);

        $I->amOnPage('/obchod/aliexpress?p=1&entranceType=pure');

        $this->logOut($I);

        $this->signUp($I, $secondEmail);

        $I->seeInSource('0 Kč');
    }

    private function signUp(AcceptanceTester $I, $email)
    {
        $I->wantTo('sign up user');

        $I->amOnPage('/');
        $I->fillField('email', $email);
        $I->click('Založit účet ZDARMA');

        $I->seeInSource('Slevové kupóny');
    }

    private function logOut(AcceptanceTester $I)
    {
        $I->wantTo('log out');

        $I->amOnPage('/odhlaseni');
    }

}
