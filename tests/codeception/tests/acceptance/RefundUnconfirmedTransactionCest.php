<?php

use Codeception\Util\HttpCode;

class RefundUnconfirmedTransactionCest
{

    public function createIncorrectAmountRefund(AcceptanceTester $I)
    {
        $I->wantTo('test incorrect amount refund');

        $this->logOut($I);
        $this->logIn($I, '<EMAIL>', '123456');

        $I->amOnPage('/reklamace');
        $I->seeInSource('Mám dotaz k odměně');

        $I->fillField('#frm-refundUnconfirmedTransactionControl-form-message', 'Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Aliquam erat volutpat.');
        $I->fillField('#frm-refundUnconfirmedTransactionControl-form-transactionId', 3);

        $I->click('#submit-unconfirmed-transaction-refund');
        $I->seeInSource('Děkujeme za o<PERSON>ání formuláře');

	    $I->seeResponseCodeIs(HttpCode::OK);

	    $refundId = $I->grabMultiple('#refundId')[0];

	    $this->seeRefundInAdmin($I, $refundId);
    }

    private function logIn(AcceptanceTester $I, $email, $password)
    {
        $I->amOnPage('/prihlaseni');
        $I->fillField('email', $email);
        $I->fillField('password', $password);
        $I->click(['name' => '_submit']);
    }

    private function logOut(AcceptanceTester $I)
    {
        $I->wantTo('log out');

        $I->amOnPage('/odhlaseni');
    }

	private function seeRefundInAdmin(AcceptanceTester $I, int $refundId)
	{
		$this->logOut($I);
		$this->logIn($I, '<EMAIL>', '123456');

		$this->verify($I);

		$I->amOnPage('/admin/refunds.refund/refund/' . $refundId);

		$I->seeResponseCodeIs(HttpCode::OK);
	}

	private function verify(AcceptanceTester $I)
	{
		$code = 'dc468c';

		$I->amOnPage('/admin');
		$I->seeInSource('Ověření přihlášení');
		$I->fillField('code', $code);
		$I->click('.sms-verify__form-submit');
	}
}
