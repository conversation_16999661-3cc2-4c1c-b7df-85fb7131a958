<?php

class ZasilkovnaCest
{

    const NEW_USER_URL = '/zasilkovna/vip-odmena';
    const EXTENSION_URL = '/zasilkovna/prodlouzeni-vip';

	public function signUpZasilkovna(AcceptanceTester $I)
	{
		return;
		$I->wantTo('sign up via zasilkovna');

		$this->logOut($I);

		// invalid token
        $packetId = uniqid();
        $invalidToken = sha1($packetId);
		$I->amOnPage(self::NEW_USER_URL . '?email=' . $this->getRandomEmail() . '&packetId=' . $packetId . '&token=' . $invalidToken);
		$I->checkOption('#1');
		$I->checkOption('#2');
		$I->checkOption('#3');
		$I->click('.z-popup-header__submit');

        $I->seeInSource('Číslo zásilky již není platn<PERSON>, nebo nebylo nalezeno.');

        // valid token & sign up
        $packetId = uniqid();
        $token = sha1($packetId . '5ce70e181516e');
        $I->amOnPage(self::NEW_USER_URL . '?email=' . $this->getRandomEmail() . '&packetId=' . $packetId . '&token=' . $token);
        $I->checkOption('#1');
        $I->checkOption('#2');
        $I->checkOption('#3');
        $I->click('.z-popup-header__submit');

        $I->seeInSource('Dárek už je na cestě.');

        $I->click('Pokračovat »');
        $I->seeInSource('Slevové kupóny');
        $I->seeInSource('Získejte 300 Kč za vaše nákupy');

        $userId = $this->getUserId($I);

		$I->seeInDatabase('tipli_conditions_approval', ['user_id' => $userId, 'document_id' => 1]);
		$I->seeInDatabase('tipli_conditions_approval', ['user_id' => $userId, 'document_id' => 4]);
		$I->seeInDatabase('tipli_conditions_approval', ['user_id' => $userId, 'document_id' => 7]);
		$I->seeInDatabase('tipli_zasilkovna_approval_synchronization', ['user_id' => $userId, 'action' => 'register']);

        $I->click('Odměny a výhody');
        $I->seeInSource('Exkluzivní výhody pro uživatele Zásilkovny');
        $I->seeInSource('Všechny obchody');

        // extension invalid token
        $packetId = uniqid();
        $token = sha1($packetId . '5ce70e181516e');
        $I->amOnPage(self::EXTENSION_URL . '?packetId=' . $packetId . '&token=' . $token);
        $I->seeInSource('Jste přihlášen pomocí emailu');
        $I->click('Pokračovat');

        $I->seeInSource('Prodloužili jsme vám');
        $I->seeInSource('VIP účet o další 1 měsíc');
    }

	private function getUserId(AcceptanceTester $I)
	{
		$I->amOnPage('/doporucit-pratelum');
		$recommendUrl = $I->grabValueFrom('.account-friend__input');
		$userId = explode('/', $recommendUrl)[4];
		return $userId;
	}

	private function getRandomEmail()
    {
        return 'zasilkovna'. uniqid() . '@example.com';
    }

    private function logOut(AcceptanceTester $I)
    {
        $I->amOnPage('/odhlaseni');
    }
}
