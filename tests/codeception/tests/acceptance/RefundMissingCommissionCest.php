<?php

use Codeception\Util\HttpCode;
use Codeception\Util\Locator;

class RefundMissingCommissionCest
{

    public function testMissingCommissionRefunds(AcceptanceTester $I)
    {
        # create and approve missing commission refund
        $this->createMissingCommissionRefund($I, 1500);

        # check balance if user sees that refund
        $this->checkBonusTransactionInMyAccount($I);

        #create and decline missing commission refund
        $this->createMissingCommissionRefund($I, 500, 'decline');
    }

    private function createMissingCommissionRefund(AcceptanceTester $I, $orderAmount, $action = 'approve')
    {
        $I->wantTo('test missing commission refund');

        $this->logOut($I);
        $this->logIn($I, '<EMAIL>', '123456');

        $I->amOnPage('/reklamace');
        $I->seeInSource('Mám dotaz k odměně');
        $I->seeInSource('Chyběj<PERSON><PERSON><PERSON> odměna');

        $I->fillField('purchasedAt', (new DateTime('-20 days'))->format('Y-m-d'));
        $I->fillField('shopId', 2);
		$I->fillField('orderId', '*********');
        $I->fillField('orderAmount', $orderAmount);

        $I->click('#frm-refundMissingCommissionControl-form input[type="submit"]');
        $I->seeInSource('Děkujeme za odeslání formuláře');

        $refundId = $I->grabMultiple('#refundId')[0];

        if ($action === 'approve') {
            $this->approveRefund($I, $refundId);
        } else {
            $this->declineRefund($I, $refundId);
        }
    }

    private function approveRefund(AcceptanceTester $I, int $refundId)
    {
        $this->logOut($I);
        $this->logIn($I, '<EMAIL>', '123456');

        $this->verify($I);

        $I->amOnPage('/admin/refunds.refunds/');
        $I->seeInSource('nepřipsaná odměna');

        $I->amOnPage('/admin/refunds.refund/refund/' . $refundId);

        $I->checkOption('noMessage');
        $I->click('#approve-refund');

        $I->seeInSource('Saved.');

	    $I->seeResponseCodeIs(HttpCode::OK);
    }

    private function declineRefund(AcceptanceTester $I, int $refundId)
    {
        $this->logOut($I);
        $this->logIn($I, '<EMAIL>', '123456');

        $I->amOnPage('/admin/refunds.refunds/');
        $I->seeInSource('nepřipsaná odměna');

        $I->amOnPage('/admin/refunds.refund/refund/' . $refundId);

        $I->checkOption('noMessage');
        $I->click('#decline-refund');

        $I->seeInSource('Saved.');

	    $I->seeResponseCodeIs(HttpCode::OK);
    }

    private function checkBonusTransactionInMyAccount(AcceptanceTester $I)
    {
        $this->logOut($I);
        $this->logIn($I, '<EMAIL>', '123456');

        $I->amOnPage('/odmeny');

        $balances = $I->grabMultiple('.account-info-table__value--green');

        if ($balances[2] != '49,59 Kč') {
            $this->throwError('Odměny k vyplacení nesedí!' . $balances[2]);
        }
    }

    private function logIn(AcceptanceTester $I, $email, $password)
    {
        $I->amOnPage('/');
        $I->click('Přihlásit se');
        $I->fillField('email', $email);
        $I->fillField('password', $password);
        $I->click(['name' => '_submit']);
    }

    private function logOut(AcceptanceTester $I)
    {
        $I->wantTo('log out');

        $I->amOnPage('/odhlaseni');
    }

    private function throwError($message)
    {
        throw new \Exception($message);
    }

    private function verify(AcceptanceTester $I)
    {
        $code = 'dc468c';

        $I->amOnPage('/admin');
        $I->seeInSource('Ověření přihlášení');
        $I->fillField('code', $code);
        $I->click('.sms-verify__form-submit');
    }
}
