<?php

class AdminUncancelTransactionCest
{
	public function uncancelTransaction(AcceptanceTester $I)
	{
		$I->wantTo('admin - uncancel transaction');

		$this->logOut($I);

		$this->logIn($I);

		$this->verify($I);

		$I->amOnPage('/admin/transactions.transaction/transaction-uncancel/1');

		$I->fillField('commissionAmount', 1000);
		$I->selectOption('currency', 'CZK');
		$I->click('Save');

		$I->seeInCurrentUrl('transaction/?_fid');
		$I->seeElement('.alert-info');

		$I->amOnPage('/odmeny');

		$I->seeInSource('475');
	}

	private function logIn(AcceptanceTester $I)
	{
		$email = '<EMAIL>';
		$password = '123456';

		$I->amOnPage('/prihlaseni');
		$I->fillField('email', $email);
		$I->fillField('password', $password);
		$I->click(['name' => '_submit']);
	}

	private function verify(AcceptanceTester $I)
	{
		$code = 'dc468c';

		$I->amOnPage('/admin');
		$I->seeInSource('Ověření přihlášení');
		$I->fillField('code', $code);
		$I->click('.sms-verify__form-submit');
	}

	private function logOut(AcceptanceTester $I)
	{
		$I->amOnPage('/odhlaseni');
	}
}
