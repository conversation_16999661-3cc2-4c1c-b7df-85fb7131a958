<?php


class DealCest
{
	public function deal(ApiTester $I)
	{
		$email = rand(0,2999) . '<EMAIL>';
		$password = '12345678';

		$this->signUp($I, $email, $password);

		$token = $this->authorize($I, $email, $password);

		$I->haveHttpHeader('Authorization', 'Bearer ' . $token);
		$I->haveHttpHeader('platform', 'ios');

		$this->tags($I);
		$this->home($I);
		$this->coupons($I);
		$this->sales($I);
	}

	private function signUp(ApiTester $I, $email, $password)
	{
		$I->haveHttpHeader('Content-Type', 'application/json');
		$I->haveHttpHeader('platform', 'ios');

		$I->sendPOST('mobile/auth/register', [
			'email' => $email,
			'password' => $password,
			'parentId' => 0,
			'utmSource' => 'test',
			'utmMedium' => 'test',
			'utmCampaign' => 'test'
		]);

		$I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);
		$I->seeResponseIsJson();
	}

	private function authorize(ApiTester $I, $email, $password)
	{
		$I->haveHttpHeader('Content-Type', 'application/json');
		$I->haveHttpHeader('platform', 'ios');

		$I->sendPOST('mobile/auth/login', [
			'email' => $email,
			'password' => $password
		]);

		$I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);
		$I->seeResponseIsJson();

		$response = $I->grabResponse();
		preg_match('/"accessToken":"(\w+)"/', $response, $matches);

		return $matches[1];
	}

	private function tags(ApiTester $I)
	{
		$I->wantTo('Test deal tags endpoint');

		$I->haveHttpHeader('Content-Type', 'application/json');
		$I->haveHttpHeader('platform', 'ios');

		$I->sendGET('mobile/deal/tags');
		$I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);
		$I->seeResponseContains('"tags"');
		$I->seeResponseIsJson();
	}

	private function home(ApiTester $I)
	{
		$I->wantTo('Test deal home endpoint');

		$I->haveHttpHeader('Content-Type', 'application/json');
		$I->haveHttpHeader('platform', 'ios');

		$I->sendGET('mobile/deal/home');

		$I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);
		$I->seeResponseContains('"tags"');
		$I->seeResponseContains('"coupons"');
		$I->seeResponseContains('"sales"');
		$I->seeResponseIsJson();
	}

	private function coupons(ApiTester $I)
	{
		$I->wantTo('Test deal coupons endpoint');

		$I->haveHttpHeader('Content-Type', 'application/json');
		$I->haveHttpHeader('platform', 'ios');

		$I->sendGET('mobile/deal/coupons');

		$I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);
		$I->seeResponseContains('"items"');
		$I->seeResponseIsJson();
	}

	private function sales(ApiTester $I)
	{
		$I->wantTo('Test deal sales endpoint');

		$I->haveHttpHeader('Content-Type', 'application/json');
		$I->haveHttpHeader('platform', 'ios');

		$I->sendGET('mobile/deal/sales');

		$I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);
		$I->seeResponseContains('"items"');
		$I->seeResponseIsJson();
	}
}
