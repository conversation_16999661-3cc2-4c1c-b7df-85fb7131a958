<?php


class ShopCest
{
	public function shop(ApiTester $I)
	{
		$email = rand(0,2999) . '<EMAIL>';
		$password = '12345678';

		$this->signUp($I, $email, $password);

		$token = $this->authorize($I, $email, $password);
		$I->haveHttpHeader('Authorization', 'Bearer ' . $token);
		$I->haveHttpHeader('platform', 'ios');

		//$this->home($I);
		$this->detail($I);
		$this->coupons($I);
		$this->sales($I);
		$this->search($I);
		$this->searchQuery($I);
		$this->list($I);
		$this->addFavorite($I);
		$this->favorites($I);
		$this->removeFavorite($I);
	}

	private function signUp(ApiTester $I, $email, $password)
	{
		$I->haveHttpHeader('Content-Type', 'application/json');
		$I->haveHttpHeader('platform', 'ios');

		$I->sendPOST('mobile/auth/register', [
			'email' => $email,
			'password' => $password,
			'parentId' => 0,
			'utmSource' => 'test',
			'utmMedium' => 'test',
			'utmCampaign' => 'test'
		]);

		$I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);
		$I->seeResponseIsJson();
	}

	private function authorize(ApiTester $I, $email, $password)
	{
		$I->haveHttpHeader('Content-Type', 'application/json');
		$I->haveHttpHeader('platform', 'ios');

		$I->sendPOST('mobile/auth/login', [
			'email' => $email,
			'password' => $password
		]);

		$I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);
		$I->seeResponseIsJson();

		$response = $I->grabResponse();
		preg_match('/"accessToken":"(\w+)"/', $response, $matches);

		return $matches[1];
	}

	private function home(ApiTester $I)
	{
		$I->wantTo('Test shop default endpoint');

		$I->haveHttpHeader('Content-Type', 'application/json');
		$I->haveHttpHeader('platform', 'ios');

		$I->sendGET('mobile/shop/home');
		$I->seeResponseCodeIs(200);
		$I->seeResponseIsJson();
	}

	private function detail(ApiTester $I)
	{
		$I->wantTo('Test shop detail endpoint');

		$I->haveHttpHeader('Content-Type', 'application/json');
		$I->haveHttpHeader('platform', 'ios');

		$I->sendGET('mobile/shop/detail/1');
		$I->seeResponseCodeIs(200);
		$I->seeResponseContains('"name":"AliExpress"');
		$I->seeResponseIsJson();
	}

	private function coupons(ApiTester $I)
	{
		$I->wantTo('Test shop detail coupons');

		$I->haveHttpHeader('Content-Type', 'application/json');
		$I->haveHttpHeader('platform', 'ios');

		$I->sendGET('mobile/shop/detail/1/coupons');
		$I->seeResponseCodeIs(200);
		$I->seeResponseIsJson();
	}

	private function sales(ApiTester $I)
	{
		$I->wantTo('Test shop detail sales');

		$I->haveHttpHeader('Content-Type', 'application/json');
		$I->haveHttpHeader('platform', 'ios');

		$I->sendGET('mobile/shop/detail/1/sales');
		$I->seeResponseCodeIs(200);
		$I->seeResponseIsJson();
	}

	private function search(ApiTester $I)
	{
		$I->wantTo('Test shop search');

		$I->haveHttpHeader('Content-Type', 'application/json');
		$I->haveHttpHeader('platform', 'ios');

		$I->sendGET('mobile/shop/search');
		$I->seeResponseCodeIs(200);
		$I->seeResponseIsJson();
	}

	private function searchQuery(ApiTester $I)
	{
		$I->wantTo('Test shop search with query string');

		$I->haveHttpHeader('Content-Type', 'application/json');
		$I->haveHttpHeader('platform', 'ios');

		$I->sendGET('mobile/shop/search/ali');
		$I->seeResponseContains('"name":"AliExpress"');
		$I->seeResponseCodeIs(200);
		$I->seeResponseIsJson();
	}

	private function list(ApiTester $I)
	{
		$I->wantTo('Test shop list');

		$I->haveHttpHeader('Content-Type', 'application/json');
		$I->haveHttpHeader('platform', 'ios');

		$I->sendGET('mobile/shop');
		$I->seeResponseContains('"items":');
		$I->seeResponseCodeIs(200);
		$I->seeResponseIsJson();
	}

	private function addFavorite(ApiTester $I)
	{
		$I->haveHttpHeader('Content-Type', 'application/json');
		$I->haveHttpHeader('platform', 'ios');

		$I->sendPUT('mobile/shop/favorite/1');

		$I->seeResponseCodeIs(\Codeception\Util\HttpCode::NO_CONTENT);
	}

	private function favorites(ApiTester $I)
	{
		$I->haveHttpHeader('Content-Type', 'application/json');
		$I->haveHttpHeader('platform', 'ios');

		$I->sendGET('mobile/shop/favorites');
		$I->seeResponseContains('"name":"AliExpress"');

		$I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);
		$I->seeResponseIsJson();
	}

	private function removeFavorite(ApiTester $I)
	{
		$I->haveHttpHeader('Content-Type', 'application/json');
		$I->haveHttpHeader('platform', 'ios');

		$I->sendDELETE('mobile/shop/favorite/1');
		$I->seeResponseCodeIs(\Codeception\Util\HttpCode::NO_CONTENT);

		$I->sendGET('mobile/shop/favorites');
		$I->dontSeeResponseContains('"name":"AliExpress"');
		$I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);
	}
}
