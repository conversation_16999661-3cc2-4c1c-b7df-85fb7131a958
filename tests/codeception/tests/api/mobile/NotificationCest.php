<?php


class NotificationCest
{
	public function notification(ApiTester $I)
	{
		$email = rand(0,2999) . '<EMAIL>';
		$password = '12345678';

		$this->signUp($I, $email, $password);

		$token = $this->authorize($I, $email, $password);
		$I->haveHttpHeader('Authorization', 'Bearer ' . $token);
		$I->haveHttpHeader('Content-Type', 'application/json');
		$I->haveHttpHeader('platform', 'ios');

		$I->sendGET('mobile/notification');
		$I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);
		$I->seeResponseContains('"notifications"');
		$I->seeResponseContains('"referral"');
		$I->seeResponseIsJson();
	}

	private function signUp(ApiTester $I, $email, $password)
	{
		$I->haveHttpHeader('Content-Type', 'application/json');
		$I->haveHttpHeader('platform', 'ios');

		$I->sendPOST('mobile/auth/register', [
			'email' => $email,
			'password' => $password,
			'parentId' => 0,
			'utmSource' => 'test',
			'utmMedium' => 'test',
			'utmCampaign' => 'test'
		]);

		$I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);
		$I->seeResponseIsJson();
	}

	private function authorize(ApiTester $I, $email, $password)
	{
		$I->haveHttpHeader('Content-Type', 'application/json');
		$I->haveHttpHeader('platform', 'ios');

		$I->sendPOST('mobile/auth/login', [
			'email' => $email,
			'password' => $password
		]);

		$I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);
		$I->seeResponseIsJson();

		$response = $I->grabResponse();
		preg_match('/"accessToken":"(\w+)"/', $response, $matches);

		return $matches[1];
	}
}
