<?php


class AccountCest
{
	public function sign(ApiTester $I)
	{
		$email = '<EMAIL>';
		$password = '123456';

		$this->signUp($I, $email, $password);

		$token = $this->authorize($I, $email, $password);
		$I->haveHttpHeader('Authorization', 'Bearer ' . $token);
		$I->haveHttpHeader('platform', 'ios');

		$this->getMe($I);

		$this->postName($I);
		$this->postBirthdate($I);
		$this->postGender($I);
		$this->postPhoneNumber($I);
		$this->postPhoneNumberVerification($I);
		$this->postAccountNumber($I);
	}

	private function signUp(ApiTester $I, $email, $password)
	{
		$I->haveHttpHeader('Content-Type', 'application/json');
		$I->haveHttpHeader('platform', 'ios');

		$I->sendPOST('mobile/auth/register', [
			'email' => $email,
			'password' => $password,
			'parentId' => 0,
			'utmSource' => 'test',
			'utmMedium' => 'test',
			'utmCampaign' => 'test'

		]);

		$I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);
		$I->seeResponseIsJson();
	}

	private function authorize(ApiTester $I, $email, $password)
	{
		$I->haveHttpHeader('Content-Type', 'application/json');
		$I->haveHttpHeader('platform', 'ios');

		$I->sendPOST('mobile/auth/login', [
			'email' => $email,
			'password' => $password
		]);

		$I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);
		$I->seeResponseIsJson();

		$response = $I->grabResponse();
		preg_match('/"accessToken":"(\w+)"/', $response, $matches);

		return $matches[1];
	}

	private function getMe(ApiTester $I)
	{
		$I->haveHttpHeader('Content-Type', 'application/json');
		$I->haveHttpHeader('platform', 'ios');

		$I->sendGET('mobile/user/me');
		$I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);
	}

	private function postName(ApiTester $I)
	{
		$I->haveHttpHeader('Content-Type', 'application/json');
		$I->haveHttpHeader('platform', 'ios');

		$I->sendPOST('mobile/user/name',  [
			'firstName' => 'Test',
			'lastName' => 'Tester'
		]);

		$I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);
	}

	private function postBirthdate(ApiTester $I)
	{
		$I->haveHttpHeader('Content-Type', 'application/json');
		$I->haveHttpHeader('platform', 'ios');

		$I->sendPOST('mobile/user/birthdate',  [
			'birthdate' => '1995-05-12'
		]);

		$I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);
	}

	private function postGender(ApiTester $I)
	{
		$I->haveHttpHeader('Content-Type', 'application/json');
		$I->haveHttpHeader('platform', 'ios');

		$I->sendPOST('mobile/user/gender',  [
			'gender' => 'male'
		]);

		$I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);
	}

	private function postPhoneNumber(ApiTester $I)
	{
		$I->haveHttpHeader('Content-Type', 'application/json');
		$I->haveHttpHeader('platform', 'ios');

		$I->sendPOST('mobile/user/phone-number',  [
			'phoneCountryCode' => '+420',
			'phoneNumber' => '*********'
		]);

		$I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);
	}

	private function postPhoneNumberVerification(ApiTester $I)
	{
		$I->haveHttpHeader('Content-Type', 'application/json');
		$I->haveHttpHeader('platform', 'ios');

		$I->sendPOST('mobile/user/phone-number/verify-code',  [
			'phoneCountryCode' => '+420',
			'phoneNumber' => '*********',
			'code' => '1234'
		]);

		$I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);
	}

	private function postAccountNumber(ApiTester $I)
	{
		$I->haveHttpHeader('Content-Type', 'application/json');
		$I->haveHttpHeader('platform', 'ios');

		$I->sendPOST('mobile/user/account-number',  [
			'accountNumber' => '*********/0300'
		]);

		$I->seeResponseCodeIs(\Codeception\Util\HttpCode::OK);
	}
}
