<?php



class RondoCest
{
    const SUB_ID = 'rondo|test';
    const EMAIL = 'rondo@tipli.cz__RONDO';

    public function _before(ApiTester $I)
    {
        $I->haveHttpHeader('HttpAuthorization', '356a19Wq0N5P1xElu7tDijpSjPwzmis7KdK4Q8h8');
        $I->haveHttpHeader('Content-Type', 'application/json');
    }

    public function _after(ApiTester $I)
    {
    }

    // tests
    public function visitShop(ApiTester $I)
    {
        $redirectionLink = $this->getShopRedirectionLink($I);

        $I->sendGET($redirectionLink);
    }

    public function seeRegisteredUser(ApiTester $I)
    {
        $I->seeInDatabase('tipli_account_user', [
            'email' => self::EMAIL,
        ]);
    }

    public function seeTransaction(ApiTester $I)
    {
        $I->sendGET('rondo/transactions?confirmed=1');
        $I->seeResponseIsJson();

        $data = json_decode($I->grabResponse());

        if (count($data) == 0) {
            throw new Exception('no transaction');
        }
    }

    private function getShopRedirectionLink(ApiTester $I)
    {
        $I->sendGET('/rondo/shops');
        $I->seeResponseIsJson();

        $data = json_decode($I->grabResponse());

        if (isset($data->message) && $data->message === 'authorization has failed') {
            throw new Exception('authorization has failed');
        }

        foreach ($data as $shop) {
            return str_replace('_subId_', self::SUB_ID, $shop->redirect);
        }
    }


}
