<?php


use Nette\Utils\Json;

class TwistoCest
{
    const USER_EMAIL = '<EMAIL>';

    public function _before(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', '0998cf4f-f375-4f10-b644-5f7cc86dc167');

        $I->haveHttpHeader('Content-Type', 'application/json');
    }

    public function _after(ApiTester $I)
    {
    }

    // tests
    public function getShops(ApiTester $I)
    {
        $I->sendGET('/twisto/shops');

        $I->seeResponseIsJson();
        $I->seeResponseContainsJson(['status' => 'ok']);

        $I->seeResponseContainsJson([
            'shops' => [
                [
                    'name' => 'AliExpress',
                ]
            ]
        ]);

        $I->seeResponseMatchesJsonType([
            'shops' => [
                [
                    'id' => 'integer',
                    'name' => 'string',
                    'url' => 'string:url|null',
                    'logo' => 'string',
                    'short_description' => 'string|null',
                    'active' => 'boolean',
                    'created_at' => 'string'
                ]
            ]
        ]);
    }

    public function createUser(ApiTester $I)
    {
        $this->createUserForPartnerOrganization($I, 'twisto', self::USER_EMAIL);
        $this->createUserForPartnerOrganization($I, 'twisto-test', '2-' . self::USER_EMAIL);
    }

    private function createUserForPartnerOrganization(ApiTester $I, string $partnerOrganizationSlug, $email)
    {
        $I->amGoingTo('Create user for ' . $partnerOrganizationSlug);

        $I->sendPOST('/' . $partnerOrganizationSlug . '/users', ['email' => $email]);

        $I->seeResponseIsJson();
        $I->seeResponseContainsJson(['status' => 'ok']);

        $I->seeResponseMatchesJsonType([
            'user' => [
                'id' => 'integer'
            ]
        ]);
    }

    public function createDuplicateUser(ApiTester $I)
    {
        $I->sendPOST('/twisto/users', ['email' => self::USER_EMAIL]);

        $I->seeResponseIsJson();
        $I->seeResponseContainsJson([
            'status' => 'error',
            'message' => 'user is already registered'
        ]);
    }

    public function searchExistingUser(ApiTester $I)
    {
        $I->sendPOST('/twisto/users/search', ['email' => self::USER_EMAIL]);

        $I->seeResponseIsJson();
        $I->seeResponseContainsJson(['status' => 'ok']);

        $I->seeResponseMatchesJsonType([
            'user' => [
                'id' => 'integer'
            ]
        ]);
    }

    public function searchNonExistingUser(ApiTester $I)
    {
        $I->sendPOST('/twisto/users/search', ['email' => '<EMAIL>']);

        $I->seeResponseIsJson();
        $I->seeResponseContainsJson([
            'status' => 'error',
            'message' => 'no matching result found'
        ]);
    }

    public function getUserTransactions(ApiTester $I)
    {
        $I->sendPOST('/twisto/users/search', ['email' => self::USER_EMAIL]);

//        $userId = $I->grabDataFromResponseByJsonPath('$user.id');
        $response = $I->grabResponse();
        $userId = (Json::decode($response))->user->id;

        $this->createTransaction($I, $userId);

        $I->sendGET("/twisto/users/$userId/transactions");

        $I->seeResponseIsJson();
        $I->seeResponseContainsJson([
            'status' => 'ok',
            'transactions' => [
                [
                    'name' => 'AliExpress'
                ]
            ]
        ]);

        $I->seeResponseMatchesJsonType([
            'transactions' => [
                [
                    'id' => 'integer',
                    'shop_id' => 'integer',
                    'name' => 'string',
                    'confirmed_at' => 'string|null'
                ]
            ]
        ]);
    }

    private function createTransaction(ApiTester $I, int $userId)
    {
        $transactionId = rand(10000, 20000);

        $I->haveInDatabase('tipli_transactions_transaction', [
            'id' => $transactionId,
            'user_id' => $userId,
            'shop_id' => 1,
            'type' => 'commission',
            'currency' => 'CZK',
            'commission_amount' => 1000,
            'user_commission_amount' => 500,
            'bonus_amount' => 100,
            'billable' => true,
            'registered_at' => date('Y-m-d H:i'),
            'created_at' => date('Y-m-d H:i')
        ]);

        $I->haveInDatabase('tipli_transactions_transaction_data', [
            'id' => $transactionId,
            'transaction_id' => $transactionId,
            'order_amount' => 1000,
            'original_commission_amount' => 1000,
            'confirmation_treshold' => 2000,
            'recommendation_bonus_treshold' => 2000,
            'prepared_for_confirm' => true,
            'original_turnover' => 10,
            'original_income' => 50,
            'turnover' => 10,
            'income' => 10,
            'confirmed_by_scoring' => true
        ]);
    }
}
