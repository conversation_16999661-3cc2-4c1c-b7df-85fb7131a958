services:
    webserver:
        container_name: webserver
        build:
            context: .
            dockerfile: Dockerfile
        environment:
            NETTE_DEBUG: dd72daf3c3654258475a34a21
            TIPLI_DEBUG: dd72daf3c3654258475a34a21
        ports:
            - "127.0.0.1:8085:80"
            - "127.0.0.1:443:443"
        extra_hosts:
            - "tipli.czlocal:127.0.0.1"
            - "www.tipli.czlocal:127.0.0.1"
        volumes:
            - .:/project
            - ./.docker/apache/sites-enabled:/etc/apache2/sites-enabled
            - ./.docker/apache/certs:/etc/apache2/ssl
            # - ./.docker/php/xdebug.ini:/usr/local/etc/php/conf.d/xdebug.ini
            - ./.docker/apache/php/custom.ini:/usr/local/etc/php/conf.d/xdebug.ini
            - ./.docker/php/custom.ini:/usr/local/etc/php/conf.d/custom.ini
        depends_on:
            - database

    database:
        image: mariadb:latest
        volumes:
            - ./volumes/mysql:/var/lib/mysql
            - ./.docker/database:/docker-entrypoint-initdb.d:ro
        environment:
            MYSQL_ROOT_PASSWORD: root
            MYSQL_PASSWORD: root
            MYSQL_DATABASE: tipli

    redis:
        image: "redis:alpine"
        ports:
            - "6379:6379"
        volumes:
            - ./.docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
        environment:
            - REDIS_REPLICATION_MODE=master
