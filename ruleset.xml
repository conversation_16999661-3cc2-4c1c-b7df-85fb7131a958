<?xml version="1.0"?>
<ruleset name="AcmeProject">
	<config name="installed_paths" value="../../slevomat/coding-standard"/><!-- relative path from PHPCS source location -->
	<rule ref="SlevomatCodingStandard.Arrays.TrailingArrayComma"/>
	<rule ref="SlevomatCodingStandard.ControlStructures.DisallowContinueWithoutIntegerOperandInSwitch"/>
	<!--	<rule ref="SlevomatCodingStandard.ControlStructures.DisallowEmpty"/>-->
	<!--	<rule ref="SlevomatCodingStandard.ControlStructures.RequireNullCoalesceEqualOperator"/>-->
	<rule ref="SlevomatCodingStandard.PHP.UselessSemicolon"/>
	<rule ref="SlevomatCodingStandard.Arrays.MultiLineArrayEndBracketPlacement"/>
	<rule ref="SlevomatCodingStandard.Arrays.SingleLineArrayWhitespace"/>
	<!--	<rule ref="SlevomatCodingStandard.ControlStructures.RequireNullCoalesceOperator"/>-->
	<rule ref="SlevomatCodingStandard.Functions.StaticClosure"/>
	<!--	<rule ref="SlevomatCodingStandard.PHP.DisallowDirectMagicInvokeCall"/>-->
	<rule ref="SlevomatCodingStandard.Operators.RequireCombinedAssignmentOperator"/>
	<rule ref="SlevomatCodingStandard.Functions.UnusedInheritedVariablePassedToClosure"/>
	<rule ref="SlevomatCodingStandard.Functions.UselessParameterDefaultValue"/>
	<rule ref="SlevomatCodingStandard.Classes.RequireConstructorPropertyPromotion"/>
	<rule ref="SlevomatCodingStandard.Classes.ClassMemberSpacing"/>
	<rule ref="SlevomatCodingStandard.Classes.ClassConstantVisibility">
		<properties>
			<property name="fixable" value="true"/>
		</properties>
	</rule>
	<rule ref="SlevomatCodingStandard.Functions.DisallowTrailingCommaInCall">
		<properties>
			<property name="enable" value="false"/>
		</properties>
	</rule>

	<rule ref="SlevomatCodingStandard.Classes.ConstantSpacing"/>
	<rule ref="SlevomatCodingStandard.Classes.DisallowMultiConstantDefinition"/>
	<rule ref="SlevomatCodingStandard.Classes.ParentCallSpacing"/>
	<rule ref="SlevomatCodingStandard.Classes.TraitUseDeclaration"/>
<!--	<rule ref="SlevomatCodingStandard.Classes.TraitUseSpacing"/>-->
	<rule ref="SlevomatCodingStandard.Functions.ArrowFunctionDeclaration"/>
	<rule ref="SlevomatCodingStandard.PHP.UselessSemicolon"/>

	<!-- other sniffs to include -->
	<rule ref="PSR12">
		<exclude name="Generic.WhiteSpace.DisallowTabIndent"/>
		<exclude name="Generic.Files.LineLength.TooLong"/>
		<exclude name="PSR1.Classes.ClassDeclaration.MultipleClasses"/>
		<exclude name="PEAR.Functions.ValidDefaultValue.NotAtEnd"/>
	</rule>
	<rule ref="Generic.WhiteSpace.DisallowSpaceIndent"/>
	<rule ref="Generic.WhiteSpace.ScopeIndent">
		<properties>
			<property name="indent" value="4"/>
			<property name="tabIndent" value="true"/>
		</properties>
	</rule>
	<rule ref="SlevomatCodingStandard.Classes.MethodSpacing">
		<properties>
			<property name="minLinesCount" value="1" />
			<property name="maxLinesCount" value="1" />
		</properties>
	</rule>
	<rule ref="SlevomatCodingStandard.Namespaces.UnusedUses">
		<properties>
			<property name="searchAnnotations" value="true" />
			<property name="maxLinesCount" value="1" />
		</properties>
	</rule>
</ruleset>



	<!--	<rule ref="SlevomatCodingStandard.Classes.DisallowMultiPropertyDefinition"/> rozbiji device detector-->
	<!--	<rule ref="SlevomatCodingStandard.Classes.ModernClassNameReference"/>-->
	<!--	<rule ref="SlevomatCodingStandard.Classes.RequireMultiLineMethodSignature"/>-->
	<!--	<rule ref="SlevomatCodingStandard.ControlStructures.LanguageConstructWithParentheses"/>-->
	<!--	<rule ref="SlevomatCodingStandard.ControlStructures.NewWithParentheses"/>-->
	<!--	<rule ref="SlevomatCodingStandard.ControlStructures.NewWithoutParentheses"/>-->
	<!--	<rule ref="SlevomatCodingStandard.ControlStructures.DisallowShortTernaryOperator"/>-->
	<!--	<rule ref="SlevomatCodingStandard.ControlStructures.RequireMultiLineTernaryOperator"/>-->
	<!--	<rule ref="SlevomatCodingStandard.ControlStructures.RequireNullSafeObjectOperator"/>-->
	<!--	<rule ref="SlevomatCodingStandard.ControlStructures.RequireSingleLineCondition"/>-->
	<!--	<rule ref="SlevomatCodingStandard.ControlStructures.RequireMultiLineCondition"/>-->
	<!--	<rule ref="SlevomatCodingStandard.ControlStructures.RequireShortTernaryOperator"/>-->
