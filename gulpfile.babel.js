// Load plugins
const autoprefixer = require('autoprefixer')
const browserSync = require('browser-sync').create()
const del = require('del')
const gulp = require('gulp')
const plumber = require('gulp-plumber')
const postcss = require('gulp-postcss')
const sass = require('gulp-sass')
const svgstore = require('gulp-svgstore')
const svgmin = require('gulp-svgmin')
const path = require('path')

// Base variables
const paths = {
	localhost: 'https://www.tipli.czlocal/',
	styles: {
		src: 'www/scss/**/*.scss',
		dest: 'www/css/',
		src2: 'www/scss2/**/*.scss',
		dest2: 'www/css2/',
	},
}

// Clean assets
function clean() {
	return del(['./www/webtemp/*/'])
}

// Clean assets
function cleanTemp() {
	return del(['./temp/cache/*/'])
}

// CSS task
function css() {
	return gulp
		.src(paths.styles.src)
		.pipe(plumber())
		.pipe(sass({ outputStyle: 'expanded' }))
		.on('error', function (err) {
			console.log(err.toString())
			this.emit('end')
		})
		.pipe(gulp.dest(paths.styles.dest))
		.pipe(postcss([autoprefixer()]))
		.pipe(gulp.dest(paths.styles.dest))
		.pipe(browserSync.stream())
}

// CSS2 task
function css2() {
	return gulp
		.src(paths.styles.src2)
		.pipe(plumber())
		.pipe(sass({ outputStyle: 'expanded' }))
		.on('error', function (err) {
			console.log(err.toString())
			this.emit('end')
		})
		.pipe(gulp.dest(paths.styles.dest2))
		.pipe(postcss([autoprefixer()]))
		.pipe(gulp.dest(paths.styles.dest2))
		.pipe(browserSync.stream())
}

// Watch files
function watchFiles() {
	gulp.watch(paths.styles.src, css), gulp.watch(paths.styles.src2, css2)
}

// SVG store
function svgStore() {
	return gulp
		.src('./www/images/svg/*.svg')
		.pipe(
			svgmin(function (file) {
				var prefix = path.basename(
					file.relative,
					path.extname(file.relative)
				)
				return {
					plugins: [
						{
							cleanupIDs: {
								prefix: prefix + '-',
								minify: true,
							},
						},
					],
				}
			})
		)
		.pipe(svgstore())
		.pipe(gulp.dest('./www/images/svg/dist/'))
}

// BrowserSync - Live reload browser
gulp.task('browser-sync', function (done) {
	browserSync.init({
		proxy: paths.localhost,
		browser: 'google chrome',
	})

	gulp.watch(paths.styles.src, gulp.series('clean', 'css'))
	gulp.watch('./app/**/*.latte').on('change', browserSync.reload)
	done()
})

// Tasks
gulp.task('default', gulp.series('browser-sync'))

gulp.task('clean', clean)
gulp.task('cleanT', cleanTemp)
gulp.task('css', css)
gulp.task('css2', css2)
gulp.task('svg', svgStore)

// watch
gulp.task('watch', gulp.parallel(watchFiles, clean))
