<svg width="267" height="267" viewBox="0 0 267 267" fill="none" xmlns="http://www.w3.org/2000/svg">
	<g clip-path="url(#clip0_0_3)">
		<g filter="url(#filter0_di_0_3)">
			<path d="M133.523 225.021C184.057 225.021 225.023 184.055 225.023 133.521C225.023 82.9869 184.057 42.021 133.523 42.021C82.9889 42.021 42.023 82.9869 42.023 133.521C42.023 184.055 82.9889 225.021 133.523 225.021Z" fill="url(#paint0_linear_0_3)"/>
			<path d="M133.523 225.021C184.057 225.021 225.023 184.055 225.023 133.521C225.023 82.9869 184.057 42.021 133.523 42.021C82.9889 42.021 42.023 82.9869 42.023 133.521C42.023 184.055 82.9889 225.021 133.523 225.021Z" fill="url(#paint1_linear_0_3)" fill-opacity="0.2"/>
		</g>
		<g filter="url(#filter1_d_0_3)">
			<path d="M133.582 201.08C170.861 201.08 201.082 170.859 201.082 133.58C201.082 96.3008 170.861 66.08 133.582 66.08C96.3028 66.08 66.082 96.3008 66.082 133.58C66.082 170.859 96.3028 201.08 133.582 201.08Z" fill="url(#paint2_linear_0_3)"/>
		</g>
		<path d="M157.5 86.9316L151.703 79.7115L148.348 88.3423L157.5 86.9316ZM151.039 83.561C143.399 80.8839 136.99 79.7722 130.435 80.2299C123.891 80.6869 117.262 82.7047 109.182 86.1955L109.818 87.6678C117.821 84.2103 124.26 82.2688 130.547 81.8298C136.824 81.3914 143.01 82.4469 150.509 85.0746L151.039 83.561Z" fill="url(#paint3_linear_0_3)"/>
		<path d="M109.383 179.953L115.18 187.173L118.534 178.542L109.383 179.953ZM115.844 183.324C123.484 186.001 129.893 187.113 136.448 186.655C142.992 186.198 149.621 184.18 157.701 180.689L157.065 179.217C149.062 182.674 142.623 184.616 136.336 185.055C130.059 185.493 123.873 184.438 116.374 181.81L115.844 183.324Z" fill="url(#paint4_linear_0_3)"/>
	</g>
	<defs>
		<filter id="filter0_di_0_3" x="0.644394" y="0.642395" width="265.757" height="265.757" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset/>
			<feGaussianBlur stdDeviation="20.6893"/>
			<feComposite in2="hardAlpha" operator="out"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.47 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_0_3"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_0_3" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dy="1.60382"/>
			<feGaussianBlur stdDeviation="1.60382"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="shape" result="effect2_innerShadow_0_3"/>
		</filter>
		<filter id="filter1_d_0_3" x="24.7034" y="24.7014" width="217.757" height="217.757" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset/>
			<feGaussianBlur stdDeviation="20.6893"/>
			<feComposite in2="hardAlpha" operator="out"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.47 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_0_3"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_0_3" result="shape"/>
		</filter>
		<linearGradient id="paint0_linear_0_3" x1="135.996" y1="225.022" x2="96.8305" y2="88.6014" gradientUnits="userSpaceOnUse">
			<stop stop-color="#EF7F1A"/>
			<stop offset="1" stop-color="#FFA439"/>
		</linearGradient>
		<linearGradient id="paint1_linear_0_3" x1="42.0234" y1="138.34" x2="219.454" y2="107.787" gradientUnits="userSpaceOnUse">
			<stop stop-color="white" stop-opacity="0"/>
			<stop offset="0.523555" stop-color="white" stop-opacity="0.59"/>
			<stop offset="1" stop-color="white" stop-opacity="0"/>
		</linearGradient>
		<linearGradient id="paint2_linear_0_3" x1="133.582" y1="66.0832" x2="133.582" y2="201.083" gradientUnits="userSpaceOnUse">
			<stop stop-color="#ECEDF0"/>
			<stop offset="0.51" stop-color="white"/>
			<stop offset="1" stop-color="#ECEDF0"/>
		</linearGradient>
		<linearGradient id="paint3_linear_0_3" x1="109.5" y1="83.9316" x2="157.5" y2="83.9316" gradientUnits="userSpaceOnUse">
			<stop stop-color="#F2F3F5"/>
			<stop offset="1" stop-color="#CCD0D7"/>
		</linearGradient>
		<linearGradient id="paint4_linear_0_3" x1="157.383" y1="182.953" x2="109.383" y2="182.953" gradientUnits="userSpaceOnUse">
			<stop stop-color="#F2F3F5"/>
			<stop offset="1" stop-color="#CCD0D7"/>
		</linearGradient>
		<clipPath id="clip0_0_3">
			<rect width="267" height="267" fill="white"/>
		</clipPath>
	</defs>
</svg>
