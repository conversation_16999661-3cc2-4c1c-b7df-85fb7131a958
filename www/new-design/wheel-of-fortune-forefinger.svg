<svg width="84" height="95" viewBox="0 0 84 95" fill="none" xmlns="http://www.w3.org/2000/svg">
	<g filter="url(#filter0_di_1_67)">
		<path d="M37.8685 34.1539C39.7026 30.8587 44.4425 30.8587 46.2767 34.154L58.3041 55.7626C64.2543 66.4528 56.525 79.6008 44.2905 79.6008H39.8547C27.6201 79.6008 19.8908 66.4527 25.841 55.7626L37.8685 34.1539Z" fill="url(#paint0_linear_1_67)"/>
		<path d="M37.8685 34.1539C39.7026 30.8587 44.4425 30.8587 46.2767 34.154L58.3041 55.7626C64.2543 66.4528 56.525 79.6008 44.2905 79.6008H39.8547C27.6201 79.6008 19.8908 66.4527 25.841 55.7626L37.8685 34.1539Z" fill="url(#paint1_linear_1_67)" fill-opacity="0.5"/>
	</g>
	<g filter="url(#filter1_i_1_67)">
		<circle cx="42.026" cy="65.6772" r="7" fill="#D9D9D9" fill-opacity="0.01"/>
	</g>
	<g filter="url(#filter2_i_1_67)">
		<circle cx="42.1297" cy="65.7825" r="5.5" fill="#FBA345"/>
	</g>
	<g filter="url(#filter3_i_1_67)">
		<circle cx="42.2337" cy="65.884" r="4" fill="#865E3B"/>
	</g>
	<defs>
		<filter id="filter0_di_1_67" x="0.698393" y="0.568429" width="82.7484" height="94.1083" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dy="-8.01909"/>
			<feGaussianBlur stdDeviation="11.5475"/>
			<feComposite in2="hardAlpha" operator="out"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.69 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_67"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1_67" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dy="3.20764"/>
			<feGaussianBlur stdDeviation="1.60382"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1_67"/>
		</filter>
		<filter id="filter1_i_1_67" x="33.4222" y="57.0733" width="15.6038" height="15.6038" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="-1.60382" dy="-1.60382"/>
			<feGaussianBlur stdDeviation="0.801909"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.24 0"/>
			<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1_67"/>
		</filter>
		<filter id="filter2_i_1_67" x="36.6297" y="60.2825" width="11.8019" height="11.8019" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0.801909" dy="0.801909"/>
			<feGaussianBlur stdDeviation="1.20286"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"/>
			<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1_67"/>
		</filter>
		<filter id="filter3_i_1_67" x="38.2337" y="61.884" width="8.80191" height="8.80191" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0.801909" dy="0.801909"/>
			<feGaussianBlur stdDeviation="1.20286"/>
			<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0"/>
			<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1_67"/>
		</filter>
		<linearGradient id="paint0_linear_1_67" x1="42.8699" y1="79.6008" x2="32.5283" y2="39.5017" gradientUnits="userSpaceOnUse">
			<stop stop-color="#EF7F1A"/>
			<stop offset="1" stop-color="#FFA439"/>
		</linearGradient>
		<linearGradient id="paint1_linear_1_67" x1="38.0861" y1="54.7069" x2="47.7735" y2="79.5541" gradientUnits="userSpaceOnUse">
			<stop stop-color="white" stop-opacity="0"/>
			<stop offset="0.435" stop-color="white" stop-opacity="0.59"/>
			<stop offset="1" stop-color="white" stop-opacity="0"/>
		</linearGradient>
	</defs>
</svg>
