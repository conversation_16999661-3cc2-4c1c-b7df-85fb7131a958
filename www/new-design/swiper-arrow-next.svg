<svg xmlns="http://www.w3.org/2000/svg" width="80" height="80" viewBox="0 0 80 80" fill="none">
    <foreignObject x="0" y="0.8" width="79.2" height="78.4"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1.5px);clip-path:url(#bgblur_0_7837_2993_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_d_7837_2993)" data-figma-bg-blur-radius="3">
    <rect x="3" y="9" width="48" height="48" rx="24" fill="white"/>
</g>
    <path d="M19 33.0009H34M27.6433 39L33.9986 33L27.6433 27" stroke="#80899C" stroke-width="2" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
    <defs>
        <filter id="filter0_d_7837_2993" x="0" y="0.8" width="79.2" height="78.4" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dx="13" dy="7"/>
            <feGaussianBlur stdDeviation="7.6"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.03 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_7837_2993"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_7837_2993" result="shape"/>
        </filter>
        <clipPath id="bgblur_0_7837_2993_clip_path" transform="translate(0 -0.8)"><rect x="3" y="9" width="48" height="48" rx="24"/>
        </clipPath></defs>
</svg>