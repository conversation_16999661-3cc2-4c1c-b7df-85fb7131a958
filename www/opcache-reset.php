<?php
if (!isset($_GET['token']) || $_GET['token'] != 'tipli8190') {
    die;
}

// Opcache
opcache_reset();

// Cloudflare
purgeCloudflare('685bfc7f0c1882393615104573dacb00');

function purgeCloudflare($identifier)
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://api.cloudflare.com/client/v4/zones/' . $identifier . '/purge_cache');
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

    $headers = [
        'X-Auth-Email:  <EMAIL>',
        'X-Auth-Key: a185649c366072ad9b7863b693cf79b3055c7',
        'Content-Type: application/json'
    ];

    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['purge_everything' => true]));
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    $result = json_decode(curl_exec($ch));

    if ($result->success == false) {
        file_put_contents('cloudflare-errors.txt', "Purge failed - " . date("Y-m-d H:i:s") . "\n", FILE_APPEND);
    }
}