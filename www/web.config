<?xml version="1.0" encoding="UTF-8"?>

<!-- IIS configuration file -->

<configuration>
	<system.webServer>
		<rewrite>
			<rules>
				<rule name="Rewrite Rule 1" stopProcessing="true">
					<match url="\.(pdf|js|ico|gif|jpg|png|css|rar|zip|tar\.gz)$" ignoreCase="false" negate="true" />
					<conditions logicalGrouping="MatchAll">
						<add input="{REQUEST_FILENAME}" matchType="IsFile" ignoreCase="false" negate="true" />
						<add input="{REQUEST_FILENAME}" matchType="IsDirectory" ignoreCase="false" negate="true" />
					</conditions>
					<action type="Rewrite" url="index.php" />
				</rule>
			</rules>
		</rewrite>
		<defaultDocument>
			<files>
				<add value="index.php" />
			</files>
		</defaultDocument>
	</system.webServer>
</configuration>
