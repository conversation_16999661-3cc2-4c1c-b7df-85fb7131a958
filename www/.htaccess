# Apache configuration file (see httpd.apache.org/docs/current/mod/quickreference.html)
# BrowserMatch "Safari" nokeepalive
#php_value newrelic.appname "Tipli"

# START - Disable server signature #
ServerSignature Off
# END - Disable server signature #

# disable directory listing
<IfModule mod_autoindex.c>
	Options -Indexes
</IfModule>

# enable cool URL
<IfModule mod_rewrite.c>
	RewriteEngine On
	# RewriteBase /

    # use www
    RewriteCond %{HTTP_HOST} !^www\.
    RewriteRule ^(.*)$ https://www.%{HTTP_HOST}/$1 [R=301,L]

	# use HTTPS
	RewriteCond %{HTTPS} !on
	RewriteRule .? https://%{HTTP_HOST}%{REQUEST_URI} [R=301,L]

	# prevents files starting with dot to be viewed by browser
	RewriteRule /\.|^\.(?!well-known/) - [F]

	# front controller
	RewriteCond %{REQUEST_FILENAME} !-f
	RewriteCond %{REQUEST_FILENAME} !-d
	RewriteRule !\.(pdf|js|ico|gif|jpg|png|css|rar|zip|tar\.gz|map)$ index.php [L]
</IfModule>

# enable gzip compression
<IfModule mod_deflate.c>
	<IfModule mod_filter.c>
		AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css application/javascript application/json application/xml image/svg+xml
	</IfModule>
</IfModule>
