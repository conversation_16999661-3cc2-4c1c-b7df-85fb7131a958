$(document).ready(function(){
	$('.switch-header').click(function(e) {
		e.preventDefault();
		$('.circle').addClass('active');
		if(!$(this).hasClass('no-scroll')) {
			$('html, body').animate({ scrollTop: $("header").offset().top}, 500); 
		}
	});
	$('#showMore').click(function(e) {
		e.preventDefault();
		$('html, body').animate({ scrollTop: $("#how").offset().top}, 500); 		
	});
	$(window).scroll(function() {
        $(this).scrollTop() > 200 ? $(".fixed-navbar").fadeIn(300) : $(".fixed-navbar").fadeOut(300)
    });
});
	background: url(../img/shops.jpg) no-repeat center center
	background: url(../img/support.jpg) no-repeat center center
