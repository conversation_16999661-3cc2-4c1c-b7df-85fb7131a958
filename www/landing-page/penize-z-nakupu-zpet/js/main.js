var buttonSwitch = document.getElementsByClassName('switch-header');
var buttonShowMore = document.getElementById('showMore');

for (var i=0; i < buttonSwitch.length; i++) {
	buttonSwitch[i].onclick = function(){
		var circle = document.getElementById('circle');
		var loginBlock = document.getElementById('login-block');
		if (document.body.contains(circle)) {
			circle.classList.add('active');
			var header = document.getElementById('header');
			animate(document.scrollingElement || document.documentElement, "scrollTop", "", document.documentElement.scrollTop || document.body.scrollTop, header.offsetTop, 300, true);
		} else if(document.body.contains(loginBlock)) {
			var header = document.getElementById('header');
			animate(document.scrollingElement || document.documentElement, "scrollTop", "", document.documentElement.scrollTop || document.body.scrollTop, header.offsetTop, 300, true);	
		} else {
			var target = document.getElementById('login-column');
			animate(document.scrollingElement || document.documentElement, "scrollTop", "", document.documentElement.scrollTop || document.body.scrollTop, target.offsetTop - 100, 300, true);
		}

		var inputs = document.getElementsByTagName('input');

		for(var i = 0; i < inputs.length; i++) {
		    if(inputs[i].type == 'email') {
		        inputs[i].focus();
		    }
		}
	}
}

if (document.body.contains(buttonShowMore)) {
    buttonShowMore.onclick = function(e) {
		var target = document.getElementById('how');
		animate(document.scrollingElement || document.documentElement, "scrollTop", "", document.documentElement.scrollTop || document.body.scrollTop, target.offsetTop - 100, 300, true);
	};
}

function animate(elem, style, unit, from, to, time, prop) {
    if (!elem) {
        return;
    }
    var start = new Date().getTime(),
        timer = setInterval(function () {
            var step = Math.min(1, (new Date().getTime() - start) / time);
            if (prop) {
                elem[style] = (from + step * (to - from))+unit;
            } else {
                elem.style[style] = (from + step * (to - from))+unit;
            }
            if (step === 1) {
                clearInterval(timer);
            }
        }, 1);
    if (prop) {
    	  elem[style] = from+unit;
    } else {
    	  elem.style[style] = from+unit;
    }
}

var numbersLoaded = false;

window.onscroll = function(){
	var navbar = document.getElementById('navbar');
	if((document.documentElement.scrollTop || document.body.scrollTop) > 200) {
		navbar.classList.add('fadeIn');
	} else {
		navbar.classList.remove('fadeIn');
	}

	var numbers = document.getElementById('users-number');
	console.log(isScrolledIntoView(numbers));
	if(isScrolledIntoView(numbers) && numbersLoaded == false) {
		numbersLoaded = true;
		var usersNumber = document.getElementById('users-number');
		var shopsNumber = document.getElementById('shops-number');
		var moneyNumber = document.getElementById('money-number');
		var options = {
		  useEasing : true, 
		  useGrouping : true, 
		  separator : ' '
		};
		var users = new CountUp(usersNumber, 1, usersNumber.getAttribute('data-to-number'), 0, 3, options);
		users.start(function() {
    		usersNumber.innerHTML = usersNumber.innerHTML+"+";
		});

		var shops = new CountUp(shopsNumber, 1, shopsNumber.getAttribute('data-to-number'), 0, 3, options);
		shops.start(function() {
    		shopsNumber.innerHTML = shopsNumber.innerHTML+"+";
		});

		var money = new CountUp(moneyNumber, 1, moneyNumber.getAttribute('data-to-number'), 0, 3, options);
		money.start(function() {
            moneyNumber.innerHTML = moneyNumber.innerHTML+"+ "+ moneyNumber.getAttribute('data-currency');
        });
	}
};

function isScrolledIntoView(el)
{
	var top = el.offsetTop;
	var left = el.offsetLeft;
	var width = el.offsetWidth;
	var height = el.offsetHeight;

	while(el.offsetParent) {
	el = el.offsetParent;
	top += el.offsetTop;
	left += el.offsetLeft;
	}

	return (
	top < (window.pageYOffset + window.innerHeight) &&
	left < (window.pageXOffset + window.innerWidth) &&
	(top + height) > window.pageYOffset &&
	(left + width) > window.pageXOffset
	);
}

function loadBackgrounds() {
	var benefits = document.getElementById('benefits');
	var shops = document.getElementById('shops');
	var support = document.getElementById('support');
	benefits.style.background = "url("+resourcesPath+"/img/benefits.jpg) no-repeat center center";
	benefits.style.backgroundSize = "cover";
	shops.style.background = "url("+resourcesPath+"/img/shops.jpg) no-repeat center center";
	shops.style.backgroundSize = "cover";
	support.style.background = "url("+resourcesPath+"/img/support.jpg) no-repeat center center";
	support.style.backgroundSize = "cover";
}

if(window.attachEvent) {
    window.attachEvent('onload', loadBackgrounds());
} else {
    if(window.onload) {
        var curronload = window.onload;
        var newonload = function(evt) {
            curronload(evt);
            loadBackgrounds();
        };
        window.onload = newonload;
    } else {
        window.onload = loadBackgrounds();
    }
}