<?php

declare(strict_types=1);

if ($_SERVER['HTTP_HOST'] === 'www.tiplino.com' || $_SERVER['HTTP_HOST'] === 'tiplino.com') {
	header("HTTP/1.1 301 Moved Permanently");
	header("Location: https://www.tipli.cz" . $_SERVER["REQUEST_URI"]);
	exit;
}

if (file_exists(__DIR__ . '/maintenance.php') && !isset($_COOKIE['2743e0d2bb88'])) {
	require __DIR__ . '/maintenance.php';
}

require __DIR__ . '/../vendor/autoload.php';
require __DIR__ . '/../app/Bootstrap.php';

$configurator = tipli\Bootstrap::boot();
$container = $configurator->createContainer();
$application = $container->getByType(Nette\Application\Application::class);
$application->run();

